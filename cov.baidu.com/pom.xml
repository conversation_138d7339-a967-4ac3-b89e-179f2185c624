<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baidu.bce</groupId>
        <artifactId>api-logic-chpc-parent</artifactId>
        <version>${api-logic-chpc-version}</version>
    </parent>

    <artifactId>cov.baidu.com</artifactId>
    <!--<groupId>baidu.com</groupId>
    <version>1.0</version>-->
    <packaging>pom</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <profiles>
        <!--单测命令：mvn verify -Pjacoco-->
        <!--maven命令行产出覆盖率
        #运行单测并开启覆盖率统计，执行后会在产出目录生成jacoco.exec
        mvn clean org.jacoco:jacoco-maven-plugin:0.8.7:prepare-agent test -Dmaven.test.failure.ignore=true
        #产出可读的html单测报告， 在产出目录的site下。多模时需加选项 -aggregate 进行单测报告的合并
        http://cov.baidu.com/module/index?module=463868
        mvn surefire-report:report-only -aggregate
        -->
        <profile>
            <id>jacoco</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.jacoco</groupId>
                        <artifactId>jacoco-maven-plugin</artifactId>
                        <version>0.8.9</version>
                        <configuration>
                            <excludes>
                                <exclude>**/*Config.*</exclude>
                                <exclude>**/*Dev.*</exclude>
                                <exclude>**/*Mapper.*</exclude>
                                <exclude>**/*DO.*</exclude>
                                <exclude>**/*Test.*</exclude>
                                <exclude>**/*Tests.*</exclude>
                                <exclude>**/*Dto.*</exclude>
                                <exclude>**/domain/**/*</exclude>
                                <exclude>**/dto/**/*</exclude>
                                <exclude>/com/baidu/bce/Application.*</exclude>
                                <exclude>/com/baidu/bce/*</exclude>
                            </excludes>
                        </configuration>
                        <executions>
                            <execution>
                                <id>default</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>report-aggregate</goal>
                                </goals>
                                <configuration>
                                    <outputDirectory>${project.basedir}/../target/site/jacoco</outputDirectory>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <!-- add project module info as below -->
    <dependencies>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-chpc-adapter</artifactId>
            <version>${api-logic-chpc-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-chpc-app</artifactId>
            <version>${api-logic-chpc-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-chpc-client</artifactId>
            <version>${api-logic-chpc-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-chpc-domain</artifactId>
            <version>${api-logic-chpc-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-chpc-infrastructure</artifactId>
            <version>${api-logic-chpc-version}</version>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-chpc-start</artifactId>
            <version>${api-logic-chpc-version}</version>
        </dependency>
    </dependencies>

    <!-- The remote central repository -->
    <distributionManagement>
        <repository>
            <id>Baidu_Local</id>
            <url>https://maven.baidu-int.com/nexus/content/repositories/Baidu_Local</url>
        </repository>
        <snapshotRepository>
            <id>Baidu_Local_Snapshots</id>
            <url>https://maven.baidu-int.com/nexus/content/repositories/Baidu_Local_Snapshots</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>baidu-nexus</id>
            <url>https://maven.baidu-int.com/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <!--设定除中央仓库(repo1.maven.org/maven2/)外的其他仓库,按设定顺序进行查找-->
        <repository>
            <id>Baidu Nexus snapshots</id>
            <name>Nexus Public Repository</name>
            <url>https://maven.baidu-int.com/nexus/content/groups/public-snapshots</url>
        </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <id>baidu-nexus</id>
            <url>https://maven.baidu-int.com/nexus/content/groups/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
    <!-- Baidu Repository End -->

</project>