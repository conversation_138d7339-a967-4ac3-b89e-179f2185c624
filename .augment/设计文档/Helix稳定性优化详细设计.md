````markdown path=.augment/设计文档/Helix稳定性优化详细设计.md mode=EDIT
# Helix稳定性优化详细设计文档

## 1. 项目背景

### 1.1 现状问题
- billing订单失败时不会主动同步到我们服务，导致失败订单的任务一直处于运行中状态
- 用户提交任务过多时，billing定时查询订单状态对chpc控制面和slurm调度器造成压力
- helixfold3调度器中排队任务过多时会导致调度器卡死

### 1.2 设计目标
- 定期请求billing查询并同步异常订单状态，及时更新异常任务状态
- 在不影响用户体验的情况下控制helixfold3调度器中排队任务数量（最大10万作业）
- 利用缓存或消息队列降低对helixfold3调度器的访问需求

### 1.3 优化范围
**本次优化仅针对helixfold3集群，包括以下产品类型：**
- helixfold3
- hf3agab  
- miniprotein_design
- antibody_design

## 2. 系统架构设计

### 2.1 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Helix Web     │    │   Billing       │    │   CHPC 控制面   │
│                 │    │   Service       │    │                 │
│ ┌─────────────┐ │    │                 │    │ ┌─────────────┐ │
│ │ 任务提交    │ │────┼─────────────────┼────┤ │ 订单执行器  │ │
│ └─────────────┘ │    │                 │    │ └─────────────┘ │
│                 │    │ ┌─────────────┐ │    │                 │
│                 │    │ │ 订单查询    │ │────┤                 │
│                 │    │ └─────────────┘ │    │ ┌─────────────┐ │
└─────────────────┘    └─────────────────┘    │ │ 缓存服务    │ │
                                              │ └─────────────┘ │
┌─────────────────┐    ┌─────────────────┐    │ ┌─────────────┐ │
│   消息队列      │    │   Redis缓存     │    │ │ 定时任务    │ │
│   (RabbitMQ)    │    │                 │    │ └─────────────┘ │
│                 │    │                 │    └─────────────────┘
└─────────────────┘    └─────────────────┘              │
         │                       │                      │
         └───────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Helixfold3调度器│
                    │   (最大10万作业) │
                    └─────────────────┘
```

### 2.2 核心组件说明
1. **任务缓冲控制器**：控制向helixfold3调度器提交的作业数量，确保不超过10万限制
2. **消息队列系统**：异步处理helixfold3作业状态更新
3. **缓存服务**：减少对helixfold3调度器的直接查询
4. **异常任务检查器**：定期同步helixfold3异常任务状态

## 3. 数据模型设计

### 3.1 数据库表结构修改

#### 3.1.1 helix_job表扩展
```sql
-- 仅针对helixfold3相关任务添加权重字段
ALTER TABLE helix_job ADD COLUMN job_weight INT DEFAULT 1 COMMENT '作业权重，仅helixfold3使用';
ALTER TABLE helix_job MODIFY COLUMN job_status VARCHAR(50) COMMENT '作业状态，新增READY_TO_SUBMIT';
```

#### 3.1.2 helixfold3集群配置表
```sql
CREATE TABLE helixfold3_job_limit (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    cluster_id VARCHAR(64) NOT NULL COMMENT '集群ID，固定为helixfold3',
    max_running_jobs INT NOT NULL DEFAULT 100000 COMMENT '最大运行作业数，固定10万',
    task_type_weights JSON COMMENT 'helixfold3任务类型权重配置',
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY uk_cluster_id (cluster_id)
) COMMENT='helixfold3集群作业限制配置表';

-- 插入helixfold3默认配置
INSERT INTO helixfold3_job_limit (cluster_id, max_running_jobs, task_type_weights) 
VALUES ('helixfold3', 100000, '{"helixfold3": 1, "hf3agab": 1, "miniprotein_design": 2, "antibody_design": 2}');
```

### 3.2 核心数据结构

#### 3.2.1 作业状态枚举扩展
- PENDING("PENDING", "待提交")
- READY_TO_SUBMIT("READY_TO_SUBMIT", "可提交") // 新增状态，仅helixfold3使用
- SUBMITTED("SUBMITTED", "已提交")
- RUNNING("RUNNING", "运行中")
- COMPLETED("COMPLETED", "已完成")
- FAILED("FAILED", "失败")
- USER_CANCEL("USER_CANCEL", "用户取消")

#### 3.2.2 helixfold3消息格式
- jobId: 作业ID
- taskId: 任务ID  
- jobStatus: 作业状态
- runTime: 运行时间
- timestamp: 时间戳
- clusterId: 集群ID，固定为helixfold3
- taskType: 任务类型(helixfold3/hf3agab/miniprotein_design/antibody_design)
- errorMessage: 错误信息（可选）

#### 3.2.3 helixfold3集群配置模型
- clusterId: 固定为helixfold3
- maxRunningJobs: 固定10万
- taskTypeWeights: 任务类型权重配置
  - helixfold3: 权重1
  - hf3agab: 权重1
  - miniprotein_design: 权重2
  - antibody_design: 权重2

**请确认以上数据结构格式是否正确，特别是helixfold3任务类型权重配置**

## 4. 详细模块设计

### 4.1 任务缓冲机制

#### 4.1.1 helixfold3作业提交控制流程
```
┌─────────────────┐
│   定时器启动    │
│   (每30秒)      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 查询helixfold3  │
│ 当前运行权重    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 计算可用权重    │
│ (10万-当前权重) │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 查询READY_TO_   │
│ SUBMIT状态作业  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 遍历作业列表    │
│ (按创建时间排序)│
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 计算当前作业    │
│ 权重            │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐      ┌─────────────────┐
│ 权重足够？      │ ────▶│ 跳过当前作业    │
│ (可用权重>=作业 │      │ 处理下一个      │
│ 权重)           │      └─────────────────┘
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 提交到调度器    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 更新作业状态    │
│ 为SUBMITTED     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 减少可用权重    │
│ 继续下一个作业  │
└─────────────────┘
```

#### 4.1.2 订单执行器修改流程
```
┌─────────────────┐
│ 接收任务提交    │
│ 请求            │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 创建订单        │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐      ┌─────────────────┐
│ 是helixfold3    │ ────▶│ 设置状态为      │
│ 任务？          │      │ READY_TO_SUBMIT │
└─────────┬───────┘      └─────────────────┘
          │
          ▼
┌─────────────────┐
│ 其他集群保持    │
│ 原有逻辑        │
└─────────────────┘
```

#### 4.1.3 helixfold3状态一致性保障机制

##### 4.1.3.1 全面状态检查机制
```
┌─────────────────┐
│ 全量检查定时器  │
│ (每30分钟)      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 查询所有非终态  │
│ helixfold3任务  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 分批处理        │
│ (每批50个)      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 遍历每个任务    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐      ┌─────────────────┐
│ 检查SUBMITTING  │ ────▶│ 执行补偿逻辑    │
│ 状态超时任务    │      │ (见4.1.2)       │
└─────────┬───────┘      └─────────────────┘
          │
          ▼
┌─────────────────┐      ┌─────────────────┐
│ 检查SUBMITTED   │ ────▶│ 查询集群实际    │
│ 状态任务        │      │ 状态并同步      │
└─────────┬───────┘      └─────────┬───────┘
          │
          ▼
┌─────────────────┐      ┌─────────────────┐
│ 检查READY_TO_   │ ────▶│ 检查是否超时    │
│ SUBMIT状态任务  │      │ 并处理          │
└─────────┬───────┘      └─────────────────┘
          │
          ▼
┌─────────────────┐
│ 记录检查结果    │
│ 和修复统计      │
└─────────────────┘
```

##### 4.1.3.2 双重提交保护机制
```
┌─────────────────┐
│ 提交任务到      │
│ 调度器          │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 生成唯一提交ID  │
│ (UUID)          │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 写入Redis记录   │
│ (提交ID+任务信息)│
│ (5分钟过期)     │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 执行实际提交    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐      ┌─────────────────┐
│ 提交成功？      │ ────▶│ 删除Redis记录   │
│                 │      │ 更新失败状态    │
└─────────┬───────┘      └─────────────────┘
          │
          ▼
┌─────────────────┐
│ 更新数据库状态  │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 删除Redis记录   │
└─────────────────┘
```

##### 4.1.3.3 孤儿任务检测机制
```
┌─────────────────┐
│ 孤儿检测定时器  │
│ (每10分钟)      │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 扫描Redis中     │
│ 未删除的提交记录│
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 遍历每条记录    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ 查询数据库中    │
│ 任务状态        │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐      ┌─────────────────┐
│ 状态是否为      │ ────▶│ 查询集群中      │
│ SUBMITTING?     │      │ 任务状态        │
└─────────┬───────┘      └─────────┬───────┘
          │                        │
          ▼                        ▼
┌─────────────────┐      ┌─────────────────┐
│ 跳过正常任务    │      │ 更新数据库状态  │
└─────────────────┘      │ 为实际状态      │
                         └─────────────────┘
```

##### 4.1.3.4 集群状态主动同步机制
在helixfold3集群端增加主动推送机制，定期（每5分钟）将所有运行中任务的状态推送到消息队列，控制面消费消息并更新数据库状态，确保即使控制面服务中断，后续也能恢复数据一致性。

```json
// 集群端主动推送的消息格式
{
  "type": "ACTIVE_SYNC",
  "timestamp": "2024-01-01T10:00:00Z",
  "jobs": [
    {
      "jobId": "slurm_job_67890",
      "status": "RUNNING",
      "startTime": "2024-01-01T09:50:00Z",
      "runTime": "600"
    },
    // 更多作业...
  ]
}
