1 背景
* billing 订单失败时不会主动同步到我们服务，会导致失败订单的任务一直处于运行中状态
* 用户提交任务过多时
    * 由于 billing 会定时调用我们的接口查询订单状态，billing 的查询请求会对 chpc 控制面和 slurm 调度器造成比较大的压力
    * 调度器中存在的排队任务过多时，会导致调度器卡死


2 设计目标
* 定期请求 billing 查询并同步异常订单的状态，及时更新异常任务的状态
* 在尽量不影响用户体验的情况下控制调度器中排队任务的数量
* 利用缓存或消息队列降低对 slurm 调度器的访问需求

3 系统设计
3.1 状态同步机制
3.1.1 现状
* 提交任务流程图

[流程图]
* 订单状态流转

[流程图]
* helix-web 任务状态流转

[流程图]
* chpc 控制面任务状态流转

[流程图]
3.1 任务缓冲机制
3.1.1 主要问题
* 集群中作业数量过多时可能导致机器宕机
* 限制集群可提交作业量后，用户提交任务导致作业量超过集群限制时，会导致任务提交失败，影响用户体验

3.1.2 解决方案
* CHPC控制面增加一个作业状态：可提交
* 当订单执行器调用CHPC控制面提交作业时，不再立刻提交作业，而是将作业状态置为 可提交
* CHPC控制面增加一个定时执行器
    * 扫描正在运行中的作业数量
    * 查询状态处于 可提交 的作业
    * 根据集群中的作业数量限制和扫描到的正在运行中的作业数量，提交状态处于 可提交 的作业

* 不同的任务类型对应不同的作业数量，以此来计算当前集群中正在运行的作业数量和可以提交的作业数量

[流程图]
3.2 任务状态缓存机制
3.2.1 主要问题
* 订单查询请求会频繁访问CHPC控制面查询任务状态
* CHPC控制面现在是直接去调度器查询对应的作业状态
* 当订单查询的访问量过大时，会导致CHPC对调度器的请求过多，进而导致调度器宕机

3.2.2 解决方案
* 消息队列 + 线程池 + 缓存
* 消息队列
    * 在集群中的作业完成时，将作业状态同步到与控制面共享的消息队列中，控制面定时消费消息，根据消息更新数据库的作业状态
    * billing服务查询作业状态时，可以直接返回本地保存的作业状态
    * 消息队列稳定性：重试机制
    * 消息队列持久化：改为多机

* 线程池
    * 用线程池来执行定时消费消息任务

* 缓存
    * 增加任务缓冲机制后，能同时存在的billing订单也将大幅增加，billing的订单查询频率也相应增加，需要用redis在访问数据库前增加一层缓存


[流程图]
3.3 异常任务同步机制
3.3.1 主要问题
* 有两种异常的任务无法将状态同步到控制面
    * 订单支付失败
    * 作业脚本执行失败导致消息未发送到消息队列


3.3.2 解决方案
* chpc 控制面增加一个定时检查异常任务的执行器
* 异常任务
    * 处于待提交状态且创建时间在1小时之前的任务
        * 如果任务对应的订单ID为空，说明创建订单失败后删除任务失败，则删除此任务
        * 根据任务对应的订单ID请求 billing 查询订单状态
        * 如果 billing 订单状态异常（退款成功、退款失败、已过期、创建失败、取消），则将任务状态改为提交失败

    * 处于提交成功且更新时间在24小时之前的任务
        * 请求集群查询任务对应的作业状态
        * 更新对应的作业状态，如果作业已完成，则说明有异常问题导致消息未同步，执行报警操作


