<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baidu.bce</groupId>
        <artifactId>api-logic-chpc-parent</artifactId>
        <version>${api-logic-chpc-version}</version>
    </parent>

    <artifactId>api-logic-chpc-infrastructure</artifactId>


    <dependencies>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-chpc-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>api-logic-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-tag-sdk</artifactId>
            <version>1.0.240.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baidu.bce</groupId>
                    <artifactId>bce-internal-sdk-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-actuator-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-metadata</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.dynamic-sql</groupId>
            <artifactId>mybatis-dynamic-sql</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>dev.akkinoc.spring.boot</groupId>
            <artifactId>logback-access-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-skywalking-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-idempotent-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-user-settings-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-plat-servicecatalog-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-service-bus-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.11.2</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.11.2</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.11.2</version>
        </dependency>
    </dependencies>
</project>