package com.baidu.bce.logic.chpc.bct.client;

import java.util.Map;

import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;

import com.baidu.bce.logic.chpc.bct.model.BctQueryEventsRequest;
import com.baidu.bce.logic.chpc.bct.model.BctQueryEventsResponse;

public class BctNioClient extends BceNioClient {
    
    public BctNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public BctQueryEventsResponse queryEvents(BctQueryEventsRequest request, String accountId) {
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path("/v1/events/query")
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(BctQueryEventsResponse.class).block();
    }

}
