package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.database.dataobject.QueueDO;
import com.baidu.bce.logic.chpc.database.mapper.QueueMapper;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.model.Queue;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.queue;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
@Service
public class QueueDAOGatewayImpl implements QueueDAOGateway {

    @Resource
    private QueueMapper queueMapper;

    @Override
    public Long count(String clusterId, String queueId, String status) {

        SelectStatementProvider selectStatement =
                select(SqlBuilder.count())
                        .from(queue)
                        .where(queue.queueId, isEqualToWhenPresent(queueId))
                        .and(queue.deleted, isEqualTo(false))
                        .and(queue.clusterId, isEqualTo(clusterId))
                        .and(queue.status, isEqualToWhenPresent(status))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);

        return queueMapper.count(selectStatement);
    }

    @Override
    public Boolean isExisted(String clusterId, String queueId, String status) {
        return count(clusterId, queueId, status) != 0;
    }

    @Override
    public Queue getByQueueId(String queueId) {
        if (StringUtils.isEmpty(queueId)) {
            return null;
        }

        SelectStatementProvider selectStatement = select(QueueMapper.selectList)
                .from(queue)
                .where(queue.queueId, isEqualTo(queueId))
                .and(queue.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        QueueDO queueDO = queueMapper.selectOne(selectStatement)
                .orElse(null);
        return BeanCopyUtil.copyObject(queueDO, Queue::new);
    }

    @Override
    public Queue getByQueueName(String queueName, String clusterId) {

        if (StringUtils.isEmpty(queueName)) {
            return null;
        }

        SelectStatementProvider selectStatement = select(QueueMapper.selectList)
                .from(queue)
                .where(queue.name, isEqualTo(queueName))
                .and(queue.deleted, isEqualTo(false))
                .and(queue.clusterId, isEqualToWhenPresent(clusterId))
                .build().render(RenderingStrategies.MYBATIS3);


        QueueDO queueDO = queueMapper.selectOne(selectStatement)
                .orElse(null);
        return BeanCopyUtil.copyObject(queueDO, Queue::new);
    }

    @Override
    public List<Queue> getByQueueIds(Collection<String> queueIds) {

        SelectStatementProvider selectStatement = select(QueueMapper.selectList)
                .from(queue)
                .where(queue.queueId, isInWhenPresent(queueIds))
                .and(queue.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);


        List<QueueDO> queueDOS = queueMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(queueDOS, Queue::new);
    }

    @Override
    public List<Queue> findAllByQueueIds(Collection<String> queueIds) {
        if (CollectionUtils.isEmpty(queueIds)) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(QueueMapper.selectList)
                .from(queue)
                .where(queue.queueId, isIn(queueIds))
                .and(queue.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);


        List<QueueDO> queueDOS = queueMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(queueDOS, Queue::new);
    }

    @Override
    public List<Queue> getByIsAutoScaling(boolean isAutoScaling) {
        SelectStatementProvider selectStatement = select(QueueMapper.selectList)
                .from(queue)
                .where(queue.isAutoScale, isInWhenPresent(isAutoScaling))
                .and(queue.deleted, isEqualTo(false))
                .orderBy(queue.createdTime.descending())
                .build().render(RenderingStrategies.MYBATIS3);


        List<QueueDO> queueDOS = queueMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(queueDOS, Queue::new);
    }


    @Override
    public Queue getByName(String clusterId, String queueName) {

        SelectStatementProvider selectStatement = select(QueueMapper.selectList)
                .from(queue)
                .where(queue.clusterId, isEqualTo(clusterId))
                .and(queue.name, isEqualTo(queueName))
                .and(queue.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);


        List<QueueDO> groups = queueMapper.selectMany(selectStatement);
        QueueDO queueDO = null;
        for (int i = 0; i < groups.size(); i++) {
            if (queueName.equals(groups.get(i).getName())) {
                queueDO = groups.get(i);
                break;
            }
        }
        return BeanCopyUtil.copyObject(queueDO, Queue::new);
    }

    @Override
    public Queue getClusterDefaultQueue(String clusterId) {
        SelectStatementProvider selectStatement = select(QueueMapper.selectList)
                .from(queue)
                .where(queue.clusterId, isEqualTo(clusterId))
                .and(queue.isDefault, isEqualTo(true))
                .build().render(RenderingStrategies.MYBATIS3);

        QueueDO queueDO = queueMapper.selectOne(selectStatement)
                .orElse(null);
        return BeanCopyUtil.copyObject(queueDO, Queue::new);
    }


    @Override
    public Boolean insert(Queue queue) {

        QueueDO queueDO = BeanCopyUtil.copyObject(queue, QueueDO::new);
        queueDO.setDeleted(false);
        queueDO.setUpdatedTime(LocalDateTime.now());
        queueDO.setCreatedTime(LocalDateTime.now());

        return queueMapper.insertSelective(queueDO) != 0;
    }


    @Override
    public List<Queue> listByClusterId(String clusterId) {

        SelectStatementProvider selectStatement = select(QueueMapper.selectList)
                .from(queue)
                .where(queue.clusterId, isEqualTo(clusterId))
                .and(queue.deleted, isEqualTo(false))
                .orderBy(queue.createdTime.descending())
                .build().render(RenderingStrategies.MYBATIS3);

        List<QueueDO> groups = queueMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(groups, Queue::new);
    }

    @Override
    public List<Queue> listByStatus(List<String> statusList) {

        SelectStatementProvider selectStatement =
                select(QueueMapper.selectList)
                        .from(queue)
                        .where(queue.status, isInWhenPresent(statusList))
                        .and(queue.deleted, isEqualTo(false))
                        .orderBy(queue.createdTime.descending())
                        .build().render(RenderingStrategies.MYBATIS3);

        List<QueueDO> groups = queueMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(groups, Queue::new);
    }

    @Override
    public Boolean updateStatus(String groupId, String status) {

        QueueDO queueDO = new QueueDO();
        queueDO.setQueueId(groupId);
        queueDO.setUpdatedTime(LocalDateTime.now());
        queueDO.setStatus(status);
        return queueMapper.updateByQueueId(queueDO) != 0;
    }

    @Override
    public Boolean update(Queue queue) {

        QueueDO queueDO = BeanCopyUtil.copyObject(queue, QueueDO::new);
        queueDO.setUpdatedTime(LocalDateTime.now());
        return queueMapper.updateByQueueId(queueDO) != 0;
    }


    @Override
    public Boolean delete(String queueId) {

        QueueDO queueDO = new QueueDO();
        queueDO.setQueueId(queueId);
        queueDO.setUpdatedTime(LocalDateTime.now());
        queueDO.setStatus(QueueStatus.DELETED.nameLowerCase());
        queueDO.setDeleted(true);

        return queueMapper.updateByQueueId(queueDO) != 0;
    }

    @Override
    public Boolean deleteWithStatus(String groupId, QueueStatus status) {

        QueueDO queueDO = new QueueDO();
        queueDO.setQueueId(groupId);
        queueDO.setUpdatedTime(LocalDateTime.now());
        queueDO.setStatus(status.nameLowerCase());
        queueDO.setDeleted(true);

        return queueMapper.updateByQueueId(queueDO) != 0;
    }
}
