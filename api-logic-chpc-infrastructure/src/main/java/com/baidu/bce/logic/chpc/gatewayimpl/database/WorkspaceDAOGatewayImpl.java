package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.WorkspaceDO;
import com.baidu.bce.logic.chpc.database.mapper.WorkspaceMapper;
import com.baidu.bce.logic.chpc.gateway.WorkspaceDAOGateway;
import com.baidu.bce.logic.chpc.model.Workspace;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.workspace;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
@Slf4j
@Service
public class WorkspaceDAOGatewayImpl implements WorkspaceDAOGateway {
    @Resource
    private WorkspaceMapper workspaceMapper;


    @Override
    public Boolean createWorkspace(Workspace workspace) {
        WorkspaceDO workspaceDO = new WorkspaceDO();
        BeanUtils.copyProperties(workspace, workspaceDO);
        workspaceDO.setDeleted(false);
        workspaceDO.setCreatedTime(LocalDateTime.now());
        workspaceDO.setUpdatedTime(LocalDateTime.now());
        return workspaceMapper.insert(workspaceDO) != 0;
    }

    @Override
    public List<Workspace> listWorkspace(String name, String accountId) {
        SelectStatementProvider selectStatement =
                select(WorkspaceMapper.selectList)
                        .from(workspace)
                        .where(workspace.deleted, isEqualTo(false))
                        .and(workspace.name, isLikeWhenPresent("%" + name + "%"))
                        .and(workspace.accountId, isEqualTo(accountId))
                        .orderBy(workspace.createdTime.descending())
                        .build()
                        .render(RenderingStrategies.MYBATIS3);

        List<WorkspaceDO> workspaceDO = workspaceMapper.selectMany(selectStatement);

        return BeanCopyUtil.copyListProperties(workspaceDO, Workspace::new);
    }

    @Override
    public Workspace getWorkspace(String workspaceId, String accountId) {
        SelectStatementProvider selectStatement =
                select(WorkspaceMapper.selectList)
                        .from(workspace)
                        .where(workspace.workspaceId, isEqualTo(workspaceId))
                        .and(workspace.deleted, isEqualTo(false))
                        .and(workspace.accountId, isEqualTo(accountId))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
        WorkspaceDO workspaceDO = workspaceMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(workspaceDO, Workspace::new);
    }

    @Override
    public Workspace getWorkspaceByName(String workspaceName, String accountId) {
        SelectStatementProvider selectStatement =
                select(WorkspaceMapper.selectList)
                        .from(workspace)
                        .where(workspace.name, isEqualTo(workspaceName))
                        .and(workspace.deleted, isEqualTo(false))
                        .and(workspace.accountId, isEqualTo(accountId))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
        WorkspaceDO workspaceDO = workspaceMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(workspaceDO, Workspace::new);
    }

    @Override
    public List<Workspace> getWorkspaceByClusterId(String clusterId) {
        SelectStatementProvider selectStatement =
                select(WorkspaceMapper.selectList)
                        .from(workspace)
                        .where(workspace.deleted, isEqualTo(false))
                        .and(workspace.clusterId, isEqualToWhenPresent(clusterId))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);

        List<WorkspaceDO> workspaceDO = workspaceMapper.selectMany(selectStatement);

        return BeanCopyUtil.copyListProperties(workspaceDO, Workspace::new);
    }


    @Override
    public Boolean updateWorkspace(String workspaceId, String status, String accountId) {
        WorkspaceDO workspaceDO = new WorkspaceDO();
        workspaceDO.setWorkspaceId(workspaceId);
        workspaceDO.setStatus(status);
        workspaceDO.setAccountId(accountId);
        workspaceDO.setUpdatedTime(LocalDateTime.now());
        return workspaceMapper.updateByWorkspaceId(workspaceDO) != 0;
    }

    @Override
    public Boolean deleteWorkspace(String workspaceId, String accountId) {
        WorkspaceDO workspaceDO = new WorkspaceDO();
        workspaceDO.setDeleted(true);
        workspaceDO.setDeletedTime(LocalDateTime.now());
        workspaceDO.setWorkspaceId(workspaceId);
        workspaceDO.setAccountId(accountId);
        return workspaceMapper.updateByWorkspaceId(workspaceDO) != 0;
    }
}
