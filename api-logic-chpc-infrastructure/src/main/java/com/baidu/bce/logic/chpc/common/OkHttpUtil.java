package com.baidu.bce.logic.chpc.common;


import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Slf4j
public class OkHttpUtil {

    private static OkHttpClient okHttpClient;

    private Map<String, String> headerMap;

    private Map<String, String> paramMap;

    private Map<String, Object> requestBodyMap;

    private String url;
    private Request.Builder request;


    private OkHttpUtil() {
        if (okHttpClient != null) {
            return;
        }

        synchronized (OkHttpUtil.class) {
            okHttpClient = new OkHttpClient.Builder()
                    .connectTimeout(15, TimeUnit.SECONDS)
                    .writeTimeout(20, TimeUnit.SECONDS)
                    .readTimeout(20, TimeUnit.SECONDS)
                    .retryOnConnectionFailure(true)
                    .build();
        }
    }

    public static OkHttpUtil builder() {
        return new OkHttpUtil();
    }


    public OkHttpUtil url(String url) {
        this.url = url;
        return this;
    }

    /**
     * 添加参数
     *
     * @param key   参数名
     * @param value 参数值
     * @return
     */
    public OkHttpUtil addParam(String key, String value) {
        if (paramMap == null) {
            paramMap = new LinkedHashMap<>(16);
        }
        paramMap.put(key, value);
        return this;
    }

    public OkHttpUtil addPostBody(String key, Object value) {
        if (requestBodyMap == null) {
            requestBodyMap = new LinkedHashMap<>(16);
        }
        requestBodyMap.put(key, value);
        return this;
    }

    /**
     * 添加请求头
     *
     * @param key   参数名
     * @param value 参数值
     * @return
     */
    public OkHttpUtil addHeader(String key, String value) {
        if (headerMap == null) {
            headerMap = new LinkedHashMap<>(16);
        }
        headerMap.put(key, value);
        return this;
    }

    /**
     * 初始化get方法
     *
     * @return
     */
    public OkHttpUtil get() {
        request = new Request.Builder().get();
        StringBuilder urlBuilder = new StringBuilder(url);
        if (paramMap != null) {
            urlBuilder.append("?");
            try {
                for (Map.Entry<String, String> entry : paramMap.entrySet()) {
                    urlBuilder.append(URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8))
                            .append("=")
                            .append(URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8))
                            .append("&");
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            urlBuilder.deleteCharAt(urlBuilder.length() - 1);
        }
        request.url(urlBuilder.toString());
        return this;
    }

    /**
     * 初始化post方法
     *
     * @return
     */
    public OkHttpUtil post() {

        RequestBody requestBody = RequestBody.create(
                MediaType.parse("application/json; charset=utf-8"),
                JacksonUtil.toJson(requestBodyMap));

        request = new Request.Builder().post(requestBody).url(url);
        return this;
    }

    public OkHttpUtil delete() {

        RequestBody requestBody = RequestBody.create(
                MediaType.parse("application/json; charset=utf-8"),
                JacksonUtil.toJson(requestBodyMap));

        request = new Request.Builder().delete(requestBody).url(url);
        return this;
    }

    /**
     * 同步请求
     *
     * @return
     */
    public String sync() {

        Response response = null;
        setHeader(request);
        try {
            response = okHttpClient.newCall(request.build()).execute();
            return response.body().string();
        } catch (Exception e) {
            log.error("failed to send http request: {}, response, error:{}",
                    JacksonUtil.toJson(request),
                    JacksonUtil.toJson(response), e);

            throw new CommonExceptions.InternalServerErrorException();
        }
    }


    /**
     * 为request添加请求头
     *
     * @param request
     */
    private void setHeader(Request.Builder request) {
        if (headerMap != null) {
            try {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    request.addHeader(entry.getKey(), entry.getValue());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


}
