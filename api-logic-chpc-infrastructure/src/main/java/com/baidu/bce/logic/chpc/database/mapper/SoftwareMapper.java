package com.baidu.bce.logic.chpc.database.mapper;

import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.category;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.dependency;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.description;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.nodeType;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.software;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.supportArch;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.supportOs;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.version;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.baidu.bce.logic.chpc.database.dataobject.SoftwareDO;

import jakarta.annotation.Generated;

@Mapper
public interface SoftwareMapper extends CommonCountMapper, CommonDeleteMapper,
        CommonInsertMapper<SoftwareDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            name,
            version,
            description,
            category,
            supportOs,
            supportArch,
            dependency,
            nodeType);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "softwareDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "version", property = "version", jdbcType = JdbcType.VARCHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR),
            @Result(column = "category", property = "category", jdbcType = JdbcType.VARCHAR),
            @Result(column = "support_os", property = "supportOs", jdbcType = JdbcType.VARCHAR),
            @Result(column = "support_arch", property = "supportArch", jdbcType = JdbcType.VARCHAR),
            @Result(column = "dependency", property = "dependency", jdbcType = JdbcType.VARCHAR),
            @Result(column = "node_type", property = "nodeType", jdbcType = JdbcType.VARCHAR)

    })
    List<SoftwareDO> selectMany(SelectStatementProvider selectStatement);

    /**
     * {@inheritDoc}
     *
     * 默认实现，使用MyBatis3Utils计算数量。
     *
     * @param completer CountDSLCompleter类型的对象，用于完成DSL语句的构建
     * @return long类型，返回查询结果的总数量
     * @see org.mybatis.generator.api.Mapper#count(CountDSLCompleter)
     * @since 2021-09-17
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, software, completer);
    }

    /**
     * {@inheritDoc}
     * 删除一条记录，返回受影响的行数。
     *
     * @param completer DeleteDSLCompleter实例，用于指定需要删除的记录条件
     * @return 受影响的行数，如果没有符合条件的记录则返回0
     * @throws org.apache.ibatis.binding.BindingException 如果发生任何数据库操作错误，将抛出此异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, software, completer);
    }

    /**
     * 根据主键删除记录。
     *
     * @param key 主键值，类型为Long
     * @return 返回受影响的行数，类型为int
     * @since 2021/3/15
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c -> c.where(id, isEqualTo(key)));
    }

    /**
     * 插入一条记录到 SoftwareDO 表中。
     *
     * @param row SoftwareDO 类型的对象，包含要插入的数据。
     * @return 返回插入后生成的主键值（自增长或者UUID）。
     * @throws org.apache.ibatis.binding.BindingException 如果出现任何数据库操作错误。
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(SoftwareDO row) {
        return MyBatis3Utils.insert(this::insert, row, software, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(name).toProperty("name")
                .map(version).toProperty("version")
                .map(description).toProperty("description")
                .map(category).toProperty("category")
                .map(supportOs).toProperty("supportOs")
                .map(supportArch).toProperty("supportArch")
                .map(dependency).toProperty("dependency")
                .map(nodeType).toProperty("nodeType"));
    }

    /**
     * 批量插入多条记录到数据库中。
     *
     * @param records 待插入的记录集合，每个元素为一条记录
     * @return 返回插入成功的记录数，如果所有记录都插入失败则返回0
     * @throws org.apache.ibatis.binding.BindingException 如果存在任何未知属性或者不支持的类型，则会抛出此异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<SoftwareDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, software, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(name).toProperty("name")
                .map(version).toProperty("version")
                .map(description).toProperty("description")
                .map(category).toProperty("category")
                .map(supportOs).toProperty("supportOs")
                .map(supportArch).toProperty("supportArch")
                .map(dependency).toProperty("dependency")
                .map(nodeType).toProperty("nodeType"));
    }

    /**
     * 插入或更新 SoftwareDO 对象，并返回影响的行数。如果 id 不为空则进行更新操作，否则进行插入操作。
     * 当 id 不为空时，会将
     * createdTime、updatedTime、deleted、name、version、description、category、supportOs、supportArch、dependency
     * 字段更新到数据库中。
     * 当 id 为空时，会将
     * name、version、description、category、supportOs、supportArch、dependency 字段插入到数据库中。
     *
     * @param row SoftwareDO 对象，包含需要插入或更新的数据
     * @return int 返回影响的行数，0表示没有任何影响（可能是因为已经存在相同的记录）
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(SoftwareDO row) {
        return MyBatis3Utils.insert(this::insert, row, software,
                c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(name).toPropertyWhenPresent("name", row::getName)
                        .map(version).toPropertyWhenPresent("version", row::getVersion)
                        .map(description).toPropertyWhenPresent("description", row::getDescription)
                        .map(category).toPropertyWhenPresent("category", row::getCategory)
                        .map(supportOs).toPropertyWhenPresent("supportOs", row::getSupportOs)
                        .map(supportArch).toPropertyWhenPresent("supportArch", row::getSupportArch)
                        .map(dependency).toPropertyWhenPresent("dependency", row::getDependency)
                        .map(nodeType).toPropertyWhenPresent("nodeType", row::getDependency));
    }

    /**
     * {@inheritDoc}
     *
     * 根据条件查询单个 SoftwareDO 对象，返回一个 Optional。如果没有找到匹配的记录，则返回空 Optional。
     *
     * @param completer SelectDSLCompleter 实例，用于完成 SQL 语句的构建
     * @return Optional<SoftwareDO> 包装了可能存在的 SoftwareDO 对象，或者为空
     *         <ul>
     *         <li>如果找到匹配的记录，则返回包装了该记录</li>
     *         <li>如果没有找到匹配的记录，则返回空 Optional</li>
     *         </ul>
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<SoftwareDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, software, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("softwareDOResult")
    Optional<SoftwareDO> selectOne(SelectStatementProvider selectStatement);

    /**
     * {@inheritDoc}
     *
     * 使用MyBatis3Utils工具类执行查询操作，返回结果为List<SoftwareDO>。
     *
     * @param completer SelectDSLCompleter实例，用于完成查询语句的构建
     * @return List<SoftwareDO> 查询到的结果列表，如果没有符合条件的记录则返回空列表
     * @see org.mybatis.generator.utils.MyBatis3Utils#selectList(java.util.function.Function,
     *      java.lang.String, java.lang.Object,
     *      org.mybatis.generator.utils.SelectDSLCompleter)
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<SoftwareDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, software, completer);
    }

    /**
     * 查询不同的软件信息列表。
     *
     * @param completer SelectDSLCompleter，用于完成SelectDSL语句的构建
     * @return List<SoftwareDO>，不同的软件信息列表
     * @see org.mybatis.generator.api.MyBatisGenerator#generated(String)
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<SoftwareDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, software, completer);
    }

    /**
     * 根据主键查询 SoftwareDO 实体对象。
     *
     * @param key 主键，类型为 Long
     * @return 返回一个包含 SoftwareDO 实体对象的 Optional，如果不存在则为空
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<SoftwareDO> selectByPrimaryKey(Long key) {
        return selectOne(c -> c.where(id, isEqualTo(key)));
    }

    /**
     * {@inheritDoc}
     * 更新软件信息
     *
     * @param completer UpdateDSLCompleter，更新条件和更新值的组合器
     * @return int，受影响的行数，如果为0则表示没有更新任何行
     * @throws org.apache.ibatis.binding.BindingException 绑定异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, software, completer);
    }

    /**
     * 更新所有列的值，并返回更新DSL对象。
     *
     * @param row 需要更新的行数据，类型为SoftwareDO
     * @param dsl 更新DSL对象，类型为UpdateDSL<UpdateModel>
     * @return 返回更新后的更新DSL对象，类型为UpdateDSL<UpdateModel>
     * @generated UpdateDSL<UpdateModel> updateAllColumns(SoftwareDO row,
     *            UpdateDSL<UpdateModel> dsl)
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(SoftwareDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(name).equalTo(row::getName)
                .set(version).equalTo(row::getVersion)
                .set(description).equalTo(row::getDescription)
                .set(category).equalTo(row::getCategory)
                .set(supportOs).equalTo(row::getSupportOs)
                .set(supportArch).equalTo(row::getSupportArch)
                .set(dependency).equalTo(row::getDependency)
                .set(nodeType).equalTo(row::getNodeType);
    }

    /**
     * 更新非空字段的值，并返回更新DSL对象。
     *
     * @param row SoftwareDO类型的行数据，包含需要更新的非空字段值
     * @param dsl UpdateDSL类型的更新DSL对象，用于设置和执行更新操作
     * @return UpdateDSL<UpdateModel>类型的更新DSL对象，包含已经设置了非空字段的更新条件
     * @throws GeneratedAnnotationException 如果方法被生成器生成时产生此异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SoftwareDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(name).equalToWhenPresent(row::getName)
                .set(version).equalToWhenPresent(row::getVersion)
                .set(description).equalToWhenPresent(row::getDescription)
                .set(category).equalToWhenPresent(row::getCategory)
                .set(supportOs).equalToWhenPresent(row::getSupportOs)
                .set(supportArch).equalToWhenPresent(row::getSupportArch)
                .set(dependency).equalToWhenPresent(row::getDependency)
                .set(nodeType).equalToWhenPresent(row::getNodeType);
    }

    /**
     * 更新指定主键的 SoftwareDO 记录。
     *
     * @param row SoftwareDO 对象，包含需要更新的字段值。
     * @return 返回影响的行数，如果没有找到匹配的记录则返回0。
     * @throws org.apache.ibatis.binding.BindingException 如果出现任何数据库操作错误。
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(SoftwareDO row) {
        return update(c -> c.set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(name).equalTo(row::getName)
                .set(version).equalTo(row::getVersion)
                .set(description).equalTo(row::getDescription)
                .set(category).equalTo(row::getCategory)
                .set(supportOs).equalTo(row::getSupportOs)
                .set(supportArch).equalTo(row::getSupportArch)
                .set(dependency).equalTo(row::getDependency)
                .set(nodeType).equalTo(row::getNodeType)
                .where(id, isEqualTo(row::getId)));
    }

    /**
     * 根据主键更新 SoftwareDO 对象中非空字段，并返回受影响的行数。
     *
     * @param row SoftwareDO 对象，包含需要更新的字段值
     * @return 受影响的行数，如果不存在则返回0
     * @throws org.apache.ibatis.binding.BindingException 如果出现任何错误，将抛出
     *                                                    BindingException 异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKeySelective(SoftwareDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(name).equalToWhenPresent(row::getName)
                .set(version).equalToWhenPresent(row::getVersion)
                .set(description).equalToWhenPresent(row::getDescription)
                .set(category).equalToWhenPresent(row::getCategory)
                .set(supportOs).equalToWhenPresent(row::getSupportOs)
                .set(supportArch).equalToWhenPresent(row::getSupportArch)
                .set(dependency).equalToWhenPresent(row::getDependency)
                .set(nodeType).equalToWhenPresent(row::getNodeType)
                .where(id, isEqualTo(row::getId)));
    }

    // @Generated("org.mybatis.generator.api.MyBatisGenerator")
    // default int updateByOrderId(SoftwareDO row) {
    // return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
    // .set(supportArch).equalToWhenPresent(row::getSupportArch)
    // .set(updatedTime).equalTo(row::getUpdatedTime)
    // .set(deleted).equalToWhenPresent(row::getDeleted)
    // .where(clusterId, isEqualTo(row::getClusterId)));
    // }

}