package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.ServiceDO;
import com.baidu.bce.logic.chpc.database.mapper.ServiceMapper;
import com.baidu.bce.logic.chpc.gateway.ServiceDAOGateway;
import com.baidu.bce.logic.chpc.model.ChpcService;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceDynamicSqlSupport.service;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;

@Slf4j
@Service
public class ServiceDAOGatewayImpl implements ServiceDAOGateway {
    @Resource
    private ServiceMapper serviceMapper;


    @Override
    public Boolean isExisted(Long serviceId) {
        SelectStatementProvider selectStatement =
                select(SqlBuilder.count())
                        .from(service)
                        .where(service.id, isEqualTo(serviceId))
                        .and(service.deleted, isEqualTo(false))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);

        return serviceMapper.count(selectStatement) == 1;
    }

    @Override
    public List<ChpcService> getALl(String name) {
        SelectStatementProvider selectStatement =
                select(ServiceMapper.selectList)
                        .from(service)
                        .where(service.deleted, isEqualTo(false))
                        .and(service.name, isLikeWhenPresent("%" + name + "%"))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);


        List<ServiceDO> serviceDOS = serviceMapper.selectMany(selectStatement);

        return BeanCopyUtil.copyListProperties(serviceDOS, ChpcService::new);
    }
}
