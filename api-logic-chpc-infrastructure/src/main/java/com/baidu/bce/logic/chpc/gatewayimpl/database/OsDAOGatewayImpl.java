package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.OsDO;
import com.baidu.bce.logic.chpc.database.mapper.OsMapper;
import com.baidu.bce.logic.chpc.gateway.OsDAOGateway;
import com.baidu.bce.logic.chpc.model.Os;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.os;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class OsDAOGatewayImpl implements OsDAOGateway {
    @Resource
    private OsMapper osMapper;

    @Override
    public List<Os> osList(String schedulerType, String schedulerVersion) {
        SelectStatementProvider selectStatement = select(OsMapper.selectList)
                .from(os)
                .where(os.schedulerType, isEqualTo(schedulerType))
                .and(os.deleted, isEqualTo(false))
                .and(os.schedulerVersion, isEqualTo(schedulerVersion))
                .orderBy(os.updatedTime.descending())
                .build().render(RenderingStrategies.MYBATIS3);

        List<OsDO> orderDOList = osMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(orderDOList, Os::new);
    }
}
