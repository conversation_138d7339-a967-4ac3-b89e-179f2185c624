package com.baidu.bce.logic.chpc.config;

import com.alibaba.ttl.TransmittableThreadLocal;

/**
 * @Author: lilu24
 * @Date: 2022-12-21
 */
public class SchedulerThreadLocalHolder {

    private static final ThreadLocal<String> ACCOUNT_ID = new TransmittableThreadLocal<>();

    public static String getAccountId() {
        return ACCOUNT_ID.get();
    }

    public static void setAccountId(String accountId) {
        ACCOUNT_ID.set(accountId);
    }


    public static void clear() {
        ACCOUNT_ID.remove();
    }
}
