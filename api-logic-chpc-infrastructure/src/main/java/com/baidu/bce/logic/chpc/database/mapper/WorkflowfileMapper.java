package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.WorkflowfileDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.workflowfile;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.workflowId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.type;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.path;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.content;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.deletedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.version;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
@Mapper
public interface WorkflowfileMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<WorkflowfileDO>, CommonUpdateMapper {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(WorkflowfileDO row) {
        return MyBatis3Utils.insert(this::insert, row, workflowfile, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getUpdatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(workflowId).toPropertyWhenPresent("workflowId", row::getWorkflowId)
                        .map(type).toPropertyWhenPresent("type", row::getType)
                        .map(path).toPropertyWhenPresent("path", row::getPath)
                        .map(version).toPropertyWhenPresent("version", row::getVersion)
                        .map(content).toPropertyWhenPresent("content", row::getContent)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<WorkflowfileDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, workflowfile, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(workflowId).toProperty("workflowId")
                        .map(type).toProperty("type")
                        .map(path).toProperty("path")
                        .map(version).toProperty("version")
                        .map(content).toProperty("content")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deletedTime,
            deleted,
            version,
            workflowId,
            type,
            path,
            content
    );


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "WorkflowfileDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted_time", property = "deletedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.TINYINT),
            @Result(column = "workflow_id", property = "workflowId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "type", property = "type", jdbcType = JdbcType.VARCHAR),
            @Result(column = "path", property = "path", jdbcType = JdbcType.VARCHAR),
            @Result(column = "content", property = "content", jdbcType = JdbcType.VARCHAR)
    })
    List<WorkflowfileDO> selectMany(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("WorkflowfileDOResult")
    Optional<WorkflowfileDO> selectOne(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<WorkflowfileDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, workflowfile, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByWorkflowId(WorkflowfileDO row) {
        return update(c ->
                c.set(content).equalToWhenPresent(row::getContent)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deletedTime).equalToWhenPresent(row::getDeletedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(path).equalToWhenPresent(row::getPath)
                        .set(type).equalToWhenPresent(row::getType)
                        .where(workflowId, isEqualTo(row::getWorkflowId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, workflowfile, completer);
    }
}
