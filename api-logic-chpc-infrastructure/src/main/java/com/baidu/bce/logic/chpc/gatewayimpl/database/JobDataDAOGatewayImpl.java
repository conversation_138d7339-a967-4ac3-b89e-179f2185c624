package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.JobStatusType;
import com.baidu.bce.logic.chpc.database.dataobject.JobDataDO;
import com.baidu.bce.logic.chpc.database.mapper.JobDataMapper;
import com.baidu.bce.logic.chpc.gateway.JobDataDAOGateway;
import com.baidu.bce.logic.chpc.model.JobData;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.jobData;

@Service
public class JobDataDAOGatewayImpl implements JobDataDAOGateway {

    @Resource
    private JobDataMapper jobDataMapper;
    @Override
    public Boolean insert(JobData jobData) {
        JobDataDO jobDataDO = BeanCopyUtil.copyObject(jobData, JobDataDO::new);
        jobDataDO.setDeleted(false);
        jobDataDO.setCreatedTime(LocalDateTime.now());
        jobDataDO.setUpdatedTime(LocalDateTime.now());
        return jobDataMapper.insert(jobDataDO) != 0;
    }

    @Override
    public Boolean updateByJobId(String jobId, String runTime, String jobStatus) {
        JobDataDO jobDataDO = new JobDataDO();
        jobDataDO.setJobId(jobId);
        jobDataDO.setRunTime(runTime);
        jobDataDO.setJobStatus(jobStatus);
        jobDataDO.setUpdatedTime(LocalDateTime.now());
        return jobDataMapper.updateByJobId(jobDataDO) != 0;
    }

    @Override
    public Boolean delete(String accountId) {
        JobDataDO jobDataDO = new JobDataDO();
        jobDataDO.setAccountId(accountId);
        jobDataDO.setDeleted(true);
        jobDataDO.setUpdatedTime(LocalDateTime.now());
        return jobDataMapper.updateByAccountId(jobDataDO) != 0;
    }

    @Override
    public List<JobData> findUnfinishedTasks(String accountId) {
        SelectStatementProvider selectStatement =
                select(JobDataMapper.selectList)
                        .from(jobData)
                        .where(jobData.jobStatus, isNotEqualTo(JobStatusType.EXIT.toString()))
                        .and(jobData.jobStatus, isNotEqualTo(JobStatusType.FINISH.toString()))
                        .and(jobData.accountId, isEqualTo(accountId))
                        .and(jobData.deleted, isEqualTo(false))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);

        List<JobDataDO> jobDataDOList = jobDataMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(jobDataDOList, JobData::new);
    }

    @Override
    public JobData findBy(String jobId) {

        SelectStatementProvider selectStatement = select(jobDataMapper.selectList)
                .from(jobData)
                .where(jobData.jobId, isEqualTo(jobId))
                .and(jobData.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);


        JobDataDO jobDataDO = jobDataMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(jobDataDO, JobData::new);
    }
}
