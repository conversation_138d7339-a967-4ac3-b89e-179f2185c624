package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.SqlColumn;
import org.mybatis.dynamic.sql.SqlTable;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;

public final class TemplateDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.473513+08:00", comments="Source Table: t_chpc_job_template")
    public static final TemplateDO templateDO = new TemplateDO();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.473669+08:00", comments="Source field: t_chpc_job_template.id")
    public static final SqlColumn<Long> id = templateDO.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.473808+08:00", comments="Source field: t_chpc_job_template.name")
    public static final SqlColumn<String> name = templateDO.name;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.473845+08:00", comments="Source field: t_chpc_job_template.cluster_id")
    public static final SqlColumn<String> clusterId = templateDO.clusterId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.473876+08:00", comments="Source field: t_chpc_job_template.job_name")
    public static final SqlColumn<String> jobName = templateDO.jobName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.473912+08:00", comments="Source field: t_chpc_job_template.job_cmd")
    public static final SqlColumn<String> jobCmd = templateDO.jobCmd;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.473948+08:00", comments="Source field: t_chpc_job_template.queue_name")
    public static final SqlColumn<String> queueName = templateDO.queueName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474029+08:00", comments="Source field: t_chpc_job_template.post_cmd")
    public static final SqlColumn<String> postCmd = templateDO.postCmd;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474224+08:00", comments="Source field: t_chpc_job_template.deleted")
    public static final SqlColumn<Boolean> deleted = templateDO.deleted;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474277+08:00", comments="Source field: t_chpc_job_template.is_unique")
    public static final SqlColumn<Boolean> isUnique = templateDO.isUnique;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474312+08:00", comments="Source field: t_chpc_job_template.nhosts")
    public static final SqlColumn<Integer> nhosts = templateDO.nhosts;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474348+08:00", comments="Source field: t_chpc_job_template.ncpus")
    public static final SqlColumn<Integer> ncpus = templateDO.ncpus;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474378+08:00", comments="Source field: t_chpc_job_template.walltime")
    public static final SqlColumn<Integer> walltime = templateDO.walltime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474412+08:00", comments="Source field: t_chpc_job_template.stdout_path")
    public static final SqlColumn<String> stdoutPath = templateDO.stdoutPath;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474441+08:00", comments="Source field: t_chpc_job_template.stderr_path")
    public static final SqlColumn<String> stderrPath = templateDO.stderrPath;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474471+08:00", comments="Source field: t_chpc_job_template.bos_file_path")
    public static final SqlColumn<String> bosFilePath = templateDO.bosFilePath;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474503+08:00", comments="Source field: t_chpc_job_template.decompress_cmd")
    public static final SqlColumn<String> decompressCmd = templateDO.decompressCmd;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474534+08:00", comments="Source field: t_chpc_job_template.create_time")
    public static final SqlColumn<LocalDateTime> createTime = templateDO.createTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.4746+08:00", comments="Source field: t_chpc_job_template.update_time")
    public static final SqlColumn<LocalDateTime> updateTime = templateDO.updateTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474635+08:00", comments="Source field: t_chpc_job_template.env_vars")
    public static final SqlColumn<String> envVars = templateDO.envVars;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.473602+08:00", comments="Source Table: t_chpc_job_template")
    public static final class TemplateDO extends SqlTable {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> clusterId = column("cluster_id", JDBCType.VARCHAR);

        public final SqlColumn<String> jobName = column("job_name", JDBCType.VARCHAR);

        public final SqlColumn<String> jobCmd = column("job_cmd", JDBCType.VARCHAR);

        public final SqlColumn<String> queueName = column("queue_name", JDBCType.VARCHAR);

        public final SqlColumn<String> postCmd = column("post_cmd", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BOOLEAN);

        public final SqlColumn<Boolean> isUnique = column("is_unique", JDBCType.BOOLEAN);

        public final SqlColumn<Integer> nhosts = column("nhosts", JDBCType.INTEGER);

        public final SqlColumn<Integer> ncpus = column("ncpus", JDBCType.INTEGER);

        public final SqlColumn<Integer> walltime = column("walltime", JDBCType.INTEGER);

        public final SqlColumn<String> stdoutPath = column("stdout_path", JDBCType.VARCHAR);

        public final SqlColumn<String> stderrPath = column("stderr_path", JDBCType.VARCHAR);

        public final SqlColumn<String> bosFilePath = column("bos_file_path", JDBCType.VARCHAR);

        public final SqlColumn<String> decompressCmd = column("decompress_cmd", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> envVars = column("env_vars", JDBCType.LONGVARCHAR);

        public TemplateDO() {
            super("t_chpc_job_template");
        }
    }
}