package com.baidu.bce.logic.chpc.database.dataobject;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;

public class JobDataDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String poolId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobStatus;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobCpu;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobMemory;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String runTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean



            deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobId() {
        return jobId;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobId(String jobId) {
        this.jobId = jobId;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobStatus() {
        return jobStatus;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobCpu() {
        return jobCpu;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobCpu(String jobCpu) {
        this.jobCpu = jobCpu;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobMemory() {
        return jobMemory;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobMemory(String jobMemory) {
        this.jobMemory = jobMemory;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getRunTime() {
        return runTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setRunTime(String runTime) {
        this.runTime = runTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getPoolId() {
        return poolId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setPoolId(String poolId) {
        this.poolId = poolId;
    }

}
