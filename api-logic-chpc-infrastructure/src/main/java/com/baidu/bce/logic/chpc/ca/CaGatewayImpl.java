package com.baidu.bce.logic.chpc.ca;

import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.baidu.bce.logic.chpc.ca.client.CaNioClient;
import com.baidu.bce.logic.chpc.ca.gateway.CaGateway;
import com.baidu.bce.logic.chpc.ca.model.CreateActionRequest;
import com.baidu.bce.logic.chpc.ca.model.CreateActionResponse;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class CaGatewayImpl implements CaGateway {
    
    @Resource
    private CaNioClient caNioClient;

    @Override
    public CreateActionResponse instanceCreateAction(CreateActionRequest request) {
        CreateActionResponse response = new CreateActionResponse();
        log.debug("start to create action");
        try {
            response = caNioClient.createAction(request, LogicUserService.getAccountId());
        } catch (WebClientResponseException e) {
            log.error("failed to create action, error: {}", e.getResponseBodyAsString());
        }  catch (Exception e) {
            log.error("failed to create action, error: {}", e.getMessage());
        }
        return response;
    }
}
