package com.baidu.bce.logic.chpc.database.dataobject;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;

public class OsDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String schedulerType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String schedulerVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String osArch;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String osName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String osVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSchedulerType() {
        return schedulerType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSchedulerType(String schedulerType) {
        this.schedulerType = schedulerType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSchedulerVersion() {
        return schedulerVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSchedulerVersion(String schedulerVersion) {
        this.schedulerVersion = schedulerVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getOsArch() {
        return osArch;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setOsArch(String osArch) {
        this.osArch = osArch;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getOsName() {
        return osName;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setOsName(String osName) {
        this.osName = osName;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getOsVersion() {
        return osVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setOsVersion(String osVersion) {
        this.osVersion = osVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
}
