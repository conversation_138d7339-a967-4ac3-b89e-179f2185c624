package com.baidu.bce.logic.chpc.bct;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.bct.client.BctNioClient;
import com.baidu.bce.logic.chpc.bct.gateway.BctGateway;
import com.baidu.bce.logic.chpc.bct.model.BctQueryEventsRequest;
import com.baidu.bce.logic.chpc.bct.model.BctQueryEventsResponse;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BctImpl implements BctGateway {
    @Resource
    IamLogicService iamLogicService;

    @Value("${bce.web.commons.sdk.bct.endpoint}")
    private String endpoint;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private BctNioClient bctNioClient;

    @Override
    public BctQueryEventsResponse queryEvents(BctQueryEventsRequest request) {
        return bctNioClient.queryEvents(request, LogicUserService.getAccountId());
    }
}
