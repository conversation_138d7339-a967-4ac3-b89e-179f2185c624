package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.InstanceDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.chargeType;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.cosStackId;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.eipNetworkCapacity;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.floatingIp;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.queueId;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.hostName;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.instance;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.instanceId;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.nodeType;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.instanceUuid;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.oosExecutionId;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.privateIp;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.publicIp;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.spec;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.subnetId;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.schedulerIp;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.schedulerHost;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.sshPort;
import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.portalPort;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

@Mapper
public interface InstanceMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<InstanceDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            clusterId,
            instanceId,
            instanceUuid,
            queueId,
            nodeType,
            subnetId,
            eipNetworkCapacity,
            cosStackId,
            hostName,
            oosExecutionId,
            status,
            spec,
            privateIp,
            publicIp,
            floatingIp,
            schedulerIp,
            schedulerHost,
            sshPort,
            portalPort,
            chargeType);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "InstanceDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.TINYINT),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "instance_id", property = "instanceId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "instance_uuid", property = "instanceUuid", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_id", property = "queueId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "instance_type", property = "nodeType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "subnet_id", property = "subnetId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "eip_network_capacity", property = "eipNetworkCapacity", jdbcType = JdbcType.INTEGER),
            @Result(column = "cos_stack_id", property = "cosStackId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "host_name", property = "hostName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "oos_execution_id", property = "oosExecutionId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "spec", property = "spec", jdbcType = JdbcType.VARCHAR),
            @Result(column = "private_ip", property = "privateIp", jdbcType = JdbcType.VARCHAR),
            @Result(column = "public_ip", property = "publicIp", jdbcType = JdbcType.VARCHAR),
            @Result(column = "floating_ip", property = "floatingIp", jdbcType = JdbcType.VARCHAR),
            @Result(column = "scheduler_ip", property = "schedulerIp", jdbcType = JdbcType.VARCHAR),
            @Result(column = "scheduler_host", property = "schedulerHost", jdbcType = JdbcType.VARCHAR),
            @Result(column = "ssh_port", property = "sshPort", jdbcType = JdbcType.BIGINT),
            @Result(column = "portal_port", property = "portalPort", jdbcType = JdbcType.BIGINT),
            @Result(column = "charge_type", property = "chargeType", jdbcType = JdbcType.VARCHAR)
    })
    List<InstanceDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, instance, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, instance, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(InstanceDO row) {
        return MyBatis3Utils.insert(this::insert, row, instance, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(clusterId).toProperty("clusterId")
                        .map(instanceId).toProperty("instanceId")
                        .map(instanceUuid).toProperty("instanceUuid")
                        .map(queueId).toProperty("queueId")
                        .map(nodeType).toProperty("nodeType")
                        .map(subnetId).toProperty("subnetId")
                        .map(eipNetworkCapacity).toProperty("eipNetworkCapacity")
                        .map(cosStackId).toProperty("cosStackId")
                        .map(hostName).toProperty("hostName")
                        .map(oosExecutionId).toProperty("oosExecutionId")
                        .map(status).toProperty("status")
                        .map(spec).toProperty("spec")
                        .map(privateIp).toProperty("privateIp")
                        .map(publicIp).toProperty("publicIp")
                        .map(schedulerIp).toProperty("schedulerIp")
                        .map(schedulerHost).toProperty("schedulerHost")
                        .map(floatingIp).toProperty("floatingIp")
                        .map(sshPort).toProperty("sshPort")
                        .map(portalPort).toProperty("portalPort")
                        .map(chargeType).toProperty("chargeType")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<InstanceDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, instance, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(clusterId).toProperty("clusterId")
                        .map(instanceId).toProperty("instanceId")
                        .map(instanceUuid).toProperty("instanceUuid")
                        .map(queueId).toProperty("queueId")
                        .map(nodeType).toProperty("nodeType")
                        .map(subnetId).toProperty("subnetId")
                        .map(eipNetworkCapacity).toProperty("eipNetworkCapacity")
                        .map(cosStackId).toProperty("cosStackId")
                        .map(hostName).toProperty("hostName")
                        .map(oosExecutionId).toProperty("oosExecutionId")
                        .map(status).toProperty("status")
                        .map(spec).toProperty("spec")
                        .map(privateIp).toProperty("privateIp")
                        .map(publicIp).toProperty("publicIp")
                        .map(floatingIp).toProperty("floatingIp")
                        .map(schedulerIp).toProperty("schedulerIp")
                        .map(schedulerHost).toProperty("schedulerHost")
                        .map(sshPort).toProperty("sshPort")
                        .map(portalPort).toProperty("portalPort")
                        .map(chargeType).toProperty("chargeType")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(InstanceDO row) {
        return MyBatis3Utils.insert(this::insert, row, instance, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                        .map(instanceId).toPropertyWhenPresent("instanceId", row::getInstanceId)
                        .map(instanceUuid).toPropertyWhenPresent("instanceUuid", row::getInstanceUuid)
                        .map(queueId).toPropertyWhenPresent("queueId", row::getQueueId)
                        .map(nodeType).toPropertyWhenPresent("nodeType", row::getNodeType)
                        .map(eipNetworkCapacity).toPropertyWhenPresent("eipNetworkCapacity", row::getEipNetworkCapacity)
                        .map(cosStackId).toPropertyWhenPresent("cosStackId", row::getCosStackId)
                        .map(hostName).toPropertyWhenPresent("hostName", row::getHostName)
                        .map(oosExecutionId).toPropertyWhenPresent("oosExecutionId", row::getOosExecutionId)
                        .map(status).toPropertyWhenPresent("status", row::getStatus)
                        .map(spec).toPropertyWhenPresent("spec", row::getSpec)
                        .map(privateIp).toPropertyWhenPresent("privateIp", row::getPrivateIp)
                        .map(publicIp).toPropertyWhenPresent("publicIp", row::getPublicIp)
                        .map(floatingIp).toPropertyWhenPresent("floating_ip", row::getFloatingIp)
                        .map(schedulerIp).toPropertyWhenPresent("scheduler_ip", row::getFloatingIp)
                        .map(schedulerHost).toPropertyWhenPresent("scheduler_host", row::getFloatingIp)
                        .map(sshPort).toPropertyWhenPresent("ssh_port", row::getSshPort)
                        .map(portalPort).toPropertyWhenPresent("portal_port", row::getPortalPort)
                        .map(chargeType).toPropertyWhenPresent("charge_type", row::getChargeType)

        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("InstanceDOResult")
    Optional<InstanceDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<InstanceDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, instance, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<InstanceDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, instance, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<InstanceDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, instance, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<InstanceDO> selectByPrimaryKey(Long key) {
        return selectOne(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, instance, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(InstanceDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(clusterId).equalTo(row::getClusterId)
                .set(instanceId).equalTo(row::getInstanceId)
                .set(instanceUuid).equalTo(row::getInstanceUuid)
                .set(queueId).equalTo(row::getQueueId)
                .set(nodeType).equalTo(row::getNodeType)
                .set(eipNetworkCapacity).equalTo(row::getEipNetworkCapacity)
                .set(cosStackId).equalTo(row::getCosStackId)
                .set(hostName).equalTo(row::getHostName)
                .set(oosExecutionId).equalTo(row::getOosExecutionId)
                .set(status).equalTo(row::getStatus)
                .set(spec).equalTo(row::getSpec)
                .set(privateIp).equalTo(row::getPrivateIp)
                .set(publicIp).equalTo(row::getPublicIp)
                .set(schedulerIp).equalTo(row::getSchedulerIp)
                .set(schedulerHost).equalTo(row::getSchedulerHost)
                .set(sshPort).equalTo(row::getSshPort)
                .set(portalPort).equalTo(row::getPortalPort)
                .set(chargeType).equalTo(row::getChargeType);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(InstanceDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(clusterId).equalToWhenPresent(row::getClusterId)
                .set(instanceId).equalToWhenPresent(row::getInstanceId)
                .set(instanceUuid).equalToWhenPresent(row::getInstanceUuid)
                .set(queueId).equalToWhenPresent(row::getQueueId)
                .set(nodeType).equalToWhenPresent(row::getNodeType)
                .set(eipNetworkCapacity).equalToWhenPresent(row::getEipNetworkCapacity)
                .set(cosStackId).equalToWhenPresent(row::getCosStackId)
                .set(hostName).equalToWhenPresent(row::getHostName)
                .set(oosExecutionId).equalToWhenPresent(row::getOosExecutionId)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(spec).equalToWhenPresent(row::getSpec)
                .set(privateIp).equalToWhenPresent(row::getPrivateIp)
                .set(schedulerIp).equalToWhenPresent(row::getSchedulerIp)
                .set(schedulerHost).equalToWhenPresent(row::getSchedulerHost)
                .set(sshPort).equalToWhenPresent(row::getSshPort)
                .set(portalPort).equalToWhenPresent(row::getPortalPort)
                .set(publicIp).equalToWhenPresent(row::getPublicIp)
                .set(chargeType).equalToWhenPresent(row::getChargeType);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(InstanceDO row) {
        return update(c ->
                c.set(createdTime).equalTo(row::getCreatedTime)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .set(deleted).equalTo(row::getDeleted)
                        .set(clusterId).equalTo(row::getClusterId)
                        .set(instanceId).equalTo(row::getInstanceId)
                        .set(instanceUuid).equalTo(row::getInstanceUuid)
                        .set(queueId).equalTo(row::getQueueId)
                        .set(nodeType).equalTo(row::getNodeType)
                        .set(eipNetworkCapacity).equalTo(row::getEipNetworkCapacity)
                        .set(cosStackId).equalTo(row::getCosStackId)
                        .set(hostName).equalTo(row::getHostName)
                        .set(oosExecutionId).equalTo(row::getOosExecutionId)
                        .set(status).equalTo(row::getStatus)
                        .set(spec).equalTo(row::getSpec)
                        .set(privateIp).equalTo(row::getPrivateIp)
                        .set(publicIp).equalTo(row::getPublicIp)
                        .set(schedulerIp).equalTo(row::getSchedulerIp)
                        .set(schedulerHost).equalTo(row::getSchedulerHost)
                        .set(sshPort).equalTo(row::getSshPort)
                        .set(portalPort).equalTo(row::getPortalPort)
                        .set(chargeType).equalTo(row::getChargeType)
                        .where(id, isEqualTo(row::getId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByInstanceId(InstanceDO row) {
        return update(c ->
                c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(cosStackId).equalToWhenPresent(row::getCosStackId)
                        .set(oosExecutionId).equalToWhenPresent(row::getOosExecutionId)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(schedulerIp).equalToWhenPresent(row::getSchedulerIp)
                        .set(schedulerHost).equalToWhenPresent(row::getSchedulerHost)
                        .set(sshPort).equalToWhenPresent(row::getSshPort)
                        .set(portalPort).equalToWhenPresent(row::getPortalPort)
                        .where(instanceId, isEqualTo(row::getInstanceId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByInstanceIdAndStatus(InstanceDO row, List<String> statusList) {
        return update(c ->
                c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(cosStackId).equalToWhenPresent(row::getCosStackId)
                        .set(oosExecutionId).equalToWhenPresent(row::getOosExecutionId)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(schedulerIp).equalToWhenPresent(row::getSchedulerIp)
                        .set(schedulerHost).equalToWhenPresent(row::getSchedulerHost)
                        .set(sshPort).equalToWhenPresent(row::getSshPort)
                        .set(portalPort).equalToWhenPresent(row::getPortalPort)
                        .where(instanceId, isEqualTo(row::getInstanceId))
                        .and(status, isIn(statusList))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByInstanceId(InstanceDO row, Boolean isDeleted) {
        return update(c ->
                c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(isDeleted)
                        .set(clusterId).equalToWhenPresent(row::getClusterId)
                        .set(instanceUuid).equalToWhenPresent(row::getInstanceUuid)
                        .set(queueId).equalToWhenPresent(row::getQueueId)
                        .set(nodeType).equalToWhenPresent(row::getNodeType)
                        .set(privateIp).equalToWhenPresent(row::getPrivateIp)
                        .set(publicIp).equalToWhenPresent(row::getPublicIp)
                        .set(floatingIp).equalToWhenPresent(row::getFloatingIp)
                        .set(subnetId).equalToWhenPresent(row::getSubnetId)
                        .set(cosStackId).equalToWhenPresent(row::getCosStackId)
                        .set(oosExecutionId).equalToWhenPresent(row::getOosExecutionId)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(schedulerIp).equalToWhenPresent(row::getSchedulerIp)
                        .set(schedulerHost).equalToWhenPresent(row::getSchedulerHost)
                        .set(sshPort).equalToWhenPresent(row::getSshPort)
                        .set(portalPort).equalToWhenPresent(row::getPortalPort)
                        .set(hostName).equalToWhenPresent(row::getHostName)
                        .set(chargeType).equalToWhenPresent(row::getChargeType)
                        .set(spec).equalToWhenPresent(row::getSpec)
                        .where(instanceId, isEqualTo(row::getInstanceId))
        );
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByClusterIDAndNodetype(InstanceDO row) {
        return update(c ->
                c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(schedulerIp).equalToWhenPresent(row::getSchedulerIp)
                        .set(schedulerHost).equalToWhenPresent(row::getSchedulerHost)
                        .set(sshPort).equalToWhenPresent(row::getSshPort)
                        .set(portalPort).equalToWhenPresent(row::getPortalPort)
                        .where(clusterId, isEqualTo(row::getClusterId))
                        .and(nodeType, isEqualTo(row::getNodeType))
        );
    }


}