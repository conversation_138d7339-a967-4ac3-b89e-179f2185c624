package com.baidu.bce.logic.chpc.cfs.client;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;

import java.util.Map;

import org.springframework.web.reactive.function.client.WebClient;

public class CfsNioClient extends BceNioClient {

    public CfsNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public CfsDetailResponse getCfsFileSystem(String cfsId, String accountId) {
        return webClient.get()
            .uri(uriBuilder -> uriBuilder
                .path("/v1/cfs")              // 设置基本路径
                .queryParam("fsId", cfsId)    // 确保 fsId 被解释为查询参数
                .build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(CfsDetailResponse.class)
            .block();
    }


    public CfsMountPointResponse getCfsMountPoint(String cfsId, String accountId) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder.path("/v1/cfs/" + cfsId).build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .retrieve()
                .bodyToMono(CfsMountPointResponse.class).block();
    }

}
