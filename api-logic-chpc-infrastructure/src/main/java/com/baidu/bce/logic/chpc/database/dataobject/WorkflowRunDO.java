package com.baidu.bce.logic.chpc.database.dataobject;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;

public class WorkflowRunDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String runId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String runUuid;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long version;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String name;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean callCaching;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String failureMode;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String inputs;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String outputs;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String workflowId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String workspaceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String workflowName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime deletedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getRunId() {
        return runId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setRunId(String runId) {
        this.runId = runId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getName() {
        return name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setName(String name) {
        this.name = name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getCallCaching() {
        return callCaching;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCallCaching(Boolean callCaching) {
        this.callCaching = callCaching;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getFailureMode() {
        return failureMode;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setFailureMode(String failureMode) {
        this.failureMode = failureMode;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getInputs() {
        return inputs;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setInputs(String inputs) {
        this.inputs = inputs;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getOutputs() {
        return outputs;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setOutputs(String outputs) {
        this.outputs = outputs;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getWorkflowId() {
        return workflowId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getStatus() {
        return status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setStatus(String status) {
        this.status = status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getDeletedTime() {
        return deletedTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getWorkflowName() {
        return workflowName;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getVersion() {
        return version;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setVersion(Long version) {
        this.version = version;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setWorkflowName(String workflowName) {
        this.workflowName = workflowName;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getWorkspaceId() {
        return workspaceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeletedTime(LocalDateTime deletedTime) {
        this.deletedTime = deletedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getRunUuid() {
        return runUuid;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setRunUuid(String runUuid) {
        this.runUuid = runUuid;
    }
}
