package com.baidu.bce.logic.chpc.database.mapper;

import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.itemKey;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.order;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.orderId;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.productType;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.reason;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.serviceType;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import jakarta.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.baidu.bce.logic.chpc.database.dataobject.OrderDO;

@Mapper
public interface OrderMapper extends CommonCountMapper, CommonDeleteMapper,
        CommonInsertMapper<OrderDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            orderId,
            accountId,
            serviceType,
            productType,
            itemKey,
            reason,
            status);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "orderDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "order_id", property = "orderId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_type", property = "serviceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "product_type", property = "productType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "item_key", property = "itemKey", jdbcType = JdbcType.VARCHAR),
            @Result(column = "reason", property = "reason", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR)
    })
    List<OrderDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, order, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, order, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c -> c.where(id, isEqualTo(key)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(OrderDO row) {
        return MyBatis3Utils.insert(this::insert, row, order, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(orderId).toProperty("orderId")
                .map(accountId).toProperty("accountId")
                .map(serviceType).toProperty("serviceType")
                .map(productType).toProperty("productType")
                .map(itemKey).toProperty("itemKey")
                .map(reason).toProperty("reason")
                .map(status).toProperty("status"));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<OrderDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, order, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(orderId).toProperty("orderId")
                .map(accountId).toProperty("accountId")
                .map(serviceType).toProperty("serviceType")
                .map(productType).toProperty("productType")
                .map(itemKey).toProperty("itemKey")
                .map(reason).toProperty("reason")
                .map(status).toProperty("status"));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(OrderDO row) {
        return MyBatis3Utils.insert(this::insert, row, order,
                c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(orderId).toPropertyWhenPresent("orderId", row::getOrderId)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(serviceType).toPropertyWhenPresent("serviceType", row::getServiceType)
                        .map(productType).toPropertyWhenPresent("productType", row::getProductType)
                        .map(itemKey).toPropertyWhenPresent("itemKey", row::getItemKey)
                        .map(reason).toPropertyWhenPresent("reason", row::getReason)
                        .map(status).toPropertyWhenPresent("status", row::getStatus));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<OrderDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, order, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("orderDOResult")
    Optional<OrderDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<OrderDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, order, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<OrderDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, order, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<OrderDO> selectByPrimaryKey(Long key) {
        return selectOne(c -> c.where(id, isEqualTo(key)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, order, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(OrderDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(orderId).equalTo(row::getOrderId)
                .set(accountId).equalTo(row::getAccountId)
                .set(serviceType).equalTo(row::getServiceType)
                .set(productType).equalTo(row::getProductType)
                .set(itemKey).equalTo(row::getItemKey)
                .set(reason).equalTo(row::getReason)
                .set(status).equalTo(row::getStatus);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(OrderDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(orderId).equalToWhenPresent(row::getOrderId)
                .set(accountId).equalToWhenPresent(row::getAccountId)
                .set(serviceType).equalToWhenPresent(row::getServiceType)
                .set(productType).equalToWhenPresent(row::getProductType)
                .set(itemKey).equalToWhenPresent(row::getItemKey)
                .set(reason).equalToWhenPresent(row::getReason)
                .set(status).equalToWhenPresent(row::getStatus);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(OrderDO row) {
        return update(c -> c.set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(orderId).equalTo(row::getOrderId)
                .set(accountId).equalTo(row::getAccountId)
                .set(serviceType).equalTo(row::getServiceType)
                .set(productType).equalTo(row::getProductType)
                .set(itemKey).equalTo(row::getItemKey)
                .set(reason).equalTo(row::getReason)
                .set(status).equalTo(row::getStatus)
                .where(id, isEqualTo(row::getId)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKeySelective(OrderDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(orderId).equalToWhenPresent(row::getOrderId)
                .set(accountId).equalToWhenPresent(row::getAccountId)
                .set(serviceType).equalToWhenPresent(row::getServiceType)
                .set(productType).equalToWhenPresent(row::getProductType)
                .set(itemKey).equalToWhenPresent(row::getItemKey)
                .set(reason).equalToWhenPresent(row::getReason)
                .set(status).equalToWhenPresent(row::getStatus)
                .where(id, isEqualTo(row::getId)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByOrderId(OrderDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .where(orderId, isEqualTo(row::getOrderId)));
    }

}