package com.baidu.bce.logic.chpc.gatewayimpl.database;

import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.task;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.annotation.Resource;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.TaskDO;
import com.baidu.bce.logic.chpc.database.mapper.TaskMapper;
import com.baidu.bce.logic.chpc.gateway.TaskDAOGateway;
import com.baidu.bce.logic.chpc.model.Task;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Service
public class TaskDAOGatewayImpl implements TaskDAOGateway {

    @Resource
    private TaskMapper taskMapper;

    @Override
    public Boolean insert(Task task) {

        TaskDO taskDO = BeanCopyUtil.copyObject(task, TaskDO::new);
        taskDO.setDeleted(false);
        taskDO.setCreatedTime(LocalDateTime.now());
        taskDO.setUpdatedTime(LocalDateTime.now());

        return taskMapper.insertSelective(taskDO) != 0;
    }

    @Override
    public Boolean update(String taskId, String status, String extra) {

        TaskDO taskDO = new TaskDO();
        taskDO.setTaskId(taskId);
        taskDO.setUpdatedTime(LocalDateTime.now());
        taskDO.setStatus(status);
        taskDO.setExtra(extra);

        return taskMapper.updateByTaskId(taskDO) != 0;
    }

    @Override
    public Boolean updateByTaskUuid(String taskUuid, String status, String extra) {

        TaskDO taskDO = new TaskDO();
        taskDO.setTaskUuid(taskUuid);
        taskDO.setUpdatedTime(LocalDateTime.now());
        taskDO.setStatus(status);
        taskDO.setExtra(extra);

        return taskMapper.updateByTaskUuid(taskDO) != 0;
    }

    /**
     * {@inheritDoc}
     * 更新任务的堆栈ID，并返回是否成功。
     *
     * @param taskId 任务ID
     * @param stackId 堆栈ID
     * @return Boolean，如果更新成功则返回true，否则返回false
     */
    @Override
    public Boolean updateStackId(String taskId, String stackId) {

        TaskDO taskDO = new TaskDO();
        taskDO.setTaskId(taskId);
        taskDO.setUpdatedTime(LocalDateTime.now());
        taskDO.setCosStackId(stackId);

        return taskMapper.updateByTaskId(taskDO) != 0;
    }

    /**
     * {@inheritDoc}
     * 根据任务UUID更新堆栈ID，并返回是否成功。
     *
     * @param taskUuid 任务UUID
     * @param stackId  堆栈ID
     * @return boolean 如果更新成功则返回true，否则返回false
     */
    @Override
    public Boolean updateStackIdByTaskUuid(String taskUuid, String stackId) {

        TaskDO taskDO = new TaskDO();
        taskDO.setTaskUuid(taskUuid);
        taskDO.setUpdatedTime(LocalDateTime.now());
        taskDO.setCosStackId(stackId);

        return taskMapper.updateByTaskUuid(taskDO) != 0;
    }

    /**
     * {@inheritDoc}
     *
     * 根据状态列表查询任务列表，返回一个包含Task对象的List。
     * 如果状态列表为空，则不添加该条件到SQL语句中。
     *
     * @param statusList 状态列表，可以是null或者空列表，但不能是null值
     * @return 包含Task对象的List，如果没有找到符合条件的任务，则返回一个空列表
     * @throws RuntimeException 当传入的参数不正确时（如：statusList为null），将抛出RuntimeException异常
     */
    @Override
    public List<Task> listByStatus(List<String> statusList) {

        SelectStatementProvider selectStatement =
            select(TaskMapper.selectList)
                .from(task)
                .where(task.deleted, isEqualTo(false))
                .and(task.status, isInWhenPresent(statusList))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        List<TaskDO> taskDOS = taskMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(taskDOS, Task::new);
    }

    @Override
    public List<Task> listByTypeAndStatus(String type, List<String> statusList) {

        SelectStatementProvider selectStatement =
            select(TaskMapper.selectList)
                .from(task)
                .where(task.deleted, isEqualTo(false))
                .and(task.taskType, isEqualToWhenPresent(type))
                .and(task.status, isInWhenPresent(statusList))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        List<TaskDO> taskDOS = taskMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(taskDOS, Task::new);
    }

    @Override
    public List<Task> listByTaskUuid(String clusterId, String taskUuid) {

        SelectStatementProvider selectStatement =
            select(TaskMapper.selectList)
                .from(task)
                .where(task.deleted, isEqualTo(false))
                .and(task.taskUuid, isEqualTo(taskUuid))
                .and(task.clusterId, isEqualToWhenPresent(clusterId))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        List<TaskDO> taskDOS = taskMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(taskDOS, Task::new);
    }
}
