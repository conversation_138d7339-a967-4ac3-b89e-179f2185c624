package com.baidu.bce.logic.chpc.gatewayimpl.external;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baidu.bce.internalsdk.bcc.model.DeleteServerRequest;
import com.baidu.bce.internalsdk.bcc.model.RebootServerRequest;
import com.baidu.bce.internalsdk.bcc.model.ServerDetail;
import com.baidu.bce.internalsdk.bcc.model.StartServerRequest;
import com.baidu.bce.internalsdk.bcc.model.StopServerRequest;
import com.baidu.bce.internalsdk.bcc.model.Tag;
import com.baidu.bce.logic.chpc.bcc.client.LogicBccNioClient;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.ChpcClientFactory;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.config.SchedulerThreadLocalHolder;
import com.baidu.bce.logic.chpc.gateway.external.BccExternalGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.BccInstanceDetail;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
@Service
public class BccExternalGatewayImpl implements BccExternalGateway {

    @Resource
    private ChpcClientFactory chpcClientFactory;

    @Resource
    private LogicBccNioClient logicBccNioClient;

    @Override
    public BccInstanceDetail getBccServerDetail(String serverId) {
        String accountId = SchedulerThreadLocalHolder.getAccountId();
        if (StringUtils.isEmpty(accountId)) {
            accountId = LogicUserService.getAccountId();
        }
        ServerDetail serverDetail = logicBccNioClient.getBccServerDetail(serverId, accountId);
        BccInstanceDetail detail = BeanCopyUtil.copyObject(serverDetail, BccInstanceDetail::new);

        List<com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag> tags = new ArrayList<>();
        if (serverDetail.getTags() == null) {
            return detail;
        }
        for (Tag tag : serverDetail.getTags()) {
            if (ChpcConstant.INSTANCE_NODE_TYPE.equalsIgnoreCase(tag.getTagKey())) {
                detail.setNodeType(tag.getTagValue());
            }
            com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag bccTag =
                new com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag();
            bccTag.setTagKey(tag.getTagKey());
            bccTag.setTagValue(tag.getTagValue());
            tags.add(bccTag);
        }
        detail.setTags(tags);

        return detail;
    }

    @Override
    public void rebootServer(String serverId, Boolean forceStop) {
        String accountId = SchedulerThreadLocalHolder.getAccountId();
        if (StringUtils.isEmpty(accountId)) {
            accountId = LogicUserService.getAccountId();
        }
        RebootServerRequest rebootServerRequest = new RebootServerRequest();
        rebootServerRequest.setForceStop(forceStop);
        rebootServerRequest.setServerIds(Collections.singletonList(serverId));

        logicBccNioClient.rebootServer(rebootServerRequest, accountId);
    }

    @Override
    public void stopServer(String serverId, Boolean forceStop, Boolean stopWithNoCharge) {
        String accountId = SchedulerThreadLocalHolder.getAccountId();
        if (StringUtils.isEmpty(accountId)) {
            accountId = LogicUserService.getAccountId();
        }
        StopServerRequest stopServerRequest = new StopServerRequest();
        stopServerRequest.setServerIds(Collections.singletonList(serverId));
        stopServerRequest.setForceStop(false);
        stopServerRequest.setStopWithNoCharge(false);

        logicBccNioClient.stopServer(stopServerRequest, accountId);
    }

    @Override
    public void startServer(String serverId) {
        String accountId = SchedulerThreadLocalHolder.getAccountId();
        if (StringUtils.isEmpty(accountId)) {
            accountId = LogicUserService.getAccountId();
        }
        StartServerRequest startServerRequest = new StartServerRequest();
        startServerRequest.setServerIds(Collections.singletonList(serverId));

        logicBccNioClient.startServer(startServerRequest, accountId);
    }

    @Override
    public void deleteServer(String serverId) {

        this.deleteServers(Collections.singletonList(serverId));
    }

    @Override
    public void deleteServers(List<String> serverIds) {
        String accountId = SchedulerThreadLocalHolder.getAccountId();
        if (StringUtils.isEmpty(accountId)) {
            accountId = LogicUserService.getAccountId();
        }
        DeleteServerRequest deleteServerRequest = new DeleteServerRequest();
        deleteServerRequest.setServerIds(serverIds);
        deleteServerRequest.setBccRecycleFlag(0);
        deleteServerRequest.setRelatedReleaseFlag(0);
        deleteServerRequest.setDeleteCdsSnapshotFlag(0);
        deleteServerRequest.setSkipRouteCheck(0);
        deleteServerRequest.setDeleteRelatedEnisFlag(0);

        logicBccNioClient.deleteServer(deleteServerRequest, accountId);
    }
}
