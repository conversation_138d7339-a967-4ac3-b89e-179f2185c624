package com.baidu.bce.logic.chpc.pfs.gatewayimpl;

import com.baidu.bce.logic.chpc.pfs.GetFilesetRequest;
import com.baidu.bce.logic.chpc.pfs.GetFilesetResponse;
import com.baidu.bce.logic.chpc.pfs.client.PfsNioClient;
import com.baidu.bce.logic.chpc.pfs.gateway.PfsGateway;
import jakarta.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class PfsGatewayImpl implements PfsGateway {
    @Value("${bce.web.commons.iam.chpc-accountId}")
    protected String accountId;

    @Resource
    private PfsNioClient pfsNioClient;

    @Override
    public GetFilesetResponse getFilesetInfo(GetFilesetRequest request) {
        return pfsNioClient.getFilesetInfo(request, accountId);
    }
}
