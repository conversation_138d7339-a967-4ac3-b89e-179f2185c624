package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.EventDO;
import com.baidu.bce.logic.chpc.database.mapper.EventMapper;
import com.baidu.bce.logic.chpc.gateway.ClusterEventDAOGateway;
import com.baidu.bce.logic.chpc.model.Event;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.event;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class EventDAOGatewayImpl implements ClusterEventDAOGateway {


    @Resource
    private EventMapper eventMapper;

    @Override
    public List<Event> findByClusterId(String clusterId) {
        SelectStatementProvider selectStatement =
                select(EventMapper.selectList)
                        .from(event)
                        .where(event.clusterId, isEqualTo(clusterId))
                        .and(event.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);

        List<EventDO> eventDOS = eventMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(eventDOS, Event::new);
    }


    @Override
    public Event findByClusterIdAndEvent(String clusterId, String name) {
        SelectStatementProvider selectStatement =
                select(EventMapper.selectList)
                        .from(event)
                        .where(event.clusterId, isEqualTo(clusterId))
                        .and(event.name, isEqualTo(name))
                        .and(event.deleted, isEqualTo(false))
                        .orderBy(event.createdTime.descending())
                        .build().render(RenderingStrategies.MYBATIS3);

        EventDO eventDO = eventMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(eventDO, Event::new);
    }

    @Override
    public Boolean insert(Event event) {
        EventDO eventDO = new EventDO();
        BeanUtils.copyProperties(event, eventDO);
        eventDO.setDeleted(false);
        eventDO.setCreatedTime(LocalDateTime.now());
        eventDO.setUpdatedTime(LocalDateTime.now());
        return eventMapper.insert(eventDO) != 0;
    }

    @Override
    public void update(String clusterId, String name, String status, String errMsg) {
        EventDO eventDO = new EventDO();
        eventDO.setClusterId(clusterId);
        eventDO.setUpdatedTime(LocalDateTime.now());
        eventDO.setStatus(status);
        eventDO.setName(name);
        eventDO.setErrMsg(errMsg);
        eventMapper.updateByClusterId(eventDO);
    }
}

