package com.baidu.bce.logic.chpc.database.dataobject;

import java.time.LocalDateTime;

import jakarta.annotation.Generated;

public class SoftwareDO {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String name;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String version;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String description;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String category;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String supportOs;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String supportArch;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String dependency;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String nodeType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getName() {
        return name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setName(String name) {
        this.name = name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getVersion() {
        return version;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setVersion(String version) {
        this.version = version;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDescription() {
        return description;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDescription(String description) {
        this.description = description;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getCategory() {
        return category;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCategory(String category) {
        this.category = category;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSupportOs() {
        return supportOs;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSupportOs(String supportOs) {
        this.supportOs = supportOs;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSupportArch() {
        return supportArch;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSupportArch(String supportArch) {
        this.supportArch = supportArch;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDependency() {
        return dependency;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDependency(String dependency) {
        this.dependency = dependency;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getNodeType() {
        return nodeType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }
}
