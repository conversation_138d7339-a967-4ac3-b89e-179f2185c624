package com.baidu.bce.logic.chpc.billing.gatewayimpl;

import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.billing.EmptyResponse;
import com.baidu.bce.logic.chpc.billing.LegacyChargeDataRequest;
import com.baidu.bce.logic.chpc.billing.client.ResourceChargeNioClient;
import com.baidu.bce.logic.chpc.billing.gateway.ResourceChargeGateway;

import jakarta.annotation.Resource;

@Service
public class ResourceChargeGatewayImpl implements ResourceChargeGateway {

    @Resource
    private ResourceChargeNioClient resourceChargeNioClient;

    @Override
    public EmptyResponse charge(LegacyChargeDataRequest request) {
        return resourceChargeNioClient.charge(request);
    }

}
