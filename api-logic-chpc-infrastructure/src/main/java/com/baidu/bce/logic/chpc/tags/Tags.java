package com.baidu.bce.logic.chpc.tags;

import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.logical.tag.sdk.LogicalTagClient;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.logical.tag.sdk.model.request.CreateTagsRequest;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class Tags implements TagsGateway {
    @Resource
    IamLogicService iamLogicService;


    @Value("${bce.web.commons.sdk.tags.endpoint}")
    private String endpoint;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Override
    public void createTags(List<Tag> tags) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
                regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        log.debug("stsCredential:{}, accountId:{}", stsCredential, LogicUserService.getAccountId());
        LogicalTagClient logicalTagClient = new LogicalTagClient(
                endpoint, stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
        CreateTagsRequest request = new CreateTagsRequest();
        request.setTags(tags);
        logicalTagClient.createTags(request);
    }

    @Override
    public TagAssociationFulls listTags(FullTagListRequest request) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
                regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        log.debug("stsCredential:{}, accountId:{}", stsCredential, LogicUserService.getAccountId());
        LogicalTagClient logicalTagClient = new LogicalTagClient(
                endpoint, stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
        TagAssociationFulls tagAssociationFulls = logicalTagClient.listFullTags(request);
        return tagAssociationFulls;
    }

    @Override
    public List<Tag> listTagsV2(FullTagListRequest request) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
                regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        log.debug("stsCredential:{}, accountId:{}", stsCredential, LogicUserService.getAccountId());
        LogicalTagClient logicalTagClient = new LogicalTagClient(
                endpoint, stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
        List<Tag> tags = logicalTagClient.listTagsV2(request);
        return tags;
    }

    @Override
    public void createAndAssignTag(CreateAndAssignTagRequest request) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
                regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        log.debug("stsCredential:{}, accountId:{}", stsCredential, LogicUserService.getAccountId());
        LogicalTagClient logicalTagClient = new LogicalTagClient(
                endpoint, stsCredential.getAccessKeyId(), stsCredential.getSecretAccessKey(),
                stsCredential.getSessionToken());
        logicalTagClient.createAndAssignTag(request);

    }
}
