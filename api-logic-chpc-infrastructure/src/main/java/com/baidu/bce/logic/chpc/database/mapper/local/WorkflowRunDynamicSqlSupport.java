package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;

public class WorkflowRunDynamicSqlSupport {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final WorkflowRun workflowRun = new WorkflowRun();
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = workflowRun.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> runId = workflowRun.runId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> runUuid = workflowRun.runUuid;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> name = workflowRun.name;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> callCaching = workflowRun.callCaching;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> failureMode = workflowRun.failureMode;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> inputs = workflowRun.inputs;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> outputs = workflowRun.outputs;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> accountId = workflowRun.accountId;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> workflowId = workflowRun.workflowId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> workspaceId = workflowRun.workspaceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> workflowName = workflowRun.workflowName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> status = workflowRun.status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> version = workflowRun.version;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = workflowRun.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = workflowRun.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = workflowRun.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> deletedTime = workflowRun.deletedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class WorkflowRun extends AliasableSqlTable<WorkflowRun> {

        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedTime = column("deleted_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);
        public final SqlColumn<String> runId = column("run_id", JDBCType.VARCHAR);
        public final SqlColumn<String> runUuid = column("run_uuid", JDBCType.VARCHAR);
        public final SqlColumn<Boolean> callCaching = column("call_caching", JDBCType.BIT);
        public final SqlColumn<String> failureMode = column("failure_mode", JDBCType.VARCHAR);
        public final SqlColumn<String> inputs = column("inputs", JDBCType.VARCHAR);
        public final SqlColumn<String> outputs = column("outputs", JDBCType.VARCHAR);
        public final SqlColumn<String> accountId = column("account_id", JDBCType.VARCHAR);
        public final SqlColumn<String> workflowId = column("workflow_id", JDBCType.VARCHAR);
        public final SqlColumn<String> workflowName = column("workflow_name", JDBCType.VARCHAR);
        public final SqlColumn<String> workspaceId = column("workspace_id", JDBCType.VARCHAR);
        public final SqlColumn<String> status = column("status", JDBCType.VARCHAR);
        public final SqlColumn<Long> version = column("version", JDBCType.BIGINT);


        public WorkflowRun() {
            super("t_chpc_workflow_run", WorkflowRun::new);
        }


    }
}
