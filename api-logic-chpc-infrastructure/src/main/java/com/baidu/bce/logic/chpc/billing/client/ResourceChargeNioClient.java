package com.baidu.bce.logic.chpc.billing.client;

import java.util.Map;

import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.billing.EmptyResponse;
import com.baidu.bce.logic.chpc.billing.LegacyChargeDataRequest;

public class ResourceChargeNioClient extends BceNioClient {

    public ResourceChargeNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public EmptyResponse charge(LegacyChargeDataRequest request) {
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path("/v1/chargedata").build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .bodyValue(request)
                .retrieve()
                .bodyToMono(EmptyResponse.class).block();
    }

}
