package com.baidu.bce.logic.chpc.database.mapper;

import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.installedInstanceId;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.msg;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.oosExecutionId;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.softwareRecord;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.version;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.baidu.bce.logic.chpc.database.dataobject.SoftwareRecordDO;

import jakarta.annotation.Generated;

@Mapper
public interface SoftwareRecordMapper extends CommonCountMapper, CommonDeleteMapper,
        CommonInsertMapper<SoftwareRecordDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            clusterId,
            name,
            version,
            oosExecutionId,
            status,
            msg,
            installedInstanceId);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "softwareRecordDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "version", property = "version", jdbcType = JdbcType.VARCHAR),
            @Result(column = "oos_execution_id", property = "oosExecutionId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "msg", property = "msg", jdbcType = JdbcType.VARCHAR),
            @Result(column = "installed_instance_id", property = "installedInstanceId", jdbcType = JdbcType.VARCHAR)
    })
    List<SoftwareRecordDO> selectMany(SelectStatementProvider selectStatement);

    /**
     * {@inheritDoc}
     *
     * 计算符合条件的软件记录数量。
     *
     * @param completer CountDSLCompleter，用于定义查询条件
     * @return long，符合条件的软件记录数量
     * @since 2021.6
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, softwareRecord, completer);
    }

    /**
     * {@inheritDoc}
     *
     * 删除软件记录。
     *
     * @param completer DeleteDSLCompleter，用于完成SQL的删除操作
     * @return int，返回被删除的行数
     * @throws org.apache.ibatis.binding.BindingException 如果发生绑定异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, softwareRecord, completer);
    }

    /**
     * 根据主键删除记录。
     *
     * @param key 主键值，类型为Long
     * @return 返回受影响的行数，类型为int
     * @since 2021/3/15
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c -> c.where(id, isEqualTo(key)));
    }

    /**
     * {@summary 插入一条软件记录到数据库。}
     *
     * 使用MyBatis3的insert方法，将SoftwareRecordDO对象插入到数据库中。返回值为int类型，表示受影响的行数。
     * 如果插入成功，则返回1；否则返回0或其他错误码。
     *
     * @param row SoftwareRecordDO类型的对象，包含要插入的软件信息
     * @return int类型，表示受影响的行数，如果插入成功则为1，否则为0或其他错误码
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(SoftwareRecordDO row) {
        return MyBatis3Utils.insert(this::insert, row, softwareRecord, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(clusterId).toProperty("clusterId")
                .map(name).toProperty("name")
                .map(version).toProperty("version")
                .map(oosExecutionId).toProperty("oosExecutionId")
                .map(status).toProperty("status")
                .map(msg).toProperty("msg")
                .map(installedInstanceId).toProperty("installedInstanceId"));
    }

    /**
     * 批量插入多条记录到数据库中。
     *
     * @param records 待插入的记录集合，每个元素为一个 SoftwareRecordDO 对象
     * @return 返回值类型为 int，表示成功插入的记录数量
     * @throws org.apache.ibatis.binding.BindingException 如果出现任何错误，将抛出 BindingException 异常
     * @since 2021/9/17
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<SoftwareRecordDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, softwareRecord,
                c -> c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(clusterId).toProperty("clusterId")
                        .map(name).toProperty("name")
                        .map(version).toProperty("version")
                        .map(oosExecutionId).toProperty("oosExecutionId")
                        .map(status).toProperty("status")
                        .map(msg).toProperty("msg")
                        .map(installedInstanceId).toProperty("installedInstanceId"));
    }

    /**
     * 插入或更新 SoftwareRecordDO 记录，并返回受影响的行数。
     * 如果 id 不为空，则进行更新操作；否则进行插入操作。
     * 当 id、createdTime、updatedTime、deleted、clusterId、name、version、oosExecutionId、status、msg 任意一个字段为空时，将该字段跳过映射。
     *
     * @param row SoftwareRecordDO 实例，包含需要插入或更新的数据
     * @return int 受影响的行数，0 表示没有受影响的行（如果存在主键约束，则会引发异常）
     * @throws org.apache.ibatis.binding.MapperMethod.MapperMethodException MapperMethodException 异常，可能由于数据库错误或其他原因引发
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(SoftwareRecordDO row) {
        return MyBatis3Utils.insert(this::insert, row, softwareRecord,
                c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                        .map(name).toPropertyWhenPresent("name", row::getName)
                        .map(version).toPropertyWhenPresent("version", row::getVersion)
                        .map(oosExecutionId).toPropertyWhenPresent("oosExecutionId", row::getOosExecutionId)
                        .map(status).toPropertyWhenPresent("status", row::getStatus)
                        .map(msg).toPropertyWhenPresent("msg", row::getMsg)
                        .map(installedInstanceId).toPropertyWhenPresent("installedInstanceId", row::getInstalledInstanceId));
    }

    /**
     * {@inheritDoc}
     * 查询一条 SoftwareRecordDO 记录，如果没有找到则返回 Optional.empty()。
     * 支持 MyBatis3 的动态 SQL 语法。
     *
     * @param completer SelectDSLCompleter 实例，用于完成动态 SQL 的构建
     * @return Optional<SoftwareRecordDO> 包装了查询结果，如果没有找到则为 Optional.empty()
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<SoftwareRecordDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, softwareRecord, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("softwareRecordDOResult")
    Optional<SoftwareRecordDO> selectOne(SelectStatementProvider selectStatement);

    /**
     * {@inheritDoc}
     *
     * 根据条件查询 SoftwareRecordDO 列表。
     *
     * @param completer SelectDSLCompleter 实例，用于完成 SQL 的选择部分
     * @return List<SoftwareRecordDO> SoftwareRecordDO 列表
     * @throws org.apache.ibatis.binding.BindingException 如果发生数据库操作异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<SoftwareRecordDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, softwareRecord, completer);
    }

    /**
     * 查询不同的软件记录，返回一个列表。
     *
     * @param completer SelectDSLCompleter，用于完成SelectDSL语句的构建
     * @return List<SoftwareRecordDO>，不同的软件记录列表
     * @see org.mybatis.generator.api.MyBatisGenerator#selectDistinct(SelectDSLCompleter)
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<SoftwareRecordDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, softwareRecord, completer);
    }

    /**
     * 根据主键查询 SoftwareRecordDO 记录，返回一个 Optional。如果找到则包含该记录，否则为空。
     *
     * @param key 主键值，类型为 Long
     * @return 包含查询结果的 Optional，如果没有找到记录则为空
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<SoftwareRecordDO> selectByPrimaryKey(Long key) {
        return selectOne(c -> c.where(id, isEqualTo(key)));
    }

    /**
     * {@inheritDoc}
     *
     * 更新软件记录。
     *
     * @param completer UpdateDSLCompleter，用于完成更新操作的DSL语句
     * @return int，更新影响的行数，如果返回值为0，则表示没有更新任何行
     * @throws org.apache.ibatis.binding.BindingException 如果出现绑定错误
     * @since 2021.8
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, softwareRecord, completer);
    }

    /**
     * 更新所有列的值，并返回一个UpdateDSL对象。
     *
     * @param row SoftwareRecordDO类型的行对象，包含要更新的所有列的值
     * @param dsl UpdateDSL对象，用于构建更新语句
     * @return UpdateDSL<UpdateModel>类型，表示已经设置了所有列的值的更新语句
     * @throws org.mybatis.dynamic.sql.render.RenderException 如果任何一个列的值为null，则抛出此异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(SoftwareRecordDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(clusterId).equalTo(row::getClusterId)
                .set(name).equalTo(row::getName)
                .set(version).equalTo(row::getVersion)
                .set(oosExecutionId).equalTo(row::getOosExecutionId)
                .set(status).equalTo(row::getStatus)
                .set(msg).equalTo(row::getMsg)
                .set(installedInstanceId).equalTo(row::getInstalledInstanceId);
    }

    /**
     * 更新指定列，仅当对应字段非空时设置。
     *
     * @param row 待更新的数据行，包含所有需要更新的字段值
     * @param dsl 用于构建更新语句的 DSL 对象
     * @return 返回一个 UpdateDSL 对象，其中已经包含了所有需要更新的列和相应的值
     * （只有当对应字段非空时才会被设置）
     * @generated UpdateDSL<UpdateModel> updateSelectiveColumns(SoftwareRecordDO row, UpdateDSL<UpdateModel> dsl)
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SoftwareRecordDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(clusterId).equalToWhenPresent(row::getClusterId)
                .set(name).equalToWhenPresent(row::getName)
                .set(version).equalToWhenPresent(row::getVersion)
                .set(oosExecutionId).equalToWhenPresent(row::getOosExecutionId)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(msg).equalToWhenPresent(row::getMsg)
                .set(installedInstanceId).equalToWhenPresent(row::getInstalledInstanceId);
    }

    /**
     * 根据主键更新 SoftwareRecordDO 记录。
     *
     * @param row SoftwareRecordDO 实例，用于更新记录的值。
     * @return 返回受影响的行数，如果没有找到匹配的记录则返回0。
     * @throws org.apache.ibatis.binding.BindingException 如果出现任何错误，包括参数为null或者不存在的列名。
     * @since 2021.10.27
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(SoftwareRecordDO row) {
        return update(c -> c.set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(clusterId).equalTo(row::getClusterId)
                .set(name).equalTo(row::getName)
                .set(version).equalTo(row::getVersion)
                .set(oosExecutionId).equalTo(row::getOosExecutionId)
                .set(status).equalTo(row::getStatus)
                .set(msg).equalTo(row::getMsg)
                .set(installedInstanceId).equalTo(row::getInstalledInstanceId)
                .where(id, isEqualTo(row::getId)));
    }

    /**
     * {@summary 更新主键不为空的 SoftwareRecordDO 记录。}
     *
     * 根据给定的 SoftwareRecordDO 对象，仅更新其中非空的字段到数据库中匹配的行。
     * 如果 SoftwareRecordDO 对象中的 id 字段为 null，则会抛出 IllegalArgumentException。
     *
     * @param row SoftwareRecordDO 类型的对象，包含需要更新的字段值
     * @return 返回一个 int 类型的结果，表示被更新的行数（0 或 1）
     * @throws IllegalArgumentException 当 SoftwareRecordDO 对象中的 id 字段为 null
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKeySelective(SoftwareRecordDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(clusterId).equalToWhenPresent(row::getClusterId)
                .set(name).equalToWhenPresent(row::getName)
                .set(version).equalToWhenPresent(row::getVersion)
                .set(oosExecutionId).equalToWhenPresent(row::getOosExecutionId)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(msg).equalToWhenPresent(row::getMsg)
                .set(installedInstanceId).equalToWhenPresent(row::getInstalledInstanceId)
                .where(id, isEqualTo(row::getId)));
    }

    /**
     * 根据名称和版本更新软件记录，并返回受影响的行数。
     *
     * @param row 包含要更新的软件信息的 SoftwareRecordDO 对象
     * @return 受影响的行数，如果没有找到符合条件的行则返回0
     * @throws org.apache.ibatis.binding.BindingException 如果出现任何数据库操作错误
     * @since 2021-11-15
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByNameAndVersion(SoftwareRecordDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(msg).equalToWhenPresent(row::getMsg)
                .set(oosExecutionId).equalToWhenPresent(row::getOosExecutionId)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .where(clusterId, isEqualTo(row::getClusterId))
                .and(name, isEqualTo(row::getName))
                .and(version, isEqualTo(row::getVersion))
                .and(installedInstanceId, isEqualToWhenPresent(row::getInstalledInstanceId))
                .and(deleted, isEqualTo(false)));
    }

}