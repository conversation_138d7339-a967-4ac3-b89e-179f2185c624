package com.baidu.bce.logic.chpc.billing.gatewayimpl;

import org.springframework.stereotype.Service;

import com.baidu.bce.internalsdk.order.OrderNioClient;
import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderRequestByServiceType;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.logic.chpc.billing.gateway.BillingGateway;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BillingGatewayImpl implements BillingGateway {

    @Resource
    private OrderNioClient orderNioClient;

    @Override
    public Order getOrder(String accountId, String orderId) {
        OrderRequestByServiceType request = new OrderRequestByServiceType();
        request.setUuid(orderId);
        return orderNioClient.getOrderForExecutor(request).block();
    }

    @Override
    public Order updateOrder(String accountId, String orderId, UpdateOrderRequest request) {
        return orderNioClient.update(orderId, request).block();
    }

}
