package com.baidu.bce.logic.chpc.common;

import lombok.SneakyThrows;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public abstract class PO<T> {

    public abstract T toEntity();

    public static <T> List<T> toEntityList(Collection<? extends PO<T>> poList) {
        List<T> entityList = new ArrayList<>();
        for (PO<T> po : poList) {
            entityList.add(po.toEntity());
        }
        return entityList;
    }

    @SneakyThrows
    public static <T, P extends PO<T>> List<P> toPOList(Class<P> clz, Collection<T> entityList) {
        List<P> result = new ArrayList<>(entityList.size());
        for (T entity : entityList) {
            P po = clz.newInstance();
//            po.fromEntity(entity);
//            BeanUtils.fillDefault(po);
            result.add(po);
        }
        return result;
    }
}
