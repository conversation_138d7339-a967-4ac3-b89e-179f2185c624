package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.UserCallbackTaskDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.userName;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.queueName;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.jobId;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.nodeName;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.task;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.taskId;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.action;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.parameters;
import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface UserCallbackTaskMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<UserCallbackTaskDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            status,
            taskId,
            queueName,
            nodeName,
            clusterId,
            jobId,
            action,
            parameters,
            userName);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "UserCallbackTaskDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "task_id", property = "taskId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "queue_name", property = "queueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "node_name", property = "nodeName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_id", property = "jobId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "action", property = "action", jdbcType = JdbcType.VARCHAR),
            @Result(column = "parameters", property = "parameters", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "user_name", property = "userName", jdbcType = JdbcType.VARCHAR)
    })
    List<UserCallbackTaskDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("UserCallbackTaskDOResult")
    Optional<UserCallbackTaskDO> selectOne(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<UserCallbackTaskDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, task, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(UserCallbackTaskDO row) {
        return MyBatis3Utils.insert(this::insert, row, task, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(status).toProperty("status")
                        .map(taskId).toProperty("taskId")
                        .map(queueName).toProperty("queueName")
                        .map(nodeName).toProperty("nodeName")
                        .map(clusterId).toProperty("clusterId")
                        .map(jobId).toProperty("jobId")
                        .map(action).toProperty("action")
                        .map(parameters).toProperty("parameters")
                        .map(userName).toProperty("userName")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<UserCallbackTaskDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, task, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(status).toProperty("status")
                        .map(taskId).toProperty("taskId")
                        .map(queueName).toProperty("queueName")
                        .map(nodeName).toProperty("nodeName")
                        .map(clusterId).toProperty("clusterId")
                        .map(jobId).toProperty("jobId")
                        .map(action).toProperty("action")
                        .map(parameters).toProperty("parameters")
                        .map(userName).toProperty("userName")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(UserCallbackTaskDO row) {
        return MyBatis3Utils.insert(this::insert, row, task, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(status).toProperty("status")
                        .map(taskId).toProperty("taskId")
                        .map(queueName).toProperty("queueName")
                        .map(nodeName).toProperty("nodeName")
                        .map(clusterId).toProperty("clusterId")
                        .map(jobId).toProperty("jobId")
                        .map(action).toProperty("action")
                        .map(parameters).toProperty("parameters")
                        .map(userName).toProperty("userName")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<UserCallbackTaskDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<UserCallbackTaskDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<UserCallbackTaskDO> selectByPrimaryKey(Long key) {
        return selectOne(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(UserCallbackTaskDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(status).equalTo(row::getStatus)
                .set(taskId).equalTo(row::getTaskId)
                .set(queueName).equalTo(row::getQueueName)
                .set(clusterId).equalTo(row::getClusterId)
                .set(jobId).equalTo(row::getJobId)
                .set(nodeName).equalTo(row::getNodeName)
                .set(action).equalTo(row::getAction)
                .set(parameters).equalTo(row::getParameters)
                .set(userName).equalTo(row::getUserName);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(UserCallbackTaskDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(status).equalTo(row::getStatus)
                .set(taskId).equalTo(row::getTaskId)
                .set(queueName).equalTo(row::getQueueName)
                .set(clusterId).equalTo(row::getClusterId)
                .set(jobId).equalTo(row::getJobId)
                .set(nodeName).equalTo(row::getNodeName)
                .set(action).equalTo(row::getAction)
                .set(parameters).equalTo(row::getParameters)
                .set(userName).equalTo(row::getUserName);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(UserCallbackTaskDO row) {
        return update(c ->
                c.set(createdTime).equalTo(row::getCreatedTime)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .set(deleted).equalTo(row::getDeleted)
                        .set(status).equalTo(row::getStatus)
                        .set(taskId).equalTo(row::getTaskId)
                        .set(queueName).equalTo(row::getQueueName)
                        .set(clusterId).equalTo(row::getClusterId)
                        .set(jobId).equalTo(row::getJobId)
                        .set(nodeName).equalTo(row::getNodeName)
                        .set(action).equalTo(row::getAction)
                        .set(parameters).equalTo(row::getParameters)
                        .set(userName).equalTo(row::getUserName)
                        .where(id, isEqualTo(row::getId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKeySelective(UserCallbackTaskDO row) {
        return update(c ->
                c.set(createdTime).equalTo(row::getCreatedTime)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .set(deleted).equalTo(row::getDeleted)
                        .set(status).equalTo(row::getStatus)
                        .set(taskId).equalTo(row::getTaskId)
                        .set(queueName).equalTo(row::getQueueName)
                        .set(clusterId).equalTo(row::getClusterId)
                        .set(jobId).equalTo(row::getJobId)
                        .set(nodeName).equalTo(row::getNodeName)
                        .set(action).equalTo(row::getAction)
                        .set(parameters).equalTo(row::getParameters)
                        .set(userName).equalTo(row::getUserName)
                        .where(id, isEqualTo(row::getId))
        );
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByTaskId(UserCallbackTaskDO row) {
        return update(c ->
                c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .where(taskId, isEqualTo(row::getTaskId))
        );
    }
}