package com.baidu.bce.logic.chpc.billing.gatewayimpl;

import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.billing.ResourceUsage;
import com.baidu.bce.logic.chpc.billing.ResourceUsageRequest;
import com.baidu.bce.logic.chpc.billing.client.ResourceUsageNioClient;
import com.baidu.bce.logic.chpc.billing.gateway.ResourceUsageGateway;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ResourceUsageGatewayImpl implements ResourceUsageGateway {

    @Resource
    private ResourceUsageNioClient resourceUsageNioClient;

    @Override
    public ResourceUsage[] getUsage(ResourceUsageRequest request) {
        return resourceUsageNioClient.getUsage(request);
    }

}
