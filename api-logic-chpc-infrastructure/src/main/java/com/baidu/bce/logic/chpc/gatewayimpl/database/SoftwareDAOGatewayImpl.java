package com.baidu.bce.logic.chpc.gatewayimpl.database;

import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareDynamicSqlSupport.software;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.util.List;

import jakarta.annotation.Resource;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.SoftwareDO;
import com.baidu.bce.logic.chpc.database.mapper.SoftwareMapper;
import com.baidu.bce.logic.chpc.gateway.SoftwareDAOGateway;
import com.baidu.bce.logic.chpc.model.Software;

@Service
public class SoftwareDAOGatewayImpl implements SoftwareDAOGateway {

    @Resource
    private SoftwareMapper softwareMapper;

    /**
     * {@inheritDoc}
     * 根据软件名称和版本号查找对应的软件信息。
     *
     * @param name 软件名称
     * @param version 软件版本号
     * @return 返回一个包含软件信息的 Software 对象，如果不存在则返回 null
     * @throws Exception 未知异常（可能是数据库连接失败或 SQL 语法错误）
     */
    @Override
    public Software findSoftwareByNameAndVersion(String name, String version) {

        SelectStatementProvider selectStatement = select(SoftwareMapper.selectList)
                .from(software)
                .where(software.name, isEqualTo(name))
                .and(software.version, isEqualTo(version))
                .and(software.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        SoftwareDO softwareDO = softwareMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(softwareDO, Software::new);
    }


    /**
     * {@inheritDoc}
     * 根据软件名或类别查找软件，并返回一个包含Software对象的列表。
     * 如果传入的名称为空字符串或null，则不进行过滤。
     *
     * @param name 软件名或类别，可以是部分匹配的字符串
     * @return 包含Software对象的列表，如果没有找到任何软件，则返回一个空列表
     * @throws Exception 当发生数据库操作时可能会抛出异常
     */
    @Override
    public List<Software> findSoftwareByNameOrCategory(String name) {
        SelectStatementProvider selectStatement = select(SoftwareMapper.selectList)
                .from(software)
                .where(software.name, isLikeWhenPresent("%" + name + "%"))
                .or(software.category, isLikeWhenPresent("%" + name + "%"))
                .and(software.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<SoftwareDO> softwareDOList = softwareMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(softwareDOList, Software::new);
    }

      /**
       * @Description 查找所有软件信息，包括已删除的软件。返回值为List<Software>类型，其中每个Software对象都是从数据库中查询到的。
       * @Return 返回一个List<Software>类型，其中每个Software对象都是从数据库中查询到的。
       */
      @Override
    public List<Software> findAll() {
        SelectStatementProvider selectStatement = select(SoftwareMapper.selectList)
                .from(software)
                .where(software.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<SoftwareDO> softwareDOList = softwareMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(softwareDOList, Software::new);
    }
}
