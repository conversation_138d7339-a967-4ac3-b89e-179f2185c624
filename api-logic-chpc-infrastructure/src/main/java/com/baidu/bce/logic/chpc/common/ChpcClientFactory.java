package com.baidu.bce.logic.chpc.common;

import com.baidu.bce.logic.chpc.config.SchedulerThreadLocalHolder;
import com.baidu.bce.logic.chpc.gateway.external.client.BackendNioClient;
import com.baidu.bce.logic.chpc.gateway.external.client.BackendNioClientFactory;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Slf4j
@Component
public class ChpcClientFactory {

    @Resource
    private BackendNioClientFactory backendNioClientFactory;

    @Value("${bce.web.commons.iam.chpc-accountId}")
    private String chpcAccountId;

    public BackendNioClient createBackendClient(String endpoint) {
        String accountId = this.getAccountId();
        log.info("accountId is {}", accountId);
        BackendNioClient backendNioClient = backendNioClientFactory.createClient(endpoint, accountId);
        return backendNioClient;
    }

    // SaaS接口，helix集群归属是，需要使用chpc资源账号Id
    public BackendNioClient createHelixBackendClient(String endpoint) {
        BackendNioClient backendNioClient = backendNioClientFactory.createClient(endpoint, chpcAccountId);
        return backendNioClient;
    }

    private String getAccountId() {
        String accountId = SchedulerThreadLocalHolder.getAccountId();
        if (StringUtils.isEmpty(accountId)) {
            accountId = LogicUserService.getAccountId();
        }

        return accountId;
    }
}
