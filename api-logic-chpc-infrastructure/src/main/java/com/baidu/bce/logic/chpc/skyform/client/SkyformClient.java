package com.baidu.bce.logic.chpc.skyform.client;

import com.baidu.bce.logic.chpc.skyform.ActiveUserOrRoleResponse;
import com.baidu.bce.logic.chpc.skyform.AddTenantOrUserResponse;
import com.baidu.bce.logic.chpc.skyform.BaseResponse;
import com.baidu.bce.logic.chpc.skyform.GetAdminTokenRequest;
import com.baidu.bce.logic.chpc.skyform.GetTenantResponse;
import com.baidu.bce.logic.chpc.skyform.GetUserResponse;
import com.baidu.bce.logic.chpc.skyform.ListJobsResponse;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.jetbrains.annotations.NotNull;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SkyformClient {

    private static final String BASE_URL = "/appspace/v1";

    private static final String ADMIN = "cadmin";

    /**
     * 管理员加密密码
     */
    private static final String ADMIN_PASSWORD = "JsDeq1XRsK6Y46nyqqoDmQ==";

    /**
     * 用户原始密码加密
     */
    private static final String USER_PASSWORD = "8WUXd7K3U8YQNr2nA+iaFA==";


    /**
     * 用户token加密方式
     */
    public static String encode = "HS512";


    /**
     * 用户token加密key
     */
    public static String key = "skyabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzcloudskyabcdefghijklmnopqrstuvwxyzabcdefghijklmnopqrstuvwxyzcloud";


    private static volatile SkyformClient skyformClient;

    public static SkyformClient newClient() {
        if (skyformClient == null) {
            synchronized (SkyformClient.class) {
                if (skyformClient == null) {
                    skyformClient = new SkyformClient();
                }
            }
        }
        return skyformClient;
    }

    public static String getEndpoint() {
        String endpoint = "";
        String filePath = "/home/<USER>/bce-api-service/bce-api-modules/api-logic-chpc/conf/application-addition.yaml";

        Pattern skyformPattern = Pattern.compile("^skyform:\\s*$");
        Pattern endpointPattern = Pattern.compile("^\\s*endpoint:\\s*(.*)$");

        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            boolean inSkyformSection = false;

            while ((line = reader.readLine()) != null) {
                if (skyformPattern.matcher(line).matches()) {
                    inSkyformSection = true;
                } else if (inSkyformSection) {
                    Matcher endpointMatcher = endpointPattern.matcher(line);
                    if (endpointMatcher.matches()) {
                        endpoint = endpointMatcher.group(1).trim();
                        break;
                    } else if (!line.trim().isEmpty() && !line.startsWith(" ")) {
                        inSkyformSection = false;
                    }
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return endpoint;
    }

    public String getAdminToken() {
        GetAdminTokenRequest getAdminTokenRequest = new GetAdminTokenRequest();
        getAdminTokenRequest.setUsername(ADMIN);
        getAdminTokenRequest.setPassword(ADMIN_PASSWORD);
        ObjectMapper mapper = new ObjectMapper();
        String jsonString = null;
        try {
            jsonString = mapper.writeValueAsString(getAdminTokenRequest);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        String responseData = post(getEndpoint() + BASE_URL + "/adminLogin", jsonString, "");

        Gson gson = new Gson();
        JsonObject jsonObject = gson.fromJson(responseData, JsonObject.class);

        JsonObject retObj = jsonObject.getAsJsonObject("retObj");
        String token = "";
        if (null != retObj) {
            token = retObj.get("token").getAsString();
        }
        return token;
    }


    public GetUserResponse getUserByloginName(String loginName) {


        JsonObject jsonObject = new JsonObject();

        jsonObject.addProperty("loginName", "u_" + loginName);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/user/listAllUsers", jsonString, getAdminToken());

        Gson gson = new Gson();
        GetUserResponse obj = gson.fromJson(responseData, GetUserResponse.class);
        return obj;
    }


    public GetUserResponse getUsersByTenantId(String tenantId) {

        JsonObject jsonObject = new JsonObject();

        jsonObject.addProperty("tenantId", tenantId);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/user/listAllUsers", jsonString, getAdminToken());

        Gson gson = new Gson();
        GetUserResponse obj = gson.fromJson(responseData, GetUserResponse.class);
        return obj;
    }

    public GetTenantResponse getTenantByTenantName(String tenantName) {

        JsonObject jsonObject = new JsonObject();

        jsonObject.addProperty("tenantName", tenantName);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/userRegister/user/getTenantByName", jsonString, getAdminToken());

        Gson gson = new Gson();
        GetTenantResponse obj = gson.fromJson(responseData, GetTenantResponse.class);
        return obj;
    }


    public AddTenantOrUserResponse addTenant(String accountId) {

        JsonObject jsonObject = new JsonObject();

        jsonObject.addProperty("tenantCode", accountId);
        jsonObject.addProperty("tenantName", accountId);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/tenant/addTenant", jsonString, getAdminToken());
        // 使用 Gson 解析 JSON 字符串

        Gson gson = new Gson();
        AddTenantOrUserResponse obj = gson.fromJson(responseData, AddTenantOrUserResponse.class);
        return obj;
    }


    public AddTenantOrUserResponse addUser(String tenantId, String loginName, String password) {

        JsonObject jsonObject = new JsonObject();

        jsonObject.addProperty("tenantId", tenantId);
        jsonObject.addProperty("loginName", "u_" + loginName);
        // 普通用户使用默认密码
        if (!"".equals(password)) {
            jsonObject.addProperty("password", password);
        } else {
            jsonObject.addProperty("password", USER_PASSWORD);
        }
        // 用户类型（0：portal用户， 1：管理用户，2：不限权限用户）
        jsonObject.addProperty("userType", "0");

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/user/addUser", jsonString, getAdminToken());

        Gson gson = new Gson();
        AddTenantOrUserResponse obj = gson.fromJson(responseData, AddTenantOrUserResponse.class);
        return obj;
    }


    public String getUserToken(String userId) {
        userId = "u_" + userId;
        String token = getToken(userId);
        return token;

    }

    public BaseResponse configTenantQuota(String tenantId, String cpuHourQuota, String gpuHourQuota, String storageQuota) {

        JsonObject jsonObject = new JsonObject();

        jsonObject.addProperty("cpuHourQuota", cpuHourQuota);
        jsonObject.addProperty("gpuHourQuota", gpuHourQuota);
        jsonObject.addProperty("storageQuota", storageQuota);
        jsonObject.addProperty("targetId", tenantId);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/quota/configTenantQuota", jsonString, getAdminToken());

        Gson gson = new Gson();
        BaseResponse obj = gson.fromJson(responseData, BaseResponse.class);
        return obj;

    }

    public BaseResponse removeUsers(List<String> uuids) {
        JsonObject jsonObject = new JsonObject();


        JsonArray jsonArray = new JsonArray();

        for (int i = 0; i < uuids.size(); i++) {
            jsonArray.add(uuids.get(i));
        }

        jsonObject.add("uuids", jsonArray);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/user/removeUsers", jsonString, getAdminToken());

        Gson gson = new Gson();
        BaseResponse obj = gson.fromJson(responseData, BaseResponse.class);
        return obj;

    }

    public BaseResponse stopjobs(List<String> uuids) {
        JsonObject jsonObject = new JsonObject();


        JsonArray jsonArray = new JsonArray();

        for (int i = 0; i < uuids.size(); i++) {
            jsonArray.add(uuids.get(i));
        }

        jsonObject.add("uuids", jsonArray);

        // 操作类型(0: 停止，1：暂停，2：恢复，3：重新置顶，4：重新置底，5：重新调度)
        jsonObject.addProperty("operateType", 0);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/job/operateJobByAdmin", jsonString, getAdminToken());

        Gson gson = new Gson();
        BaseResponse obj = gson.fromJson(responseData, BaseResponse.class);
        return obj;
    }

    public BaseResponse removeTenants(List<String> uuids) {
        JsonObject jsonObject = new JsonObject();


        JsonArray jsonArray = new JsonArray();

        for (int i = 0; i < uuids.size(); i++) {
            jsonArray.add(uuids.get(i));
        }

        jsonObject.add("uuids", jsonArray);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/tenant/removeTenants", jsonString, getAdminToken());

        Gson gson = new Gson();
        BaseResponse obj = gson.fromJson(responseData, BaseResponse.class);
        return obj;

    }

    // TODO 后续数据量大，分页查询
    public ListJobsResponse listJobs(String tenantId, String beginSubmitTime, String endSubmitTime) {
        JsonObject jsonObject = new JsonObject();

        JsonObject params = new JsonObject();
        if (!"".equals(beginSubmitTime)) {
            params.addProperty("beginSubmitTime", beginSubmitTime);
        }
        if (!"".equals(endSubmitTime)) {
            params.addProperty("endSubmitTime", endSubmitTime);
        }
        if ((!"".equals(beginSubmitTime)) || (!"".equals(endSubmitTime))) {
            jsonObject.add("params", params);
        }

        jsonObject.addProperty("tenantId", tenantId);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/job/listJobs", jsonString, getAdminToken());

        Gson gson = new Gson();
        ListJobsResponse obj = gson.fromJson(responseData, ListJobsResponse.class);
        return obj;
    }


    public ListJobsResponse listJobById(String jobId) {
        JsonObject jsonObject = new JsonObject();

        jsonObject.addProperty("jobId", jobId);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/job/listJobs", jsonString, getAdminToken());

        Gson gson = new Gson();
        ListJobsResponse obj = gson.fromJson(responseData, ListJobsResponse.class);
        return obj;
    }


    public ActiveUserOrRoleResponse activeUser(String userId) {

        JsonObject jsonObject = new JsonObject();

        // 激活用户,状态（0未激活1激活）
        jsonObject.addProperty("status", "1");

        JsonArray jsonArray = new JsonArray();

        jsonArray.add(userId);

        jsonObject.add("uuids", jsonArray);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/user/activeUser", jsonString, getAdminToken());

        Gson gson = new Gson();
        ActiveUserOrRoleResponse obj = gson.fromJson(responseData, ActiveUserOrRoleResponse.class);
        return obj;
    }

    public ActiveUserOrRoleResponse addUserRole(String userId, List<String> roleIds) {

        JsonObject jsonObject = new JsonObject();

        // 激活用户
        jsonObject.addProperty("userId", userId);

        JsonArray jsonArray = new JsonArray();

        for (int i = 0; i < roleIds.size(); i++) {
            jsonArray.add(roleIds.get(i));
        }

        jsonObject.add("roleIds", jsonArray);

        String jsonString = jsonObject.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/userRole/addUserRole", jsonString, getAdminToken());

        Gson gson = new Gson();
        ActiveUserOrRoleResponse obj = gson.fromJson(responseData, ActiveUserOrRoleResponse.class);
        return obj;
    }


    public BaseResponse batchSaveAspTerminalPermission(String tenantId) {

        JsonObject permission = new JsonObject();
        permission.addProperty("permissionId", tenantId);
        // 权限主体类型（租户：tenant，用户：user）
        permission.addProperty("permissionType", "tenant");

        JsonArray aspTerminalPermissionList = new JsonArray();

        JsonObject aspTerminalPermission = new JsonObject();
        aspTerminalPermission.addProperty("targetId", tenantId);
        // 权限对象类型(0：用户，1：租户，2：部门)
        aspTerminalPermission.addProperty("targetType", "1");
        // 终端ID
        aspTerminalPermission.addProperty("terminalId", "1");

        aspTerminalPermissionList.add(aspTerminalPermission);

        permission.add("aspTerminalPermissionList", aspTerminalPermissionList);

        String jsonString = permission.toString();

        String responseData = post(getEndpoint() + BASE_URL + "/system/terminal/batchSaveAspTerminalPermission", jsonString, getAdminToken());

        Gson gson = new Gson();
        BaseResponse obj = gson.fromJson(responseData, BaseResponse.class);
        return obj;

    }


    @NotNull
    private String post(String postUrl, String body, String authorization) {

        if (!(postUrl.startsWith("http") || postUrl.startsWith("https"))) {
            throw new CommonExceptions.RequestInvalidException("Illegal agreement");
        }

        HttpsURLConnection.setDefaultHostnameVerifier(new NullHostNameVerifier());
        SSLContext sc;
        String responseData = "";
        try {
            sc = SSLContext.getInstance("TLS");
            sc.init(null, trustAllCerts, new SecureRandom());
            HttpsURLConnection.setDefaultSSLSocketFactory(sc.getSocketFactory());
            URL url = new URL(postUrl);

            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            // 禁用自动重定向
            conn.setInstanceFollowRedirects(false);
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            if (!"".equals(authorization)) {
                conn.setRequestProperty("Authorization", authorization);
            }
            conn.setConnectTimeout(130000);
            conn.setReadTimeout(130000);

            try (OutputStream os = conn.getOutputStream()) {
                byte[] input = body.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            int responseCode = conn.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) {
                InputStream inStream = conn.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(inStream));
                String line;
                StringBuilder response = new StringBuilder();
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                responseData = response.toString();
            }
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (KeyManagementException e) {
            e.printStackTrace();
        }
        return responseData;
    }


    static TrustManager[] trustAllCerts = new TrustManager[]{new X509TrustManager() {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) throws CertificateException {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[0];
        }
    }};

    public class NullHostNameVerifier implements HostnameVerifier {

        @Override
        public boolean verify(String arg0, SSLSession arg1) {
            return true;
        }
    }


    // 获取用户token,只在登录时候用到
    private String getToken(String loginName) {
        Claims claims = Jwts.claims();
        claims.put("loginNameKey", loginName);
        JwtBuilder jwtBuilder = Jwts.builder().setClaims(claims);
        jwtBuilder.signWith(SignatureAlgorithm.valueOf(encode), key).
                setExpiration(new Date(System.currentTimeMillis() + 60 * 1000));
        return jwtBuilder.compact();
    }
}
