package com.baidu.bce.logic.chpc.gatewayimpl.database;

import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.chargeData;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.time.LocalDateTime;

import jakarta.annotation.Resource;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.ChargeDataDO;
import com.baidu.bce.logic.chpc.database.mapper.ChargeDataMapper;
import com.baidu.bce.logic.chpc.gateway.ChargeDataDAOGateway;
import com.baidu.bce.logic.chpc.model.ChargeData;

@Service
public class ChargeDataDAOGatewayImpl implements ChargeDataDAOGateway {

    @Resource
    private ChargeDataMapper chargeDataMapper;

    @Override
    public Boolean insert(ChargeData chargeData) {

        ChargeDataDO chargeDataDO = BeanCopyUtil.copyObject(chargeData, ChargeDataDO::new);
        chargeDataDO.setDeleted(false);
        chargeDataDO.setCreatedTime(LocalDateTime.now());
        chargeDataDO.setUpdatedTime(LocalDateTime.now());
        return chargeDataMapper.insertSelective(chargeDataDO) != 0;
    }

    @Override
    public Boolean update(ChargeData chargeData) {

        ChargeDataDO chargeDataDO = BeanCopyUtil.copyObject(chargeData, ChargeDataDO::new);
        chargeDataDO.setUpdatedTime(LocalDateTime.now());
        return chargeDataMapper.updateByChargeId(chargeDataDO) != 0;
    }

    @Override
    public Boolean delete(String instanceId, String chargeItem, LocalDateTime chargeTime) {

        ChargeDataDO chargeDataDO = new ChargeDataDO();
        chargeDataDO.setOrderId(instanceId);
        chargeDataDO.setChargeItem(chargeItem);
        chargeDataDO.setChargeTime(chargeTime);
        chargeDataDO.setUpdatedTime(LocalDateTime.now());
        chargeDataDO.setDeleted(true);

        return chargeDataMapper.updateByChargeId(chargeDataDO) != 0;
    }

    @Override
    public ChargeData findByInstanceId(String instanceId, String chargeItem, LocalDateTime chargeTime) {

        SelectStatementProvider selectStatement = select(ChargeDataMapper.selectList)
                .from(chargeData)
                .where(chargeData.instanceId, isEqualTo(instanceId))
                .and(chargeData.chargeItem, isEqualTo(chargeItem))
                .and(chargeData.chargeTime, isEqualTo(chargeTime))
                .and(chargeData.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        ChargeDataDO chargeDataDO = chargeDataMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(chargeDataDO, ChargeData::new);
    }

}
