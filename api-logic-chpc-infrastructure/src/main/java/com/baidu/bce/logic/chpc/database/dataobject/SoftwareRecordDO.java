package com.baidu.bce.logic.chpc.database.dataobject;

import java.time.LocalDateTime;

import jakarta.annotation.Generated;

public class SoftwareRecordDO {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterId;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String name;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String version;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String oosExecutionId;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String status;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String msg;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String installedInstanceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterId() {
        return clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getName() {
        return name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setName(String name) {
        this.name = name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getVersion() {
        return version;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setVersion(String version) {
        this.version = version;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getOosExecutionId() {
        return oosExecutionId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setOosExecutionId(String oosExecutionId) {
        this.oosExecutionId = oosExecutionId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getStatus() {
        return status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setStatus(String status) {
        this.status = status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getMsg() {
        return msg;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setMsg(String msg) {
        this.msg = msg;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getInstalledInstanceId() {
        return installedInstanceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setInstalledInstanceId(String installedInstanceId) {
        this.installedInstanceId = installedInstanceId;
    }

}