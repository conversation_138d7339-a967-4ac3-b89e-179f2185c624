package com.baidu.bce.logic.chpc.oos.client;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.oos.CreateOosExecutionRequest;
import com.baidu.bce.logic.chpc.oos.CreateOosExecutionResponse;
import com.baidu.bce.logic.chpc.oos.GetOosExecutionResponse;

import java.util.Map;
import java.util.UUID;

import org.springframework.web.reactive.function.client.WebClient;

public class OosNioClient extends BceNioClient {

    private static final String BASE_URL = "/api/logic/oos/v2/execution";

    public OosNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public CreateOosExecutionResponse createExecution(CreateOosExecutionRequest request, String accountId) {
        String requestId = BceInternalRequest.getThreadRequestId();
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL).build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .header(BceConstant.X_BCE_REQUEST_ID, requestId + UUID.randomUUID().toString())
                .bodyValue(request)
                .retrieve()
                .bodyToMono(CreateOosExecutionResponse.class).block();
    }

    public GetOosExecutionResponse getExecutionById(String executionId, String accountId) {
        String requestId = BceInternalRequest.getThreadRequestId();
        return webClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL)
                .queryParam("id", executionId)
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .header(BceConstant.X_BCE_REQUEST_ID, requestId + UUID.randomUUID().toString())
                .retrieve()
                .bodyToMono(GetOosExecutionResponse.class).block();
    }
}
