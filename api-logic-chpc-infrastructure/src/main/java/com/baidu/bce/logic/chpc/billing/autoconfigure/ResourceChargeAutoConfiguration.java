package com.baidu.bce.logic.chpc.billing.autoconfigure;

import com.baidu.bce.logic.chpc.billing.client.ResourceChargeNioClient;
import com.baidu.bce.logic.core.constants.CommonsConstants;
import com.baidu.bce.logic.core.util.WebClientBuilderUtil;
import com.baidu.bce.logic.core.webclient.SignByServiceExchangeFilter;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;


@AutoConfiguration
@EnableConfigurationProperties({ResourceChargeProperties.class})
@ConditionalOnClass(ResourceChargeNioClient.class)
@ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".resource-charge",
        value = CommonsConstants.STARTER_ENABLED,
        havingValue = CommonsConstants.ENABLE, matchIfMissing = true)
@Slf4j
@AllArgsConstructor
public class ResourceChargeAutoConfiguration {
        
        private final ResourceChargeProperties properties;

        private final RegionConfiguration regionConfiguration;
        
        @Bean
        @ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".resource-charge",
                value = CommonsConstants.CLIENT_TYPE,
                havingValue = CommonsConstants.CLIENT_TYPE_SINGLE, matchIfMissing = true)
        public ResourceChargeNioClient resourceChargeNioClient(WebClient.Builder webClientBuilder,
                                             Map<String, SignByServiceExchangeFilter> signByServiceExchangeFilterMap) {
            return WebClientBuilderUtil.nioClient(properties.getEndpoint(), webClientBuilder,
                signByServiceExchangeFilterMap.get(regionConfiguration.getCurrentRegion()),
                    webClient -> new ResourceChargeNioClient(webClient, properties.getConfigs()));
        }
}
