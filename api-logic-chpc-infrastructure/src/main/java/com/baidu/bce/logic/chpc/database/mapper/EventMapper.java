package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.EventDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.event;
import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.EventDynamicSqlSupport.errMsg;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface EventMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<EventDO>, CommonUpdateMapper {


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            name,
            createdTime,
            updatedTime,
            status,
            deleted,
            clusterId,
            errMsg
    );


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "EventDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "event_name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "err_msg", property = "errMsg", jdbcType = JdbcType.VARCHAR),
    })
    List<EventDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("EventDOResult")
    Optional<EventDO> selectOne(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<EventDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, event, completer);
    }





    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(EventDO row) {
        return MyBatis3Utils.insert(this::insert, row, event, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(clusterId).toProperty("clusterId")
                        .map(name).toProperty("name")
                        .map(status).toProperty("status")
                        .map(errMsg).toProperty("errMsg")
        );
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, event, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByClusterId(EventDO row) {
        return update(c ->
                c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(errMsg).equalToWhenPresent(row::getErrMsg)
                        .where(clusterId, isEqualTo(row::getClusterId))
                        .and(name, isEqualTo(row::getName))
        );
    }


}
