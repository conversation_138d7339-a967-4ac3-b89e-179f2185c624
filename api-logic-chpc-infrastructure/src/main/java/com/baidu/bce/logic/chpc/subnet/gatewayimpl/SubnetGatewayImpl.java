package com.baidu.bce.logic.chpc.subnet.gatewayimpl;

import com.baidu.bce.externalsdk.logical.network.subnet.ExternalSubnetNioClient;
import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class SubnetGatewayImpl implements SubnetGateway {

    @Autowired
    IamLogicService iamLogicService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private ExternalSubnetNioClient externalSubnetNioClient;

    @Override
    public SubnetVo getSubnet(String subnetId) {
        return externalSubnetNioClient.findBySubnetId(LogicUserService.getAccountId(), subnetId).block();
    }

    @Override
    public List<SubnetVo> findAllByAzAndVpc(String az, String vpcId) {
        return externalSubnetNioClient.findAllByAzAndVpc(LogicUserService.getAccountId(), az, vpcId).block();
    }


}
