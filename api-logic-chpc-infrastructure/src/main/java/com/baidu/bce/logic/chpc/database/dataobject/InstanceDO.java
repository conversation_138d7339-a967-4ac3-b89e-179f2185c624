package com.baidu.bce.logic.chpc.database.dataobject;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;

public class InstanceDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String instanceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String instanceUuid;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String queueId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String nodeType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String subnetId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String schedulerIp;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String schedulerHost;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Integer eipNetworkCapacity;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String cosStackId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String hostName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String oosExecutionId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String spec;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String privateIp;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String publicIp;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String floatingIp;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String chargeType;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long sshPort;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long portalPort;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getSshPort() {
        return sshPort;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSshPort(Long sshPort) {
        this.sshPort = sshPort;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getPortalPort() {
        return portalPort;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setPortalPort(Long portalPort) {
        this.portalPort = portalPort;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterId() {
        return clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId == null ? null : clusterId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getInstanceId() {
        return instanceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId == null ? null : instanceId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getInstanceUuid() {
        return instanceUuid;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid == null ? null : instanceUuid.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getQueueId() {
        return queueId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setQueueId(String queueId) {
        this.queueId = queueId == null ? null : queueId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getNodeType() {
        return nodeType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setNodeType(String nodeType) {
        this.nodeType = nodeType == null ? null : nodeType.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Integer getEipNetworkCapacity() {
        return eipNetworkCapacity;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setEipNetworkCapacity(Integer eipNetworkCapacity) {
        this.eipNetworkCapacity = eipNetworkCapacity;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getCosStackId() {
        return cosStackId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCosStackId(String cosStackId) {
        this.cosStackId = cosStackId == null ? null : cosStackId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getHostName() {
        return hostName;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setHostName(String hostName) {
        this.hostName = hostName == null ? null : hostName.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getOosExecutionId() {
        return oosExecutionId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setOosExecutionId(String oosExecutionId) {
        this.oosExecutionId = oosExecutionId == null ? null : oosExecutionId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getStatus() {
        return status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSpec() {
        return spec;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSpec(String spec) {
        this.spec = spec == null ? null : spec.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getPrivateIp() {
        return privateIp;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setPrivateIp(String privateIp) {
        this.privateIp = privateIp == null ? null : privateIp.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getPublicIp() {
        return publicIp;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setPublicIp(String publicIp) {
        this.publicIp = publicIp == null ? null : publicIp.trim();
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getFloatingIp() {
        return floatingIp;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setFloatingIp(String floatingIp) {
        this.floatingIp = floatingIp == null ? null : floatingIp.trim();
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSubnetId() {
        return subnetId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSubnetId(String subnetId) {
        this.subnetId = subnetId == null ? null : subnetId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getChargeType() {
        return chargeType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType == null ? null : chargeType.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSchedulerIp() {
        return schedulerIp;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSchedulerIp(String schedulerIp) {
        this.schedulerIp = schedulerIp == null ? null : schedulerIp.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSchedulerHost() {
        return schedulerHost;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSchedulerHost(String schedulerHost) {
        this.schedulerHost = schedulerHost == null ? null : schedulerHost.trim();
    }

}