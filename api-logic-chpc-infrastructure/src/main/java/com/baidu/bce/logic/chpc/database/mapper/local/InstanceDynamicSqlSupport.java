package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;

public final class InstanceDynamicSqlSupport {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final Instance instance = new Instance();

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = instance.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = instance.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = instance.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = instance.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> clusterId = instance.clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> instanceId = instance.instanceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> instanceUuid = instance.instanceUuid;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> queueId = instance.queueId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> nodeType = instance.nodeType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> subnetId = instance.subnetId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Integer> eipNetworkCapacity = instance.eipNetworkCapacity;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> cosStackId = instance.cosStackId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> hostName = instance.hostName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> oosExecutionId = instance.oosExecutionId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> status = instance.status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> spec = instance.spec;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> privateIp = instance.privateIp;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> publicIp = instance.publicIp;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> floatingIp = instance.floatingIp;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> schedulerIp = instance.schedulerIp;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> schedulerHost = instance.schedulerHost;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> chargeType = instance.chargeType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> sshPort = instance.sshPort;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> portalPort = instance.portalPort;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class Instance extends AliasableSqlTable<Instance> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.TINYINT);

        public final SqlColumn<String> clusterId = column("cluster_id", JDBCType.VARCHAR);

        public final SqlColumn<String> instanceId = column("instance_id", JDBCType.VARCHAR);

        public final SqlColumn<String> instanceUuid = column("instance_uuid", JDBCType.VARCHAR);

        public final SqlColumn<String> queueId = column("group_id", JDBCType.VARCHAR);

        public final SqlColumn<String> nodeType = column("instance_type", JDBCType.VARCHAR);

        public final SqlColumn<String> subnetId = column("subnet_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> eipNetworkCapacity = column("eip_network_capacity", JDBCType.INTEGER);

        public final SqlColumn<String> cosStackId = column("cos_stack_id", JDBCType.VARCHAR);

        public final SqlColumn<String> hostName = column("host_name", JDBCType.VARCHAR);

        public final SqlColumn<String> oosExecutionId = column("oos_execution_id", JDBCType.VARCHAR);

        public final SqlColumn<String> status = column("status", JDBCType.VARCHAR);

        public final SqlColumn<String> spec = column("spec", JDBCType.VARCHAR);

        public final SqlColumn<String> privateIp = column("private_ip", JDBCType.VARCHAR);

        public final SqlColumn<String> publicIp = column("public_ip", JDBCType.VARCHAR);

        public final SqlColumn<String> floatingIp = column("floating_ip", JDBCType.VARCHAR);

        public final SqlColumn<String> schedulerIp = column("scheduler_ip", JDBCType.VARCHAR);

        public final SqlColumn<String> schedulerHost = column("scheduler_host", JDBCType.VARCHAR);

        public final SqlColumn<String> chargeType = column("charge_type", JDBCType.VARCHAR);

        public final SqlColumn<Long> sshPort = column("ssh_port", JDBCType.BIGINT);

        public final SqlColumn<Long> portalPort = column("portal_port", JDBCType.BIGINT);

        public Instance() {
            super("t_chpc_instance", Instance::new);
        }
    }
}