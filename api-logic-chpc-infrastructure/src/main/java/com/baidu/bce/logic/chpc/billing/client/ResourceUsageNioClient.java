package com.baidu.bce.logic.chpc.billing.client;

import java.util.Map;

import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.billing.ResourceUsage;
import com.baidu.bce.logic.chpc.billing.ResourceUsageRequest;

public class ResourceUsageNioClient extends BceNioClient {

    public ResourceUsageNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public ResourceUsage[] getUsage(ResourceUsageRequest request) {
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path("/v1/resourceusage/usage/data/trail").build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .bodyValue(request)
                .retrieve()
                .bodyToMono(ResourceUsage[].class).block();
    }

}
