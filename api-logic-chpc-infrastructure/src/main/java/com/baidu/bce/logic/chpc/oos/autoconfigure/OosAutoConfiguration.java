package com.baidu.bce.logic.chpc.oos.autoconfigure;

import com.baidu.bce.logic.chpc.oos.client.OosNioClient;
import com.baidu.bce.logic.core.constants.CommonsConstants;
import com.baidu.bce.logic.core.util.WebClientBuilderUtil;
import com.baidu.bce.logic.core.webclient.SignByStsExchangeFilter;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;


@AutoConfiguration
@EnableConfigurationProperties({OosProperties.class})
@ConditionalOnClass(OosNioClient.class)
@ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".oos",
        value = CommonsConstants.STARTER_ENABLED,
        havingValue = CommonsConstants.ENABLE, matchIfMissing = true)
@Slf4j
@AllArgsConstructor
public class OosAutoConfiguration {
    
    private final OosProperties properties;

    private final RegionConfiguration regionConfiguration;

    @Bean
    @ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".oos",
            value = CommonsConstants.CLIENT_TYPE,
            havingValue = CommonsConstants.CLIENT_TYPE_SINGLE, matchIfMissing = true)
    public OosNioClient oosNioClient(WebClient.Builder webClientBuilder,
                                         Map<String, SignByStsExchangeFilter> signByStsExchangeFilterMap) {
        return WebClientBuilderUtil.nioClient(properties.getEndpoint(), webClientBuilder,
            signByStsExchangeFilterMap.get(regionConfiguration.getCurrentRegion()),
                webClient -> new OosNioClient(webClient, properties.getConfigs()));
    }
}
