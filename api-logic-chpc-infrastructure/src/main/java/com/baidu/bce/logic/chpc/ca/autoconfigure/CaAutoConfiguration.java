package com.baidu.bce.logic.chpc.ca.autoconfigure;

import com.baidu.bce.logic.chpc.ca.client.CaNioClient;
import com.baidu.bce.logic.core.constants.CommonsConstants;
import com.baidu.bce.logic.core.util.WebClientBuilderUtil;
import com.baidu.bce.logic.core.webclient.SignByStsExchangeFilter;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;


@AutoConfiguration
@EnableConfigurationProperties({CaProperties.class})
@ConditionalOnClass(CaNioClient.class)
@ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".ca",
        value = CommonsConstants.STARTER_ENABLED,
        havingValue = CommonsConstants.ENABLE, matchIfMissing = true)
@Slf4j
@AllArgsConstructor
public class CaAutoConfiguration {
    
    private final CaProperties properties;

    private final RegionConfiguration regionConfiguration;

    @Bean
    @ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".ca",
            value = CommonsConstants.CLIENT_TYPE,
            havingValue = CommonsConstants.CLIENT_TYPE_SINGLE, matchIfMissing = true)
    public CaNioClient caNioClient(WebClient.Builder webClientBuilder,
                                         Map<String, SignByStsExchangeFilter> signByStsExchangeFilterMap) {
        return WebClientBuilderUtil.nioClient(properties.getEndpoint(), webClientBuilder,
                signByStsExchangeFilterMap.get(regionConfiguration.getCurrentRegion()),
                webClient -> new CaNioClient(webClient, properties.getConfigs()));
    }
}
