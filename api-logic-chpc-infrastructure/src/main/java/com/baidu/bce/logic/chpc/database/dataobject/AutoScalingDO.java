package com.baidu.bce.logic.chpc.database.dataobject;

import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.common.PO;

import org.springframework.beans.BeanUtils;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;

public class AutoScalingDO extends PO<AutoScaling> {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String asId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String asName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String queueId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String queueName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String zoneName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String subnetId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String securityGroupId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Integer maxNodesInQueue;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Integer minNodesInQueue;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean enableAutoGrow;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean enableAutoShrink;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String spec;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Integer systemDiskSize;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String systemDiskType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String excludeNodes;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String imageId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Integer maxScalePerCycle;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String hostnamePrefix;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String cudaVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String gpuDriverVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String cudnnVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String dataDiskList;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String cpuThreadConfig;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String numaConfig;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAsId() {
        return asId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAsId(String asId) {
        this.asId = asId == null ? null : asId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAsName() {
        return asName;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAsName(String asName) {
        this.asName = asName == null ? null : asName.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId == null ? null : accountId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterId() {
        return clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId == null ? null : clusterId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterName() {
        return clusterName;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterName(String clusterName) {
        this.clusterName = clusterName == null ? null : clusterName.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getQueueId() {
        return queueId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setQueueId(String queueId) {
        this.queueId = queueId == null ? null : queueId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getQueueName() {
        return queueName;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setQueueName(String queueName) {
        this.queueName = queueName == null ? null : queueName.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getZoneName() {
        return zoneName;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setZoneName(String zoneName) {
        this.zoneName = zoneName == null ? null : zoneName.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSubnetId() {
        return subnetId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSubnetId(String subnetId) {
        this.subnetId = subnetId == null ? null : subnetId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSecurityGroupId() {
        return securityGroupId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSecurityGroupId(String securityGroupId) {
        this.securityGroupId = securityGroupId == null ? null : securityGroupId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Integer getMaxNodesInQueue() {
        return maxNodesInQueue;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setMaxNodesInQueue(Integer maxNodesInQueue) {
        this.maxNodesInQueue = maxNodesInQueue;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Integer getMinNodesInQueue() {
        return minNodesInQueue;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setMinNodesInQueue(Integer minNodesInQueue) {
        this.minNodesInQueue = minNodesInQueue;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getEnableAutoGrow() {
        return enableAutoGrow;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setEnableAutoGrow(Boolean enableAutoGrow) {
        this.enableAutoGrow = enableAutoGrow;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getEnableAutoShrink() {
        return enableAutoShrink;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setEnableAutoShrink(Boolean enableAutoShrink) {
        this.enableAutoShrink = enableAutoShrink;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSpec() {
        return spec;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSpec(String spec) {
        this.spec = spec == null ? null : spec.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Integer getSystemDiskSize() {
        return systemDiskSize;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSystemDiskSize(Integer systemDiskSize) {
        this.systemDiskSize = systemDiskSize;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSystemDiskType() {
        return systemDiskType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSystemDiskType(String systemDiskType) {
        this.systemDiskType = systemDiskType == null ? null : systemDiskType.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getExcludeNodes() {
        return excludeNodes;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setExcludeNodes(String excludeNodes) {
        this.excludeNodes = excludeNodes == null ? null : excludeNodes.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getStatus() {
        return status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getImageId() {
        return imageId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setImageId(String imageId) {
        this.imageId = imageId == null ? null : imageId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Integer getMaxScalePerCycle() {
        return maxScalePerCycle;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setMaxScalePerCycle(Integer maxScalePerCycle) {
        this.maxScalePerCycle = maxScalePerCycle;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getHostnamePrefix() {
        return hostnamePrefix;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setHostnamePrefix(String hostnamePrefix) {
        this.hostnamePrefix = hostnamePrefix == null ? null : hostnamePrefix.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getCudaVersion() {
        return cudaVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCudaVersion(String cudaVersion) {
        this.cudaVersion = cudaVersion == null ? null : cudaVersion.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getGpuDriverVersion() {
        return gpuDriverVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setGpuDriverVersion(String gpuDriverVersion) {
        this.gpuDriverVersion = gpuDriverVersion == null ? null : gpuDriverVersion.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getCudnnVersion() {
        return cudnnVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCudnnVersion(String cudnnVersion) {
        this.cudnnVersion = cudnnVersion == null ? null : cudnnVersion.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDataDiskList() {
        return dataDiskList;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDataDiskList(String dataDiskList) {
        this.dataDiskList = dataDiskList == null ? null : dataDiskList.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getCpuThreadConfig() {
        return cpuThreadConfig;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCpuThreadConfig(String cpuThreadConfig) {
        this.cpuThreadConfig = cpuThreadConfig == null ? null : cpuThreadConfig.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getNumaConfig() {
        return numaConfig;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setNumaConfig(String numaConfig) {
        this.numaConfig = numaConfig == null ? null : numaConfig.trim();
    }

    @Override
    public AutoScaling toEntity() {
        AutoScaling autoScaling = new AutoScaling();
        BeanUtils.copyProperties(this, autoScaling);
        autoScaling.setStatus(AutoScalingStatus.valueOf(this.getStatus()));
        return autoScaling;
    }
}