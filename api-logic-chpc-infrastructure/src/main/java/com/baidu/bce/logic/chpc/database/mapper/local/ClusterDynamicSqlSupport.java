package com.baidu.bce.logic.chpc.database.mapper.local;

import java.sql.JDBCType;
import java.time.LocalDateTime;

import jakarta.annotation.Generated;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ClusterDynamicSqlSupport {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final Cluster cluster = new Cluster();

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = cluster.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = cluster.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = cluster.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> deleteTime = cluster.deleteTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = cluster.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> clusterId = cluster.clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> chargeType = cluster.chargeType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> name = cluster.name;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> description = cluster.description;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> vpcId = cluster.vpcId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> subnetId = cluster.subnetId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> logicalZone = cluster.logicalZone;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> status = cluster.status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> securityGroupId = cluster.securityGroupId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> securityGroupType = cluster.securityGroupType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> schedulerType = cluster.schedulerType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> schedulerVersion = cluster.schedulerVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> enableHa = cluster.enableHa;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> enableMonitor = cluster.enableMonitor;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> accountId = cluster.accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> extra = cluster.extra;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Long> heartbeat = cluster.heartbeat;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> imageId = cluster.imageId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> spec = cluster.spec;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> cosStackId = cluster.cosStackId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> errorMessage = cluster.errorMessage;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> softwareDir = cluster.softwareDir;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Integer> schedulePlugin = cluster.schedulePlugin;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> schedulePluginVersion = cluster.schedulePluginVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> clusterType = cluster.clusterType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Integer> maxNodes = cluster.maxNodes;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Integer> maxCpus = cluster.maxCpus;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> password = cluster.password;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> keypairId = cluster.keypairId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Boolean> forbidDelete = cluster.forbidDelete;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Boolean> createdDone = cluster.createdDone;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> domainAccount = cluster.domainAccount;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class Cluster extends AliasableSqlTable<Cluster> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deleteTime = column("delete_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<String> clusterId = column("cluster_id", JDBCType.VARCHAR);

        public final SqlColumn<String> chargeType = column("charge_type", JDBCType.VARCHAR);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> description = column("description", JDBCType.VARCHAR);

        public final SqlColumn<String> vpcId = column("vpc_id", JDBCType.VARCHAR);

        public final SqlColumn<String> subnetId = column("subnet_id", JDBCType.VARCHAR);

        public final SqlColumn<String> securityGroupId = column("security_group_id", JDBCType.VARCHAR);

        public final SqlColumn<String> securityGroupType = column("security_group_type", JDBCType.VARCHAR);

        public final SqlColumn<String> status = column("status", JDBCType.VARCHAR);

        public final SqlColumn<String> logicalZone = column("logical_zone", JDBCType.VARCHAR);

        public final SqlColumn<String> schedulerType = column("scheduler_type", JDBCType.VARCHAR);

        public final SqlColumn<String> schedulerVersion = column("scheduler_version", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> enableHa = column("enable_ha", JDBCType.BIT);

        public final SqlColumn<Boolean> enableMonitor = column("enable_monitor", JDBCType.BIT);

        public final SqlColumn<String> accountId = column("account_id", JDBCType.VARCHAR);

        public final SqlColumn<String> extra = column("extra", JDBCType.LONGVARCHAR);

        public final SqlColumn<String> imageId = column("image_id", JDBCType.VARCHAR);

        public final SqlColumn<String> spec = column("spec", JDBCType.VARCHAR);

        public final SqlColumn<String> cosStackId = column("cos_stack_id", JDBCType.VARCHAR);

        public final SqlColumn<String> errorMessage = column("error_message", JDBCType.VARCHAR);

        public final SqlColumn<String> softwareDir = column("software_dir", JDBCType.VARCHAR);

        public final SqlColumn<Long> heartbeat = column("heartbeat", JDBCType.BIGINT);

        public final SqlColumn<Integer> schedulePlugin = column("schedule_plugin", JDBCType.INTEGER);

        public final SqlColumn<String> schedulePluginVersion = column("schedule_plugin_version", JDBCType.VARCHAR);

        public final SqlColumn<String> clusterType = column("cluster_type", JDBCType.VARCHAR);

        public final SqlColumn<Integer> maxNodes = column("max_nodes", JDBCType.INTEGER);

        public final SqlColumn<Integer> maxCpus = column("max_cpus", JDBCType.INTEGER);

        public final SqlColumn<String> password = column("password", JDBCType.VARCHAR);

        public final SqlColumn<String> keypairId = column("keypair_id", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> forbidDelete = column("forbid_delete", JDBCType.BIT);

        public final SqlColumn<Boolean> createdDone = column("created_done", JDBCType.BIT);

        public final SqlColumn<String> domainAccount = column("domain_account", JDBCType.VARCHAR);

        public Cluster() {
            super("t_chpc_cluster", Cluster::new);
        }
    }
}