package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.ServiceStatusDO;
import com.baidu.bce.logic.chpc.database.mapper.ServiceStatusMapper;
import com.baidu.bce.logic.chpc.gateway.ServiceStatusDAOGateway;
import com.baidu.bce.logic.chpc.model.ServiceStatus;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

import java.time.LocalDateTime;

import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport.serviceStatus;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class ServiceStatusDAOGatewayImpl implements ServiceStatusDAOGateway {
    @Resource
    private ServiceStatusMapper serviceStatusMapper;


    @Override
    public Boolean isExisted(Long serviceId, String accountId) {
        SelectStatementProvider selectStatement =
                select(SqlBuilder.count())
                        .from(serviceStatus)
                        .where(serviceStatus.serviceId, isEqualTo(serviceId))
                        .and(serviceStatus.accountId, isEqualTo(accountId))
                        .and(serviceStatus.deleted, isEqualTo(false))
                        .orderBy(serviceStatus.createdTime.descending())
                        .build()
                        .render(RenderingStrategies.MYBATIS3);

        return serviceStatusMapper.count(selectStatement) == 1;
    }

    @Override
    public ServiceStatus findBy(Long serviceId, String accountId) {

        SelectStatementProvider selectStatement = select(serviceStatusMapper.selectList)
                .from(serviceStatus)
                .where(serviceStatus.serviceId, isEqualTo(serviceId))
                .and(serviceStatus.accountId, isEqualTo(accountId))
                .and(serviceStatus.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        ServiceStatusDO serviceStatusDO = serviceStatusMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(serviceStatusDO, ServiceStatus::new);
    }

    @Override
    public Boolean insert(Long serviceId, String accountId, String status) {
        ServiceStatusDO serviceStatus = new ServiceStatusDO();
        serviceStatus.setAccountId(accountId);
        serviceStatus.setServiceId(serviceId);
        serviceStatus.setStatus(status);
        serviceStatus.setCreatedTime(LocalDateTime.now());
        serviceStatus.setUpdatedTime(LocalDateTime.now());
        return serviceStatusMapper.insert(serviceStatus) != 0;
    }

    @Override
    public Boolean delete(Long serviceId, String accountId) {
        ServiceStatusDO serviceStatus = new ServiceStatusDO();
        serviceStatus.setAccountId(accountId);
        serviceStatus.setServiceId(serviceId);
        serviceStatus.setDeleted(true);
        serviceStatus.setUpdatedTime(LocalDateTime.now());
        return serviceStatusMapper.update(serviceStatus) != 0;
    }

    @Override
    public Boolean update(Long serviceId, String accountId, String status) {
        ServiceStatusDO serviceStatus = new ServiceStatusDO();
        serviceStatus.setAccountId(accountId);
        serviceStatus.setServiceId(serviceId);
        serviceStatus.setStatus(status);
        serviceStatus.setUpdatedTime(LocalDateTime.now());
        return serviceStatusMapper.update(serviceStatus) != 0;
    }

}
