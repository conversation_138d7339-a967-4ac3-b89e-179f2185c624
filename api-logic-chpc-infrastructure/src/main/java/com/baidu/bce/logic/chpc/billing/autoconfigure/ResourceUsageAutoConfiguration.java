package com.baidu.bce.logic.chpc.billing.autoconfigure;

import com.baidu.bce.logic.chpc.billing.client.ResourceUsageNioClient;
import com.baidu.bce.logic.core.constants.CommonsConstants;
import com.baidu.bce.logic.core.util.WebClientBuilderUtil;
import com.baidu.bce.logic.core.webclient.SignByServiceExchangeFilter;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;


@AutoConfiguration
@EnableConfigurationProperties({ResourceUsageProperties.class})
@ConditionalOnClass(ResourceUsageNioClient.class)
@ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".resource-usage",
        value = CommonsConstants.STARTER_ENABLED,
        havingValue = CommonsConstants.ENABLE, matchIfMissing = true)
@Slf4j
@AllArgsConstructor
public class ResourceUsageAutoConfiguration {
    
    private final ResourceUsageProperties properties;

    private final RegionConfiguration regionConfiguration;

    @Bean
    @ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".resource-usage",
            value = CommonsConstants.CLIENT_TYPE,
            havingValue = CommonsConstants.CLIENT_TYPE_SINGLE, matchIfMissing = true)
    public ResourceUsageNioClient resourceUsageNioClient(WebClient.Builder webClientBuilder,
                                         Map<String, SignByServiceExchangeFilter> signByServiceExchangeFilteMap) {
        return WebClientBuilderUtil.nioClient(properties.getEndpoint(), webClientBuilder,
                signByServiceExchangeFilteMap.get(regionConfiguration.getCurrentRegion()),
                webClient -> new ResourceUsageNioClient(webClient, properties.getConfigs()));
    }
}
