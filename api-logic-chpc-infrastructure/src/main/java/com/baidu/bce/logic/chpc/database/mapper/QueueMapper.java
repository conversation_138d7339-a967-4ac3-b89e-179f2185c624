package com.baidu.bce.logic.chpc.database.mapper;

import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.defaultImageId;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.defaultSpec;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.defaultUserData;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.description;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.hasAutoScale;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.isAutoScale;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.isDefault;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.queue;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.queueId;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.subnetId;
import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import jakarta.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.baidu.bce.logic.chpc.database.dataobject.QueueDO;

@Mapper
public interface QueueMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<QueueDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            clusterId,
            name,
            description,
            queueId,
            isDefault,
            isAutoScale,
            hasAutoScale,
            defaultSpec,
            defaultImageId,
            defaultUserData,
//            logicalZone,
            subnetId,
            status);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "QueueDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_id", property = "queueId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "is_default", property = "isDefault", jdbcType = JdbcType.BIT),
            @Result(column = "is_auto_scale", property = "isAutoScale", jdbcType = JdbcType.BIT),
            @Result(column = "has_auto_scale", property = "hasAutoScale", jdbcType = JdbcType.BIT),
            @Result(column = "default_spec", property = "defaultSpec", jdbcType = JdbcType.VARCHAR),
            @Result(column = "default_image_id", property = "defaultImageId", jdbcType = JdbcType.VARCHAR),
            // @Result(column = "logical_zone", property = "logicalZone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "default_user_data", property = "defaultUserData", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "subnet_id", property = "subnetId", jdbcType = JdbcType.VARCHAR)
    })
    List<QueueDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<QueueDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, queue, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("QueueDOResult")
    Optional<QueueDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, queue, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, queue, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(QueueDO row) {
        return MyBatis3Utils.insert(this::insert, row, queue, c ->
                        c.map(id).toProperty("id")
                                .map(createdTime).toProperty("createdTime")
                                .map(updatedTime).toProperty("updatedTime")
                                .map(deleted).toProperty("deleted")
                                .map(clusterId).toProperty("clusterId")
                                .map(name).toProperty("name")
                                .map(description).toProperty("description")
                                .map(queueId).toProperty("queueId")
                                .map(isDefault).toProperty("isDefault")
                                .map(isAutoScale).toProperty("isAutoScale")
                                .map(defaultSpec).toProperty("defaultSpec")
                                .map(defaultImageId).toProperty("defaultImageId")
                                .map(defaultUserData).toProperty("defaultUserData")
                                .map(status).toProperty("status")
                                .map(subnetId).toProperty("subnetId")
//                        .map(logicalZone).toProperty("logicalZone")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<QueueDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, queue, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(clusterId).toProperty("clusterId")
                        .map(name).toProperty("name")
                        .map(description).toProperty("description")
                        .map(queueId).toProperty("queueId")
                        .map(isDefault).toProperty("isDefault")
                        .map(isAutoScale).toProperty("isAutoScale")
                        .map(defaultSpec).toProperty("defaultSpec")
                        .map(defaultImageId).toProperty("defaultImageId")
                        .map(defaultUserData).toProperty("defaultUserData")
                        .map(status).toProperty("status")
                        .map(subnetId).toProperty("subnetId")
//                        .map(logicalZone).toProperty("logicalZone")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(QueueDO row) {
        return MyBatis3Utils.insert(this::insert, row, queue, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                        .map(name).toPropertyWhenPresent("name", row::getName)
                        .map(description).toPropertyWhenPresent("description", row::getDescription)
                        .map(queueId).toPropertyWhenPresent("queueId", row::getQueueId)
                        .map(isDefault).toPropertyWhenPresent("isDefault", row::getIsDefault)
                        .map(isAutoScale).toPropertyWhenPresent("isAutoScale", row::getIsAutoScale)
                        .map(defaultSpec).toPropertyWhenPresent("defaultSpec", row::getDefaultSpec)
                        .map(defaultImageId).toPropertyWhenPresent("defaultImageId", row::getDefaultImageId)
                        .map(defaultUserData).toPropertyWhenPresent("defaultUserData", row::getDefaultUserData)
                        .map(status).toPropertyWhenPresent("status", row::getStatus)
                        .map(subnetId).toPropertyWhenPresent("subnetId", row::getSubnetId)

//                        .map(logicalZone).toPropertyWhenPresent("logicalZone", row::getLogicalZone)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<QueueDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, queue, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<QueueDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, queue, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<QueueDO> selectByPrimaryKey(Long key) {
        return selectOne(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, queue, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(QueueDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(clusterId).equalTo(row::getClusterId)
                .set(name).equalTo(row::getName)
                .set(description).equalTo(row::getDescription)
                .set(queueId).equalTo(row::getQueueId)
                .set(isDefault).equalTo(row::getIsDefault)
                .set(isAutoScale).equalTo(row::getIsAutoScale)
                .set(defaultSpec).equalTo(row::getDefaultSpec)
                .set(defaultImageId).equalTo(row::getDefaultImageId)
                .set(defaultUserData).equalTo(row::getDefaultUserData)
                .set(status).equalTo(row::getStatus)
                .set(subnetId).equalTo(row::getSubnetId);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(QueueDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(clusterId).equalToWhenPresent(row::getClusterId)
                .set(name).equalToWhenPresent(row::getName)
                .set(description).equalToWhenPresent(row::getDescription)
                .set(queueId).equalToWhenPresent(row::getQueueId)
                .set(isDefault).equalToWhenPresent(row::getIsDefault)
                .set(isAutoScale).equalToWhenPresent(row::getIsAutoScale)
                .set(defaultSpec).equalToWhenPresent(row::getDefaultSpec)
                .set(defaultImageId).equalToWhenPresent(row::getDefaultImageId)
                .set(defaultUserData).equalToWhenPresent(row::getDefaultUserData)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(subnetId).equalToWhenPresent(row::getSubnetId);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(QueueDO row) {
        return update(c ->
                c.set(createdTime).equalTo(row::getCreatedTime)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .set(deleted).equalTo(row::getDeleted)
                        .set(clusterId).equalTo(row::getClusterId)
                        .set(name).equalTo(row::getName)
                        .set(description).equalTo(row::getDescription)
                        .set(queueId).equalTo(row::getQueueId)
                        .set(isDefault).equalTo(row::getIsDefault)
                        .set(isAutoScale).equalTo(row::getIsAutoScale)
                        .set(defaultSpec).equalTo(row::getDefaultSpec)
                        .set(defaultImageId).equalTo(row::getDefaultImageId)
                        .set(defaultUserData).equalTo(row::getDefaultUserData)
                        .set(status).equalTo(row::getStatus)
                        .set(subnetId).equalTo(row::getSubnetId)
                        // .set(logicalZone).equalTo(row::getLogicalZone)
                        .where(id, isEqualTo(row::getId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByQueueId(QueueDO row) {
        return update(c ->
                c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(name).equalToWhenPresent(row::getName)
                        .set(description).equalToWhenPresent(row::getDescription)
                        .set(defaultUserData).equalToWhenPresent(row::getDefaultUserData)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(subnetId).equalToWhenPresent(row::getSubnetId)
                        .set(isAutoScale).equalToWhenPresent(row.getIsAutoScale())
                        .set(hasAutoScale).equalToWhenPresent(row.getHasAutoScale())
                        .where(queueId, isEqualTo(row::getQueueId))
        );
    }
}