package com.baidu.bce.logic.chpc.skyform;


import com.baidu.bce.logic.chpc.skyform.gateway.SkyFormGateway;
import com.baidu.bce.logic.chpc.skyform.client.SkyformClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class SkyformGatewayImpl implements SkyFormGateway {


    @Override
    public String getAdminToken() {
        return SkyformClient.newClient().getAdminToken();
    }

    @Override
    public GetUserResponse getUserByloginName(String loginName) {
        return SkyformClient.newClient().getUserByloginName(loginName);
    }

    @Override
    public GetUserResponse getUsersByTenantId(String tenantId) {
        return SkyformClient.newClient().getUsersByTenantId(tenantId);
    }

    @Override
    public GetTenantResponse getTenantByTenantName(String tenantName) {
        return SkyformClient.newClient().getTenantByTenantName(tenantName);
    }

    @Override
    public AddTenantOrUserResponse addTenant(String accountId) {
        return SkyformClient.newClient().addTenant(accountId);
    }

    @Override
    public AddTenantOrUserResponse addUser(String tenantId, String loginName, String password) {
        return SkyformClient.newClient().addUser(tenantId, loginName, password);
    }

    @Override
    public ActiveUserOrRoleResponse activeUser(String userId) {
        return SkyformClient.newClient().activeUser(userId);
    }

    @Override
    public ActiveUserOrRoleResponse addUserRole(String userId, List<String> roleIds) {
        return SkyformClient.newClient().addUserRole(userId, roleIds);
    }

    @Override
    public BaseResponse batchSaveAspTerminalPermission(String tenantId) {
        return SkyformClient.newClient().batchSaveAspTerminalPermission(tenantId);
    }

    @Override
    public String getUserToken(String userId) {
        return SkyformClient.newClient().getUserToken(userId);
    }

    @Override
    public BaseResponse configTenantQuota(String tenantId, String cpuHourQuota, String gpuHourQuota, String storageQuota) {
        return SkyformClient.newClient().configTenantQuota(tenantId, cpuHourQuota, gpuHourQuota, storageQuota);
    }

    @Override
    public BaseResponse removeUsers(List<String> uuids) {
        return SkyformClient.newClient().removeUsers(uuids);
    }

    @Override
    public BaseResponse stopJobs(List<String> uuids) {
        return SkyformClient.newClient().stopjobs(uuids);
    }

    @Override
    public BaseResponse removeTenants(List<String> uuids) {
        return SkyformClient.newClient().removeTenants(uuids);
    }

    @Override
    public ListJobsResponse listJobs(String tenantId, String beginSubmitTime, String endSubmitTime) {
        return SkyformClient.newClient().listJobs(tenantId, beginSubmitTime, endSubmitTime);
    }

    @Override
    public ListJobsResponse listJobById(String jobId) {
        return SkyformClient.newClient().listJobById(jobId);
    }

}
