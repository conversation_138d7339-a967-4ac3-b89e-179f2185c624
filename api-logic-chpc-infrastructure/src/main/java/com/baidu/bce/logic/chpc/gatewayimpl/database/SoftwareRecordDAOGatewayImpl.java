package com.baidu.bce.logic.chpc.gatewayimpl.database;

import static com.baidu.bce.logic.chpc.database.mapper.local.SoftwareRecordDynamicSqlSupport.softwareRecord;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.time.LocalDateTime;
import java.util.List;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.SoftwareRecordDO;
import com.baidu.bce.logic.chpc.database.mapper.SoftwareRecordMapper;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.model.SoftwareRecord;

import jakarta.annotation.Resource;

@Service
public class SoftwareRecordDAOGatewayImpl implements SoftwareRecordDAOGateway {

    @Resource
    private SoftwareRecordMapper softwareRecordMapper;

    /**
     * {@inheritDoc}
     * 插入一条软件记录到数据库中。
     *
     * @param softwareRecord 软件记录对象，包含软件名称、版本号和下载地址等信息
     * @return 返回布尔值，表示是否成功插入了一条新的软件记录，如果插入失败则返回 false
     */
    @Override
    public Boolean insert(SoftwareRecord softwareRecord) {

        SoftwareRecordDO softwareRecordDO = BeanCopyUtil.copyObject(softwareRecord, SoftwareRecordDO::new);
        softwareRecordDO.setDeleted(false);
        softwareRecordDO.setCreatedTime(LocalDateTime.now());
        softwareRecordDO.setUpdatedTime(LocalDateTime.now());
        return softwareRecordMapper.insertSelective(softwareRecordDO) != 0;
    }

    /**
     * {@inheritDoc}
     * 更新软件记录，返回是否成功。
     *
     * @param softwareRecord 软件记录对象，包含名称和版本号等信息
     * @return boolean 如果更新成功则返回true，否则返回false
     */
    @Override
    public Boolean update(SoftwareRecord softwareRecord) {

        SoftwareRecordDO softwareRecordDO = BeanCopyUtil.copyObject(softwareRecord, SoftwareRecordDO::new);
        softwareRecordDO.setUpdatedTime(LocalDateTime.now());
        return softwareRecordMapper.updateByNameAndVersion(softwareRecordDO) != 0;
    }

    /**
     * {@inheritDoc}
     * 根据集群ID、软件名称和版本号查找软件记录。
     *
     * @param clusterId 集群ID，不能为空
     * @param name 软件名称，不能为空
     * @param version 软件版本号，不能为空
     * @return 返回一个包含软件信息的SoftwareRecord对象，如果没有找到则返回null
     * @throws IllegalArgumentException 当集群ID、软件名称或版本号为空时抛出此异常
     */
    @Override
    public SoftwareRecord findSoftwareByNameAndVersion(String clusterId, String name, String version) {

        SelectStatementProvider selectStatement = select(SoftwareRecordMapper.selectList)
                .from(softwareRecord)
                .where(softwareRecord.clusterId, isEqualTo(clusterId))
                .and(softwareRecord.name, isEqualTo(name))
                .and(softwareRecord.version, isEqualTo(version))
                .and(softwareRecord.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        SoftwareRecordDO softwareRecordDO = softwareRecordMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(softwareRecordDO, SoftwareRecord::new);
    }

    @Override
    public SoftwareRecord findSoftwareByNameAndVersionAndInstanceId(String clusterId, String name, String version, String installedInstanceId) {

        SelectStatementProvider selectStatement = select(SoftwareRecordMapper.selectList)
                .from(softwareRecord)
                .where(softwareRecord.clusterId, isEqualTo(clusterId))
                .and(softwareRecord.name, isEqualTo(name))
                .and(softwareRecord.version, isEqualTo(version))
                .and(softwareRecord.installedInstanceId, isEqualToWhenPresent(installedInstanceId))
                .and(softwareRecord.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        SoftwareRecordDO softwareRecordDO = softwareRecordMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(softwareRecordDO, SoftwareRecord::new);
    }

    /**
     * {@inheritDoc}
     * 根据集群ID和实例ID查找软件信息，返回一个包含SoftwareRecord对象的列表。
     *
     * @param clusterId 集群ID，不能为null或空字符串
     * @param instanceId 实例ID，不能为null或空字符串
     * @return 包含SoftwareRecord对象的列表，如果没有找到则返回一个空列表
     * @throws IllegalArgumentException 如果clusterId或instanceId为null或空字符串时抛出此异常
     */
    @Override
    public List<SoftwareRecord> findSoftwareByInstanceId(String clusterId, String instanceId) {

        SelectStatementProvider selectStatement = select(SoftwareRecordMapper.selectList)
                .from(softwareRecord)
                .where(softwareRecord.clusterId, isEqualTo(clusterId))
                .and(softwareRecord.installedInstanceId, isEqualTo(instanceId))
                .and(softwareRecord.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<SoftwareRecordDO> softwareDOList = softwareRecordMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(softwareDOList, SoftwareRecord::new);
    }

    /**
     * {@inheritDoc}
     * 根据集群ID查找软件记录列表。
     *
     * @param clusterId 集群ID，不能为null或空字符串
     * @return 包含所有匹配的软件记录的列表，如果没有匹配项则返回一个空列表
     * @throws IllegalArgumentException 当clusterId为null或空字符串时抛出此异常
     */
    @Override
    public List<SoftwareRecord> findSoftwareByClusterId(String clusterId) {

        SelectStatementProvider selectStatement = select(SoftwareRecordMapper.selectList)
                .from(softwareRecord)
                .where(softwareRecord.clusterId, isEqualTo(clusterId))
                .and(softwareRecord.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<SoftwareRecordDO> softwareDOList = softwareRecordMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(softwareDOList, SoftwareRecord::new);
    }
}
