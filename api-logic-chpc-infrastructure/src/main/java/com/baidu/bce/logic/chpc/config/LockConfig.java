package com.baidu.bce.logic.chpc.config;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.TimeUnit;

import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * @Author: lilu24
 * @Date: 2022-12-23
 */
@Slf4j
@Component
public class LockConfig {

    @Resource
    private RedissonClient redissonClient;


    public Boolean tryLock(final String lockKey) {

        RLock rLock = redissonClient.getLock(lockKey);

        try {
            return rLock.tryLock();
        } catch (Exception e) {
            log.debug("failed to get lock, lockKey:{}, error message:{}",
                    lockKey, e.getMessage());
        }

        return false;
    }

    public Boolean tryLock (final String lockKey, final long waitTime, final long leaseTime, final TimeUnit timeUnit) {

        RLock rLock = redissonClient.getLock(lockKey);
        if (!rLock.isLocked()) {
            try {
                return rLock.tryLock(waitTime, leaseTime, timeUnit);
            } catch (Exception e) {
                log.debug("failed to get lock with expire time, lockKey:{}, error message:{}",
                        lockKey, e.getMessage());
            }
        }

        return false;
    }

    public Boolean unLock(final String lockKey) {

        try {
            RLock rLock = redissonClient.getLock(lockKey);
            rLock.unlock();
            return true;
        } catch (Exception e) {
            log.debug("failed to unlock, lockKey:{}, error message:{}",
                    lockKey, e.getMessage());
        }

        return false;
    }

}
