package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.UserCallbackTaskDO;
import com.baidu.bce.logic.chpc.database.mapper.UserCallbackTaskMapper;
import com.baidu.bce.logic.chpc.gateway.UserCallbackTaskDAOGateway;
import com.baidu.bce.logic.chpc.model.UserCallbackTask;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.UserCallbackTaskDynamicSqlSupport.task;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

/**
 * @Author: qiansheng01
 * @Date: 2024-03-24
 */
@Service
public class UserCallbackTaskDAOGatewayImpl implements UserCallbackTaskDAOGateway {

    @Resource
    private UserCallbackTaskMapper userCallbackTaskMapper;

    @Override
    public Boolean insert(UserCallbackTask task) {

        UserCallbackTaskDO taskDO = BeanCopyUtil.copyObject(task, UserCallbackTaskDO::new);
        taskDO.setDeleted(false);
        taskDO.setCreatedTime(LocalDateTime.now());
        taskDO.setUpdatedTime(LocalDateTime.now());

        return userCallbackTaskMapper.insertSelective(taskDO) != 0;
    }

    @Override
    public Boolean update(String taskId, String status) {


        UserCallbackTaskDO taskDO = new UserCallbackTaskDO();
        taskDO.setTaskId(taskId);
        taskDO.setUpdatedTime(LocalDateTime.now());
        taskDO.setStatus(status);

        return userCallbackTaskMapper.updateByTaskId(taskDO) != 0;
    }

    @Override
    public List<UserCallbackTask> listByTaskId(String clusterId, String taskId) {

        SelectStatementProvider selectStatement = select(UserCallbackTaskMapper.selectList)
                .from(task)
                .where(task.deleted, isEqualTo(false))
                .and(task.taskId, isEqualTo(taskId))
                .and(task.clusterId, isEqualToWhenPresent(clusterId))
                .build().render(RenderingStrategies.MYBATIS3);

        List<UserCallbackTaskDO> taskDOS = userCallbackTaskMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(taskDOS, UserCallbackTask::new);
    }

    @Override
    public List<UserCallbackTask> listByJobIdAndStatus(String clusterId, List<String> jobId, String status) {
            SelectStatementProvider selectStatement = select(UserCallbackTaskMapper.selectList)
                    .from(task)
                    .where(task.deleted, isEqualTo(false))
                    .and(task.jobId, isIn(jobId))
                    .and(task.clusterId, isEqualToWhenPresent(clusterId))
                    .and(task.status, isEqualTo(status))
                    .build().render(RenderingStrategies.MYBATIS3);
    
            List<UserCallbackTaskDO> taskDOS = userCallbackTaskMapper.selectMany(selectStatement);
            return BeanCopyUtil.copyListProperties(taskDOS, UserCallbackTask::new);
    }
}
