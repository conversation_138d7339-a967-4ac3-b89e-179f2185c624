package com.baidu.bce.logic.chpc.cfs.autoconfigure;

import com.baidu.bce.logic.chpc.cfs.client.CfsNioClient;
import com.baidu.bce.logic.core.constants.CommonsConstants;
import com.baidu.bce.logic.core.util.WebClientBuilderUtil;
import com.baidu.bce.logic.core.webclient.SignByStsExchangeFilter;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.web.reactive.function.client.WebClient;

import java.util.Map;


@AutoConfiguration
@EnableConfigurationProperties({CfsProperties.class})
@ConditionalOnClass(CfsNioClient.class)
@ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".cfs",
        value = CommonsConstants.STARTER_ENABLED,
        havingValue = CommonsConstants.ENABLE, matchIfMissing = true)
@Slf4j
@AllArgsConstructor
public class CfsAutoConfiguration {
    
    private final CfsProperties properties;

    private final RegionConfiguration regionConfiguration;

    @Bean
    @ConditionalOnProperty(prefix = CommonsConstants.CONFIG_SDK_PREFIX + ".cfs",
            value = CommonsConstants.CLIENT_TYPE,
            havingValue = CommonsConstants.CLIENT_TYPE_SINGLE, matchIfMissing = true)
    public CfsNioClient cfseNioClient(WebClient.Builder webClientBuilder,
                                         Map<String, SignByStsExchangeFilter> signByStsExchangeFilterMap) {
        return WebClientBuilderUtil.nioClient(properties.getEndpoint(), webClientBuilder,
                signByStsExchangeFilterMap.get(regionConfiguration.getCurrentRegion()),
                webClient -> new CfsNioClient(webClient, properties.getConfigs()));
    }
}
