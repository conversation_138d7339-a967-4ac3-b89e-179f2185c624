package com.baidu.bce.logic.chpc.oos.gatewayimpl;

import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.oos.CreateOosExecutionRequest;
import com.baidu.bce.logic.chpc.oos.CreateOosExecutionResponse;
import com.baidu.bce.logic.chpc.oos.GetOosExecutionResponse;
import com.baidu.bce.logic.chpc.oos.client.OosNioClient;
import com.baidu.bce.logic.chpc.oos.gateway.OosGateway;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
public class OosGatewayImpl implements OosGateway {

    @Resource
    IamLogicService iamLogicService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private OosNioClient oosNioClient;

    @Override
    public String createExecution(CreateOosExecutionRequest request) {

        CreateOosExecutionResponse response = oosNioClient.createExecution(request, LogicUserService.getAccountId());

        if (!response.isSuccess()) {
            throw new CommonException.RelatedServiceException(response.getMsg(), "OosServiceException");
        }

        return response.getResult().getId();
    }

    @Override
    public GetOosExecutionResponse.Result getExecutionById(String executionId) {

        GetOosExecutionResponse response = oosNioClient.getExecutionById(executionId, LogicUserService.getAccountId());

        if (!response.isSuccess()) {
            throw new CommonException.RelatedServiceException(response.getMsg(), "OosServiceException");
        }

        return response.getResult();
    }

}
