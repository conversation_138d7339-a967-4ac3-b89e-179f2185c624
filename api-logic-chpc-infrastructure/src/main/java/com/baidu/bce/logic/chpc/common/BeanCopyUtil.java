package com.baidu.bce.logic.chpc.common;

import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Supplier;

/**
 * @Author: lilu24
 * @Date: 2022-12-20
 */
public class BeanCopyUtil extends BeanUtils {


    /**
     * 集合数据的拷贝
     *
     * @param sources: 数据源类
     * @param target:  目标类::new
     * @return
     */
    public static <S, T> List<T> copyListProperties(List<S> sources, Supplier<T> target) {
        List<T> list = new ArrayList<>(sources.size());
        for (S source : sources) {
            T t = target.get();
            copyProperties(source, t);
            list.add(t);
        }
        return list;
    }

    /**
     * 对象数据的拷贝
     *
     * @param source: 数据源类
     * @param target: 目标类::new
     * @return
     */
    public static <T> T copyObject(Object source, Supplier<T> target) {

        if (source == null) {
            return null;
        }

        T t = target.get();
        copyProperties(source, t);
        return t;
    }

}
