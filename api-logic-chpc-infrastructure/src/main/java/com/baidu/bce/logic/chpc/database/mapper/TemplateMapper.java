package com.baidu.bce.logic.chpc.database.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.DeleteProvider;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.UpdateProvider;
import org.apache.ibatis.type.JdbcType;
import com.baidu.bce.logic.chpc.database.dataobject.TemplateDO;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.delete.render.DeleteStatementProvider;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.insert.render.MultiRowInsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.jobName;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.jobCmd;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.queueName;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.postCmd;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.isUnique;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.nhosts;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.ncpus;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.walltime;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.stderrPath;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.stdoutPath;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.bosFilePath;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.decompressCmd;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.createTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.updateTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.envVars;
import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.templateDO;


import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;


@Mapper
public interface TemplateMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<TemplateDO>, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.478104+08:00", comments="Source Table: t_chpc_job_template")
    BasicColumn[] selectList = BasicColumn.columnList(id,
            name,
            clusterId,
            jobName,
            jobCmd,
            queueName,
            postCmd,
            deleted,
            isUnique,
            nhosts,
            ncpus,
            walltime,
            stdoutPath,
            stderrPath,
            bosFilePath,
            decompressCmd, createTime, updateTime, envVars);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.474871+08:00", comments="Source Table: t_chpc_job_template")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    long count(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.475307+08:00", comments="Source Table: t_chpc_job_template")
    @DeleteProvider(type=SqlProviderAdapter.class, method="delete")
    int delete(DeleteStatementProvider deleteStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.475458+08:00", comments="Source Table: t_chpc_job_template")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    int insert(InsertStatementProvider<TemplateDO> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.47568+08:00", comments="Source Table: t_chpc_job_template")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultiple")
    int insertMultiple(MultiRowInsertStatementProvider<TemplateDO> multipleInsertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.475838+08:00", comments="Source Table: t_chpc_job_template")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("TemplateDOResult")
    Optional<TemplateDO> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.475998+08:00", comments="Source Table: t_chpc_job_template")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="TemplateDOResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="name", property="name", jdbcType=JdbcType.VARCHAR),
        @Result(column="cluster_id", property="clusterId", jdbcType=JdbcType.VARCHAR),
        @Result(column="job_name", property="jobName", jdbcType=JdbcType.VARCHAR),
        @Result(column="job_cmd", property="jobCmd", jdbcType=JdbcType.VARCHAR),
        @Result(column="queue_name", property="queueName", jdbcType=JdbcType.VARCHAR),
        @Result(column="post_cmd", property="postCmd", jdbcType=JdbcType.VARCHAR),
        @Result(column="deleted", property="deleted", jdbcType=JdbcType.BOOLEAN),
        @Result(column="is_unique", property="isUnique", jdbcType=JdbcType.BOOLEAN),
        @Result(column="nhosts", property="nhosts", jdbcType=JdbcType.INTEGER),
        @Result(column="ncpus", property="ncpus", jdbcType=JdbcType.INTEGER),
        @Result(column="walltime", property="walltime", jdbcType=JdbcType.INTEGER),
        @Result(column="stdout_path", property="stdoutPath", jdbcType=JdbcType.VARCHAR),
        @Result(column="stderr_path", property="stderrPath", jdbcType=JdbcType.VARCHAR),
        @Result(column="bos_file_path", property="bosFilePath", jdbcType=JdbcType.VARCHAR),
        @Result(column="decompress_cmd", property="decompressCmd", jdbcType=JdbcType.VARCHAR),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="env_vars", property="envVars", jdbcType=JdbcType.LONGVARCHAR)
    })
    List<TemplateDO> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.476442+08:00", comments="Source Table: t_chpc_job_template")
    @UpdateProvider(type=SqlProviderAdapter.class, method="update")
    int update(UpdateStatementProvider updateStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.476553+08:00", comments="Source Table: t_chpc_job_template")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, templateDO, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.476676+08:00", comments="Source Table: t_chpc_job_template")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, templateDO, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.476797+08:00", comments="Source Table: t_chpc_job_template")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.476931+08:00", comments="Source Table: t_chpc_job_template")
    default int insert(TemplateDO record) {
        return MyBatis3Utils.insert(this::insert, record, templateDO, c ->
            c.map(id).toProperty("id")
            .map(name).toProperty("name")
            .map(clusterId).toProperty("clusterId")
            .map(jobName).toProperty("jobName")
            .map(jobCmd).toProperty("jobCmd")
            .map(queueName).toProperty("queueName")
            .map(postCmd).toProperty("postCmd")
            .map(deleted).toProperty("deleted")
            .map(isUnique).toProperty("isUnique")
            .map(nhosts).toProperty("nhosts")
            .map(ncpus).toProperty("ncpus")
            .map(walltime).toProperty("walltime")
            .map(stdoutPath).toProperty("stdoutPath")
            .map(stderrPath).toProperty("stderrPath")
            .map(bosFilePath).toProperty("bosFilePath")
            .map(decompressCmd).toProperty("decompressCmd")
            .map(createTime).toProperty("createTime")
            .map(updateTime).toProperty("updateTime")
            .map(envVars).toProperty("envVars")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.477453+08:00", comments="Source Table: t_chpc_job_template")
    default int insertMultiple(Collection<TemplateDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, templateDO, c ->
            c.map(id).toProperty("id")
            .map(name).toProperty("name")
            .map(clusterId).toProperty("clusterId")
            .map(jobName).toProperty("jobName")
            .map(jobCmd).toProperty("jobCmd")
            .map(queueName).toProperty("queueName")
            .map(postCmd).toProperty("postCmd")
            .map(deleted).toProperty("deleted")
            .map(isUnique).toProperty("isUnique")
            .map(nhosts).toProperty("nhosts")
            .map(ncpus).toProperty("ncpus")
            .map(walltime).toProperty("walltime")
            .map(stdoutPath).toProperty("stdoutPath")
            .map(stderrPath).toProperty("stderrPath")
            .map(bosFilePath).toProperty("bosFilePath")
            .map(decompressCmd).toProperty("decompressCmd")
            .map(createTime).toProperty("createTime")
            .map(updateTime).toProperty("updateTime")
            .map(envVars).toProperty("envVars")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.477604+08:00", comments="Source Table: t_chpc_job_template")
    default int insertSelective(TemplateDO record) {
        return MyBatis3Utils.insert(this::insert, record, templateDO, c ->
            c.map(id).toPropertyWhenPresent("id", record::getId)
            .map(name).toPropertyWhenPresent("name", record::getName)
            .map(clusterId).toPropertyWhenPresent("clusterId", record::getClusterId)
            .map(jobName).toPropertyWhenPresent("jobName", record::getJobName)
            .map(jobCmd).toPropertyWhenPresent("jobCmd", record::getJobCmd)
            .map(queueName).toPropertyWhenPresent("queueName", record::getQueueName)
            .map(postCmd).toPropertyWhenPresent("postCmd", record::getPostCmd)
            .map(deleted).toPropertyWhenPresent("deleted", record::getDeleted)
            .map(isUnique).toPropertyWhenPresent("isUnique", record::getIsUnique)
            .map(nhosts).toPropertyWhenPresent("nhosts", record::getNhosts)
            .map(ncpus).toPropertyWhenPresent("ncpus", record::getNcpus)
            .map(walltime).toPropertyWhenPresent("walltime", record::getWalltime)
            .map(stdoutPath).toPropertyWhenPresent("stdoutPath", record::getStdoutPath)
            .map(stderrPath).toPropertyWhenPresent("stderrPath", record::getStderrPath)
            .map(bosFilePath).toPropertyWhenPresent("bosFilePath", record::getBosFilePath)
            .map(decompressCmd).toPropertyWhenPresent("decompressCmd", record::getDecompressCmd)
            .map(createTime).toPropertyWhenPresent("createTime", record::getCreateTime)
            .map(updateTime).toPropertyWhenPresent("updateTime", record::getUpdateTime)
            .map(envVars).toPropertyWhenPresent("envVars", record::getEnvVars)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.478414+08:00", comments="Source Table: t_chpc_job_template")
    default Optional<TemplateDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, templateDO, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.478575+08:00", comments="Source Table: t_chpc_job_template")
    default List<TemplateDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, templateDO, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.478716+08:00", comments="Source Table: t_chpc_job_template")
    default List<TemplateDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, templateDO, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.47884+08:00", comments="Source Table: t_chpc_job_template")
    default Optional<TemplateDO> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.478962+08:00", comments="Source Table: t_chpc_job_template")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, templateDO, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.479079+08:00", comments="Source Table: t_chpc_job_template")
    static UpdateDSL<UpdateModel> updateAllColumns(TemplateDO record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(record::getId)
                .set(name).equalTo(record::getName)
                .set(clusterId).equalTo(record::getClusterId)
                .set(jobName).equalTo(record::getJobName)
                .set(jobCmd).equalTo(record::getJobCmd)
                .set(queueName).equalTo(record::getQueueName)
                .set(postCmd).equalTo(record::getPostCmd)
                .set(deleted).equalTo(record::getDeleted)
                .set(isUnique).equalTo(record::getIsUnique)
                .set(nhosts).equalTo(record::getNhosts)
                .set(ncpus).equalTo(record::getNcpus)
                .set(walltime).equalTo(record::getWalltime)
                .set(stdoutPath).equalTo(record::getStdoutPath)
                .set(stderrPath).equalTo(record::getStderrPath)
                .set(bosFilePath).equalTo(record::getBosFilePath)
                .set(decompressCmd).equalTo(record::getDecompressCmd)
                .set(createTime).equalTo(record::getCreateTime)
                .set(updateTime).equalTo(record::getUpdateTime)
                .set(envVars).equalTo(record::getEnvVars);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.479275+08:00", comments="Source Table: t_chpc_job_template")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(TemplateDO record, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(record::getId)
                .set(name).equalToWhenPresent(record::getName)
                .set(clusterId).equalToWhenPresent(record::getClusterId)
                .set(jobName).equalToWhenPresent(record::getJobName)
                .set(jobCmd).equalToWhenPresent(record::getJobCmd)
                .set(queueName).equalToWhenPresent(record::getQueueName)
                .set(postCmd).equalToWhenPresent(record::getPostCmd)
                .set(deleted).equalToWhenPresent(record::getDeleted)
                .set(isUnique).equalToWhenPresent(record::getIsUnique)
                .set(nhosts).equalToWhenPresent(record::getNhosts)
                .set(ncpus).equalToWhenPresent(record::getNcpus)
                .set(walltime).equalToWhenPresent(record::getWalltime)
                .set(stdoutPath).equalToWhenPresent(record::getStdoutPath)
                .set(stderrPath).equalToWhenPresent(record::getStderrPath)
                .set(bosFilePath).equalToWhenPresent(record::getBosFilePath)
                .set(decompressCmd).equalToWhenPresent(record::getDecompressCmd)
                .set(createTime).equalToWhenPresent(record::getCreateTime)
                .set(updateTime).equalToWhenPresent(record::getUpdateTime)
                .set(envVars).equalToWhenPresent(record::getEnvVars);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.47956+08:00", comments="Source Table: t_chpc_job_template")
    default int updateByPrimaryKey(TemplateDO record) {
        return update(c ->
            c.set(name).equalTo(record::getName)
            .set(clusterId).equalTo(record::getClusterId)
            .set(jobName).equalTo(record::getJobName)
            .set(jobCmd).equalTo(record::getJobCmd)
            .set(queueName).equalTo(record::getQueueName)
            .set(postCmd).equalTo(record::getPostCmd)
            .set(deleted).equalTo(record::getDeleted)
            .set(isUnique).equalTo(record::getIsUnique)
            .set(nhosts).equalTo(record::getNhosts)
            .set(ncpus).equalTo(record::getNcpus)
            .set(walltime).equalTo(record::getWalltime)
            .set(stdoutPath).equalTo(record::getStdoutPath)
            .set(stderrPath).equalTo(record::getStderrPath)
            .set(bosFilePath).equalTo(record::getBosFilePath)
            .set(decompressCmd).equalTo(record::getDecompressCmd)
            .set(createTime).equalTo(record::getCreateTime)
            .set(updateTime).equalTo(record::getUpdateTime)
            .set(envVars).equalTo(record::getEnvVars)
            .where(id, isEqualTo(record::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.479736+08:00", comments="Source Table: t_chpc_job_template")
    default int updateByPrimaryKeySelective(TemplateDO record) {
        return update(c ->
            c.set(name).equalToWhenPresent(record::getName)
            .set(clusterId).equalToWhenPresent(record::getClusterId)
            .set(jobName).equalToWhenPresent(record::getJobName)
            .set(jobCmd).equalToWhenPresent(record::getJobCmd)
            .set(queueName).equalToWhenPresent(record::getQueueName)
            .set(postCmd).equalToWhenPresent(record::getPostCmd)
            .set(deleted).equalToWhenPresent(record::getDeleted)
            .set(isUnique).equalToWhenPresent(record::getIsUnique)
            .set(nhosts).equalToWhenPresent(record::getNhosts)
            .set(ncpus).equalToWhenPresent(record::getNcpus)
            .set(walltime).equalToWhenPresent(record::getWalltime)
            .set(stdoutPath).equalToWhenPresent(record::getStdoutPath)
            .set(stderrPath).equalToWhenPresent(record::getStderrPath)
            .set(bosFilePath).equalToWhenPresent(record::getBosFilePath)
            .set(decompressCmd).equalToWhenPresent(record::getDecompressCmd)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(envVars).equalToWhenPresent(record::getEnvVars)
            .where(id, isEqualTo(record::getId))
        );
    }

        @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.479736+08:00", comments="Source Table: t_chpc_job_template")
    default int updateByNameAndClusterSelective(TemplateDO record) {
        return update(c ->
            c.set(jobName).equalToWhenPresent(record::getJobName)
            .set(jobCmd).equalToWhenPresent(record::getJobCmd)
            .set(queueName).equalToWhenPresent(record::getQueueName)
            .set(postCmd).equalToWhenPresent(record::getPostCmd)
            .set(deleted).equalToWhenPresent(record::getDeleted)
            .set(isUnique).equalToWhenPresent(record::getIsUnique)
            .set(nhosts).equalToWhenPresent(record::getNhosts)
            .set(ncpus).equalToWhenPresent(record::getNcpus)
            .set(walltime).equalToWhenPresent(record::getWalltime)
            .set(stdoutPath).equalToWhenPresent(record::getStdoutPath)
            .set(stderrPath).equalToWhenPresent(record::getStderrPath)
            .set(bosFilePath).equalToWhenPresent(record::getBosFilePath)
            .set(decompressCmd).equalToWhenPresent(record::getDecompressCmd)
            .set(createTime).equalToWhenPresent(record::getCreateTime)
            .set(updateTime).equalToWhenPresent(record::getUpdateTime)
            .set(envVars).equalToWhenPresent(record::getEnvVars)
            .where(clusterId, isEqualTo(record::getClusterId))
                    .and(name, isEqualTo(record::getName))
                    .and(deleted, isEqualTo(false))
        );
    }

}