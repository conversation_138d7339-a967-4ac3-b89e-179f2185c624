package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.WorkspaceDO;
import com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.workspace;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.workspaceId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.description;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.bosBucket;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.deletedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkspaceDynamicSqlSupport.clusterName;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface WorkspaceMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<WorkspaceDO>, CommonUpdateMapper {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(WorkspaceDO row) {
        return MyBatis3Utils.insert(this::insert, row, workspace, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(workspaceId).toPropertyWhenPresent("workspaceId", row::getWorkspaceId)
                        .map(name).toPropertyWhenPresent("name", row::getName)
                        .map(description).toPropertyWhenPresent("description", row::getDescription)
                        .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                        .map(clusterName).toPropertyWhenPresent("clusterName", row::getClusterName)
                        .map(bosBucket).toPropertyWhenPresent("bosBucket", row::getBosBucket)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(status).toPropertyWhenPresent("status", row::getStatus)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            clusterId,
            name,
            description,
            status,
            workspaceId,
            bosBucket,
            accountId,
            clusterName,
            deletedTime
    );


    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "WorkspaceDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted_time", property = "deletedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.TINYINT),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "workspace_id", property = "workspaceId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "bos_bucket", property = "bosBucket", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cluster_name", property = "clusterName", jdbcType = JdbcType.VARCHAR)
    })
    List<WorkspaceDO> selectMany(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("WorkspaceDOResult")
    Optional<WorkspaceDO> selectOne(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<WorkspaceDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, workspace, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByWorkspaceId(WorkspaceDO row) {
        return update(c ->
                c.set(WorkspaceDynamicSqlSupport.updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(WorkspaceDynamicSqlSupport.deleted).equalToWhenPresent(row::getDeleted)
                        .set(WorkspaceDynamicSqlSupport.status).equalToWhenPresent(row::getStatus)
                        .where(workspaceId, isEqualTo(row::getWorkspaceId))
                        .and(accountId, isEqualTo(row::getAccountId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, workspace, completer);
    }


}
