package com.baidu.bce.logic.chpc.cos.client;

import java.util.Map;
import java.util.UUID;

import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.cos.CreateStackRequest;
import com.baidu.bce.logic.chpc.cos.CreateStackResponse;
import com.baidu.bce.logic.chpc.cos.DeleteStackResponse;
import com.baidu.bce.logic.chpc.cos.GetStackListRequest;
import com.baidu.bce.logic.chpc.cos.GetStackListResponse;
import com.baidu.bce.logic.chpc.cos.StackDetail;


public class CosNioClient extends BceNioClient {

    private static final String BASE_URL = "/api/logic/cos/v3/stack";

    private static final String DEFAULT_FROM = "logic-chpc";

    public CosNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    /**
     * {@inheritDoc}
     *
     * 创建一个堆栈。
     *
     * @param createStackRequest 包含堆栈信息的请求对象
     * @return 包含堆栈ID的响应对象
     * @throws CosClientException COS客户端异常
     */
    public CreateStackResponse createStack(CreateStackRequest createStackRequest, String accountId) {
        String requestId = BceInternalRequest.getThreadRequestId();
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL).build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .header(BceConstant.X_BCE_REQUEST_ID, requestId + UUID.randomUUID().toString())
                .header("from", DEFAULT_FROM)
                .bodyValue(createStackRequest)
                .retrieve()
                .bodyToMono(CreateStackResponse.class).block();
    }

    /**
     * 更新栈资源，实现集群的扩容、缩容
     *
     * @param createStackRequest 更新后的栈资源信息
     * @return 请求结果
     */
    public CreateStackResponse updateStack(CreateStackRequest createStackRequest, String accountId) {
        String requestId = BceInternalRequest.getThreadRequestId();
        return webClient.patch()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL).build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .header(BceConstant.X_BCE_REQUEST_ID, requestId + UUID.randomUUID().toString())
                .header("from", DEFAULT_FROM)
                .bodyValue(createStackRequest)
                .retrieve()
                .bodyToMono(CreateStackResponse.class).block();
    }

    /**
     * {@inheritDoc}
     *
     * 删除一个堆栈。
     *
     * @param stackId 堆栈ID，不能为空
     * @return {@link DeleteStackResponse} 响应对象，包含了删除操作的结果信息
     * @throws CosXmlClientException  SDK客户端异常时抛出，请参考SDK文档解决
     * @throws CosXmlServiceException COS服务异常时抛出，请参考SDK文档解决
     */
    public DeleteStackResponse deleteStack(String stackId, String accountId) {
        String requestId = BceInternalRequest.getThreadRequestId();
        return webClient.delete()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL)
                .queryParam("id", stackId)
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .header(BceConstant.X_BCE_REQUEST_ID, requestId + UUID.randomUUID().toString())
                .header("from", DEFAULT_FROM)
                .retrieve()
                .bodyToMono(DeleteStackResponse.class).block();
    }

    public StackDetail getStack(String stackId, String accountId) {
        String requestId = BceInternalRequest.getThreadRequestId();
        return webClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL)
                .queryParam("id", stackId)
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .header(BceConstant.X_BCE_REQUEST_ID, requestId + UUID.randomUUID().toString())
                .header("from", DEFAULT_FROM)
                .retrieve()
                .bodyToMono(StackDetail.class).block();
    }

    /**
     * @Description:
     * 获取堆栈列表，返回一个GetStackListResponse对象。
     * 请求方式为POST，Content-Type为application/json。
     * @Param request GetStackListRequest类型的参数，包含了请求体中需要传递的参数。
     * @Return GetStackListResponse类型，包含了服务器端返回的结果信息。
     * @Throws IOException 当发生IO异常时抛出该异常。
     * @Throws CosServiceException 当COS服务器返回错误码时抛出该异常。
     */
    public GetStackListResponse getStackList(GetStackListRequest request, String accountId) {
        String requestId = BceInternalRequest.getThreadRequestId();
        return webClient.get()
                .uri(uriBuilder -> uriBuilder.path(BASE_URL + "/list").build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .header(BceConstant.X_BCE_REQUEST_ID, requestId + UUID.randomUUID().toString())
                .header("from", DEFAULT_FROM)
                .retrieve()
                .bodyToMono(GetStackListResponse.class).block();
    }
}
