package com.baidu.bce.logic.chpc.database.dataobject;

import jakarta.annotation.Generated;

import java.time.LocalDateTime;

public class TagsDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterId;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String resourceId;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getResourceId() {
        return resourceId;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String name;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String tagType;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String tagKey;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String tagValue;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterId() {
        return clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getName() {
        return name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setName(String name) {
        this.name = name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getTagType() {
        return tagType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setTagType(String tagType) {
        this.tagType = tagType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getTagKey() {
        return tagKey;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setTagKey(String tagKey) {
        this.tagKey = tagKey;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getTagValue() {
        return tagValue;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

}
