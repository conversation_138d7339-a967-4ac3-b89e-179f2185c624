package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;

public class WorkflowDynamicSqlSupport {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final Workflow workflow = new Workflow();
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = workflow.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> workflowId = workflow.workflowId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> name = workflow.name;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> description = workflow.description;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> version = workflow.version;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")

    public static final SqlColumn<String> language = workflow.language;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")

    public static final SqlColumn<String> languageVersion = workflow.languageVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")

    public static final SqlColumn<String> accountId = workflow.accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> workspaceId = workflow.workspaceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")

    public static final SqlColumn<Boolean> deleted = workflow.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> introduction = workflow.introduction;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> document = workflow.document;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> inputs = workflow.inputs;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = workflow.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = workflow.updatedTime;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> deletedTime = workflow.deletedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class Workflow extends AliasableSqlTable<Workflow> {

        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> version = column("version", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedTime = column("deleted_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> description = column("description", JDBCType.VARCHAR);

        public final SqlColumn<String> workflowId = column("workflow_id", JDBCType.VARCHAR);

        public final SqlColumn<String> language = column("language", JDBCType.VARCHAR);
        public final SqlColumn<String> languageVersion = column("language_version", JDBCType.VARCHAR);
        public final SqlColumn<String> accountId = column("account_id", JDBCType.VARCHAR);
        public final SqlColumn<String> workspaceId = column("workspace_id", JDBCType.VARCHAR);

        public final SqlColumn<String> introduction = column("introduction", JDBCType.LONGVARCHAR);
        public final SqlColumn<String> document = column("document", JDBCType.LONGVARCHAR);

        public final SqlColumn<String> inputs = column("inputs", JDBCType.LONGVARCHAR);

        public Workflow() {
            super("t_chpc_workflow", Workflow::new);
        }


    }
}
