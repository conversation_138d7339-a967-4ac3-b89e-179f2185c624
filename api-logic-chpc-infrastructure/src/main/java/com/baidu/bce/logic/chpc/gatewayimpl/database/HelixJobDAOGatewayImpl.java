package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.HelixJobDO;
import com.baidu.bce.logic.chpc.database.mapper.HelixJobMapper;
import com.baidu.bce.logic.chpc.gateway.HelixJobDAOGateway;
import com.baidu.bce.logic.chpc.model.HelixJob;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.helixJob;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class HelixJobDAOGatewayImpl implements HelixJobDAOGateway {


    @Resource
    private HelixJobMapper helixJobMapper;

    @Override
    public HelixJob findByTaskId(String taskId) {
        SelectStatementProvider selectStatement =
                select(HelixJobMapper.selectList)
                        .from(helixJob)
                        .where(helixJob.taskId, isEqualTo(taskId))
                        .and(helixJob.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);

        HelixJobDO helixJobDOS = helixJobMapper.selectOne(selectStatement).orElse(null);
        ;
        return BeanCopyUtil.copyObject(helixJobDOS, HelixJob::new);
    }


    @Override
    public Boolean insert(HelixJob helixJob) {
        HelixJobDO helixJobDO = new HelixJobDO();
        BeanUtils.copyProperties(helixJob, helixJobDO);
        helixJobDO.setDeleted(false);
        helixJobDO.setCreatedTime(LocalDateTime.now());
        helixJobDO.setUpdatedTime(LocalDateTime.now());
        return helixJobMapper.insertSelective(helixJobDO) != 0;
    }

    @Override
    public Boolean delete(String taskId) {
        HelixJobDO helixJobDO = new HelixJobDO();
        helixJobDO.setTaskId(taskId);
        helixJobDO.setUpdatedTime(LocalDateTime.now());
        helixJobDO.setDeleted(true);
        return helixJobMapper.updateSelectiveColumns(helixJobDO) != 0;
    }

    @Override
    public Boolean update(String taskId, String orderId, String jobId, String jobStatus, String action, String jobRunTime, Boolean deleted) {
        HelixJobDO helixJobDO = new HelixJobDO();
        helixJobDO.setTaskId(taskId);
        helixJobDO.setUpdatedTime(LocalDateTime.now());
        if (StringUtils.isNotEmpty(orderId)) {
            helixJobDO.setOrderId(orderId);
        }
        if (StringUtils.isNotEmpty(jobId)) {
            helixJobDO.setJobId(jobId);
        }
        if (StringUtils.isNotEmpty(jobStatus)) {
            helixJobDO.setJobStatus(jobStatus);
        }
        if (StringUtils.isNotEmpty(action)) {
            helixJobDO.setAction(action);
        }
        if (StringUtils.isNotEmpty(jobRunTime)) {
            helixJobDO.setJobRunTime(jobRunTime);
        }
        helixJobDO.setDeleted(deleted);
        return helixJobMapper.updateSelectiveColumns(helixJobDO) != 0;
    }
}

