package com.baidu.bce.logic.chpc.gatewayimpl.external;

import com.baidu.bce.logic.chpc.gateway.external.WhitelistGateway;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.user.settings.sdk.UserSettingsNioClient;
import com.baidu.bce.user.settings.sdk.model.QuotaBatchRequest;
import com.baidu.bce.user.settings.sdk.model.QuotaRequest;

import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;

import jakarta.annotation.Resource;

@Service
public class WhitelistGatewayImpl implements WhitelistGateway {

    @Autowired
    public IamLogicService iamLogicService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private UserSettingsNioClient userSettingsNioClient;

    @Override
    public Map<String, String> getQuota(String accountId, String... types) {
        if (StringUtils.isEmpty(accountId) || types.length == 0) {
            return Collections.emptyMap();
        }
        QuotaBatchRequest quotaRequest = new QuotaBatchRequest();
        quotaRequest.setQuotaTypes(Arrays.asList(types));
        quotaRequest.setUserValue(accountId);
        quotaRequest.setUserType(QuotaRequest.UserType.AccountId);
        return userSettingsNioClient.getBatchQuota(accountId, quotaRequest).block().getQuotaType2quota();
    }
}
