package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.HelixJobDO;

import jakarta.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.extra;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.helixJob;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.jobChargeAmount;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.jobCouponPrice;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.jobId;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.jobPackageDeduction;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.jobPrice;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.jobProduct;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.jobRunTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.jobStatus;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.action;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.jobTokenLength;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.orderId;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.platformId;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.taskId;
import static com.baidu.bce.logic.chpc.database.mapper.local.HelixJobDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface HelixJobMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<HelixJobDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            accountId,
            platformId,
            orderId,
            taskId,
            jobId,
            clusterId,
            jobProduct,
            jobStatus,
            action,
            jobRunTime,
            jobChargeAmount,
            jobPrice,
            jobCouponPrice,
            jobTokenLength,
            jobPackageDeduction,
            createdTime,
            updatedTime,
            extra,
            deleted);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "HelixJobDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "platform_id", property = "platformId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "order_id", property = "orderId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "task_id", property = "taskId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_id", property = "jobId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_product", property = "jobProduct", jdbcType = JdbcType.VARCHAR),
            @Result(column = "action", property = "action", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_status", property = "jobStatus", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_run_time", property = "jobRunTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_charge_amount", property = "jobChargeAmount", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_price", property = "jobPrice", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_coupon_price", property = "jobCouponPrice", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_token_length", property = "jobTokenLength", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_package_deduction", property = "jobPackageDeduction", jdbcType = JdbcType.VARCHAR),
            @Result(column = "extra", property = "extra", jdbcType = JdbcType.VARCHAR),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),

    })
    List<HelixJobDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("HelixJobDOResult")
    Optional<HelixJobDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<HelixJobDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, helixJob, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, helixJob, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, helixJob, completer);
    }

    /**
     * 根据主键删除记录。
     *
     * @param key 主键值，类型为 Long
     * @return 返回受影响的行数，类型为 int
     * @since 2021/4/19
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c -> c.where(id, isEqualTo(key)));
    }

    /**
     * 插入或者更新一条记录，当主键存在时更新，否则插入。
     * 如果字段值为null，则忽略对应字段的更新操作。
     *
     * @param row ClusterDO类型的数据行，包含需要插入或更新的数据
     * @return 返回影响的行数，大于等于0表示成功，小于0表示失败
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(HelixJobDO row) {
        return MyBatis3Utils.insert(this::insert, row, helixJob, c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                .map(platformId).toPropertyWhenPresent("platformId", row::getPlatformId)
                .map(orderId).toPropertyWhenPresent("orderId", row::getOrderId)
                .map(taskId).toPropertyWhenPresent("taskId", row::getTaskId)
                .map(jobId).toPropertyWhenPresent("jobId", row::getJobId)
                .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                .map(jobProduct).toPropertyWhenPresent("jobProduct", row::getJobProduct)
                .map(jobStatus).toPropertyWhenPresent("jobStatus", row::getJobStatus)
                .map(action).toPropertyWhenPresent("action", row::getAction)
                .map(jobRunTime).toPropertyWhenPresent("jobRunTime", row::getJobRunTime)
                .map(jobChargeAmount).toPropertyWhenPresent("jobChargeAmount", row::getJobChargeAmount)
                .map(jobPrice).toPropertyWhenPresent("jobPrice", row::getJobPrice)
                .map(jobCouponPrice).toPropertyWhenPresent("jobCouponPrice", row::getJobCouponPrice)
                .map(jobTokenLength).toPropertyWhenPresent("jobTokenLength", row::getJobTokenLength)
                .map(jobPackageDeduction).toPropertyWhenPresent("jobPackageDeduction", row::getJobPackageDeduction)
                .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                .map(extra).toPropertyWhenPresent("extra", row::getExtra)
                .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted));
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<HelixJobDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, helixJob, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<HelixJobDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, helixJob, completer);
    }

    /**
     * 根据主键查询集群信息，返回一个Optional类型的结果。如果找到则返回包含结果的Optional，否则返回空Optional。
     *
     * @param key 主键，类型为Long，不能为null
     * @return 返回一个Optional类型的结果，如果找到则返回包含结果的Optional，否则返回空Optional
     * @throws IllegalArgumentException 当参数key为null时抛出此异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<HelixJobDO> selectByPrimaryKey(Long key) {
        return selectOne(c -> c.where(id, isEqualTo(key)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, helixJob, completer);
    }


    /**
     * 更新选择性列，返回一个UpdateDSL对象。
     * 当给定的行对象中的属性不为null时，才会设置相应的值。
     *
     * @param row ClusterDO类型的行对象，包含需要更新的数据
     * @param dsl UpdateDSL类型的对象，用于构建更新语句
     * @return UpdateDSL<UpdateModel>类型的对象，包含已经设置了选择性列的更新语句
     * @throws Generated 由MyBatis-Generator生成的注解，表示此方法是由MyBatis-Generator自动生成的
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateSelectiveColumns(HelixJobDO row) {
        return update(c -> c.set(id).equalToWhenPresent(row::getId)
                .set(accountId).equalToWhenPresent(row::getAccountId)
                .set(platformId).equalToWhenPresent(row::getPlatformId)
                .set(orderId).equalToWhenPresent(row::getOrderId)
                .set(jobId).equalToWhenPresent(row::getJobId)
                .set(clusterId).equalToWhenPresent(row::getClusterId)
                .set(jobProduct).equalToWhenPresent(row::getJobProduct)
                .set(jobStatus).equalToWhenPresent(row::getJobStatus)
                .set(action).equalToWhenPresent(row::getAction)
                .set(jobRunTime).equalToWhenPresent(row::getJobRunTime)
                .set(jobChargeAmount).equalToWhenPresent(row::getJobChargeAmount)
                .set(jobPrice).equalToWhenPresent(row::getJobPrice)
                .set(jobCouponPrice).equalToWhenPresent(row::getJobCouponPrice)
                .set(jobTokenLength).equalToWhenPresent(row::getJobTokenLength)
                .set(jobPackageDeduction).equalToWhenPresent(row::getJobPackageDeduction)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(extra).equalToWhenPresent(row::getExtra)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .where(taskId, isEqualTo(row::getTaskId))
                .and(deleted, isEqualTo(false)));
    }
}