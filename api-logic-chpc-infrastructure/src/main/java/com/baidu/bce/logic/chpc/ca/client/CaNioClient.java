package com.baidu.bce.logic.chpc.ca.client;

import java.util.Map;

import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.ca.model.CreateActionRequest;
import com.baidu.bce.logic.chpc.ca.model.CreateActionResponse;

public class CaNioClient extends BceNioClient {

    public CaNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public CreateActionResponse createAction(CreateActionRequest request, String accountId) {
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path("/v1/ca/action")
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(CreateActionResponse.class).block();
    }

}
