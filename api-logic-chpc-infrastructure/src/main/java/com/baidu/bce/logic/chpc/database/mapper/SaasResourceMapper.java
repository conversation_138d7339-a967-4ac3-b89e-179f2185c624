package com.baidu.bce.logic.chpc.database.mapper;

import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.orderId;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.filesetId;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.resourceType;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.resourceUuid;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.saasResource;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.pfsId;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import jakarta.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.baidu.bce.logic.chpc.database.dataobject.SaasResourceDO;

@Mapper
public interface SaasResourceMapper extends CommonCountMapper, CommonDeleteMapper,
        CommonInsertMapper<SaasResourceDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            pfsId,
            accountId,
            orderId,
            resourceType,
            resourceUuid,
            filesetId,
            status);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "SaasResourceDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "pfs_id", property = "pfsId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "order_id", property = "orderId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "resource_type", property = "resourceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "resource_uuid", property = "resourceUuid", jdbcType = JdbcType.VARCHAR),
            @Result(column = "fileset_id", property = "filesetId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR)
    })
    List<SaasResourceDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, saasResource, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, saasResource, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c -> c.where(id, isEqualTo(key)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(SaasResourceDO row) {
        return MyBatis3Utils.insert(this::insert, row, saasResource, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(pfsId).toProperty("pfsId")
                .map(accountId).toProperty("accountId")
                .map(orderId).toProperty("orderId")
                .map(resourceType).toProperty("resourceType")
                .map(resourceUuid).toProperty("resourceUuid")
                .map(filesetId).toProperty("filesetId")
                .map(status).toProperty("status"));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<SaasResourceDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, saasResource, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(pfsId).toProperty("pfsId")
                .map(accountId).toProperty("accountId")
                .map(orderId).toProperty("orderId")
                .map(resourceType).toProperty("resourceType")
                .map(resourceUuid).toProperty("resourceUuid")
                .map(filesetId).toProperty("filesetId")
                .map(status).toProperty("status"));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(SaasResourceDO row) {
        return MyBatis3Utils.insert(this::insert, row, saasResource,
                c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(pfsId).toPropertyWhenPresent("pfsId", row::getPfsId)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(orderId).toPropertyWhenPresent("orderId", row::getOrderId)
                        .map(resourceType).toPropertyWhenPresent("resourceType", row::getResourceType)
                        .map(resourceUuid).toPropertyWhenPresent("resourceUuid", row::getResourceUuid)
                        .map(filesetId).toPropertyWhenPresent("filesetId", row::getFilesetId)
                        .map(status).toPropertyWhenPresent("status", row::getStatus));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<SaasResourceDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, saasResource, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("SaasResourceDOResult")
    Optional<SaasResourceDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<SaasResourceDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, saasResource, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<SaasResourceDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, saasResource, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<SaasResourceDO> selectByPrimaryKey(Long key) {
        return selectOne(c -> c.where(id, isEqualTo(key)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, saasResource, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(SaasResourceDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(filesetId).equalTo(row::getFilesetId)
                .set(accountId).equalTo(row::getAccountId)
                .set(orderId).equalTo(row::getOrderId)
                .set(resourceType).equalTo(row::getResourceType)
                .set(resourceUuid).equalTo(row::getResourceUuid)
                .set(pfsId).equalTo(row::getPfsId)
                .set(status).equalTo(row::getStatus);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(SaasResourceDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(filesetId).equalToWhenPresent(row::getFilesetId)
                .set(accountId).equalToWhenPresent(row::getAccountId)
                .set(orderId).equalToWhenPresent(row::getOrderId)
                .set(resourceType).equalToWhenPresent(row::getResourceType)
                .set(resourceUuid).equalToWhenPresent(row::getResourceUuid)
                .set(pfsId).equalToWhenPresent(row::getPfsId)
                .set(status).equalToWhenPresent(row::getStatus);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(SaasResourceDO row) {
        return update(c -> c.set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(filesetId).equalTo(row::getFilesetId)
                .set(accountId).equalTo(row::getAccountId)
                .set(orderId).equalTo(row::getOrderId)
                .set(resourceType).equalTo(row::getResourceType)
                .set(resourceUuid).equalTo(row::getResourceUuid)
                .set(pfsId).equalTo(row::getPfsId)
                .set(status).equalTo(row::getStatus)
                .where(id, isEqualTo(row::getId)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKeySelective(SaasResourceDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(filesetId).equalToWhenPresent(row::getFilesetId)
                .set(accountId).equalToWhenPresent(row::getAccountId)
                .set(orderId).equalToWhenPresent(row::getOrderId)
                .set(resourceType).equalToWhenPresent(row::getResourceType)
                .set(resourceUuid).equalToWhenPresent(row::getResourceUuid)
                .set(pfsId).equalToWhenPresent(row::getPfsId)
                .set(status).equalToWhenPresent(row::getStatus)
                .where(id, isEqualTo(row::getId)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByResourceUuid(SaasResourceDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .where(resourceUuid, isEqualTo(row::getResourceUuid)));
    }

}