package com.baidu.bce.logic.chpc.billing.gatewayimpl;

import jakarta.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.billing.gateway.ServiceCatalogGateway;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import com.baidu.bce.plat.servicecatalog.ServiceCatalogOrderNioClient;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;

@Service
public class ServiceCatalogGatewayImpl implements ServiceCatalogGateway {

    @Resource
    IamLogicService iamLogicService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private ServiceCatalogOrderNioClient serviceCatalogOrderNioClient;

    @Override
    public OrderUuidResult createOrder(String accountId, CreateOrderRequest<CreateNewTypeOrderItem> request) {
        return serviceCatalogOrderNioClient.createOrder(accountId, accountId, request).block();
    }

}
