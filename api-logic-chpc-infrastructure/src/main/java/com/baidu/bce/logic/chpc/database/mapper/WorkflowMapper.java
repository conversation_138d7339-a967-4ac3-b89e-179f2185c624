package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.WorkflowDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.workflow;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.version;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.workflowId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.language;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.languageVersion;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.introduction;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.workspaceId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.document;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.description;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.deletedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.inputs;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;

import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface WorkflowMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<WorkflowDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(WorkflowDO row) {
        return MyBatis3Utils.insert(this::insert, row, workflow, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(version).toPropertyWhenPresent("version", row::getVersion)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getUpdatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(name).toPropertyWhenPresent("name", row::getName)
                        .map(description).toPropertyWhenPresent("description", row::getDescription)
                        .map(workflowId).toPropertyWhenPresent("workflowId", row::getWorkflowId)
                        .map(language).toPropertyWhenPresent("language", row::getLanguage)
                        .map(languageVersion).toPropertyWhenPresent("languageVersion", row::getLanguageVersion)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(introduction).toPropertyWhenPresent("introduction", row::getIntroduction)
                        .map(document).toPropertyWhenPresent("document", row::getDocument)
                        .map(inputs).toPropertyWhenPresent("inputs", row::getInputs)
                        .map(workspaceId).toPropertyWhenPresent("workspaceId", row::getWorkspaceId)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            version,
            createdTime,
            updatedTime,
            deletedTime,
            deleted,
            name,
            description,
            workflowId,
            version,
            language,
            languageVersion,
            accountId,
            introduction,
            document,
            inputs,
            workspaceId
    );


    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "WorkflowDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted_time", property = "deletedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.TINYINT),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "workflow_id", property = "workflowId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "language", property = "language", jdbcType = JdbcType.VARCHAR),
            @Result(column = "language_version", property = "languageVersion", jdbcType = JdbcType.VARCHAR),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "introduction", property = "introduction", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "document", property = "document", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "inputs", property = "inputs", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "version", property = "version", jdbcType = JdbcType.BIGINT),
            @Result(column = "workspace_id", property = "workspaceId", jdbcType = JdbcType.VARCHAR)
    })
    List<WorkflowDO> selectMany(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("WorkflowDOResult")
    Optional<WorkflowDO> selectOne(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<WorkflowDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, workflow, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByWorkflowId(WorkflowDO row) {
        return update(c ->
                c.set(version).equalToWhenPresent(row::getVersion)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deletedTime).equalToWhenPresent(row::getDeletedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(description).equalToWhenPresent(row::getDescription)
                        .set(introduction).equalToWhenPresent(row::getIntroduction)
                        .set(document).equalToWhenPresent(row::getDocument)
                        .set(inputs).equalToWhenPresent(row::getInputs)
                        .where(workflowId, isEqualTo(row::getWorkflowId))
                        .and(accountId, isEqualTo(row::getAccountId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, workflow, completer);
    }
}
