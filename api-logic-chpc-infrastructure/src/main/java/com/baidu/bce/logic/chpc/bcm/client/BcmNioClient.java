package com.baidu.bce.logic.chpc.bcm.client;

import java.util.Map;

import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.bcm.BcmBatchQueryRequest;
import com.baidu.bce.logic.chpc.bcm.BcmBatchQueryResponse;
import com.baidu.bce.logic.chpc.bcm.BcmPartialQueryRequest;
import com.baidu.bce.logic.chpc.bcm.BcmPartialQueryResponse;

public class BcmNioClient extends BceNioClient {

    private static final String PARTIONAL_URL = "/csm/api/v2/userId/%s/services/BCE_CHPC/data/metricData/PartialDimension";

    public BcmNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public BcmBatchQueryResponse batchQuery(BcmBatchQueryRequest request, String accountId) {
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path("/csm/api/v2/data/metricAllData/batch")
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(BcmBatchQueryResponse.class).block();
    }

    public BcmPartialQueryResponse partialQuery(BcmPartialQueryRequest request, String accountId) {
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path(String.format(PARTIONAL_URL, request.getUserId()))
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(BcmPartialQueryResponse.class).block();
    }
}
