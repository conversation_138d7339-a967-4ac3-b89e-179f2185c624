package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import jakarta.annotation.Generated;

import java.sql.JDBCType;
import java.time.LocalDateTime;

public final class HelixJobDynamicSqlSupport {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final HelixJob helixJob = new HelixJob();

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = helixJob.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> accountId = helixJob.accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> platformId = helixJob.platformId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> orderId = helixJob.orderId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> taskId = helixJob.taskId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobId = helixJob.jobId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> clusterId = helixJob.clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobProduct = helixJob.jobProduct;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobStatus = helixJob.jobStatus;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> action = helixJob.action;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobRunTime = helixJob.jobRunTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobChargeAmount = helixJob.jobChargeAmount;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobPrice = helixJob.jobPrice;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobCouponPrice = helixJob.jobCouponPrice;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobTokenLength = helixJob.jobTokenLength;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobPackageDeduction = helixJob.jobPackageDeduction;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = helixJob.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = helixJob.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> extra = helixJob.extra;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = helixJob.deleted;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class HelixJob extends AliasableSqlTable<HelixJob> {

        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> accountId = column("account_id", JDBCType.VARCHAR);

        public final SqlColumn<String> platformId = column("platform_id", JDBCType.VARCHAR);

        public final SqlColumn<String> orderId = column("order_id", JDBCType.VARCHAR);

        public final SqlColumn<String> taskId = column("task_id", JDBCType.VARCHAR);

        public final SqlColumn<String> jobId = column("job_id", JDBCType.VARCHAR);

        public final SqlColumn<String> clusterId = column("cluster_id", JDBCType.VARCHAR);

        public final SqlColumn<String> jobProduct = column("job_product", JDBCType.VARCHAR);

        public final SqlColumn<String> jobStatus = column("job_status", JDBCType.VARCHAR);

        public final SqlColumn<String> action = column("action", JDBCType.VARCHAR);

        public final SqlColumn<String> jobRunTime = column("job_run_time", JDBCType.VARCHAR);

        public final SqlColumn<String> jobChargeAmount = column("job_charge_amount", JDBCType.VARCHAR);

        public final SqlColumn<String> jobPrice = column("job_price", JDBCType.VARCHAR);

        public final SqlColumn<String> jobCouponPrice = column("job_coupon_price", JDBCType.VARCHAR);

        public final SqlColumn<String> jobTokenLength = column("job_token_length", JDBCType.VARCHAR);

        public final SqlColumn<String> jobPackageDeduction = column("job_package_deduction", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> extra = column("extra", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);


        public HelixJob() {
            super("t_chpc_helix_job_data", HelixJob::new);
        }
    }
}