package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.TagsDO;
import com.baidu.bce.logic.chpc.database.mapper.TagsMapper;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.tag.DbTag;
import com.baidu.bce.logic.chpc.tag.Tag;
import jakarta.annotation.Resource;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.tags;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Service
public class TagsDAOGatewayImpl implements TagsDAOGateway {

    @Resource
    private TagsMapper tagsMapper;

    @Override
    public Boolean insert(String accountId, String clusterId, String tagType, String name, String tagKey, String tagValue) {
        TagsDO tagsRecordDO = new TagsDO();
        tagsRecordDO.setDeleted(false);
        tagsRecordDO.setAccountId(accountId);
        tagsRecordDO.setClusterId(clusterId);
        tagsRecordDO.setTagType(tagType);
        tagsRecordDO.setName(name);
        tagsRecordDO.setTagKey(tagKey);
        tagsRecordDO.setResourceId(clusterId + "_" + name);
        tagsRecordDO.setTagValue(tagValue);
        tagsRecordDO.setCreatedTime(LocalDateTime.now());
        tagsRecordDO.setUpdatedTime(LocalDateTime.now());
        return tagsMapper.insertSelective(tagsRecordDO) != 0;
    }

    @Override
    public List<Tag> findTags(String accountId, String clusterId, String tagType, String name) {
        SelectStatementProvider selectStatement = select(tagsMapper.selectList)
                .from(tags)
                .where(tags.clusterId, isEqualToWhenPresent(clusterId))
                .and(tags.accountId, isEqualToWhenPresent(accountId))
                .and(tags.tagType, isEqualToWhenPresent(tagType))
                .and(tags.name, isEqualToWhenPresent(name))
                .and(tags.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<TagsDO> tagsDOList = tagsMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(tagsDOList, Tag::new);
    }

    @Override
    public List<Tag> findTagsByKey(String accountId, String tagType, String tagKey) {
        SelectStatementProvider selectStatement = select(tagsMapper.selectList)
                .from(tags)
                .where(tags.accountId, isEqualToWhenPresent(accountId))
                .and(tags.tagType, isEqualToWhenPresent(tagType))
                .and(tags.tagKey, isEqualToWhenPresent(tagKey))
                .and(tags.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<TagsDO> tagsDOList = tagsMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(tagsDOList, Tag::new);
    }


    @Override
    public List<DbTag> findDbTags(String accountId, String clusterId, String tagType) {
        SelectStatementProvider selectStatement = select(tagsMapper.selectList)
                .from(tags)
                .where(tags.clusterId, isEqualToWhenPresent(clusterId))
                .and(tags.accountId, isEqualToWhenPresent(accountId))
                .and(tags.tagType, isEqualToWhenPresent(tagType))
                .and(tags.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<TagsDO> tagsDOList = tagsMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(tagsDOList, DbTag::new);
    }

    @Override
    public Boolean deleteByClusterId(String clusterId, String tagType) {
        TagsDO tagsDO = new TagsDO();
        tagsDO.setUpdatedTime(LocalDateTime.now());
        tagsDO.setClusterId(clusterId);
        tagsDO.setTagType(tagType);
        tagsDO.setDeleted(true);
        return tagsMapper.deleteByClusterId(tagsDO) != 0;
    }

    @Override
    public Boolean deleteByClusterIdAndName(String clusterId, String tagType, String name) {
        TagsDO tagsDO = new TagsDO();
        tagsDO.setUpdatedTime(LocalDateTime.now());
        tagsDO.setClusterId(clusterId);
        tagsDO.setName(name);
        tagsDO.setTagType(tagType);
        tagsDO.setDeleted(true);
        return tagsMapper.deleteByClusterIdAndName(tagsDO) != 0;
    }

    @Override
    public Boolean deleteByClusterIdAndKey(String clusterId, String tagType, String tagKey) {
        TagsDO tagsDO = new TagsDO();
        tagsDO.setUpdatedTime(LocalDateTime.now());
        tagsDO.setClusterId(clusterId);
        tagsDO.setTagKey(tagKey);
        tagsDO.setTagType(tagType);
        tagsDO.setDeleted(true);
        return tagsMapper.deleteByClusterIdAndKey(tagsDO) != 0;
    }
}
