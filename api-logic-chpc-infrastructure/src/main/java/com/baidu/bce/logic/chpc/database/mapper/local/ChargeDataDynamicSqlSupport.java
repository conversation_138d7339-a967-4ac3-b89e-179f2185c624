package com.baidu.bce.logic.chpc.database.mapper.local;

import java.sql.JDBCType;
import java.time.LocalDateTime;

import jakarta.annotation.Generated;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class ChargeDataDynamicSqlSupport {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final ChargeData chargeData = new ChargeData();

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = chargeData.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = chargeData.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = chargeData.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = chargeData.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> orderId = chargeData.orderId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> accountId = chargeData.accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> serviceType = chargeData.serviceType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> instanceId = chargeData.instanceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> chargeItem = chargeData.chargeItem;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> chargeAmount = chargeData.chargeAmount;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> chargeMonth = chargeData.chargeMonth;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> chargeTime = chargeData.chargeTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> chargeTotal = chargeData.chargeTotal;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> valid = chargeData.valid;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class ChargeData extends AliasableSqlTable<ChargeData> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<String> orderId = column("order_id", JDBCType.VARCHAR);

        public final SqlColumn<String> accountId = column("account_id", JDBCType.VARCHAR);

        public final SqlColumn<String> serviceType = column("service_type", JDBCType.VARCHAR);

        public final SqlColumn<String> instanceId = column("instance_id", JDBCType.VARCHAR);

        public final SqlColumn<String> chargeItem = column("charge_item", JDBCType.VARCHAR);

        public final SqlColumn<String> chargeAmount = column("charge_amount", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> chargeMonth = column("charge_month", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> chargeTime = column("charge_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> chargeTotal = column("charge_total", JDBCType.BIT);

        public final SqlColumn<Boolean> valid = column("valid", JDBCType.BIT);

        public ChargeData() {
            super("t_chpc_charge_data", ChargeData::new);
        }
    }
}