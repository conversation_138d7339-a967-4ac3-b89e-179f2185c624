package com.baidu.bce.logic.chpc.database.dataobject;

import java.time.LocalDateTime;

import jakarta.annotation.Generated;

public class SaasResourceDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String filesetId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String orderId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String resourceType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String resourceUuid;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String pfsId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getFilesetId() {
        return filesetId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setFilesetId(String filesetId) {
        this.filesetId = filesetId == null ? null : filesetId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId == null ? null : accountId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getOrderId() {
        return orderId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getResourceType() {
        return resourceType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setResourceType(String resourceType) {
        this.resourceType = resourceType == null ? null : resourceType.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getResourceUuid() {
        return resourceUuid;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setResourceUuid(String resourceUuid) {
        this.resourceUuid = resourceUuid == null ? null : resourceUuid.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getPfsId() {
        return pfsId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setPfsId(String pfsId) {
        this.pfsId = pfsId == null ? null : pfsId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getStatus() {
        return status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

}