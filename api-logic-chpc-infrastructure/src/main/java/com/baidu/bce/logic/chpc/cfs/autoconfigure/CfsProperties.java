package com.baidu.bce.logic.chpc.cfs.autoconfigure;

import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;

import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.logic.core.constants.CommonsConstants;
import com.google.common.collect.Maps;

import lombok.Data;
import lombok.NoArgsConstructor;

@ConfigurationProperties(CommonsConstants.CONFIG_SDK_PREFIX + ".cfs")
@NoArgsConstructor
@Data
public class CfsProperties {
    
    private String endpoint;

    private Map<String, WebclientConfigProperties> configs = Maps.newHashMap();

}
