package com.baidu.bce.logic.chpc.database.mapper;

import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.chargeType;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.cluster;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.clusterType;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.cosStackId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.createdDone;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.description;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.enableHa;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.enableMonitor;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.errorMessage;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.extra;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.forbidDelete;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.heartbeat;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.imageId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.logicalZone;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.maxCpus;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.maxNodes;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.password;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.keypairId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.schedulePlugin;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.schedulePluginVersion;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.schedulerType;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.schedulerVersion;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.securityGroupId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.securityGroupType;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.softwareDir;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.spec;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.subnetId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.vpcId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.deleteTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.domainAccount;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import jakarta.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.baidu.bce.logic.chpc.database.dataobject.ClusterDO;

@Mapper
public interface ClusterMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<ClusterDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleteTime,
            deleted,
            clusterId,
            chargeType,
            name,
            description,
            vpcId,
            subnetId,
            securityGroupId,
            securityGroupType,
            status,
            logicalZone,
            schedulerType,
            schedulerVersion,
            enableHa,
            enableMonitor,
            accountId,
            extra,
            imageId,
            spec,
            cosStackId,
            errorMessage,
            softwareDir,
            heartbeat,
            schedulePlugin,
            schedulePluginVersion,
            clusterType,
            maxNodes,
            maxCpus,
            password,
            forbidDelete,
            domainAccount,
            createdDone);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "ClusterDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "delete_time", property = "deleteTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "charge_type", property = "chargeType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vpc_id", property = "vpcId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "subnet_id", property = "subnetId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "security_group_id", property = "securityGroupId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "security_group_type", property = "securityGroupType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "logical_zone", property = "logicalZone", jdbcType = JdbcType.VARCHAR),
            @Result(column = "scheduler_type", property = "schedulerType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "scheduler_version", property = "schedulerVersion", jdbcType = JdbcType.VARCHAR),
            @Result(column = "enable_ha", property = "enableHa", jdbcType = JdbcType.BIT),
            @Result(column = "enable_monitor", property = "enableMonitor", jdbcType = JdbcType.BIT),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "extra", property = "extra", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "cos_stack_id", property = "cosStackId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "software_dir", property = "softwareDir", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "error_message", property = "errorMessage", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "heartbeat", property = "heartbeat", jdbcType = JdbcType.BIGINT),
            @Result(column = "schedule_plugin", property = "schedulePlugin", jdbcType = JdbcType.BIGINT),
            @Result(column = "schedule_plugin_version", property = "schedulePluginVersion", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cluster_type", property = "clusterType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "max_nodes", property = "maxNodes", jdbcType = JdbcType.INTEGER),
            @Result(column = "max_cpus", property = "maxCpus", jdbcType = JdbcType.INTEGER),
            @Result(column = "password", property = "password", jdbcType = JdbcType.VARCHAR),
            @Result(column = "keypair_id", property = "keypairId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "forbid_delete", property = "forbidDelete", jdbcType = JdbcType.BIT),
            @Result(column = "created_done", property = "createdDone", jdbcType = JdbcType.BIT),
            @Result(column = "domain_account", property = "domainAccount", jdbcType = JdbcType.VARCHAR),
    })
    List<ClusterDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("ClusterDOResult")
    Optional<ClusterDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<ClusterDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, cluster, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, cluster, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, cluster, completer);
    }

    /**
     * 根据主键删除记录。
     *
     * @param key 主键值，类型为 Long
     * @return 返回受影响的行数，类型为 int
     * @since 2021/4/19
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c -> c.where(id, isEqualTo(key)));
    }

    /**
     * {@summary 默认实现方法，用于插入一条记录到集群表中。}
     *
     * 该方法使用MyBatis3的工具类`MyBatis3Utils`来完成插入操作，并返回插入后的ID值。
     * 需要注意的是，该方法只能在MyBatis3框架下使用，且必须包含`@Generated("org.mybatis.generator.api.MyBatisGenerator")`注解。
     *
     * @param row 待插入的集群对象（类型为`ClusterDO`）
     * @return 返回插入后的ID值（int类型）
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(ClusterDO row) {
        return MyBatis3Utils.insert(this::insert, row, cluster, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(clusterId).toProperty("clusterId")
                .map(chargeType).toProperty("chargeType")
                .map(name).toProperty("name")
                .map(description).toProperty("description")
                .map(vpcId).toProperty("vpcId")
                .map(subnetId).toProperty("subnetId")
                .map(securityGroupId).toProperty("securityGroupId")
                .map(securityGroupType).toProperty("securityGroupType")
                .map(status).toProperty("status")
                .map(logicalZone).toProperty("logicalZone")
                .map(schedulerType).toProperty("schedulerType")
                .map(schedulerVersion).toProperty("schedulerVersion")
                .map(enableHa).toProperty("enableHa")
                .map(enableMonitor).toProperty("enableMonitor")
                .map(accountId).toProperty("accountId")
                .map(extra).toProperty("extra")
                .map(imageId).toProperty("imageId")
                .map(spec).toProperty("spec")
                .map(cosStackId).toProperty("cosStackId")
                .map(clusterType).toProperty("clusterType")
                .map(softwareDir).toProperty("softwareDir")
                .map(schedulePlugin).toProperty("schedulePlugin")
                .map(schedulePluginVersion).toProperty("schedulePluginVersion")
                .map(maxNodes).toProperty("maxNodes")
                .map(maxCpus).toProperty("maxCpus")
                .map(forbidDelete).toProperty("forbidDelete")
                .map(forbidDelete).toProperty("forbidDelete")
                .map(domainAccount).toProperty("domainAccount"));
    }

    /**
     * {@summary 批量插入集群信息}
     * 使用MyBatis3的insertMultiple方法，将一组集群信息批量插入数据库。
     * 返回值为int类型，表示成功插入的记录数。
     *
     * @param records Collection<ClusterDO> - 待插入的集群信息列表，包含每个集群的所有属性值
     * @return int - 成功插入的记录数
     * @throws org.apache.ibatis.binding.MapperMethod.MapperMethodException 当发生任何错误时抛出该异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<ClusterDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, cluster, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(clusterId).toProperty("clusterId")
                .map(chargeType).toProperty("chargeType")
                .map(name).toProperty("name")
                .map(description).toProperty("description")
                .map(vpcId).toProperty("vpcId")
                .map(subnetId).toProperty("subnetId")
                .map(securityGroupId).toProperty("securityGroupId")
                .map(securityGroupType).toProperty("securityGroupType")
                .map(status).toProperty("status")
                .map(logicalZone).toProperty("logicalZone")
                .map(schedulerType).toProperty("schedulerType")
                .map(schedulerVersion).toProperty("schedulerVersion")
                .map(enableHa).toProperty("enableHa")
                .map(enableMonitor).toProperty("enableMonitor")
                .map(accountId).toProperty("accountId")
                .map(extra).toProperty("extra")
                .map(imageId).toProperty("imageId")
                .map(spec).toProperty("spec")
                .map(cosStackId).toProperty("cosStackId")
                .map(clusterType).toProperty("clusterType")
                .map(softwareDir).toProperty("softwareDir")
                .map(schedulePlugin).toProperty("schedulePlugin")
                .map(schedulePluginVersion).toProperty("schedulePluginVersion")
                .map(maxNodes).toProperty("maxNodes")
                .map(maxCpus).toProperty("maxCpus")
                .map(forbidDelete).toProperty("forbidDelete")
                .map(domainAccount).toProperty("domainAccount")
                .map(createdDone).toProperty("createdDone"));
    }

    /**
     * 插入或者更新一条记录，当主键存在时更新，否则插入。
     * 如果字段值为null，则忽略对应字段的更新操作。
     *
     * @param row ClusterDO类型的数据行，包含需要插入或更新的数据
     * @return 返回影响的行数，大于等于0表示成功，小于0表示失败
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(ClusterDO row) {
        return MyBatis3Utils.insert(this::insert, row, cluster, c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                .map(chargeType).toPropertyWhenPresent("chargeType", row::getChargeType)
                .map(name).toPropertyWhenPresent("name", row::getName)
                .map(description).toPropertyWhenPresent("description", row::getDescription)
                .map(vpcId).toPropertyWhenPresent("vpcId", row::getVpcId)
                .map(subnetId).toPropertyWhenPresent("subnetId", row::getSubnetId)
                .map(securityGroupId).toPropertyWhenPresent("securityGroupId", row::getSecurityGroupId)
                .map(securityGroupType).toPropertyWhenPresent("securityGroupType", row::getSecurityGroupType)
                .map(status).toPropertyWhenPresent("status", row::getStatus)
                .map(logicalZone).toPropertyWhenPresent("logicalZone", row::getLogicalZone)
                .map(schedulerType).toPropertyWhenPresent("schedulerType", row::getSchedulerType)
                .map(schedulerVersion).toPropertyWhenPresent("schedulerVersion", row::getSchedulerVersion)
                .map(enableHa).toPropertyWhenPresent("enableHa", row::getEnableHa)
                .map(enableMonitor).toPropertyWhenPresent("enableHa", row::getEnableMonitor)
                .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                .map(extra).toPropertyWhenPresent("extra", row::getExtra)
                .map(imageId).toPropertyWhenPresent("imageId", row::getImageId)
                .map(spec).toPropertyWhenPresent("spec", row::getSpec)
                .map(cosStackId).toPropertyWhenPresent("cosStackId", row::getCosStackId)
                .map(clusterType).toProperty("clusterType")
                .map(softwareDir).toPropertyWhenPresent("softwareDir", row::getSoftwareDir)
                .map(schedulePlugin).toPropertyWhenPresent("schedulePlugin", row::getSchedulePlugin)
                .map(schedulePluginVersion)
                .toPropertyWhenPresent("schedulePluginVersion", row::getSchedulePluginVersion)
                .map(maxNodes).toPropertyWhenPresent("maxNodes", row::getMaxNodes)
                .map(maxCpus).toPropertyWhenPresent("maxCpus", row::getMaxCpus)
                .map(password).toPropertyWhenPresent("password", row::getPassword)
                .map(keypairId).toPropertyWhenPresent("keypairId", row::getKeypairId)
                .map(forbidDelete).toPropertyWhenPresent("forbidDelete", row::getForbidDelete)
                .map(domainAccount).toPropertyWhenPresent("domainAccount", row::getDomainAccount)
                .map(createdDone).toPropertyWhenPresent("createdDone", row::getCreatedDone));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<ClusterDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, cluster, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<ClusterDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, cluster, completer);
    }

    /**
     * 根据主键查询集群信息，返回一个Optional类型的结果。如果找到则返回包含结果的Optional，否则返回空Optional。
     *
     * @param key 主键，类型为Long，不能为null
     * @return 返回一个Optional类型的结果，如果找到则返回包含结果的Optional，否则返回空Optional
     * @throws IllegalArgumentException 当参数key为null时抛出此异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<ClusterDO> selectByPrimaryKey(Long key) {
        return selectOne(c -> c.where(id, isEqualTo(key)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, cluster, completer);
    }

    /**
     * {@summary 更新所有列的值。}
     *
     * 将给定的`ClusterDO`对象中的所有列的值设置为相应的属性，并返回一个包含这些更新操作的`UpdateDSL`对象。
     *
     * @param row 一个`ClusterDO`类型的对象，其中包含要更新的列的值。
     * @param dsl 一个`UpdateDSL`类型的对象，用于构建更新操作。
     * @return 一个包含所有列的更新操作的`UpdateDSL`对象。
     * @since 2021.12.30
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(ClusterDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleteTime).equalTo(row::getDeleteTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(clusterId).equalTo(row::getClusterId)
                .set(chargeType).equalTo(row::getChargeType)
                .set(name).equalTo(row::getName)
                .set(description).equalTo(row::getDescription)
                .set(vpcId).equalTo(row::getVpcId)
                .set(subnetId).equalTo(row::getSubnetId)
                .set(status).equalTo(row::getStatus)
                .set(logicalZone).equalTo(row::getLogicalZone)
                .set(schedulerType).equalTo(row::getSchedulerType)
                .set(schedulerVersion).equalTo(row::getSchedulerVersion)
                .set(enableHa).equalTo(row::getEnableHa)
                .set(accountId).equalTo(row::getAccountId)
                .set(extra).equalTo(row::getExtra)
                .set(imageId).equalTo(row::getImageId)
                .set(spec).equalTo(row::getSpec)
                .set(cosStackId).equalTo(row::getCosStackId)
                .set(clusterType).equalTo(row::getClusterType)
                .set(softwareDir).equalTo(row::getSoftwareDir)
                .set(schedulePlugin).equalTo(row::getSchedulePlugin)
                .set(schedulePluginVersion).equalTo(row::getSchedulePluginVersion)
                .set(maxNodes).equalTo(row::getMaxNodes)
                .set(maxCpus).equalTo(row::getMaxCpus)
                .set(forbidDelete).equalTo(row::getForbidDelete)
                .set(domainAccount).equalTo(row::getDomainAccount)
                .set(createdDone).equalTo(row::getCreatedDone);
    }

    /**
     * 更新选择性列，返回一个UpdateDSL对象。
     * 当给定的行对象中的属性不为null时，才会设置相应的值。
     *
     * @param row ClusterDO类型的行对象，包含需要更新的数据
     * @param dsl UpdateDSL类型的对象，用于构建更新语句
     * @return UpdateDSL<UpdateModel>类型的对象，包含已经设置了选择性列的更新语句
     * @throws Generated 由MyBatis-Generator生成的注解，表示此方法是由MyBatis-Generator自动生成的
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ClusterDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleteTime).equalToWhenPresent(row::getDeleteTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(clusterId).equalToWhenPresent(row::getClusterId)
                .set(chargeType).equalToWhenPresent(row::getChargeType)
                .set(name).equalToWhenPresent(row::getName)
                .set(description).equalToWhenPresent(row::getDescription)
                .set(vpcId).equalToWhenPresent(row::getVpcId)
                .set(subnetId).equalToWhenPresent(row::getSubnetId)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(logicalZone).equalToWhenPresent(row::getLogicalZone)
                .set(schedulerType).equalToWhenPresent(row::getSchedulerType)
                .set(schedulerVersion).equalToWhenPresent(row::getSchedulerVersion)
                .set(enableHa).equalToWhenPresent(row::getEnableHa)
                .set(accountId).equalToWhenPresent(row::getAccountId)
                .set(extra).equalToWhenPresent(row::getExtra)
                .set(imageId).equalToWhenPresent(row::getImageId)
                .set(spec).equalToWhenPresent(row::getSpec)
                .set(cosStackId).equalToWhenPresent(row::getCosStackId)
                .set(clusterType).equalTo(row::getClusterType)
                .set(softwareDir).equalTo(row::getSoftwareDir)
                .set(schedulePlugin).equalTo(row::getSchedulePlugin)
                .set(schedulePluginVersion).equalTo(row::getSchedulePluginVersion)
                .set(maxNodes).equalToWhenPresent(row::getMaxNodes)
                .set(maxCpus).equalToWhenPresent(row::getMaxCpus)
                .set(forbidDelete).equalToWhenPresent(row::getForbidDelete)
                .set(domainAccount).equalToWhenPresent(row::getDomainAccount)
                .set(createdDone).equalToWhenPresent(row::getCreatedDone);
    }

    /**
     * 根据主键更新记录，返回受影响的行数。
     *
     * @param row 包含要更新的字段值的 ClusterDO 对象
     * @return 受影响的行数，如果更新失败则返回0
     * @since 2021/3/29
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(ClusterDO row) {
        return update(c -> c.set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleteTime).equalTo(row::getDeleteTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(clusterId).equalTo(row::getClusterId)
                .set(chargeType).equalTo(row::getChargeType)
                .set(name).equalTo(row::getName)
                .set(description).equalTo(row::getDescription)
                .set(vpcId).equalTo(row::getVpcId)
                .set(subnetId).equalTo(row::getSubnetId)
                .set(status).equalTo(row::getStatus)
                .set(logicalZone).equalTo(row::getLogicalZone)
                .set(schedulerType).equalTo(row::getSchedulerType)
                .set(schedulerVersion).equalTo(row::getSchedulerVersion)
                .set(enableHa).equalTo(row::getEnableHa)
                .set(accountId).equalTo(row::getAccountId)
                .set(extra).equalTo(row::getExtra)
                .set(imageId).equalTo(row::getImageId)
                .set(spec).equalTo(row::getSpec)
                .set(cosStackId).equalTo(row::getCosStackId)
                .set(clusterType).equalTo(row::getClusterType)
                .set(softwareDir).equalTo(row::getSoftwareDir)
                .set(schedulePlugin).equalTo(row::getSchedulePlugin)
                .set(schedulePluginVersion).equalTo(row::getSchedulePluginVersion)
                .set(maxNodes).equalTo(row::getMaxNodes)
                .set(maxCpus).equalTo(row::getMaxCpus)
                .set(forbidDelete).equalTo(row::getForbidDelete)
                .set(createdDone).equalTo(row::getCreatedDone)
                .set(domainAccount).equalTo(row::getDomainAccount)
                .where(id, isEqualTo(row::getId)));
    }

    /**
     * 更新记录，根据集群ID进行更新。
     *
     * @param row 包含要更新的字段值的ClusterDO对象
     * @return 返回影响的行数，如果没有符合条件的记录则返回0
     * @generated 由MyBatis Generator生成此代码，请勿手动修改
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByClusterId(ClusterDO row) {
        return update(c -> c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleteTime).equalToWhenPresent(row::getDeleteTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(name).equalToWhenPresent(row::getName)
                .set(securityGroupId).equalToWhenPresent(row::getSecurityGroupId)
                .set(securityGroupType).equalToWhenPresent(row::getSecurityGroupType)
                .set(description).equalToWhenPresent(row::getDescription)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(extra).equalToWhenPresent(row::getExtra)
                .set(forbidDelete).equalToWhenPresent(row::getForbidDelete)
                .set(createdDone).equalToWhenPresent(row::getCreatedDone)
                .set(domainAccount).equalToWhenPresent(row::getDomainAccount)
                .set(maxCpus).equalToWhenPresent(row::getMaxCpus)
                .set(maxNodes).equalToWhenPresent(row::getMaxNodes)
                .set(description).equalToWhenPresent(row::getDescription)
                .set(password).equalToWhenPresent(row::getPassword)
                .where(clusterId, isEqualTo(row::getClusterId)));
    }

    /**
     * 更新集群错误信息，根据集群ID进行更新。
     *
     * @param row 包含集群信息的对象，其中包括集群ID、更新时间、状态和错误信息等字段。
     * @return 返回一个int类型值，表示更新记录的数量。如果没有符合条件的记录，则返回0。
     * @see ClusterDO#getClusterId() 获取集群ID
     * @see ClusterDO#getUpdatedTime() 获取更新时间
     * @see ClusterDO#getStatus() 获取状态
     * @see ClusterDO#getErrorMessage() 获取错误信息
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateErrMsgByClusterId(ClusterDO row) {
        return update(c -> c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleteTime).equalToWhenPresent(row::getDeleteTime)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(errorMessage).equalToWhenPresent(row::getErrorMessage)
                .where(clusterId, isEqualTo(row::getClusterId)));
    }

    /**
     * 更新集群的软件目录。
     * 当集群对象中有更新时间和软件目录时，将这些信息更新到数据库中。
     *
     * @param row 包含集群信息的对象，其中可能包括更新时间和软件目录
     * @return 返回影响的行数，如果没有符合条件的行则返回0
     * @see ClusterDO#getUpdatedTime() 获取更新时间
     * @see ClusterDO#getSoftwareDir() 获取软件目录
     * @see ClusterDO#getClusterId() 获取集群ID
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updatesoftwareDirByClusterId(ClusterDO row) {
        return update(c -> c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(softwareDir).equalToWhenPresent(row::getSoftwareDir)
                .where(clusterId, isEqualTo(row::getClusterId)));
    }

    /**
     * 更新指定集群ID的心跳时间和心跳信息。
     *
     * @param row 包含要更新的集群信息的对象，包括心跳时间和心跳信息
     * @return 返回更新记录数，如果没有符合条件的记录则返回0
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateHeartbeatByClusterId(ClusterDO row) {
        return update(c -> c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(heartbeat).equalToWhenPresent(row::getHeartbeat)
                .where(clusterId, isEqualTo(row::getClusterId)));
    }
}