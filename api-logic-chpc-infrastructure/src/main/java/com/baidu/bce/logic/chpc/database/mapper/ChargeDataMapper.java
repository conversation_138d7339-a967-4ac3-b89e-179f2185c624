package com.baidu.bce.logic.chpc.database.mapper;

import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.chargeAmount;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.chargeData;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.chargeItem;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.chargeMonth;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.chargeTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.chargeTotal;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.instanceId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.orderId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.serviceType;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.ChargeDataDynamicSqlSupport.valid;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import jakarta.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.baidu.bce.logic.chpc.database.dataobject.ChargeDataDO;

@Mapper
public interface ChargeDataMapper extends CommonCountMapper, CommonDeleteMapper,
        CommonInsertMapper<ChargeDataDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            orderId,
            accountId,
            serviceType,
            instanceId,
            chargeItem,
            chargeAmount,
            chargeMonth,
            chargeTime,
            chargeTotal,
            valid);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "ChargeDataDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "cluster_id", property = "orderId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "service_type", property = "serviceType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "instance_id", property = "instanceId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "charge_item", property = "chargeItem", jdbcType = JdbcType.VARCHAR),
            @Result(column = "charge_amount", property = "chargeAmount", jdbcType = JdbcType.VARCHAR),
            @Result(column = "charge_month", property = "chargeMonth", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "charge_time", property = "chargeTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "charge_total", property = "chargeTotal", jdbcType = JdbcType.VARCHAR),
            @Result(column = "valid", property = "valid", jdbcType = JdbcType.VARCHAR)

    })
    List<ChargeDataDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, chargeData, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, chargeData, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c -> c.where(id, isEqualTo(key)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(ChargeDataDO row) {
        return MyBatis3Utils.insert(this::insert, row, chargeData, c -> c.map(id).toProperty("id")
                .map(createdTime).toProperty("createdTime")
                .map(updatedTime).toProperty("updatedTime")
                .map(deleted).toProperty("deleted")
                .map(orderId).toProperty("orderId")
                .map(accountId).toProperty("accountId")
                .map(serviceType).toProperty("serviceType")
                .map(instanceId).toProperty("accountId")
                .map(chargeItem).toProperty("chargeItem")
                .map(chargeAmount).toProperty("chargeAmount")
                .map(chargeMonth).toProperty("chargeMonth")
                .map(chargeTime).toProperty("chargeTime")
                .map(chargeTotal).toProperty("chargeTotal")
                .map(valid).toProperty("valid"));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<ChargeDataDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, chargeData,
                c -> c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(orderId).toProperty("orderId")
                        .map(accountId).toProperty("accountId")
                        .map(serviceType).toProperty("serviceType")
                        .map(instanceId).toProperty("instanceId")
                        .map(chargeItem).toProperty("chargeItem")
                        .map(chargeAmount).toProperty("chargeAmount")
                        .map(chargeMonth).toProperty("chargeMonth")
                        .map(chargeTime).toProperty("chargeTime")
                        .map(chargeTotal).toProperty("chargeTotal")
                        .map(valid).toProperty("valid"));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(ChargeDataDO row) {
        return MyBatis3Utils.insert(this::insert, row, chargeData,
                c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(orderId).toPropertyWhenPresent("orderId", row::getOrderId)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(serviceType).toPropertyWhenPresent("serviceType", row::getAccountId)
                        .map(instanceId).toPropertyWhenPresent("instanceId", row::getAccountId)
                        .map(chargeItem).toPropertyWhenPresent("chargeItem", row::getChargeItem)
                        .map(chargeAmount).toPropertyWhenPresent("chargeAmount", row::getChargeAmount)
                        .map(chargeMonth).toPropertyWhenPresent("chargeMonth", row::getChargeMonth)
                        .map(chargeTime).toPropertyWhenPresent("chargeTime", row::getChargeTime)
                        .map(chargeTotal).toPropertyWhenPresent("chargeTotal", row::getChargeTotal)
                        .map(valid).toPropertyWhenPresent("valid", row::getValid));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<ChargeDataDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, chargeData, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("ChargeDataDOResult")
    Optional<ChargeDataDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<ChargeDataDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, chargeData, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<ChargeDataDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, chargeData, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<ChargeDataDO> selectByPrimaryKey(Long key) {
        return selectOne(c -> c.where(id, isEqualTo(key)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, chargeData, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(ChargeDataDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(orderId).equalTo(row::getOrderId)
                .set(accountId).equalTo(row::getAccountId)
                .set(serviceType).equalTo(row::getServiceType)
                .set(instanceId).equalTo(row::getInstanceId)
                .set(chargeItem).equalTo(row::getChargeItem)
                .set(chargeAmount).equalTo(row::getChargeAmount)
                .set(chargeMonth).equalTo(row::getChargeMonth)
                .set(chargeTime).equalTo(row::getChargeTime)
                .set(chargeTotal).equalTo(row::getChargeTotal)
                .set(valid).equalTo(row::getValid);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(ChargeDataDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(orderId).equalToWhenPresent(row::getOrderId)
                .set(accountId).equalToWhenPresent(row::getAccountId)
                .set(serviceType).equalToWhenPresent(row::getServiceType)
                .set(instanceId).equalToWhenPresent(row::getInstanceId)
                .set(chargeItem).equalToWhenPresent(row::getChargeItem)
                .set(chargeAmount).equalToWhenPresent(row::getChargeAmount)
                .set(chargeMonth).equalToWhenPresent(row::getChargeMonth)
                .set(chargeTime).equalToWhenPresent(row::getChargeTime)
                .set(chargeTotal).equalToWhenPresent(row::getChargeTotal)
                .set(valid).equalToWhenPresent(row::getValid);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(ChargeDataDO row) {
        return update(c -> c.set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(orderId).equalTo(row::getOrderId)
                .set(accountId).equalTo(row::getAccountId)
                .set(serviceType).equalTo(row::getServiceType)
                .set(instanceId).equalTo(row::getInstanceId)
                .set(chargeItem).equalTo(row::getChargeItem)
                .set(chargeAmount).equalTo(row::getChargeAmount)
                .set(chargeMonth).equalTo(row::getChargeMonth)
                .set(chargeTime).equalTo(row::getChargeTime)
                .set(chargeTotal).equalTo(row::getChargeTotal)
                .set(valid).equalTo(row::getValid)
                .where(id, isEqualTo(row::getId)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKeySelective(ChargeDataDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(orderId).equalToWhenPresent(row::getOrderId)
                .set(accountId).equalToWhenPresent(row::getAccountId)
                .set(serviceType).equalToWhenPresent(row::getServiceType)
                .set(instanceId).equalToWhenPresent(row::getInstanceId)
                .set(chargeItem).equalToWhenPresent(row::getChargeItem)
                .set(chargeAmount).equalToWhenPresent(row::getChargeAmount)
                .set(chargeMonth).equalToWhenPresent(row::getChargeMonth)
                .set(chargeTime).equalToWhenPresent(row::getChargeTime)
                .set(chargeTotal).equalToWhenPresent(row::getChargeTotal)
                .set(valid).equalToWhenPresent(row::getValid)
                .where(id, isEqualTo(row::getId)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByChargeId(ChargeDataDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(valid).equalTo(row::getValid)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .where(instanceId, isEqualTo(row::getInstanceId))
                .and(chargeItem, isEqualTo(row::getChargeItem))
                .and(chargeTime, isEqualTo(row::getChargeTime)));
    }

}