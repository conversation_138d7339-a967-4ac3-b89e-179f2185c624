package com.baidu.bce.logic.chpc.chpc.client;

import java.util.Map;

import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.chpc.model.PushAutoScalingBctEventRequest;
import com.baidu.bce.logic.chpc.chpc.model.PushAutoScalingBctEventResponse;


public class ChpcNioClient extends BceNioClient {

    public ChpcNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public PushAutoScalingBctEventResponse pushAutoScalingBctEvent(PushAutoScalingBctEventRequest request, String accountId, String action) {
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path("/v1/autoscaler/bctevent")
                .queryParam("action", action)
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(PushAutoScalingBctEventResponse.class).block();
    }
}
