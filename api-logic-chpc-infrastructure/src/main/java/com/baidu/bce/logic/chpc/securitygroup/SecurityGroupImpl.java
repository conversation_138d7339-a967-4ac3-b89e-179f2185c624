package com.baidu.bce.logic.chpc.securitygroup;

import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.logic.chpc.securitygroup.gateway.SecurityGroupGateway;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Value;

import com.baidubce.services.bcc.model.securitygroup.CreateSecurityGroupRequest;
import com.baidubce.services.bcc.model.securitygroup.CreateSecurityGroupResponse;
import com.baidubce.services.bcc.model.securitygroup.ListSecurityGroupsRequest;
import com.baidubce.services.bcc.model.securitygroup.ListSecurityGroupsResponse;
import com.baidubce.services.bcc.model.securitygroup.SecurityGroupRuleOperateRequest;
import com.baidubce.services.esg.EsgClient;
import com.baidubce.services.esg.EsgClientConfiguration;
import com.baidubce.services.esg.model.CreateEsgRequest;
import com.baidubce.services.esg.model.CreateEsgResponse;
import com.baidubce.services.esg.model.EsgRuleOperateRequest;
import com.baidubce.services.esg.model.ListEsgRequest;
import com.baidubce.services.esg.model.ListEsgResponse;
import com.baidubce.auth.DefaultBceSessionCredentials;
import com.baidubce.services.bcc.BccClient;
import com.baidubce.services.bcc.BccClientConfiguration;

import jakarta.annotation.Resource;

@Slf4j
@Service
public class SecurityGroupImpl implements SecurityGroupGateway {

    @Resource
    IamLogicService iamLogicService;

    @Value("${bce.web.commons.sdk.security-group.endpoint}")
    private String endpoint;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Override
    public ListSecurityGroupsResponse listSecurityGroups(ListSecurityGroupsRequest request) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
            regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        log.debug("stsCredential:{}, accountId:{}", stsCredential, LogicUserService.getAccountId());
        BccClientConfiguration config = new BccClientConfiguration();
        config.setCredentials(new DefaultBceSessionCredentials(stsCredential.getAccessKeyId(), 
            stsCredential.getSecretAccessKey(), stsCredential.getSessionToken()));
        config.setEndpoint(endpoint);
        BccClient bccClient = new BccClient(config);
        ListSecurityGroupsResponse response = bccClient.listSecurityGroups(request);
        log.debug("list security group succ, resp:{}", response.toString());
        return response;
    }

    @Override
    public String createSecurityGroup(CreateSecurityGroupRequest request) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
            regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        BccClientConfiguration config = new BccClientConfiguration();
        config.setCredentials(new DefaultBceSessionCredentials(stsCredential.getAccessKeyId(), 
            stsCredential.getSecretAccessKey(), stsCredential.getSessionToken()));
        config.setEndpoint(endpoint);
        BccClient bccClient = new BccClient(config);
        CreateSecurityGroupResponse response = bccClient.createSecurityGroup(request);
        log.debug("create security group succ, resp:{}", response.toString());
        return response.getSecurityGroupId();
    }

    @Override
    public void addSecurityGroupRule(SecurityGroupRuleOperateRequest request){
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
            regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        BccClientConfiguration config = new BccClientConfiguration();
        config.setCredentials(new DefaultBceSessionCredentials(stsCredential.getAccessKeyId(), 
            stsCredential.getSecretAccessKey(), stsCredential.getSessionToken()));
        config.setEndpoint(endpoint);
        BccClient bccClient = new BccClient(config);
        bccClient.authorizeSecurityGroupRule(request);
    }

    @Override
    public String createEnterpriseSecurityGroup(CreateEsgRequest request) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
            regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        EsgClientConfiguration config = new EsgClientConfiguration();
        config.setCredentials(new DefaultBceSessionCredentials(stsCredential.getAccessKeyId(), 
            stsCredential.getSecretAccessKey(), stsCredential.getSessionToken()));
        config.setEndpoint(endpoint);
        EsgClient esgClient = new EsgClient(config);
        CreateEsgResponse response = esgClient.createEsg(request);
        log.debug("create enterprise security group succ, resp:{}", response.toString());
        return response.getEnterpriseSecurityGroupId();
    }

    @Override
    public ListEsgResponse listEnterpriseSecurityGroups(ListEsgRequest request) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
            regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        EsgClientConfiguration config = new EsgClientConfiguration();
        config.setCredentials(new DefaultBceSessionCredentials(stsCredential.getAccessKeyId(), 
            stsCredential.getSecretAccessKey(), stsCredential.getSessionToken()));
        config.setEndpoint(endpoint);
        EsgClient esgClient = new EsgClient(config);
        ListEsgResponse response = esgClient.listEsg(request);
        log.debug("list enterprise security group succ, resp:{}", response.toString());
        return response;
    }

    @Override
    public void addEnterpriseSecurityGroupRule(EsgRuleOperateRequest request) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
            regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        EsgClientConfiguration config = new EsgClientConfiguration();
        config.setCredentials(new DefaultBceSessionCredentials(stsCredential.getAccessKeyId(), 
            stsCredential.getSecretAccessKey(), stsCredential.getSessionToken()));
        config.setEndpoint(endpoint);
        EsgClient esgClient = new EsgClient(config);
        esgClient.authorizeEsgRule(request);
    }
}
