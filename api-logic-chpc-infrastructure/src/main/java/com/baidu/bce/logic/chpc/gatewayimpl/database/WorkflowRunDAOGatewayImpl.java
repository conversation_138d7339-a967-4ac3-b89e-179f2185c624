package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.WorkflowRunDO;
import com.baidu.bce.logic.chpc.database.mapper.WorkflowRunMapper;
import com.baidu.bce.logic.chpc.gateway.WorkflowRunDAOGateway;
import com.baidu.bce.logic.chpc.model.WorkflowRun;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.workflowRun;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;

@Service
public class WorkflowRunDAOGatewayImpl implements WorkflowRunDAOGateway {

    @Resource
    WorkflowRunMapper workflowRunMapper;

    @Override
    public Boolean createWorkflowRun(WorkflowRun workflowRun) {
        WorkflowRunDO workflowRunDO = new WorkflowRunDO();
        BeanUtils.copyProperties(workflowRun, workflowRunDO);
        workflowRunDO.setDeleted(false);
        workflowRunDO.setCreatedTime(LocalDateTime.now());
        workflowRunDO.setUpdatedTime(LocalDateTime.now());
        return workflowRunMapper.insert(workflowRunDO) != 0;
    }

    @Override
    public WorkflowRun getWorkflowRun(String workflowRunId, String accountId) {
        SelectStatementProvider selectStatement =
                select(workflowRunMapper.selectList)
                        .from(workflowRun)
                        .where(workflowRun.runId, isEqualTo(workflowRunId))
                        .and(workflowRun.deleted, isEqualTo(false))
                        .and(workflowRun.accountId, isEqualTo(accountId))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
        WorkflowRunDO workflowRunDO = workflowRunMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(workflowRunDO, WorkflowRun::new);
    }

    @Override
    public List<WorkflowRun> getWorkflowRunByName(String workflowId, String workflowRunName, String accountId) {
        SelectStatementProvider selectStatement =
                select(workflowRunMapper.selectList)
                        .from(workflowRun)
                        .where(workflowRun.name, isEqualTo(workflowRunName))
                        .and(workflowRun.deleted, isEqualTo(false))
                        .and(workflowRun.accountId, isEqualTo(accountId))
                        .and(workflowRun.workflowId, isEqualTo(workflowId))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
        List<WorkflowRunDO> workflowRunDOList = workflowRunMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(workflowRunDOList, WorkflowRun::new);
    }


    @Override
    public List<WorkflowRun> getWorkflowRunById(String workspaceId, String workflowId, String name, String status, String accountId) {
        SelectStatementProvider selectStatement =
                select(workflowRunMapper.selectList)
                        .from(workflowRun)
                        .where(workflowRun.workspaceId, isEqualToWhenPresent(workspaceId))
                        .and(workflowRun.workflowId, isEqualToWhenPresent(workflowId))
                        .and(workflowRun.status, isEqualToWhenPresent(status))
                        .and(workflowRun.deleted, isEqualTo(false))
                        .and(workflowRun.accountId, isEqualTo(accountId))
                        .and(workflowRun.name, isLikeWhenPresent("%" + name + "%"))
                        .orderBy(workflowRun.updatedTime.descending())
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
        List<WorkflowRunDO> workflowRunDOList = workflowRunMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(workflowRunDOList, WorkflowRun::new);
    }

    @Override
    public Boolean deleteWorkflowRun(String runId, String accountId) {
        WorkflowRunDO workflowRunDO = new WorkflowRunDO();
        workflowRunDO.setDeleted(true);
        workflowRunDO.setRunId(runId);
        workflowRunDO.setAccountId(accountId);
        workflowRunDO.setUpdatedTime(LocalDateTime.now());
        workflowRunDO.setDeletedTime(LocalDateTime.now());
        return workflowRunMapper.updateByWorkflowRunId(workflowRunDO) != 0;
    }

    @Override
    public Boolean deleteWorkflowRunByWorkspaceId(String workspaceId, String accountId) {
        WorkflowRunDO workflowRunDO = new WorkflowRunDO();
        workflowRunDO.setDeleted(true);
        workflowRunDO.setWorkspaceId(workspaceId);
        workflowRunDO.setAccountId(accountId);
        workflowRunDO.setUpdatedTime(LocalDateTime.now());
        workflowRunDO.setDeletedTime(LocalDateTime.now());
        return workflowRunMapper.updateByWorkspaceId(workflowRunDO) != 0;
    }

    @Override
    public Boolean updateWorkflowRunStatus(String workflowRunId, String status, String outputs) {
        WorkflowRunDO workflowRunDO = new WorkflowRunDO();
        workflowRunDO.setRunId(workflowRunId);
        workflowRunDO.setStatus(status);
        workflowRunDO.setAccountId(null);
        if (!"".equals(outputs)) {
            workflowRunDO.setOutputs(outputs);
        }
        workflowRunDO.setUpdatedTime(LocalDateTime.now());
        return workflowRunMapper.updateByWorkflowRunId(workflowRunDO) != 0;
    }
}
