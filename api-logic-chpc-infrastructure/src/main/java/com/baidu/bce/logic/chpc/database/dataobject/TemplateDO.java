package com.baidu.bce.logic.chpc.database.dataobject;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;

public class TemplateDO {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470049+08:00", comments="Source field: t_chpc_job_template.id")
    private Long id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470679+08:00", comments="Source field: t_chpc_job_template.name")
    private String name;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470774+08:00", comments="Source field: t_chpc_job_template.cluster_id")
    private String clusterId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470882+08:00", comments="Source field: t_chpc_job_template.job_name")
    private String jobName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470974+08:00", comments="Source field: t_chpc_job_template.job_cmd")
    private String jobCmd;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.47105+08:00", comments="Source field: t_chpc_job_template.queue_name")
    private String queueName;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471135+08:00", comments="Source field: t_chpc_job_template.post_cmd")
    private String postCmd;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471215+08:00", comments="Source field: t_chpc_job_template.deleted")
    private Boolean deleted;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471292+08:00", comments="Source field: t_chpc_job_template.is_unique")
    private Boolean isUnique;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471364+08:00", comments="Source field: t_chpc_job_template.nhosts")
    private Integer nhosts;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471437+08:00", comments="Source field: t_chpc_job_template.ncpus")
    private Integer ncpus;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471519+08:00", comments="Source field: t_chpc_job_template.walltime")
    private Integer walltime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471589+08:00", comments="Source field: t_chpc_job_template.stdout_path")
    private String stdoutPath;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471661+08:00", comments="Source field: t_chpc_job_template.stderr_path")
    private String stderrPath;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.47173+08:00", comments="Source field: t_chpc_job_template.bos_file_path")
    private String bosFilePath;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471804+08:00", comments="Source field: t_chpc_job_template.decompress_cmd")
    private String decompressCmd;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471916+08:00", comments="Source field: t_chpc_job_template.create_time")
    private LocalDateTime createTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471993+08:00", comments="Source field: t_chpc_job_template.update_time")
    private LocalDateTime updateTime;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.472069+08:00", comments="Source field: t_chpc_job_template.env_vars")
    private String envVars;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470518+08:00", comments="Source field: t_chpc_job_template.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470646+08:00", comments="Source field: t_chpc_job_template.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470705+08:00", comments="Source field: t_chpc_job_template.name")
    public String getName() {
        return name;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470742+08:00", comments="Source field: t_chpc_job_template.name")
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.4708+08:00", comments="Source field: t_chpc_job_template.cluster_id")
    public String getClusterId() {
        return clusterId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470827+08:00", comments="Source field: t_chpc_job_template.cluster_id")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId == null ? null : clusterId.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470914+08:00", comments="Source field: t_chpc_job_template.job_name")
    public String getJobName() {
        return jobName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470947+08:00", comments="Source field: t_chpc_job_template.job_name")
    public void setJobName(String jobName) {
        this.jobName = jobName == null ? null : jobName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.470999+08:00", comments="Source field: t_chpc_job_template.job_cmd")
    public String getJobCmd() {
        return jobCmd;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471024+08:00", comments="Source field: t_chpc_job_template.job_cmd")
    public void setJobCmd(String jobCmd) {
        this.jobCmd = jobCmd == null ? null : jobCmd.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471083+08:00", comments="Source field: t_chpc_job_template.queue_name")
    public String getQueueName() {
        return queueName;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471112+08:00", comments="Source field: t_chpc_job_template.queue_name")
    public void setQueueName(String queueName) {
        this.queueName = queueName == null ? null : queueName.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471165+08:00", comments="Source field: t_chpc_job_template.post_cmd")
    public String getPostCmd() {
        return postCmd;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.47119+08:00", comments="Source field: t_chpc_job_template.post_cmd")
    public void setPostCmd(String postCmd) {
        this.postCmd = postCmd == null ? null : postCmd.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471241+08:00", comments="Source field: t_chpc_job_template.deleted")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471269+08:00", comments="Source field: t_chpc_job_template.deleted")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471317+08:00", comments="Source field: t_chpc_job_template.is_unique")
    public Boolean getIsUnique() {
        return isUnique;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.47134+08:00", comments="Source field: t_chpc_job_template.is_unique")
    public void setIsUnique(Boolean isUnique) {
        this.isUnique = isUnique;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471388+08:00", comments="Source field: t_chpc_job_template.nhosts")
    public Integer getNhosts() {
        return nhosts;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471413+08:00", comments="Source field: t_chpc_job_template.nhosts")
    public void setNhosts(Integer nhosts) {
        this.nhosts = nhosts;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471462+08:00", comments="Source field: t_chpc_job_template.ncpus")
    public Integer getNcpus() {
        return ncpus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471495+08:00", comments="Source field: t_chpc_job_template.ncpus")
    public void setNcpus(Integer ncpus) {
        this.ncpus = ncpus;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471541+08:00", comments="Source field: t_chpc_job_template.walltime")
    public Integer getWalltime() {
        return walltime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471567+08:00", comments="Source field: t_chpc_job_template.walltime")
    public void setWalltime(Integer walltime) {
        this.walltime = walltime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471614+08:00", comments="Source field: t_chpc_job_template.stdout_path")
    public String getStdoutPath() {
        return stdoutPath;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471638+08:00", comments="Source field: t_chpc_job_template.stdout_path")
    public void setStdoutPath(String stdoutPath) {
        this.stdoutPath = stdoutPath == null ? null : stdoutPath.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471683+08:00", comments="Source field: t_chpc_job_template.stderr_path")
    public String getStderrPath() {
        return stderrPath;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471709+08:00", comments="Source field: t_chpc_job_template.stderr_path")
    public void setStderrPath(String stderrPath) {
        this.stderrPath = stderrPath == null ? null : stderrPath.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471756+08:00", comments="Source field: t_chpc_job_template.bos_file_path")
    public String getBosFilePath() {
        return bosFilePath;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471779+08:00", comments="Source field: t_chpc_job_template.bos_file_path")
    public void setBosFilePath(String bosFilePath) {
        this.bosFilePath = bosFilePath == null ? null : bosFilePath.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471858+08:00", comments="Source field: t_chpc_job_template.decompress_cmd")
    public String getDecompressCmd() {
        return decompressCmd;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471883+08:00", comments="Source field: t_chpc_job_template.decompress_cmd")
    public void setDecompressCmd(String decompressCmd) {
        this.decompressCmd = decompressCmd == null ? null : decompressCmd.trim();
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471949+08:00", comments="Source field: t_chpc_job_template.create_time")
    public LocalDateTime getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.471972+08:00", comments="Source field: t_chpc_job_template.create_time")
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.472015+08:00", comments="Source field: t_chpc_job_template.update_time")
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.472048+08:00", comments="Source field: t_chpc_job_template.update_time")
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.472093+08:00", comments="Source field: t_chpc_job_template.env_vars")
    public String getEnvVars() {
        return envVars;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", date="2024-11-28T15:16:24.472114+08:00", comments="Source field: t_chpc_job_template.env_vars")
    public void setEnvVars(String envVars) {
        this.envVars = envVars == null ? null : envVars.trim();
    }
}