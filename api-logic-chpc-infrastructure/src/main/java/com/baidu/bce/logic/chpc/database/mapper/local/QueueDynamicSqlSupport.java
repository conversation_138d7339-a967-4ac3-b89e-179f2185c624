package com.baidu.bce.logic.chpc.database.mapper.local;

import java.sql.JDBCType;
import java.time.LocalDateTime;

import jakarta.annotation.Generated;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class QueueDynamicSqlSupport {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final Queue queue = new Queue();

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = queue.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = queue.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = queue.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = queue.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> clusterId = queue.clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> name = queue.name;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> description = queue.description;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> queueId = queue.queueId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> isDefault = queue.isDefault;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Boolean> isAutoScale = queue.isAutoScale;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Boolean> hasAutoScale = queue.hasAutoScale;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> defaultSpec = queue.defaultSpec;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> defaultImageId = queue.defaultImageId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> defaultUserData = queue.defaultUserData;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> status = queue.status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> cosStackId = queue.cosStackId;

    // @Generated("org.mybatis.generator.api.MyBatisGenerator")
    // public static SqlColumn<String> logicalZone = queue.logicalZone;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> subnetId = queue.subnetId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class Queue extends AliasableSqlTable<Queue> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<String> clusterId = column("cluster_id", JDBCType.VARCHAR);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);

        public final SqlColumn<String> description = column("description", JDBCType.VARCHAR);

        public final SqlColumn<String> queueId = column("group_id", JDBCType.VARCHAR);

        public final SqlColumn<Boolean> isDefault = column("is_default", JDBCType.BIT);

        public final SqlColumn<Boolean> isAutoScale = column("is_auto_scale", JDBCType.BIT);

        public final SqlColumn<Boolean> hasAutoScale = column("has_auto_scale", JDBCType.BIT);

        public final SqlColumn<String> defaultSpec = column("default_spec", JDBCType.VARCHAR);

        public final SqlColumn<String> defaultImageId = column("default_image_id", JDBCType.VARCHAR);

        public final SqlColumn<String> defaultUserData = column("default_user_data", JDBCType.VARCHAR);

        public final SqlColumn<String> status = column("status", JDBCType.VARCHAR);

        public final SqlColumn<String> cosStackId = column("cos_stack_id", JDBCType.VARCHAR);

        // public final SqlColumn<String> logicalZone = column("logical_zone", JDBCType.VARCHAR);

        public final SqlColumn<String> subnetId = column("subnet_id", JDBCType.VARCHAR);

        public Queue() {
            super("t_chpc_group", Queue::new);
        }
    }
}