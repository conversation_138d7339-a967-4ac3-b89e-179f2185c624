package com.baidu.bce.logic.chpc.gatewayimpl.database;

import static com.baidu.bce.logic.chpc.database.mapper.local.SaasResourceDynamicSqlSupport.saasResource;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.time.LocalDateTime;

import jakarta.annotation.Resource;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.SaasResourceDO;
import com.baidu.bce.logic.chpc.database.mapper.SaasResourceMapper;
import com.baidu.bce.logic.chpc.gateway.SaasResourceDAOGateway;
import com.baidu.bce.logic.chpc.model.SaasResource;

@Service
public class SaasResourceDAOGatewayImpl implements SaasResourceDAOGateway {

    @Resource
    private SaasResourceMapper saasResourceMapper;

    @Override
    public Boolean insert(SaasResource saasResource) {

        SaasResourceDO saasResourceDO = BeanCopyUtil.copyObject(saasResource, SaasResourceDO::new);
        saasResourceDO.setDeleted(false);
        saasResourceDO.setCreatedTime(LocalDateTime.now());
        saasResourceDO.setUpdatedTime(LocalDateTime.now());
        return saasResourceMapper.insertSelective(saasResourceDO) != 0;
    }

    @Override
    public Boolean update(SaasResource saasResource) {

        SaasResourceDO saasResourceDO = BeanCopyUtil.copyObject(saasResource, SaasResourceDO::new);
        saasResourceDO.setUpdatedTime(LocalDateTime.now());
        return saasResourceMapper.updateByResourceUuid(saasResourceDO) != 0;
    }

    @Override
    public Boolean delete(String resourceUuid) {

        SaasResourceDO saasResourceDO = new SaasResourceDO();
        saasResourceDO.setResourceUuid(resourceUuid);
        saasResourceDO.setUpdatedTime(LocalDateTime.now());
        saasResourceDO.setDeleted(true);

        return saasResourceMapper.updateByResourceUuid(saasResourceDO) != 0;
    }

    @Override
    public SaasResource findByUserId(String userId, String resourceType) {

        SelectStatementProvider selectStatement = select(SaasResourceMapper.selectList)
                .from(saasResource)
                .where(saasResource.userId, isEqualTo(userId))
                .and(saasResource.resourceType, isEqualToWhenPresent(resourceType))
                .and(saasResource.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        SaasResourceDO saasResourceDO = saasResourceMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(saasResourceDO, SaasResource::new);
    }

    @Override
    public SaasResource findByAccountId(String accountId, String resourceType) {

        SelectStatementProvider selectStatement = select(SaasResourceMapper.selectList)
                .from(saasResource)
                .where(saasResource.accountId, isEqualTo(accountId))
                .and(saasResource.resourceType, isEqualToWhenPresent(resourceType))
                .and(saasResource.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        SaasResourceDO saasResourceDO = saasResourceMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(saasResourceDO, SaasResource::new);
    }

    @Override
    public SaasResource findByOrderId(String orderId) {
        SelectStatementProvider selectStatement = select(SaasResourceMapper.selectList)
                .from(saasResource)
                .where(saasResource.orderId, isEqualToWhenPresent(orderId))
                .and(saasResource.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        SaasResourceDO saasResourceDO = saasResourceMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(saasResourceDO, SaasResource::new);
    }

       @Override
    public SaasResource findByResourceUuid(String resourceUuid, String accountId) {
        SelectStatementProvider selectStatement = select(SaasResourceMapper.selectList)
                .from(saasResource)
                .where(saasResource.resourceUuid, isEqualTo(resourceUuid))
                .and(saasResource.accountId, isEqualToWhenPresent(accountId))
                .and(saasResource.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        SaasResourceDO saasResourceDO = saasResourceMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(saasResourceDO, SaasResource::new);
    }
}
