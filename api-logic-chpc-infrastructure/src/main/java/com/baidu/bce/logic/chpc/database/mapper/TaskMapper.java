package com.baidu.bce.logic.chpc.database.mapper;

import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.cosStackId;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.extra;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.queueId;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.source;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.task;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.taskId;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.taskType;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.taskUuid;
import static com.baidu.bce.logic.chpc.database.mapper.local.TaskDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

import jakarta.annotation.Generated;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import com.baidu.bce.logic.chpc.database.dataobject.TaskDO;

@Mapper
public interface TaskMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<TaskDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            status,
            taskId,
            taskUuid,
            taskType,
            clusterId,
            queueId,
            source,
            extra,
            cosStackId);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "TaskDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "task_id", property = "taskId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "task_uuid", property = "taskUuid", jdbcType = JdbcType.VARCHAR),
            @Result(column = "task_type", property = "taskType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_id", property = "queueId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "source", property = "source", jdbcType = JdbcType.VARCHAR),
            @Result(column = "extra", property = "extra", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "cos_stack_id", property = "cosStackId", jdbcType = JdbcType.VARCHAR)
    })
    List<TaskDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("TaskDOResult")
    Optional<TaskDO> selectOne(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<TaskDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, task, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(TaskDO row) {
        return MyBatis3Utils.insert(this::insert, row, task, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(status).toProperty("status")
                        .map(taskId).toProperty("taskId")
                        .map(taskUuid).toProperty("taskUuid")
                        .map(taskType).toProperty("taskType")
                        .map(clusterId).toProperty("clusterId")
                        .map(queueId).toProperty("queueId")
                        .map(source).toProperty("source")
                        .map(extra).toProperty("extra")
                        .map(cosStackId).toProperty("cosStackId")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<TaskDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, task, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(status).toProperty("status")
                        .map(taskId).toProperty("taskId")
                        .map(taskUuid).toProperty("taskUuid")
                        .map(taskType).toProperty("taskType")
                        .map(clusterId).toProperty("clusterId")
                        .map(queueId).toProperty("queueId")
                        .map(source).toProperty("source")
                        .map(extra).toProperty("extra")
                        .map(cosStackId).toProperty("cosStackId")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(TaskDO row) {
        return MyBatis3Utils.insert(this::insert, row, task, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(status).toPropertyWhenPresent("status", row::getStatus)
                        .map(taskId).toPropertyWhenPresent("taskId", row::getTaskId)
                        .map(taskUuid).toPropertyWhenPresent("taskUuid", row::getTaskUuid)
                        .map(taskType).toPropertyWhenPresent("taskType", row::getTaskType)
                        .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                        .map(queueId).toPropertyWhenPresent("queueId", row::getQueueId)
                        .map(source).toPropertyWhenPresent("source", row::getSource)
                        .map(extra).toPropertyWhenPresent("extra", row::getExtra)
                        .map(cosStackId).toPropertyWhenPresent("cosStackId", row::getCosStackId)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<TaskDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<TaskDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<TaskDO> selectByPrimaryKey(Long key) {
        return selectOne(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, task, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(TaskDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(status).equalTo(row::getStatus)
                .set(taskId).equalTo(row::getTaskId)
                .set(taskType).equalTo(row::getTaskType)
                .set(clusterId).equalTo(row::getClusterId)
                .set(queueId).equalTo(row::getQueueId)
                .set(source).equalTo(row::getSource)
                .set(extra).equalTo(row::getExtra)
                .set(cosStackId).equalTo(row::getCosStackId);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(TaskDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(taskId).equalToWhenPresent(row::getTaskId)
                .set(taskType).equalToWhenPresent(row::getTaskType)
                .set(clusterId).equalToWhenPresent(row::getClusterId)
                .set(queueId).equalToWhenPresent(row::getQueueId)
                .set(source).equalToWhenPresent(row::getSource)
                .set(extra).equalToWhenPresent(row::getExtra)
                .set(cosStackId).equalToWhenPresent(row::getCosStackId);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(TaskDO row) {
        return update(c ->
                c.set(createdTime).equalTo(row::getCreatedTime)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .set(deleted).equalTo(row::getDeleted)
                        .set(status).equalTo(row::getStatus)
                        .set(taskId).equalTo(row::getTaskId)
                        .set(taskType).equalTo(row::getTaskType)
                        .set(clusterId).equalTo(row::getClusterId)
                        .set(queueId).equalTo(row::getQueueId)
                        .set(source).equalTo(row::getSource)
                        .set(extra).equalTo(row::getExtra)
                        .set(cosStackId).equalTo(row::getCosStackId)
                        .where(id, isEqualTo(row::getId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKeySelective(TaskDO row) {
        return update(c ->
                c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(taskId).equalToWhenPresent(row::getTaskId)
                        .set(taskType).equalToWhenPresent(row::getTaskType)
                        .set(clusterId).equalToWhenPresent(row::getClusterId)
                        .set(queueId).equalToWhenPresent(row::getQueueId)
                        .set(source).equalToWhenPresent(row::getSource)
                        .set(extra).equalToWhenPresent(row::getExtra)
                        .set(cosStackId).equalToWhenPresent(row::getCosStackId)
                        .where(id, isEqualTo(row::getId))
        );
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByTaskId(TaskDO row) {
        return update(c ->
                c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(extra).equalToWhenPresent(row::getExtra)
                        .set(cosStackId).equalToWhenPresent(row::getCosStackId)
                        .where(taskId, isEqualTo(row::getTaskId))
        );
    }

    /**
     * 更新一条记录，根据任务UUID进行更新。
     *
     * @param row TaskDO类型的对象，包含需要更新的字段值
     * @return int类型，返回影响的行数，0表示没有更新的行
     * @see TaskDO TaskDO类描述 */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByTaskUuid(TaskDO row) {
        return update(c ->
                c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(extra).equalToWhenPresent(row::getExtra)
                        .set(cosStackId).equalToWhenPresent(row::getCosStackId)
                        .where(taskUuid, isEqualTo(row::getTaskUuid))
        );
    }
}