package com.baidu.bce.logic.chpc.bcc;

import com.baidu.bce.internalsdk.bcc.model.RebootServerRequest;
import com.baidu.bce.internalsdk.core.Entity;
import com.baidu.bce.internalsdk.iam.model.IamEncryptResponse;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.baidu.bce.logic.chpc.bcc.client.LogicBccNioClient;
import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.holder.AccessKeyThreadHolder;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.BccInstanceDetail;
import com.baidu.bce.logic.chpc.iam.IamGateway;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidubce.services.bcc.model.InstanceModel;

import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.hadoop.hbase.util.Strings;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class BccGatewayImpl implements BccGateway {

    @Resource
    IamGateway iamGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private LogicBccNioClient logicBccNioClient;

    @Override
    public List<BccInstance> getBccInstances(List<String> instanceIds) {
        if (CollectionUtils.isEmpty(instanceIds)) {
            return new ArrayList<>();
        }

        log.info("instanceIds count: {}", instanceIds.size());

        List<List<String>> partitions = Lists.partition(instanceIds, ChpcConstant.BCC_MAX_COUNT);

        List<BccInstance> bccInstances = new ArrayList<>();
        ListBccDetailRequest request = new ListBccDetailRequest();
        for (List<String> partition : partitions) {
            request.setInstanceIds(partition);
            bccInstances.addAll(
                logicBccNioClient.getInstanceDetails(request, LogicUserService.getAccountId()).getResult());
        }
        return bccInstances;
    }

    @Override
    public List<InstanceModel> getBccInstancesByZoneName(String zoneName, String instanceIds, String instanceNames) {
        ListBccInstancesRequest request = new ListBccInstancesRequest();
        List<InstanceModel> res = new ArrayList<>();
        request.setZoneName(zoneName);
        request.setInstanceIds(instanceIds);
        request.setInstanceNames(instanceNames);
        request.setStatus("Running");
        request.setMaxKeys(1000);
        // ListBccInstancesResponse resp = bccClient.listBccInstances(request);
        // res.addAll(resp.getInstances());

        Boolean isTruncated = true;
        while (isTruncated) {
            ListBccInstancesResponse resp =
                logicBccNioClient.listBccInstances(request, LogicUserService.getAccountId());
            res.addAll(resp.getInstances());
            if (resp.getIsTruncated()) {
                log.debug(
                    "[AddNodeDebug] listBccInstances request:{} lenInst:{}",
                    Entity.json(request).getEntity(),
                    resp.getInstances().size());
                request.setMarker(resp.getNextMarker());
            } else {
                break;
            }
        }

        Collections.reverse(res);
        return res;
    }

    @Override
    public BccInstanceDetail getBccInstanceDetail(String instanceId) {
        BccInstanceDetailResponse bccInstanceDetail =
            logicBccNioClient.getBccInstanceDetail(instanceId, LogicUserService.getAccountId());
        BccInstanceDetail detail = new BccInstanceDetail();
        detail.setInstanceId(bccInstanceDetail.getInstance().getId());

        return detail;
    }

    @Override
    public void rebuildBatchBccInstance(List<String> instanceIds, String imageId, String password, String keypairId) {
        RebuildBatchInstanceRequest request = new RebuildBatchInstanceRequest();
        if (CollectionUtils.isEmpty(instanceIds)) {
            log.warn("[AddNodeDebug] instanceIds is empty, do nothing");
            throw new IllegalArgumentException("instanceIds is empty");
        }
        request.setInstanceIds(instanceIds);
        request.setImageId(imageId);

        String ak = AccessKeyThreadHolder.getAccessKey();

        if (!Strings.isEmpty(keypairId)) {
            request.setKeypairId(keypairId);
        } else {
            if (password.length() != 32) {
                log.warn(
                    "[AddNodeDebug] keypairId is null and password is not encrypted, use random password:{} instead ",
                    password);
                IamEncryptResponse encryptResponse = iamGateway.encrypt(password, LogicUserService.getAccountId());
                ak = encryptResponse.getAccesskeyId();
                password = encryptResponse.getCipherHex();
            }
            request.setAdminPass(password);
        }

        log.debug(
            "[AddNodeDebug] start to rebuild batch bcc instance, instanceIds:{}, imageId:{}, password:{}, keypairId:{}, accountId:{}, request:{}",
            instanceIds,
            imageId,
            password,
            keypairId,
            LogicUserService.getAccountId(),
            Entity.json(request).getEntity());

        logicBccNioClient.rebuildBatchInstance(request, ak, LogicUserService.getAccountId());
    }

    @Override
    public void rebootBatchBccInstance(List<String> instanceIds, Boolean forceStop) {
        RebootServerRequest request = new RebootServerRequest();
        if (CollectionUtils.isEmpty(instanceIds)) {
            log.warn("[AddNodeDebug] instanceIds is empty, do nothing");
            throw new IllegalArgumentException("instanceIds is empty");
        }
        request.setServerIds(instanceIds);
        request.setForceStop(forceStop);

        logicBccNioClient.rebootServer(request, LogicUserService.getAccountId());
    }

    /*
     * 将字节数组转换为十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder(2 * bytes.length);
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    /*
     * 使用 AES 加密算法加密字符串
     */
    public static String encrypt(String plaintext, String sk) throws Exception {
        // 获取 SK 的前16位作为密钥
        String aesKey = sk.substring(0, 16);
        SecretKeySpec secretKeySpec = new SecretKeySpec(aesKey.getBytes(StandardCharsets.UTF_8), "AES");

        // 创建 AES 加密器
        Cipher cipher = Cipher.getInstance("AES");
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);

        // 加密
        return bytesToHex(cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8)));
    }
}
