package com.baidu.bce.logic.chpc.vpc;

import jakarta.annotation.Resource;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baidu.bce.internalsdk.sts.model.StsCredential;
import com.baidu.bce.logic.chpc.vpc.gateway.VpcGateway;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidubce.auth.DefaultBceSessionCredentials;
import com.baidubce.services.vpc.VpcClient;
import com.baidubce.services.vpc.VpcClientConfiguration;
import com.baidubce.services.vpc.model.GetVpcResponse;

import com.baidu.bce.plat.webframework.region.RegionConfiguration;

@Slf4j
@Service
public class VpcImpl implements VpcGateway {
    @Resource
    IamLogicService iamLogicService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Value("${bce.web.commons.sdk.vpc.endpoint}")
    private String endpoint;

    @Override
    public GetVpcResponse getVpc(String vpcId) {
        StsCredential stsCredential = iamLogicService.getUserStsAccessKeyWithToken(
            regionConfiguration.getCurrentRegion(), LogicUserService.getAccountId()).join();
        log.debug("stsCredential:{}, accountId:{}", stsCredential, LogicUserService.getAccountId());
        VpcClientConfiguration config = new VpcClientConfiguration();
        config.setCredentials(new DefaultBceSessionCredentials(stsCredential.getAccessKeyId(), 
            stsCredential.getSecretAccessKey(), stsCredential.getSessionToken()));
        config.setEndpoint(endpoint);
        VpcClient vpcClient = new VpcClient(config);
        GetVpcResponse response = vpcClient.getVpc(vpcId);
        log.debug("get vpc succ, resp:{}", response.toString());
        return response;
    }
}
