package com.baidu.bce.logic.chpc.database.dataobject;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;

public class CfsDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String cfsId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String mountOption;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String cfsType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String name;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String storageProtocol;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String mountTarget;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String mountDir;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterId() {
        return clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId == null ? null : clusterId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getCfsId() {
        return cfsId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCfsId(String cfsId) {
        this.cfsId = cfsId == null ? null : cfsId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getCfsType() {
        return cfsType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCfsType(String cfsType) {
        this.cfsType = cfsType == null ? null : cfsType.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getName() {
        return name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getStorageProtocol() {
        return storageProtocol;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setStorageProtocol(String storageProtocol) {
        this.storageProtocol = storageProtocol == null ? null : storageProtocol.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getMountTarget() {
        return mountTarget;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setMountTarget(String mountTarget) {
        this.mountTarget = mountTarget == null ? null : mountTarget.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getMountDir() {
        return mountDir;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setMountDir(String mountDir) {
        this.mountDir = mountDir == null ? null : mountDir.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getMountOption() {
        return mountOption;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setMountOption(String mountOption) {
        this.mountOption = mountOption == null ? null : mountOption.trim();
    }
}