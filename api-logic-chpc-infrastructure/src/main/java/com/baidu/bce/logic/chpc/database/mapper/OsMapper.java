package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.OsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;

import jakarta.annotation.Generated;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.osArch;
import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.osName;
import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.osVersion;
import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.schedulerType;
import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.schedulerVersion;
import static com.baidu.bce.logic.chpc.database.mapper.local.OsDynamicSqlSupport.updatedTime;

@Mapper
public interface OsMapper extends CommonCountMapper, CommonDeleteMapper,
        CommonInsertMapper<OsDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            schedulerType,
            schedulerVersion,
            osArch,
            osName,
            osVersion);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "osDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "scheduler_type", property = "schedulerType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "scheduler_version", property = "schedulerVersion", jdbcType = JdbcType.VARCHAR),
            @Result(column = "os_arch", property = "osArch", jdbcType = JdbcType.VARCHAR),
            @Result(column = "os_name", property = "osName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "os_version", property = "osVersion", jdbcType = JdbcType.VARCHAR)
    })
    List<OsDO> selectMany(SelectStatementProvider selectStatement);
}
