package com.baidu.bce.logic.chpc.pfs.client;

import java.util.Map;

import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.pfs.GetFilesetRequest;
import com.baidu.bce.logic.chpc.pfs.GetFilesetResponse;

public class PfsNioClient extends BceNioClient {

    public PfsNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public GetFilesetResponse getFilesetInfo(GetFilesetRequest request, String accountId) {
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path("/v1/pfs/fileset").build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(GetFilesetResponse.class).block();
    }
}
