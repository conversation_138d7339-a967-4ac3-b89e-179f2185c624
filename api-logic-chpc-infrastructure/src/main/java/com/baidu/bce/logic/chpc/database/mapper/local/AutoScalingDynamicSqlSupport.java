package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;

public final class AutoScalingDynamicSqlSupport {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static AutoScaling autoScaling = new AutoScaling();

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Long> id = autoScaling.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> asId = autoScaling.asId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> asName = autoScaling.asName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> accountId = autoScaling.accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<LocalDateTime> createdTime = autoScaling.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<LocalDateTime> updatedTime = autoScaling.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Boolean> deleted = autoScaling.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> clusterId = autoScaling.clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> clusterName = autoScaling.clusterName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> queueId = autoScaling.queueId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> queueName = autoScaling.queueName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> zoneName = autoScaling.zoneName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> subnetId = autoScaling.subnetId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> securityGroupId = autoScaling.securityGroupId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Integer> maxNodesInQueue = autoScaling.maxNodesInQueue;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Integer> minNodesInQueue = autoScaling.minNodesInQueue;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Boolean> enableAutoGrow = autoScaling.enableAutoGrow;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Boolean> enableAutoShrink = autoScaling.enableAutoShrink;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> spec = autoScaling.spec;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Integer> systemDiskSize = autoScaling.systemDiskSize;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> systemDiskType = autoScaling.systemDiskType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> excludeNodes = autoScaling.excludeNodes;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> status = autoScaling.status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> imageId = autoScaling.imageId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<Integer> maxScalePerCycle = autoScaling.maxScalePerCycle;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> hostnamePrefix = autoScaling.hostnamePrefix;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> cudaVersion = autoScaling.cudaVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> gpuDriverVersion = autoScaling.gpuDriverVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> cudnnVersion = autoScaling.cudnnVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> dataDiskList = autoScaling.dataDiskList;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> cpuThreadConfig = autoScaling.cpuThreadConfig;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> numaConfig = autoScaling.numaConfig;
    
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class AutoScaling extends AliasableSqlTable<AutoScaling> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<String> asId = column("as_id", JDBCType.VARCHAR);

        public final SqlColumn<String> asName = column("as_name", JDBCType.VARCHAR);

        public final SqlColumn<String> accountId = column("account_id", JDBCType.VARCHAR);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<String> clusterId = column("cluster_id", JDBCType.VARCHAR);

        public final SqlColumn<String> clusterName = column("cluster_name", JDBCType.VARCHAR);

        public final SqlColumn<String> queueId = column("group_id", JDBCType.VARCHAR);

        public final SqlColumn<String> queueName = column("queue_name", JDBCType.VARCHAR);

        public final SqlColumn<String> zoneName = column("zone_name", JDBCType.VARCHAR);

        public final SqlColumn<String> subnetId = column("subnet_id", JDBCType.VARCHAR);

        public final SqlColumn<String> securityGroupId = column("security_group_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> maxNodesInQueue = column("max_nodes_in_queue", JDBCType.INTEGER);

        public final SqlColumn<Integer> minNodesInQueue = column("min_nodes_in_queue", JDBCType.INTEGER);

        public final SqlColumn<Boolean> enableAutoGrow = column("enable_auto_grow", JDBCType.BIT);

        public final SqlColumn<Boolean> enableAutoShrink = column("enable_auto_shrink", JDBCType.BIT);

        public final SqlColumn<String> spec = column("spec", JDBCType.VARCHAR);

        public final SqlColumn<Integer> systemDiskSize = column("system_disk_size", JDBCType.INTEGER);

        public final SqlColumn<String> systemDiskType = column("system_disk_type", JDBCType.VARCHAR);

        public final SqlColumn<String> excludeNodes = column("exclude_nodes", JDBCType.LONGVARCHAR);

        public final SqlColumn<String> status = column("status", JDBCType.VARCHAR);

        public final SqlColumn<String> imageId = column("image_id", JDBCType.VARCHAR);

        public final SqlColumn<Integer> maxScalePerCycle = column("max_scale_per_cycle", JDBCType.INTEGER);

        public final SqlColumn<String> hostnamePrefix = column("hostname_prefix", JDBCType.VARCHAR);

        public final SqlColumn<String> cudaVersion = column("cuda_version", JDBCType.VARCHAR);

        public final SqlColumn<String> gpuDriverVersion = column("gpu_driver_version", JDBCType.VARCHAR);

        public final SqlColumn<String> cudnnVersion = column("cudnn_version", JDBCType.VARCHAR);

        public final SqlColumn<String> dataDiskList = column("data_disk_list", JDBCType.LONGNVARCHAR);

        public final SqlColumn<String> cpuThreadConfig = column("cpu_thread_config", JDBCType.VARCHAR);

        public final SqlColumn<String> numaConfig = column("numa_config", JDBCType.VARCHAR);

        public AutoScaling() {
            super("t_chpc_autoscaling", AutoScaling::new);
        }
    }
}