package com.baidu.bce.logic.chpc.iam;

import com.baidu.bce.internalsdk.iam.IamApiNioClient;
import com.baidu.bce.internalsdk.iam.model.IamDecryptRequest;
import com.baidu.bce.internalsdk.iam.model.IamDecryptResponse;
import com.baidu.bce.internalsdk.iam.model.IamEncryptRequest;
import com.baidu.bce.internalsdk.iam.model.IamEncryptResponse;
import com.baidu.bce.logic.core.iam.service.IamLogicService;

import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import org.apache.commons.codec.binary.Hex;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
public class IamGatewayImpl implements IamGateway {


    @Resource
    IamLogicService iamLogicService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private IamApiNioClient iamApiNioClient;

    @Override
    public IamEncryptResponse encrypt(String password, String userId) {
        IamEncryptRequest request = new IamEncryptRequest();
        request.setUserId(userId);
        request.setRawHex(Hex.encodeHexString(password.getBytes()));
        IamEncryptResponse encrypt = iamApiNioClient.encrypt(request).block();
        return encrypt;

    }

    @Override
    public IamDecryptResponse decrypt(String encryptedPassword, String ak) {
        IamDecryptRequest request = new IamDecryptRequest();
        request.setAccesskeyId(ak);
        request.setCipherHex(encryptedPassword);
        IamDecryptResponse decrypt = iamApiNioClient.decrypt(request).block();
        return decrypt;
    }
}
