package com.baidu.bce.logic.chpc.bcc.autoconfigure;

import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;

import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.logic.core.constants.CommonsConstants;
import com.google.common.collect.Maps;

import lombok.Data;
import lombok.NoArgsConstructor;

@ConfigurationProperties(CommonsConstants.CONFIG_SDK_PREFIX + ".bcc")
@NoArgsConstructor
@Data
public class LogicBccProperties {
    
    private String endpoint;

    private Map<String, WebclientConfigProperties> configs = Maps.newHashMap();

}
