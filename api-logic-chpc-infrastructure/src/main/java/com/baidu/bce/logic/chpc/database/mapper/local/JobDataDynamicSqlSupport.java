package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;

public class JobDataDynamicSqlSupport {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final JobData jobData = new JobData();

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = jobData.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = jobData.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = jobData.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = jobData.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> accountId = jobData.accountId;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobId = jobData.jobId;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> poolId = jobData.poolId;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobStatus = jobData.jobStatus;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobCpu = jobData.jobCpu;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> jobMemory = jobData.jobMemory;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> runTime = jobData.runTime;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class JobData extends AliasableSqlTable<JobData> {

        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<String> accountId = column("account_id", JDBCType.VARCHAR);

        public final SqlColumn<String> jobId = column("job_id", JDBCType.VARCHAR);

        public final SqlColumn<String> poolId = column("pool_id", JDBCType.VARCHAR);

        public final SqlColumn<String> jobStatus = column("job_status", JDBCType.VARCHAR);

        public final SqlColumn<String> jobCpu = column("job_cpu", JDBCType.VARCHAR);

        public final SqlColumn<String> jobMemory = column("job_memory", JDBCType.VARCHAR);

        public final SqlColumn<String> runTime = column("run_time", JDBCType.VARCHAR);

        public JobData() {
            super("t_chpc_saas_job_data", JobData::new);
        }


    }
}
