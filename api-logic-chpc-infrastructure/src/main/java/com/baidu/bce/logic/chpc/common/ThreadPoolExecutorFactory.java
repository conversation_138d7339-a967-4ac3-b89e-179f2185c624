package com.baidu.bce.logic.chpc.common;

import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author: lilu24
 * @Date: 2022-12-20
 */
@Component
public class ThreadPoolExecutorFactory {


    @Bean(name = "clusterScheduledTaskThreadPool")
    public ThreadPoolTaskExecutor clusterScheduledTaskThreadPool() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        executor.setCorePoolSize(32);
        executor.setMaxPoolSize(64);

        // 缓冲队列数
        executor.setQueueCapacity(512);

        // 允许线程空闲时间（单位为秒）
        executor.setKeepAliveSeconds(60);

        // 线程池名前缀
        executor.setThreadNamePrefix("ClusterScheduledTask");

        // 用来设置线程池关闭的时候等待所有任务都完成再继续销毁其他的Bean
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 线程池中任务的等待时间，如果超过这个时候还没有销毁就强制销毁
        executor.setAwaitTerminationSeconds(20);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();

        return executor;
    }
}
