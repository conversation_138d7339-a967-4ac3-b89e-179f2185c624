package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.CfsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.cfs;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.cfsId;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.cfsType;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.mountDir;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.mountOption;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.mountTarget;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.storageProtocol;
import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface CfsMapper extends CommonCountMapper, CommonDeleteMapper,
        CommonInsertMapper<CfsDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            clusterId,
            cfsId,
            cfsType,
            name,
            storageProtocol,
            mountTarget,
            mountOption,
            mountDir);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "CfsDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cfs_id", property = "cfsId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cfs_type", property = "cfsType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "storage_protocol", property = "storageProtocol", jdbcType = JdbcType.VARCHAR),
            @Result(column = "mount_target", property = "mountTarget", jdbcType = JdbcType.VARCHAR),
            @Result(column = "mount_option", property = "mountOption", jdbcType = JdbcType.VARCHAR),
            @Result(column = "mount_dir", property = "mountDir", jdbcType = JdbcType.VARCHAR)
    })
    List<CfsDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, cfs, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, cfs, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(CfsDO row) {
        return MyBatis3Utils.insert(this::insert, row, cfs, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(clusterId).toProperty("clusterId")
                        .map(cfsId).toProperty("cfsId")
                        .map(cfsType).toProperty("cfsType")
                        .map(name).toProperty("name")
                        .map(storageProtocol).toProperty("storageProtocol")
                        .map(mountTarget).toProperty("mountTarget")
                        .map(mountOption).toProperty("mountOption")
                        .map(mountDir).toProperty("mountDir")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<CfsDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, cfs, c ->
                c.map(id).toProperty("id")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(clusterId).toProperty("clusterId")
                        .map(cfsId).toProperty("cfsId")
                        .map(cfsType).toProperty("cfsType")
                        .map(name).toProperty("name")
                        .map(storageProtocol).toProperty("storageProtocol")
                        .map(mountTarget).toProperty("mountTarget")
                        .map(mountOption).toProperty("mountOption")
                        .map(mountDir).toProperty("mountDir")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(CfsDO row) {
        return MyBatis3Utils.insert(this::insert, row, cfs, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                        .map(cfsId).toPropertyWhenPresent("cfsId", row::getCfsId)
                        .map(cfsType).toPropertyWhenPresent("cfsType", row::getCfsType)
                        .map(name).toPropertyWhenPresent("name", row::getName)
                        .map(storageProtocol).toPropertyWhenPresent("storageProtocol", row::getStorageProtocol)
                        .map(mountTarget).toPropertyWhenPresent("mountTarget", row::getMountTarget)
                        .map(mountOption).toPropertyWhenPresent("mountOption", row::getMountOption)
                        .map(mountDir).toPropertyWhenPresent("mountDir", row::getMountDir)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<CfsDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, cfs, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("CfsDOResult")
    Optional<CfsDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<CfsDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, cfs, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<CfsDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, cfs, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<CfsDO> selectByPrimaryKey(Long key) {
        return selectOne(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, cfs, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(CfsDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(deleted).equalTo(row::getDeleted)
                .set(clusterId).equalTo(row::getClusterId)
                .set(cfsId).equalTo(row::getCfsId)
                .set(cfsType).equalTo(row::getCfsType)
                .set(name).equalTo(row::getName)
                .set(storageProtocol).equalTo(row::getStorageProtocol)
                .set(mountTarget).equalTo(row::getMountTarget)
                .set(mountOption).equalTo(row::getMountOption)
                .set(mountDir).equalTo(row::getMountDir);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(CfsDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .set(clusterId).equalToWhenPresent(row::getClusterId)
                .set(cfsId).equalToWhenPresent(row::getCfsId)
                .set(cfsType).equalToWhenPresent(row::getCfsType)
                .set(name).equalToWhenPresent(row::getName)
                .set(storageProtocol).equalToWhenPresent(row::getStorageProtocol)
                .set(mountTarget).equalToWhenPresent(row::getMountTarget)
                .set(mountOption).equalToWhenPresent(row::getMountOption)
                .set(mountDir).equalToWhenPresent(row::getMountDir);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(CfsDO row) {
        return update(c ->
                c.set(createdTime).equalTo(row::getCreatedTime)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .set(deleted).equalTo(row::getDeleted)
                        .set(clusterId).equalTo(row::getClusterId)
                        .set(cfsId).equalTo(row::getCfsId)
                        .set(cfsType).equalTo(row::getCfsType)
                        .set(name).equalTo(row::getName)
                        .set(storageProtocol).equalTo(row::getStorageProtocol)
                        .set(mountTarget).equalTo(row::getMountTarget)
                        .set(mountDir).equalTo(row::getMountDir)
                        .set(mountOption).equalTo(row::getMountOption)
                        .where(id, isEqualTo(row::getId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKeySelective(CfsDO row) {
        return update(c ->
                c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(clusterId).equalToWhenPresent(row::getClusterId)
                        .set(cfsId).equalToWhenPresent(row::getCfsId)
                        .set(cfsType).equalToWhenPresent(row::getCfsType)
                        .set(name).equalToWhenPresent(row::getName)
                        .set(storageProtocol).equalToWhenPresent(row::getStorageProtocol)
                        .set(mountTarget).equalToWhenPresent(row::getMountTarget)
                        .set(mountDir).equalToWhenPresent(row::getMountDir)
                        .set(mountOption).equalToWhenPresent(row::getMountOption)
                        .where(id, isEqualTo(row::getId))
        );
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByClusterIdAndCfsId(CfsDO row) {
        return update(c ->
                c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .set(deleted).equalTo(row::getDeleted)
                        .where(clusterId, isEqualTo(row::getClusterId))
                        .and(cfsId, isEqualTo(row::getCfsId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByClusterId(CfsDO row) {
        return update(c ->
                c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(mountTarget).equalToWhenPresent(row::getMountTarget)
                        .set(mountDir).equalToWhenPresent(row::getMountDir)
                        .set(mountOption).equalToWhenPresent(row::getMountOption)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .where(clusterId, isEqualTo(row::getClusterId))
        );
    }

    /**
     * 更新一条 CfsDO 记录，根据 mountTarget、mountDir 和 deleted 字段进行唯一性约束。
     * 如果存在则更新，不存在则插入。
     *
     * @param row CfsDO 对象，包含需要更新的数据
     * @return int 返回影响的行数，0表示没有更新或者插入任何行
     * @throws Exception 可能会抛出 SQLException、RuntimeException 等异常
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByMountInfo(CfsDO row) {
        return update(c ->
                c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(mountTarget).equalToWhenPresent(row::getMountTarget)
                        .set(mountDir).equalToWhenPresent(row::getMountDir)
                        .set(mountOption).equalToWhenPresent(row::getMountOption)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .where(clusterId, isEqualTo(row::getClusterId))
                        .and(cfsId, isEqualTo(row::getCfsId))
                        .and(mountTarget, isEqualTo(row::getMountTarget))
                        .and(mountDir, isEqualTo(row::getMountDir))
        );
    }

}