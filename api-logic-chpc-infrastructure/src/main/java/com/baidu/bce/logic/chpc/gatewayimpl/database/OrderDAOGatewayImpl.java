package com.baidu.bce.logic.chpc.gatewayimpl.database;

import static com.baidu.bce.logic.chpc.database.mapper.local.OrderDynamicSqlSupport.order;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.annotation.Resource;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.OrderDO;
import com.baidu.bce.logic.chpc.database.mapper.OrderMapper;
import com.baidu.bce.logic.chpc.gateway.OrderDAOGateway;
import com.baidu.bce.logic.chpc.model.Order;

@Service
public class OrderDAOGatewayImpl implements OrderDAOGateway {

    @Resource
    private OrderMapper orderMapper;

    @Override
    public Boolean insert(Order order) {

        OrderDO orderDO = BeanCopyUtil.copyObject(order, OrderDO::new);
        orderDO.setDeleted(false);
        orderDO.setCreatedTime(LocalDateTime.now());
        orderDO.setUpdatedTime(LocalDateTime.now());
        return orderMapper.insertSelective(orderDO) != 0;
    }

    @Override
    public Boolean update(Order order) {

        OrderDO orderDO = BeanCopyUtil.copyObject(order, OrderDO::new);
        orderDO.setUpdatedTime(LocalDateTime.now());
        return orderMapper.updateByOrderId(orderDO) != 0;
    }

    @Override
    public Boolean delete(String orderId) {

        OrderDO orderDO = new OrderDO();
        orderDO.setOrderId(orderId);
        orderDO.setUpdatedTime(LocalDateTime.now());
        orderDO.setDeleted(true);

        return orderMapper.updateByOrderId(orderDO) != 0;
    }

    @Override
    public Order findByOrderId(String orderId) {

        SelectStatementProvider selectStatement = select(OrderMapper.selectList)
                .from(order)
                .where(order.orderId, isEqualTo(orderId))
                .and(order.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        OrderDO orderDO = orderMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(orderDO, Order::new);
    }

    @Override
    public Order findByAccountIdAndServiceType(String accountId, String serviceType) {

        SelectStatementProvider selectStatement = select(OrderMapper.selectList)
                .from(order)
                .where(order.accountId, isEqualTo(accountId))
                .and(order.serviceType, isEqualTo(serviceType))
                .and(order.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        OrderDO orderDO = orderMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(orderDO, Order::new);
    }

    @Override
    public List<Order> findAll(String serviceType) {
        SelectStatementProvider selectStatement = select(OrderMapper.selectList)
                .from(order)
                .where(order.serviceType, isEqualTo(serviceType))
                .and(order.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<OrderDO> orderDOList = orderMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(orderDOList, Order::new);
    }
}
