package com.baidu.bce.logic.chpc.database.dataobject;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;

public class WorkflowDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String workflowId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String name;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String description;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long version;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String language;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String languageVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String workspaceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String introduction;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String document;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String inputs;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime deletedTime;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getName() {
        return name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setName(String name) {
        this.name = name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDescription() {
        return description;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDescription(String description) {
        this.description = description;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getLanguage() {
        return language;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setLanguage(String language) {
        this.language = language;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getLanguageVersion() {
        return languageVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setLanguageVersion(String languageVersion) {
        this.languageVersion = languageVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getWorkspaceId() {
        return workspaceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getWorkflowId() {
        return workflowId;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setWorkflowId(String workflowId) {
        this.workflowId = workflowId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getVersion() {
        return version;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setVersion(Long version) {
        this.version = version;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getDeletedTime() {
        return deletedTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeletedTime(LocalDateTime deletedTime) {
        this.deletedTime = deletedTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getIntroduction() {
        return introduction;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDocument() {
        return document;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDocument(String document) {
        this.document = document;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getInputs() {
        return inputs;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setInputs(String inputs) {
        this.inputs = inputs;
    }
}
