package com.baidu.bce.logic.chpc.database.dataobject;

import java.time.LocalDateTime;

import jakarta.annotation.Generated;

public class ClusterDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime deleteTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String softwareDir;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String chargeType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String name;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String description;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String vpcId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String subnetId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String securityGroupId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String securityGroupType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String logicalZone;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String schedulerType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String schedulerVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean enableHa;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean enableMonitor;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String extra;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String imageId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String spec;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String errorMessage;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long heartbeat;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private int schedulePlugin;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String schedulePluginVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterType;
    /**
     * 创建集群时，cos对应的资源栈
     */
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String cosStackId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Integer maxNodes;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Integer maxCpus;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String password;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String keypairId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean forbidDelete;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String domainAccount;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDomainAccount() {
        return domainAccount;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDomainAccount(String domainAccount) {
        this.domainAccount = domainAccount;
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean createdDone;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getDeleteTime() {
        return deleteTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleteTime(LocalDateTime deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterId() {
        return clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId == null ? null : clusterId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getChargeType() {
        return chargeType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setChargeType(String chargeType) {
        this.chargeType = chargeType == null ? null : chargeType.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getName() {
        return name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDescription() {
        return description;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getVpcId() {
        return vpcId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setVpcId(String vpcId) {
        this.vpcId = vpcId == null ? null : vpcId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSubnetId() {
        return subnetId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSubnetId(String subnetId) {
        this.subnetId = subnetId == null ? null : subnetId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getStatus() {
        return status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getLogicalZone() {
        return logicalZone;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setLogicalZone(String logicalZone) {
        this.logicalZone = logicalZone == null ? null : logicalZone.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSchedulerType() {
        return schedulerType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSchedulerType(String schedulerType) {
        this.schedulerType = schedulerType == null ? null : schedulerType.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSchedulerVersion() {
        return schedulerVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSchedulerVersion(String schedulerVersion) {
        this.schedulerVersion = schedulerVersion == null ? null : schedulerVersion.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getEnableHa() {
        return enableHa;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setEnableHa(Boolean enableHa) {
        this.enableHa = enableHa;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getEnableMonitor() {
        return enableMonitor;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setEnableMonitor(Boolean enableMonitor) {
        this.enableMonitor = enableMonitor;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId == null ? null : accountId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getExtra() {
        return extra;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setExtra(String extra) {
        this.extra = extra == null ? null : extra.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSecurityGroupId() {
        return securityGroupId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSecurityGroupId(String securityGroupId) {
        this.securityGroupId = securityGroupId == null ? null : securityGroupId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSecurityGroupType() {
        return securityGroupType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSecurityGroupType(String securityGroupType) {
        this.securityGroupType = securityGroupType == null ? null : securityGroupType.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getImageId() {
        return imageId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setImageId(String imageId) {
        this.imageId = imageId == null ? null : imageId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSpec() {
        return spec;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSpec(String spec) {
        this.spec = spec == null ? null : spec.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getCosStackId() {
        return cosStackId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCosStackId(String cosStackId) {
        this.cosStackId = cosStackId == null ? null : cosStackId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getErrorMessage() {
        return errorMessage;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setErrorMessage(String errMsg) {
        this.errorMessage = errMsg;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getHeartbeat() {
        return heartbeat;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setHeartbeat(Long heartbeat) {
        this.heartbeat = heartbeat;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public int getSchedulePlugin() {
        return schedulePlugin;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSchedulePlugin(int schedulePlugin) {
        this.schedulePlugin = schedulePlugin;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSchedulePluginVersion() {
        return schedulePluginVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSchedulePluginVersion(String schedulePluginVersion) {
        this.schedulePluginVersion = schedulePluginVersion;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterType() {
        return clusterType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterType(String clusterType) {
        this.clusterType = clusterType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSoftwareDir() {
        return softwareDir;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSoftwareDir(String softwareDir) {
        this.softwareDir = softwareDir;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Integer getMaxNodes() {
        return maxNodes;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setMaxNodes(Integer maxNodes) {
        this.maxNodes = maxNodes;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Integer getMaxCpus() {
        return maxCpus;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setMaxCpus(Integer maxCpus) {
        this.maxCpus = maxCpus;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getPassword() {
        return password;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setPassword(String password) {
        this.password = password;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getKeypairId() {
        return keypairId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setKeypairId(String keypairId) {
        this.keypairId = keypairId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getForbidDelete() {
        return forbidDelete;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setForbidDelete(Boolean forbidDelete) {
        this.forbidDelete = forbidDelete;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getCreatedDone() {
        return createdDone;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreateDone(Boolean createdDone) {
        this.createdDone = createdDone;
    }
}