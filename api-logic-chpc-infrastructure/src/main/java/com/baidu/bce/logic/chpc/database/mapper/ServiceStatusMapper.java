package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.ServiceStatusDO;
import com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;

import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport.serviceStatus;
import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport.serviceId;
import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.ServiceStatusDynamicSqlSupport.status;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface ServiceStatusMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<ServiceStatusDO>, CommonUpdateMapper {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            ServiceStatusDynamicSqlSupport.id,
            ServiceStatusDynamicSqlSupport.createdTime,
            ServiceStatusDynamicSqlSupport.updatedTime,
            ServiceStatusDynamicSqlSupport.deleted,
            ServiceStatusDynamicSqlSupport.accountId,
            ServiceStatusDynamicSqlSupport.status);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "ServiceStatusDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "accountId", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR)
    })
    List<ServiceStatusDO> selectMany(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(ServiceStatusDO row) {
        return MyBatis3Utils.insert(this::insert, row, serviceStatus, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(serviceId).toPropertyWhenPresent("serviceId", row::getServiceId)
                        .map(status).toPropertyWhenPresent("status", row::getServiceId)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("ServiceStatusDOResult")
    Optional<ServiceStatusDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<ServiceStatusDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, serviceStatus, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(ServiceStatusDO row) {
        return update(c ->
                c.set(status).equalToWhenPresent(row::getStatus)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .where(accountId, isEqualTo(row::getAccountId))
                        .and(serviceId, isEqualTo(row::getServiceId))
        );
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, serviceStatus, completer);
    }

}


