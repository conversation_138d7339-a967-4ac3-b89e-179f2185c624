package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;

public final class UserCallbackTaskDynamicSqlSupport {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final UserCallbackTask task = new UserCallbackTask();

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = task.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = task.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = task.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = task.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> status = task.status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> taskId = task.taskId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> queueName = task.queueName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> nodeName = task.nodeName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> clusterId = task.clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> jobId = task.jobId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> action = task.action;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> parameters = task.parameters;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static SqlColumn<String> userName = task.userName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class UserCallbackTask extends AliasableSqlTable<UserCallbackTask> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<String> status = column("status", JDBCType.VARCHAR);

        public final SqlColumn<String> taskId = column("task_id", JDBCType.VARCHAR);

        public final SqlColumn<String> queueName = column("queue_name", JDBCType.VARCHAR);

        public final SqlColumn<String> nodeName = column("node_name", JDBCType.VARCHAR);

        public final SqlColumn<String> clusterId = column("cluster_id", JDBCType.VARCHAR);

        public final SqlColumn<String> jobId = column("job_id", JDBCType.VARCHAR);

        public final SqlColumn<String> action = column("action", JDBCType.VARCHAR);

        public final SqlColumn<String> parameters = column("parameters", JDBCType.LONGVARCHAR);

        public final SqlColumn<String> userName = column("user_name", JDBCType.VARCHAR);

        public UserCallbackTask() {
            super("t_chpc_user_callback_task", UserCallbackTask::new);
        }
    }
}