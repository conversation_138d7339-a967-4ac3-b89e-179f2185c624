package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.WorkflowRunDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.workflowRun;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.runId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.runUuid;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.callCaching;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.failureMode;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.inputs;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.outputs;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.workflowId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.deletedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.workspaceId;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.workflowName;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowRunDynamicSqlSupport.version;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface WorkflowRunMapper extends CommonCountMapper,
        CommonDeleteMapper, CommonInsertMapper<WorkflowRunDO>, CommonUpdateMapper{
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(WorkflowRunDO row) {
        return MyBatis3Utils.insert(this::insert, row, workflowRun, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(runId).toPropertyWhenPresent("runId", row::getRunId)
                        .map(runUuid).toPropertyWhenPresent("runUuid", row::getRunUuid)
                        .map(version).toPropertyWhenPresent("version", row::getVersion)
                        .map(name).toPropertyWhenPresent("name", row::getName)
                        .map(callCaching).toPropertyWhenPresent("callCaching", row::getCallCaching)
                        .map(inputs).toPropertyWhenPresent("inputs", row::getInputs)
                        .map(outputs).toPropertyWhenPresent("outputs", row::getOutputs)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(workflowId).toPropertyWhenPresent("workflowId", row::getWorkflowId)
                        .map(workspaceId).toPropertyWhenPresent("workspaceId", row::getWorkspaceId)
                        .map(workflowName).toPropertyWhenPresent("workflowName", row::getWorkflowName)
                        .map(status).toPropertyWhenPresent("status", row::getStatus)
                        .map(failureMode).toPropertyWhenPresent("failureMode", row::getFailureMode)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deletedTime,
            deleted,
            runId,
            runUuid,
            version,
            name,
            callCaching,
            inputs,
            outputs,
            accountId,
            workflowId,
            workflowName,
            workspaceId,
            status,
            failureMode
    );


    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "WorkflowRunDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted_time", property = "deletedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.TINYINT),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "call_caching", property = "callCaching", jdbcType = JdbcType.VARCHAR),
            @Result(column = "inputs", property = "inputs", jdbcType = JdbcType.VARCHAR),
            @Result(column = "outputs", property = "outputs", jdbcType = JdbcType.VARCHAR),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "workflow_id", property = "workflowId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "workspace_id", property = "workspaceId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "workflow_name", property = "workflowName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "run_id", property = "runId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "run_uuid", property = "runUuid", jdbcType = JdbcType.VARCHAR),
            @Result(column = "version", property = "version", jdbcType = JdbcType.BIGINT),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "failure_mode", property = "failureMode", jdbcType = JdbcType.VARCHAR)
    })
    List<WorkflowRunDO> selectMany(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("WorkflowRunDOResult")
    Optional<WorkflowRunDO> selectOne(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<WorkflowRunDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, workflowRun, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByWorkflowRunId(WorkflowRunDO row) {
        return update(c ->
                c.set(status).equalToWhenPresent(row::getStatus)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deletedTime).equalToWhenPresent(row::getDeletedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(outputs).equalToWhenPresent(row::getOutputs)
                        .where(runId, isEqualTo(row::getRunId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByWorkspaceId(WorkflowRunDO row) {
        return update(c ->
                c.set(status).equalToWhenPresent(row::getStatus)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(deletedTime).equalToWhenPresent(row::getDeletedTime)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(outputs).equalToWhenPresent(row::getOutputs)
                        .where(workspaceId, isEqualTo(row::getWorkspaceId))
                        .and(accountId, isEqualTo(row::getAccountId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, workflowRun, completer);
    }
}
