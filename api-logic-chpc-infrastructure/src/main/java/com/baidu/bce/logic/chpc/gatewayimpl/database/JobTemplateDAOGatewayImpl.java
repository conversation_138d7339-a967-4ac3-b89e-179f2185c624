package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.TemplateDO;
import com.baidu.bce.logic.chpc.database.mapper.TemplateMapper;
import com.baidu.bce.logic.chpc.gateway.JobTemplateDAOGateway;
import com.baidu.bce.logic.chpc.model.JobTemplate;
import jakarta.annotation.Resource;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.render.UpdateStatementProvider;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

import static com.baidu.bce.logic.chpc.database.mapper.local.TemplateDynamicSqlSupport.templateDO;

import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.update;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;


@Service
public class JobTemplateDAOGatewayImpl implements JobTemplateDAOGateway {

    @Resource
    private TemplateMapper templateMapper;


    @Override
    public JobTemplate findByTemplateNameAndClusterId(String templateName, String clusterId) {
        SelectStatementProvider selectStatementProvider = select(templateMapper.selectList)
                .from(templateDO)
                .where(templateDO.clusterId, isEqualTo(clusterId))
                .and(templateDO.name, isEqualTo(templateName))
                .and(templateDO.deleted, isEqualTo(false))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        TemplateDO templateDo = templateMapper.selectOne(selectStatementProvider).orElse(null);
        return BeanCopyUtil.copyObject(templateDo, JobTemplate::new);
    }

    @Override
    public Boolean insertTemplate(JobTemplate template) {
        TemplateDO templateDO = new TemplateDO();
        BeanCopyUtil.copyProperties(template, templateDO);
        templateDO.setDeleted(false);
        // 唯一索引，避免重复
        templateDO.setIsUnique(true);
        LocalDateTime now = LocalDateTime.now();
        templateDO.setCreateTime(now);
        templateDO.setUpdateTime(now);
        int i = templateMapper.insertSelective(templateDO);
        return i != 0;
    }

    @Override
    public List<JobTemplate> findTemplatesByCluster(String clusterId) {
        SelectStatementProvider selectStatementProvider = select(templateMapper.selectList)
                .from(templateDO)
                .where(templateDO.clusterId, isEqualTo(clusterId))
                .and(templateDO.deleted, isEqualTo(false))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        List<TemplateDO> templateDOS = templateMapper.selectMany(selectStatementProvider);
        // 将TemplateDO转型为JobTemplate
        return BeanCopyUtil.copyListProperties(templateDOS, JobTemplate::new);
    }

    @Override
    public Boolean updateTemplate(JobTemplate template) {
        TemplateDO templateDO = new TemplateDO();
        // name、clusterId别重复更新
        BeanCopyUtil.copyProperties(template, templateDO);
        templateDO.setUpdateTime(LocalDateTime.now());
        return templateMapper.updateByNameAndClusterSelective(templateDO) != 0;
    }

    @Override
    public Boolean deleteTemplate(String clusterId, String templateName) {

        // 将isUnique设置为null，这样删除后就可以创建重名的模版
        // 不能使用updateSelective, selective不能更新为null
        UpdateStatementProvider updateStatementProvider = update(templateDO)
                .set(templateDO.deleted).equalTo(true)
                .set(templateDO.isUnique).equalToNull()
                .set(templateDO.updateTime).equalTo(LocalDateTime.now())
                .where(templateDO.clusterId, isEqualTo(clusterId))
                .and(templateDO.name, isEqualTo(templateName))
                .and(templateDO.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);
        return templateMapper.update(updateStatementProvider) != 0;
    }

    @Override
    public void deleteAllJobTemplatesByCluster(String clusterId) {
        UpdateStatementProvider updateStatementProvider = update(templateDO)
                .set(templateDO.deleted).equalTo(true)
                .set(templateDO.isUnique).equalToNull()
                .set(templateDO.updateTime).equalTo(LocalDateTime.now())
                .where(templateDO.clusterId, isEqualTo(clusterId))
                .and(templateDO.deleted, isEqualTo(false))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        templateMapper.update(updateStatementProvider);
    }
}
