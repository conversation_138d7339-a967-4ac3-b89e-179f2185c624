package com.baidu.bce.logic.chpc.database.dataobject;

import static com.baidu.bce.logic.chpc.database.mapper.local.QueueDynamicSqlSupport.hasAutoScale;

import java.time.LocalDateTime;

import jakarta.annotation.Generated;

public class QueueDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String name;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String queueId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean isDefault;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean isAutoScale;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean hasAutoScale;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String defaultSpec;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String defaultImageId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String defaultUserData;

    // @Generated("org.mybatis.generator.api.MyBatisGenerator")
    // private String logicalZone;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String status;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String description;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String subnetId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterId() {
        return clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId == null ? null : clusterId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getName() {
        return name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getQueueId() {
        return queueId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setQueueId(String queueId) {
        this.queueId = queueId == null ? null : queueId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getIsDefault() {
        return isDefault;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getIsAutoScale() {
        return isAutoScale;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setIsAutoScale(Boolean isAutoScale) {
        this.isAutoScale = isAutoScale;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getHasAutoScale() {
        return hasAutoScale;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setHasAutoScale(Boolean hasAutoScale) {
        this.hasAutoScale = hasAutoScale;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDefaultSpec() {
        return defaultSpec;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDefaultSpec(String defaultSpec) {
        this.defaultSpec = defaultSpec == null ? null : defaultSpec.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDefaultImageId() {
        return defaultImageId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDefaultImageId(String defaultImageId) {
        this.defaultImageId = defaultImageId == null ? null : defaultImageId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDefaultUserData() {
        return defaultUserData;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDefaultUserData(String defaultUserData) {
        this.defaultUserData = defaultUserData == null ? null : defaultUserData.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getStatus() {
        return status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDescription() {
        return description;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    // @Generated("org.mybatis.generator.api.MyBatisGenerator")
    // public String getLogicalZone() {
    //     return logicalZone;
    // }

    // @Generated("org.mybatis.generator.api.MyBatisGenerator")
    // public void setLogicalZone(String logicalZone) {
    //     this.logicalZone = logicalZone == null ? null : logicalZone.trim();
    // }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getSubnetId() {
        return subnetId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setSubnetId(String subnetId) {
        this.subnetId = subnetId == null ? null : subnetId.trim();
    }

}