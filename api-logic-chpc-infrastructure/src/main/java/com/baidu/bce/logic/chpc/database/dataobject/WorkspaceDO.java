package com.baidu.bce.logic.chpc.database.dataobject;

import jakarta.annotation.Generated;
import java.time.LocalDateTime;

public class WorkspaceDO {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String workspaceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String name;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String description;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String bosBucket;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String status;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime deletedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getWorkspaceId() {
        return workspaceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setWorkspaceId(String workspaceId) {
        this.workspaceId = workspaceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")

    public String getName() {
        return name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setName(String name) {
        this.name = name;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getDescription() {
        return description;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDescription(String description) {
        this.description = description;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterId() {
        return clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getBosBucket() {
        return bosBucket;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setBosBucket(String bosBucket) {
        this.bosBucket = bosBucket;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getStatus() {
        return status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setStatus(String status) {
        this.status = status;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterName() {
        return clusterName;
    }
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getDeletedTime() {
        return deletedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeletedTime(LocalDateTime deletedTime) {
        this.deletedTime = deletedTime;
    }

}
