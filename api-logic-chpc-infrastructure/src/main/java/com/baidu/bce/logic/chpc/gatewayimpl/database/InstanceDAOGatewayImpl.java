package com.baidu.bce.logic.chpc.gatewayimpl.database;

import static com.baidu.bce.logic.chpc.database.mapper.local.InstanceDynamicSqlSupport.instance;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.database.dataobject.InstanceDO;
import com.baidu.bce.logic.chpc.database.mapper.InstanceMapper;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.model.Instance;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
@Service
public class InstanceDAOGatewayImpl implements InstanceDAOGateway {

    @Resource
    private InstanceMapper instanceMapper;


    @Override
    public Boolean batchInsert(List<Instance> instances) {

        List<InstanceDO> instanceDOS = BeanCopyUtil.copyListProperties(instances, InstanceDO::new)
                .stream()
                .peek(ins -> {
                    ins.setCreatedTime(LocalDateTime.now());
                    ins.setUpdatedTime(LocalDateTime.now());
                    ins.setDeleted(false);
                }).collect(Collectors.toList());
        
        Boolean res = true;
        for (InstanceDO instanceDO : instanceDOS) {
            // 检查是否存在相同 instance_id, deleted 为 0 或 1 的记录
            InstanceDO existing = findBy(instanceDO.getInstanceId(), true);
            if (existing != null) {
                // 存在则更新该记录
                res = res && instanceMapper.updateByInstanceId(instanceDO, false) != 0;
            } else {
                // 不存在则插入新记录
                res = res && instanceMapper.insert(instanceDO) != 0;
            }
        }

        return res;
    }

    @Override
    public List<Instance> findByClusterId(String clusterId) {
        return findBy(clusterId, null, null, null, null);
    }

    @Override
    public List<Instance> findByClusterIdIn(List<String> clusterIds) {
        if (CollectionUtils.isEmpty(clusterIds)) {
            return new ArrayList<>();
        }
        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.clusterId, isIn(clusterIds))
                .and(instance.deleted, isEqualTo(false))
                .orderBy(instance.createdTime.descending())
                .build().render(RenderingStrategies.MYBATIS3);

        List<InstanceDO> instanceDOS = instanceMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(instanceDOS, Instance::new);
    }

    @Override
    public List<Instance> findBy(String clusterId, String queueId,
                                 String instanceNodeType,
                                 String cosStackId, List<String> statusList) {

        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.clusterId, isEqualTo(clusterId))
                .and(instance.queueId, isEqualToWhenPresent(queueId))
                .and(instance.status, isInWhenPresent(statusList))
                .and(instance.cosStackId, isEqualToWhenPresent(cosStackId))
                .and(instance.deleted, isEqualTo(false))
                .and(instance.nodeType, isEqualToWhenPresent(instanceNodeType))
                .orderBy(instance.createdTime.descending())
                .build().render(RenderingStrategies.MYBATIS3);


        List<InstanceDO> instanceDOS = instanceMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(instanceDOS, Instance::new);
    }

    @Override
    public List<Instance> findByClusterIdAndQueueId(String clusterId, String queueId, int offset, int limit) {
        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.clusterId, isEqualTo(clusterId))
                .and(instance.queueId, isEqualTo(queueId))
                .and(instance.nodeType, isEqualTo(InstanceNodeType.COMPUTE.getType()))
                .and(instance.deleted, isEqualTo(false))
                .orderBy(instance.createdTime.descending())
                .limit(limit)
                .offset(offset)
                .build().render(RenderingStrategies.MYBATIS3);

        List<InstanceDO> instanceDOS = instanceMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(instanceDOS, Instance::new);
    }

    @Override
    public List<Instance> findAllByClusterIdAndQueueId(String clusterId, String queueId) {
        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.clusterId, isEqualTo(clusterId))
                .and(instance.queueId, isEqualTo(queueId))
                .and(instance.nodeType, isEqualTo(InstanceNodeType.COMPUTE.getType()))
                .and(instance.deleted, isEqualTo(false))
                .orderBy(instance.createdTime.descending())
                .build().render(RenderingStrategies.MYBATIS3);

        List<InstanceDO> instanceDOS = instanceMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(instanceDOS, Instance::new);
    }

    @Override
    public List<Instance> findAllByClusterIdAndQueueIdAndChargeType(
            String clusterId, String queueId, ChargeType chargeType
    ) {
        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.clusterId, isEqualTo(clusterId))
                .and(instance.queueId, isEqualTo(queueId))
                .and(instance.nodeType, isEqualTo(InstanceNodeType.COMPUTE.getType()))
                .and(instance.chargeType, isEqualTo(chargeType.name().toLowerCase()))
                .and(instance.deleted, isEqualTo(false))
                .orderBy(instance.createdTime.descending())
                .build().render(RenderingStrategies.MYBATIS3);

        List<InstanceDO> instanceDOS = instanceMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(instanceDOS, Instance::new);
    }

    @Override
    public Instance findBy(String instanceId) {

        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.instanceId, isEqualTo(instanceId))
                .and(instance.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);


        InstanceDO instanceDO = instanceMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(instanceDO, Instance::new);
    }

    public InstanceDO findBy(String instanceId, Boolean ignoreDeleted) {
        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.instanceId, isEqualTo(instanceId))
                .build().render(RenderingStrategies.MYBATIS3);

        return instanceMapper.selectOne(selectStatement).orElse(null);
    }

    @Override
    public Instance findByHostName(String hsotName) {

        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.hostName, isEqualTo(hsotName))
                .and(instance.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);


        InstanceDO instanceDO = instanceMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(instanceDO, Instance::new);
    }

    @Override
    public Instance findByHostNameIgnoreDeleted(String hsotName) {

        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.hostName, isEqualTo(hsotName))
                .build().render(RenderingStrategies.MYBATIS3);


        InstanceDO instanceDO = instanceMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(instanceDO, Instance::new);
    }

    @Override
    public List<Instance> findBy(List<String> instanceIds) {
        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.instanceId, isInWhenPresent(instanceIds))
                .and(instance.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<InstanceDO> instanceDOS = instanceMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(instanceDOS, Instance::new);
    }

    @Override
    public List<Instance> findAllByClusterIdAndInstanceIds(String clusterId, List<String> instanceIds) {
        if (CollectionUtils.isEmpty(instanceIds)) {
            return Collections.emptyList();
        }

        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.instanceId, isIn(instanceIds))
                .and(instance.clusterId, isEqualTo(clusterId))
                .and(instance.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<InstanceDO> instanceDOS = instanceMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(instanceDOS, Instance::new);
    }

    @Override
    public List<Instance> findAllByHostNames(List<String> instanceNames) {
        if (CollectionUtils.isEmpty(instanceNames)) {
            return Collections.emptyList();
        }
        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.hostName, isIn(instanceNames))
                .and(instance.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<InstanceDO> instanceDOS = instanceMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(instanceDOS, Instance::new);
    }

    @Override
    public List<Instance> findAllByClusterIdAndHostNames(String clusterId, List<String> hostnames) {
        if (CollectionUtils.isEmpty(hostnames)) {
            return Collections.emptyList();
        }
        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.hostName, isIn(hostnames))
                .and(instance.clusterId, isEqualTo(clusterId))
                .and(instance.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);

        List<InstanceDO> instanceDOS = instanceMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(instanceDOS, Instance::new);
    }


    @Override
    public Instance findMasterInstance(String clusterId) {

        SelectStatementProvider selectStatement = select(InstanceMapper.selectList)
                .from(instance)
                .where(instance.clusterId, isEqualTo(clusterId))
                .and(instance.nodeType, isEqualTo(InstanceNodeType.MASTER.getType()))
                .and(instance.deleted, isEqualTo(false))
                .build().render(RenderingStrategies.MYBATIS3);


        InstanceDO instanceDO = instanceMapper.selectOne(selectStatement).orElse(null);
        if (instanceDO == null) {
            return null;
        }

        return BeanCopyUtil.copyObject(instanceDO, Instance::new);
    }

    @Override
    public Long count(String clusterId, String queueId,
                      String instanceId, String hostname, String status, String nodeType) {

        SelectStatementProvider selectStatement =
                select(SqlBuilder.count())
                        .from(instance)
                        .where(instance.instanceId, isEqualToWhenPresent(instanceId))
                        .and(instance.hostName, isEqualToWhenPresent(hostname))
                        .and(instance.status, isEqualToWhenPresent(status))
                        .and(instance.queueId, isEqualToWhenPresent(queueId))
                        .and(instance.clusterId, isEqualToWhenPresent(clusterId))
                        .and(instance.nodeType, isEqualToWhenPresent(nodeType))
                        .and(instance.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);

        return instanceMapper.count(selectStatement);
    }

    @Override
    public Long countComputeNode(String queueId, String nodeType) {
        return count(null, queueId, null, null, null, nodeType);
    }


    @Override
    public Boolean updateStatus(String instanceId, String oosExecutionId, String status) {

        InstanceDO instanceDO = new InstanceDO();
        instanceDO.setUpdatedTime(LocalDateTime.now());
        instanceDO.setInstanceId(instanceId);
        instanceDO.setStatus(status);
        if (!"".equals(oosExecutionId)) {
            instanceDO.setOosExecutionId(oosExecutionId);
        }
        return instanceMapper.updateByInstanceId(instanceDO) != 0;
    }

    @Override
    public Boolean updateSchedulerConfig(String clusterId, String instanceType, String schedulerIp, String schedulerHost) {

        InstanceDO instanceDO = new InstanceDO();
        instanceDO.setUpdatedTime(LocalDateTime.now());
        instanceDO.setClusterId(clusterId);
        instanceDO.setNodeType(instanceType);
        instanceDO.setSchedulerIp(schedulerIp);
        instanceDO.setSchedulerHost(schedulerHost);
        return instanceMapper.updateByClusterIDAndNodetype(instanceDO) != 0;
    }


    @Override
    public Boolean update(Instance instance) {

        InstanceDO instanceDO = BeanCopyUtil.copyObject(instance, InstanceDO::new);
        instanceDO.setUpdatedTime(LocalDateTime.now());
        return instanceMapper.updateByInstanceId(instanceDO) != 0;
    }

    @Override
    public Boolean updateByStatus(Instance instance, List<String> statusList) {

        InstanceDO instanceDO = BeanCopyUtil.copyObject(instance, InstanceDO::new);
        instanceDO.setUpdatedTime(LocalDateTime.now());
        return instanceMapper.updateByInstanceIdAndStatus(instanceDO, statusList) != 0;
    }

    @Override
    public Boolean delete(String instanceId) {

        InstanceDO instanceDO = new InstanceDO();
        instanceDO.setUpdatedTime(LocalDateTime.now());
        instanceDO.setInstanceId(instanceId);
        instanceDO.setStatus(InstanceStatus.DELETED.nameLowerCase());
        instanceDO.setDeleted(true);

        return instanceMapper.updateByInstanceId(instanceDO) != 0;
    }
}
