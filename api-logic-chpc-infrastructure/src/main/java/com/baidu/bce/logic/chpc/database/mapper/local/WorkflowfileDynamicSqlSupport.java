package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;

public class WorkflowfileDynamicSqlSupport {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final Workflowfile workflowfile = new Workflowfile();
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = workflowfile.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> workflowId = workflowfile.workflowId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> type = workflowfile.type;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> path = workflowfile.path;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> version = workflowfile.version;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> content = workflowfile.content;


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = workflowfile.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = workflowfile.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = workflowfile.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> deletedTime = workflowfile.deletedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class Workflowfile extends AliasableSqlTable<Workflowfile> {

        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedTime = column("deleted_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<String> name = column("name", JDBCType.VARCHAR);
        public final SqlColumn<String> workflowId = column("workflow_id", JDBCType.VARCHAR);
        public final SqlColumn<String> type = column("type", JDBCType.VARCHAR);
        public final SqlColumn<Long> version = column("version", JDBCType.VARCHAR);
        public final SqlColumn<String> path = column("path", JDBCType.VARCHAR);
        public final SqlColumn<String> content = column("content", JDBCType.VARCHAR);


        public Workflowfile() {
            super("t_chpc_workflowfile", Workflowfile::new);
        }


    }
}
