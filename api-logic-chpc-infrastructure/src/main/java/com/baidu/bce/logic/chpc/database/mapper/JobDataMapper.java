package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.JobDataDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.jobCpu;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.jobStatus;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.jobMemory;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.runTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.poolId;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.jobId;
import static com.baidu.bce.logic.chpc.database.mapper.local.JobDataDynamicSqlSupport.jobData;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface JobDataMapper extends CommonCountMapper, CommonDeleteMapper,
        CommonInsertMapper<JobDataDO>, CommonUpdateMapper {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            jobCpu,
            accountId,
            jobStatus,
            jobMemory,
            runTime,
            poolId,
            jobId);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "JobDataDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "job_cpu", property = "jobCpu", jdbcType = JdbcType.VARCHAR),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_status", property = "jobStatus", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_memory", property = "jobMemory", jdbcType = JdbcType.VARCHAR),
            @Result(column = "run_time", property = "runTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "job_id", property = "jobId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "pool_id", property = "poolId", jdbcType = JdbcType.VARCHAR),
    })
    List<JobDataDO> selectMany(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(JobDataDO row) {
        return MyBatis3Utils.insert(this::insert, row, jobData, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(jobCpu).toPropertyWhenPresent("jobCpu", row::getJobCpu)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(jobStatus).toPropertyWhenPresent("jobStatus", row::getJobStatus)
                        .map(jobMemory).toPropertyWhenPresent("jobMemory", row::getJobMemory)
                        .map(runTime).toPropertyWhenPresent("runTime", row::getRunTime)
                        .map(jobId).toPropertyWhenPresent("jobId", row::getJobId)
                        .map(poolId).toPropertyWhenPresent("poolId", row::getJobId)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("JobDataDOResult")
    Optional<JobDataDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<JobDataDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, jobData, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByJobId(JobDataDO row) {
        return update(c ->
                c.set(jobStatus).equalToWhenPresent(row::getJobStatus)
                        .set(runTime).equalToWhenPresent(row::getRunTime)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .where(jobId, isEqualTo(row::getJobId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByAccountId(JobDataDO row) {
        return update(c ->
                c.set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .where(accountId, isEqualTo(row::getAccountId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, jobData, completer);
    }
}
