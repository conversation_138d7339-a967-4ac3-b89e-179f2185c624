package com.baidu.bce.logic.chpc.bcm;

import jakarta.annotation.Resource;

import org.springframework.stereotype.Service;
import com.baidu.bce.logic.chpc.bcm.client.BcmNioClient;
import com.baidu.bce.logic.chpc.bcm.gateway.BcmGateway;

import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BcmGatewayImpl implements BcmGateway {

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private BcmNioClient bcmNioClient;

    @Override
    public BcmBatchQueryResponse batchQuery(BcmBatchQueryRequest request) {
        return bcmNioClient.batchQuery(request, request.getUserId());
    }

    @Override
    public BcmPartialQueryResponse partialQuery(BcmPartialQueryRequest request) {
        return bcmNioClient.partialQuery(request, request.getUserId());
    }

}
