package com.baidu.bce.logic.chpc.cos;

import jakarta.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baidu.bce.internalsdk.cos.model.ClusterResponse;
import com.baidu.bce.logic.chpc.cos.client.CosNioClient;
import com.baidu.bce.logic.chpc.cos.gateway.CosGateway;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class CosGatewayImpl implements CosGateway {

    @Resource
    IamLogicService iamLogicService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private CosNioClient cosNioClient;

    @Override
    public CreateStackResponse createStack(CreateStackRequest createStackRequest) {
        return cosNioClient.createStack(createStackRequest, createStackRequest.getUserId());
    }

    @Override
    public StackDetail getStack(String stackId) {
        return cosNioClient.getStack(stackId, LogicUserService.getAccountId());
    }

    @Override
    public GetStackListResponse getStackList(GetStackListRequest request) {
        return cosNioClient.getStackList(request, LogicUserService.getAccountId());
    }

    /**
     * {@inheritDoc}
     *
     * 删除指定的堆栈。如果堆栈ID为null，则返回一个空的DeleteStackResponse对象。否则，使用新建的{@link LogicUserService}
     * 实例调用{@code deleteStack}方法来删除指定的堆栈。
     *
     * @param stackId 要删除的堆栈ID，可以为null
     * @return 包含删除结果的DeleteStackResponse对象，如果堆栈ID为null，则返回一个空的DeleteStackResponse对象
     * @throws CloudRuntimeException 如果发生云端运行时错误
     */
    @Override
    public DeleteStackResponse deleteStack(String stackId) {
        if (stackId == null) {
            return new DeleteStackResponse();
        }
        return cosNioClient.deleteStack(stackId, LogicUserService.getAccountId());
    }

    @Override
    public ClusterResponse getClusterStatus(String stackId) {
        return null;
    }
}
