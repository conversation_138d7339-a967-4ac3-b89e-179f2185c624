package com.baidu.bce.logic.chpc.gatewayimpl.database;

import static com.baidu.bce.logic.chpc.database.mapper.local.ClusterDynamicSqlSupport.cluster;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isInWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.annotation.Resource;

import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.database.dataobject.ClusterDO;
import com.baidu.bce.logic.chpc.database.mapper.ClusterMapper;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
@Service
public class ClusterDAOGatewayImpl implements ClusterDAOGateway {

    @Resource
    private ClusterMapper clusterMapper;

    @Override
    public Long count(String clusterName, String accountID) {
        SelectStatementProvider selectStatement = select(SqlBuilder.count())
                .from(cluster)
                .where(cluster.accountId, isEqualTo(accountID))
                .and(cluster.name, isEqualTo(clusterName))
                .and(cluster.deleted, isEqualTo(false))
                .build()
                .render(RenderingStrategies.MYBATIS3);
        return clusterMapper.count(selectStatement);
    }

    @Override
    public Long count(String clusterId, String accountId, String status) {
        SelectStatementProvider selectStatement = select(SqlBuilder.count())
                .from(cluster)
                .where(cluster.clusterId, isEqualToWhenPresent(clusterId))
                .and(cluster.deleted, isEqualTo(false))
                .and(cluster.accountId, isEqualToWhenPresent(accountId))
                .and(cluster.status, isEqualToWhenPresent(status))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return clusterMapper.count(selectStatement);
    }

    @Override
    public Cluster findByClusterId(String clusterId, String accountId) {

        SelectStatementProvider selectStatement =
                select(ClusterMapper.selectList)
                        .from(cluster)
                        .where(cluster.clusterId, isEqualTo(clusterId))
                        .and(cluster.deleted, isEqualTo(false))
                        .and(cluster.accountId, isEqualToWhenPresent(accountId))
                        .build().render(RenderingStrategies.MYBATIS3);


        ClusterDO clusterDO = clusterMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(clusterDO, Cluster::new);
    }

    @Override
    public Cluster findByClusterIdAll(String clusterId, String accountId) {

        SelectStatementProvider selectStatement =
                select(ClusterMapper.selectList)
                        .from(cluster)
                        .where(cluster.clusterId, isEqualTo(clusterId))
                        .and(cluster.status, isNotEqualTo("deleted"))
                        .and(cluster.accountId, isEqualToWhenPresent(accountId))
                        .build().render(RenderingStrategies.MYBATIS3);


        ClusterDO clusterDO = clusterMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(clusterDO, Cluster::new);
    }

    @Override
    public List<Cluster> findByAccountId(String accountId) {

        SelectStatementProvider selectStatement =
                select(ClusterMapper.selectList)
                        .from(cluster)
                        .where(cluster.accountId, isEqualTo(accountId))
                        .and(cluster.deleted, isEqualTo(false))
                        .orderBy(cluster.createdTime.descending())
                        .build().render(RenderingStrategies.MYBATIS3);

        List<ClusterDO> clusterDOS = clusterMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(clusterDOS, Cluster::new);
    }

    @Override
    public List<Cluster> findByAccountIdAll(String accountId) {

        SelectStatementProvider selectStatement =
                select(ClusterMapper.selectList)
                        .from(cluster)
                        .where(cluster.accountId, isEqualTo(accountId))
                        .and(cluster.status, isNotEqualTo("deleted"))
                        .orderBy(cluster.createdTime.descending())
                        .build().render(RenderingStrategies.MYBATIS3);

        List<ClusterDO> clusterDOS = clusterMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(clusterDOS, Cluster::new);
    }

    @Override
    public Boolean insert(Cluster cluster) {
        ClusterDO clusterDO = new ClusterDO();
        BeanUtils.copyProperties(cluster, clusterDO);
        clusterDO.setDeleted(false);
        clusterDO.setCreatedTime(LocalDateTime.now());
        clusterDO.setUpdatedTime(LocalDateTime.now());
        return clusterMapper.insertSelective(clusterDO) != 0;
    }

    @Override
    public List<Cluster> listByStatus(List<String> statusList) {

        SelectStatementProvider selectStatement =
                select(ClusterMapper.selectList)
                        .from(cluster)
                        .where(cluster.status, isInWhenPresent(statusList))
                        .and(cluster.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);

        List<ClusterDO> clusterDOS = clusterMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(clusterDOS, Cluster::new);
    }

    @Override
    public void updateClusterStatus(String clusterId, String status) {

        ClusterDO clusterDO = new ClusterDO();
        clusterDO.setClusterId(clusterId);
        clusterDO.setUpdatedTime(LocalDateTime.now());
        clusterDO.setStatus(status);
        if (ClusterStatus.ACTIVE.nameLowerCase().equalsIgnoreCase(status)) {
            clusterDO.setCreateDone(true);
        }
        clusterMapper.updateByClusterId(clusterDO);
    }

    @Override
    public void updateClusterPassword(String clusterId, String password) {

        ClusterDO clusterDO = new ClusterDO();
        clusterDO.setClusterId(clusterId);
        clusterDO.setUpdatedTime(LocalDateTime.now());
        clusterDO.setPassword(password);
        clusterMapper.updateByClusterId(clusterDO);
    }


    @Override
    public void updateClusterWithErrorMessage(String clusterId, String status, String message) {

        ClusterDO clusterDO = new ClusterDO();
        clusterDO.setClusterId(clusterId);
        clusterDO.setUpdatedTime(LocalDateTime.now());
        clusterDO.setStatus(status);
        clusterDO.setErrorMessage(message);
        clusterMapper.updateErrMsgByClusterId(clusterDO);
    }


    @Override
    public void updatesoftwareDirByClusterId(String clusterId, String softwareDir) {

        ClusterDO clusterDO = new ClusterDO();
        clusterDO.setClusterId(clusterId);
        clusterDO.setUpdatedTime(LocalDateTime.now());
        clusterDO.setSoftwareDir(softwareDir);
        clusterMapper.updatesoftwareDirByClusterId(clusterDO);
    }

    @Override
    public void updateClusterHeartbeat(String clusterId, Long heartbeat) {
        ClusterDO clusterDO = new ClusterDO();
        clusterDO.setClusterId(clusterId);
        clusterDO.setHeartbeat(heartbeat);
        clusterDO.setUpdatedTime(LocalDateTime.now());
        clusterMapper.updateHeartbeatByClusterId(clusterDO);
    }

    @Override
    public Boolean update(Cluster cluster) {

        ClusterDO clusterDO = BeanCopyUtil.copyObject(cluster, ClusterDO::new);
        clusterDO.setUpdatedTime(LocalDateTime.now());
        return clusterMapper.updateByClusterId(clusterDO) != 0;
    }

    @Override
    public Boolean delete(String clusterId) {

        LocalDateTime now = LocalDateTime.now();
        ClusterDO clusterDO = new ClusterDO();
        clusterDO.setClusterId(clusterId);
        clusterDO.setUpdatedTime(now);
        clusterDO.setStatus(ClusterStatus.DELETED.nameLowerCase());
        clusterDO.setDeleteTime(now);
        clusterDO.setDeleted(true);

        return clusterMapper.updateByClusterId(clusterDO) != 0;
    }

    @Override
    public void deleteWithStatus(String clusterId, ClusterStatus status) {

        ClusterDO clusterDO = new ClusterDO();
        clusterDO.setClusterId(clusterId);
        clusterDO.setUpdatedTime(LocalDateTime.now());
        clusterDO.setStatus(status.nameLowerCase());
        // clusterDO.setDeleted(true);

        clusterMapper.updateByClusterId(clusterDO);
    }


}
