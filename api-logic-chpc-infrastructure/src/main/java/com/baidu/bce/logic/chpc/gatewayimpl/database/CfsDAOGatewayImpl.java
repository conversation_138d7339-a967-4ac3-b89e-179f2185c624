package com.baidu.bce.logic.chpc.gatewayimpl.database;

import static com.baidu.bce.logic.chpc.database.mapper.local.CfsDynamicSqlSupport.cfs;
import static org.mybatis.dynamic.sql.SqlBuilder.count;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

import java.time.LocalDateTime;
import java.util.List;

import jakarta.annotation.Resource;

import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.CfsDO;
import com.baidu.bce.logic.chpc.database.mapper.CfsMapper;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.model.Cfs;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
@Service
@Slf4j
public class CfsDAOGatewayImpl implements CfsDAOGateway {

    @Resource
    private CfsMapper cfsMapper;

    @Override
    public Boolean insert(Cfs cfs) {

        CfsDO cfsDO = BeanCopyUtil.copyObject(cfs, CfsDO::new);
        cfsDO.setDeleted(false);
        cfsDO.setCreatedTime(LocalDateTime.now());
        cfsDO.setUpdatedTime(LocalDateTime.now());
        return cfsMapper.insert(cfsDO) != 0;
    }

    @Override
    public Long countByClusterId(String clusterId, String cfsId) {

        SelectStatementProvider selectStatement = select(count())
                .from(cfs)
                .where(cfs.clusterId, isEqualTo(clusterId))
                .and(cfs.cfsId, isEqualTo(cfsId))
                .build().render(RenderingStrategies.MYBATIS3);

        return cfsMapper.count(selectStatement);
    }

    @Override
    public Long countByCluster(String clusterId) {

        SelectStatementProvider selectStatement = select(count())
                .from(cfs)
                .where(cfs.clusterId, isEqualTo(clusterId))
                .build().render(RenderingStrategies.MYBATIS3);

        return cfsMapper.count(selectStatement);
    }


    /**
     * {@inheritDoc}
     * 根据集群ID和CFS ID查找Cfs列表，并返回一个包含Cfs对象的List。
     * 如果cfsId为空或null，则不添加该条件到SQL语句中。
     *
     * @param clusterId 集群ID，不能为空或null
     * @param cfsId     CFS ID，可以为空或null
     * @return 包含Cfs对象的List，如果没有找到符合条件的Cfs，则返回一个空List
     * @throws RuntimeException 当集群ID或CFS ID为空或null时抛出RuntimeException
     */
    @Override
    public List<Cfs> findBy(String clusterId, String cfsId) {

        SelectStatementProvider selectStatement =
                select(CfsMapper.selectList)
                        .from(cfs)
                        .where(cfs.clusterId, isEqualTo(clusterId))
                        .and(cfs.cfsId, isEqualToWhenPresent(cfsId))
                        .and(cfs.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);

        log.info("select statement: {}", selectStatement);
        List<CfsDO> cfsDOS = cfsMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(cfsDOS, Cfs::new);
    }

    @Override
    public Boolean update(Cfs cfs) {

        CfsDO cfsDO = BeanCopyUtil.copyObject(cfs, CfsDO::new);
        cfsDO.setUpdatedTime(LocalDateTime.now());
        return cfsMapper.updateByClusterIdAndCfsId(cfsDO) != 0;
    }

    @Override
    public Boolean updateByClusterId(String clusterId, String mountTarget, String mountDir, String mountOption) {

        CfsDO cfsDO = new CfsDO();
        cfsDO.setClusterId(clusterId);
        cfsDO.setMountTarget(mountTarget);
        cfsDO.setMountDir(mountDir);
        if (mountOption != null && !"".equals(mountOption)) {
            cfsDO.setMountOption(mountOption);
        }
        cfsDO.setUpdatedTime(LocalDateTime.now());
        cfsDO.setDeleted(false);
        return cfsMapper.updateByClusterId(cfsDO) != 0;
    }


    @Override
    public Boolean delete(String clusterId, String cfsId) {

        CfsDO cfsDO = new CfsDO();
        cfsDO.setCfsId(cfsId);
        cfsDO.setClusterId(clusterId);
        cfsDO.setUpdatedTime(LocalDateTime.now());
        cfsDO.setDeleted(true);

        return cfsMapper.updateByClusterIdAndCfsId(cfsDO) != 0;
    }

    /**
     * {@inheritDoc}
     * 根据挂载信息删除文件系统，返回是否成功的布尔值。
     *
     * @param clusterId 集群ID，不能为空
     * @param cfsId 文件系统ID，不能为空
     * @param mountTarget 挂载目标，可以为空
     * @param mountDir 挂载目录，可以为空
     * @return 如果删除成功则返回true，否则返回false
     * @throws RuntimeException 当任何一个参数为空时抛出异常
     */
    @Override
    public Boolean deleteByMountInfo(String clusterId, String cfsId, String mountTarget, String mountDir) {

        CfsDO cfsDO = new CfsDO();
        cfsDO.setCfsId(cfsId);
        cfsDO.setClusterId(clusterId);
        cfsDO.setMountTarget(mountTarget);
        cfsDO.setMountDir(mountDir);
        cfsDO.setUpdatedTime(LocalDateTime.now());
        cfsDO.setDeleted(true);

        return cfsMapper.updateByMountInfo(cfsDO) != 0;
    }
}
