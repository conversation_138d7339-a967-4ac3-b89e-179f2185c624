package com.baidu.bce.logic.chpc.database.mapper.local;

import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;

public class OsDynamicSqlSupport {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final Os os = new Os();

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Long> id = os.id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<Boolean> deleted = os.deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> schedulerType = os.schedulerType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> schedulerVersion = os.schedulerVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> createdTime = os.createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<LocalDateTime> updatedTime = os.updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> osArch = os.osArch;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> osName = os.osName;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final SqlColumn<String> osVersion = os.osVersion;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public static final class Os extends AliasableSqlTable<OsDynamicSqlSupport.Os> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Boolean> deleted = column("deleted", JDBCType.BIT);

        public final SqlColumn<LocalDateTime> createdTime = column("created_time", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedTime = column("updated_time", JDBCType.TIMESTAMP);

        public final SqlColumn<String> schedulerType = column("scheduler_type", JDBCType.VARCHAR);

        public final SqlColumn<String> schedulerVersion = column("scheduler_version", JDBCType.VARCHAR);

        public final SqlColumn<String> osArch = column("os_arch", JDBCType.VARCHAR);

        public final SqlColumn<String> osName = column("os_name", JDBCType.VARCHAR);

        public final SqlColumn<String> osVersion = column("os_version", JDBCType.VARCHAR);

        public Os() {
            super("t_chpc_os", OsDynamicSqlSupport.Os::new);
        }
    }
}
