package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.TagsDO;
import jakarta.annotation.Generated;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.resourceId;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.name;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.tagKey;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.tagType;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.tagValue;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.tags;
import static com.baidu.bce.logic.chpc.database.mapper.local.TagsDynamicSqlSupport.updatedTime;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface TagsMapper extends CommonCountMapper, CommonDeleteMapper,
        CommonInsertMapper<TagsDO>, CommonUpdateMapper {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            createdTime,
            updatedTime,
            deleted,
            clusterId,
            resourceId,
            name,
            accountId,
            tagKey,
            tagValue,
            tagType);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "tagsDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "resource_id", property = "resourceId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "name", property = "name", jdbcType = JdbcType.VARCHAR),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "tag_key", property = "tagKey", jdbcType = JdbcType.VARCHAR),
            @Result(column = "tag_value", property = "tagValue", jdbcType = JdbcType.VARCHAR),
            @Result(column = "tag_type", property = "tagType", jdbcType = JdbcType.VARCHAR)
    })
    List<TagsDO> selectMany(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(TagsDO row) {
        return MyBatis3Utils.insert(this::insert, row, tags,
                c -> c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                        .map(resourceId).toPropertyWhenPresent("resourceId", row::getResourceId)
                        .map(name).toPropertyWhenPresent("name", row::getName)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(tagKey).toPropertyWhenPresent("tagKey", row::getTagKey)
                        .map(tagValue).toPropertyWhenPresent("tagValue", row::getTagValue)
                        .map(tagType).toPropertyWhenPresent("tagType", row::getTagType));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("tagsDOResult")
    default List<TagsDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, tags, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("tagsDOResult")
    default List<TagsDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, tags, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("tagsDOResult")
    Optional<TagsDO> selectOne(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateTag(TagsDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(tagKey).equalToWhenPresent(row::getTagKey)
                .set(tagValue).equalToWhenPresent(row::getTagValue)
                .set(name).equalToWhenPresent(row::getName)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .where(clusterId, isEqualTo(row::getClusterId))
                .and(tagType, isEqualTo(row::getTagType)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateTagByClusterIdAndName(TagsDO row) {
        return update(c -> c.set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(tagKey).equalToWhenPresent(row::getTagKey)
                .set(tagValue).equalToWhenPresent(row::getTagValue)
                .set(deleted).equalToWhenPresent(row::getDeleted)
                .where(clusterId, isEqualTo(row::getClusterId))
                .and(tagType, isEqualTo(row::getTagType))
                .and(name, isEqualTo(row::getName)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, tags, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, tags, completer);
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByClusterId(TagsDO row) {
        return delete(c ->
                c.where(clusterId, isEqualTo(row::getClusterId))
                        .and(tagType, isEqualTo(row::getTagType)));
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByClusterIdAndName(TagsDO row) {
        return delete(c ->
                c.where(clusterId, isEqualTo(row::getClusterId))
                        .and(tagType, isEqualTo(row::getTagType))
                        .and(name, isEqualTo(row::getName)));
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByClusterIdAndKey(TagsDO row) {
        return delete(c ->
                c.where(clusterId, isEqualTo(row::getClusterId))
                        .and(tagType, isEqualTo(row::getTagType))
                        .and(tagKey, isEqualTo(row::getTagKey)));
    }

}
