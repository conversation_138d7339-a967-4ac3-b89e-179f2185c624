package com.baidu.bce.logic.chpc.database.mapper;

import com.baidu.bce.logic.chpc.database.dataobject.AutoScalingDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonInsertMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.queueName;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.accountId;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.asId;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.asName;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.autoScaling;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.clusterId;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.clusterName;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.createdTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.cudaVersion;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.cudnnVersion;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.dataDiskList;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.deleted;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.enableAutoGrow;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.enableAutoShrink;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.excludeNodes;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.gpuDriverVersion;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.hostnamePrefix;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.queueId;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.id;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.maxNodesInQueue;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.maxScalePerCycle;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.minNodesInQueue;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.securityGroupId;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.spec;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.status;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.imageId;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.subnetId;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.systemDiskSize;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.systemDiskType;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.updatedTime;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.zoneName;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.cpuThreadConfig;
import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.numaConfig;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

@Mapper
public interface AutoScalingMapper extends
        CommonCountMapper, CommonDeleteMapper, CommonInsertMapper<AutoScalingDO>, CommonUpdateMapper {

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    BasicColumn[] selectList = BasicColumn.columnList(
            id,
            asId,
            asName,
            accountId,
            createdTime,
            updatedTime,
            deleted,
            clusterId,
            clusterName,
            queueId,
            queueName,
            zoneName,
            subnetId,
            securityGroupId,
            maxNodesInQueue,
            minNodesInQueue,
            enableAutoGrow,
            enableAutoShrink,
            spec,
            systemDiskSize,
            systemDiskType,
            excludeNodes,
            status,
            imageId,
            maxScalePerCycle,
            hostnamePrefix,
            cudaVersion,
            gpuDriverVersion,
            cudnnVersion,
            dataDiskList,
            cpuThreadConfig,
            numaConfig);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @Results(id = "AutoScalingDOResult", value = {
            @Result(column = "id", property = "id", jdbcType = JdbcType.BIGINT, id = true),
            @Result(column = "as_id", property = "asId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "as_name", property = "asName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "account_id", property = "accountId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "created_time", property = "createdTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "updated_time", property = "updatedTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deleted", property = "deleted", jdbcType = JdbcType.BIT),
            @Result(column = "cluster_id", property = "clusterId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cluster_name", property = "clusterName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "group_id", property = "queueId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "queue_name", property = "queueName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "zone_name", property = "zoneName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "subnet_id", property = "subnetId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "security_group_id", property = "securityGroupId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "max_nodes_in_queue", property = "maxNodesInQueue", jdbcType = JdbcType.INTEGER),
            @Result(column = "min_nodes_in_queue", property = "minNodesInQueue", jdbcType = JdbcType.INTEGER),
            @Result(column = "enable_auto_grow", property = "enableAutoGrow", jdbcType = JdbcType.BIT),
            @Result(column = "enable_auto_shrink", property = "enableAutoShrink", jdbcType = JdbcType.BIT),
            @Result(column = "spec", property = "spec", jdbcType = JdbcType.VARCHAR),
            @Result(column = "system_disk_size", property = "systemDiskSize", jdbcType = JdbcType.INTEGER),
            @Result(column = "system_disk_type", property = "systemDiskType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "exclude_nodes", property = "excludeNodes", jdbcType = JdbcType.LONGVARCHAR),
            @Result(column = "status", property = "status", jdbcType = JdbcType.VARCHAR),
            @Result(column = "image_id", property = "imageId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "max_scale_per_cycle", property = "maxScalePerCycle", jdbcType = JdbcType.INTEGER),
            @Result(column = "hostname_prefix", property = "hostnamePrefix", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cuda_version", property = "cudaVersion", jdbcType = JdbcType.VARCHAR),
            @Result(column = "gpu_driver_version", property = "gpuDriverVersion", jdbcType = JdbcType.VARCHAR),
            @Result(column = "cudnn_version", property = "cudnnVersion", jdbcType = JdbcType.VARCHAR),
            @Result(column = "data_disk_list", property = "dataDiskList", jdbcType = JdbcType.LONGNVARCHAR),
            @Result(column = "cpu_thread_config", property = "cpuThreadConfig", jdbcType = JdbcType.VARCHAR),
            @Result(column = "numa_config", property = "numaConfig", jdbcType = JdbcType.VARCHAR)
    })
    List<AutoScalingDO> selectMany(SelectStatementProvider selectStatement);

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, autoScaling, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, autoScaling, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByPrimaryKey(Long key) {
        return delete(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insert(AutoScalingDO row) {
        return MyBatis3Utils.insert(this::insert, row, autoScaling, c ->
                c.map(id).toProperty("id")
                        .map(asId).toProperty("asId")
                        .map(asName).toProperty("asName")
                        .map(accountId).toProperty("accountId")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(clusterId).toProperty("clusterId")
                        .map(clusterName).toProperty("clusterName")
                        .map(queueId).toProperty("queueId")
                        .map(queueName).toProperty("queueName")
                        .map(zoneName).toProperty("zoneName")
                        .map(subnetId).toProperty("subnetId")
                        .map(securityGroupId).toProperty("securityGroupId")
                        .map(maxNodesInQueue).toProperty("maxNodesInQueue")
                        .map(minNodesInQueue).toProperty("minNodesInQueue")
                        .map(enableAutoGrow).toProperty("enableAutoGrow")
                        .map(enableAutoShrink).toProperty("enableAutoShrink")
                        .map(spec).toProperty("spec")
                        .map(systemDiskSize).toProperty("systemDiskSize")
                        .map(systemDiskType).toProperty("systemDiskType")
                        .map(excludeNodes).toProperty("excludeNodes")
                        .map(status).toProperty("status")
                        .map(imageId).toProperty("imageId")
                        .map(maxScalePerCycle).toProperty("maxScalePerCycle")
                        .map(hostnamePrefix).toProperty("hostnamePrefix")
                        .map(cudaVersion).toProperty("cudaVersion")
                        .map(gpuDriverVersion).toProperty("gpuDriverVersion")
                        .map(cudnnVersion).toProperty("cudnnVersion")
                        .map(dataDiskList).toProperty("dataDiskList")
                        .map(cpuThreadConfig).toProperty("cpuThreadConfig")
                        .map(numaConfig).toProperty("numaConfig")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertMultiple(Collection<AutoScalingDO> records) {
        return MyBatis3Utils.insertMultiple(this::insertMultiple, records, autoScaling, c ->
                c.map(id).toProperty("id")
                        .map(asId).toProperty("asId")
                        .map(asName).toProperty("asName")
                        .map(accountId).toProperty("accountId")
                        .map(createdTime).toProperty("createdTime")
                        .map(updatedTime).toProperty("updatedTime")
                        .map(deleted).toProperty("deleted")
                        .map(clusterId).toProperty("clusterId")
                        .map(clusterName).toProperty("clusterName")
                        .map(queueId).toProperty("queueId")
                        .map(queueName).toProperty("queueName")
                        .map(zoneName).toProperty("zoneName")
                        .map(subnetId).toProperty("subnetId")
                        .map(securityGroupId).toProperty("securityGroupId")
                        .map(maxNodesInQueue).toProperty("maxNodesInQueue")
                        .map(minNodesInQueue).toProperty("minNodesInQueue")
                        .map(enableAutoGrow).toProperty("enableAutoGrow")
                        .map(enableAutoShrink).toProperty("enableAutoShrink")
                        .map(spec).toProperty("spec")
                        .map(systemDiskSize).toProperty("systemDiskSize")
                        .map(systemDiskType).toProperty("systemDiskType")
                        .map(excludeNodes).toProperty("excludeNodes")
                        .map(status).toProperty("status")
                        .map(imageId).toProperty("imageId")
                        .map(maxScalePerCycle).toProperty("maxScalePerCycle")
                        .map(hostnamePrefix).toProperty("hostnamePrefix")
                        .map(cudaVersion).toProperty("cudaVersion")
                        .map(gpuDriverVersion).toProperty("gpuDriverVersion")
                        .map(cudnnVersion).toProperty("cudnnVersion")
                        .map(dataDiskList).toProperty("dataDiskList")
                        .map(cpuThreadConfig).toProperty("cpuThreadConfig")
                        .map(numaConfig).toProperty("numaConfig")
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int insertSelective(AutoScalingDO row) {
        return MyBatis3Utils.insert(this::insert, row, autoScaling, c ->
                c.map(id).toPropertyWhenPresent("id", row::getId)
                        .map(asId).toPropertyWhenPresent("asId", row::getAsId)
                        .map(asName).toPropertyWhenPresent("asName", row::getAsName)
                        .map(accountId).toPropertyWhenPresent("accountId", row::getAccountId)
                        .map(createdTime).toPropertyWhenPresent("createdTime", row::getCreatedTime)
                        .map(updatedTime).toPropertyWhenPresent("updatedTime", row::getUpdatedTime)
                        .map(deleted).toPropertyWhenPresent("deleted", row::getDeleted)
                        .map(clusterId).toPropertyWhenPresent("clusterId", row::getClusterId)
                        .map(clusterName).toPropertyWhenPresent("clusterName", row::getClusterName)
                        .map(queueId).toPropertyWhenPresent("queueId", row::getQueueId)
                        .map(queueName).toPropertyWhenPresent("queueName", row::getQueueName)
                        .map(zoneName).toPropertyWhenPresent("zoneName", row::getZoneName)
                        .map(subnetId).toPropertyWhenPresent("subnetId", row::getSubnetId)
                        .map(securityGroupId).toPropertyWhenPresent("securityGroupId", row::getSecurityGroupId)
                        .map(maxNodesInQueue).toPropertyWhenPresent("maxNodesInQueue", row::getMaxNodesInQueue)
                        .map(minNodesInQueue).toPropertyWhenPresent("minNodesInQueue", row::getMinNodesInQueue)
                        .map(enableAutoGrow).toPropertyWhenPresent("enableAutoGrow", row::getEnableAutoGrow)
                        .map(enableAutoShrink).toPropertyWhenPresent("enableAutoShrink",
                                row::getEnableAutoShrink)
                        .map(spec).toPropertyWhenPresent("spec", row::getSpec)
                        .map(systemDiskSize).toPropertyWhenPresent("systemDiskSize", row::getSystemDiskSize)
                        .map(systemDiskType).toPropertyWhenPresent("systemDiskType", row::getSystemDiskType)
                        .map(excludeNodes).toPropertyWhenPresent("excludeNodes", row::getExcludeNodes)
                        .map(status).toPropertyWhenPresent("status", row::getStatus)
                        .map(imageId).toPropertyWhenPresent("imageId", row::getImageId)
                        .map(maxScalePerCycle).toPropertyWhenPresent("maxScalePerCycle", row::getMaxScalePerCycle)
                        .map(hostnamePrefix).toPropertyWhenPresent("hostnamePrefix", row::getHostnamePrefix)
                        .map(cudaVersion).toPropertyWhenPresent("cudaVersion", row::getCudaVersion)
                        .map(gpuDriverVersion).toPropertyWhenPresent("gpuDriverVersion", row::getGpuDriverVersion)
                        .map(cudnnVersion).toPropertyWhenPresent("cudnnVersion", row::getCudnnVersion)
                        .map(dataDiskList).toPropertyWhenPresent("dataDiskList", row::getDataDiskList)
                        .map(cpuThreadConfig).toPropertyWhenPresent("cpuThreadConfig", row::getCpuThreadConfig)
                        .map(numaConfig).toPropertyWhenPresent("numaConfig", row::getNumaConfig)
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    @SelectProvider(type = SqlProviderAdapter.class, method = "select")
    @ResultMap("AutoScalingDOResult")
    Optional<AutoScalingDO> selectOne(SelectStatementProvider selectStatement);


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<AutoScalingDO> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, autoScaling, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<AutoScalingDO> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, autoScaling, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default List<AutoScalingDO> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, autoScaling, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default Optional<AutoScalingDO> selectByPrimaryKey(Long key) {
        return selectOne(c ->
                c.where(id, isEqualTo(key))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, autoScaling, completer);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateAllColumns(AutoScalingDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalTo(row::getId)
                .set(asId).equalTo(row::getAsId)
                .set(asName).equalTo(row::getAsName)
                .set(accountId).equalTo(row::getAccountId)
                .set(createdTime).equalTo(row::getCreatedTime)
                .set(updatedTime).equalTo(row::getUpdatedTime)
                .set(clusterId).equalTo(row::getClusterId)
                .set(clusterName).equalTo(row::getClusterName)
                .set(queueId).equalTo(row::getQueueId)
                .set(queueName).equalTo(row::getQueueName)
                .set(zoneName).equalTo(row::getZoneName)
                .set(subnetId).equalTo(row::getSubnetId)
                .set(securityGroupId).equalTo(row::getSecurityGroupId)
                .set(maxNodesInQueue).equalTo(row::getMaxNodesInQueue)
                .set(minNodesInQueue).equalTo(row::getMinNodesInQueue)
                .set(enableAutoGrow).equalTo(row::getEnableAutoGrow)
                .set(enableAutoShrink).equalTo(row::getEnableAutoShrink)
                .set(spec).equalTo(row::getSpec)
                .set(systemDiskSize).equalTo(row::getSystemDiskSize)
                .set(systemDiskType).equalTo(row::getSystemDiskType)
                .set(excludeNodes).equalTo(row::getExcludeNodes)
                .set(status).equalTo(row::getStatus)
                .set(imageId).equalTo(row::getImageId)
                .set(maxScalePerCycle).equalTo(row::getMaxScalePerCycle)
                .set(hostnamePrefix).equalTo(row::getHostnamePrefix)
                .set(cudaVersion).equalTo(row::getCudaVersion)
                .set(gpuDriverVersion).equalTo(row::getGpuDriverVersion)
                .set(cudnnVersion).equalTo(row::getCudnnVersion)
                .set(dataDiskList).equalTo(row::getDataDiskList)
                .set(cpuThreadConfig).equalTo(row::getCpuThreadConfig)
                .set(numaConfig).equalTo(row::getNumaConfig);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(AutoScalingDO row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(id).equalToWhenPresent(row::getId)
                .set(asId).equalToWhenPresent(row::getAsId)
                .set(asName).equalToWhenPresent(row::getAsName)
                .set(accountId).equalToWhenPresent(row::getAccountId)
                .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                .set(clusterId).equalToWhenPresent(row::getClusterId)
                .set(clusterName).equalToWhenPresent(row::getClusterName)
                .set(queueId).equalToWhenPresent(row::getQueueId)
                .set(queueName).equalToWhenPresent(row::getQueueName)
                .set(zoneName).equalToWhenPresent(row::getZoneName)
                .set(subnetId).equalToWhenPresent(row::getSubnetId)
                .set(securityGroupId).equalToWhenPresent(row::getSecurityGroupId)
                .set(maxNodesInQueue).equalToWhenPresent(row::getMaxNodesInQueue)
                .set(minNodesInQueue).equalToWhenPresent(row::getMinNodesInQueue)
                .set(enableAutoGrow).equalToWhenPresent(row::getEnableAutoGrow)
                .set(enableAutoShrink).equalToWhenPresent(row::getEnableAutoShrink)
                .set(spec).equalToWhenPresent(row::getSpec)
                .set(systemDiskSize).equalToWhenPresent(row::getSystemDiskSize)
                .set(systemDiskType).equalToWhenPresent(row::getSystemDiskType)
                .set(excludeNodes).equalToWhenPresent(row::getExcludeNodes)
                .set(imageId).equalToWhenPresent(row::getImageId)
                .set(status).equalToWhenPresent(row::getStatus)
                .set(maxScalePerCycle).equalToWhenPresent(row::getMaxScalePerCycle)
                .set(hostnamePrefix).equalToWhenPresent(row::getHostnamePrefix)
                .set(cudaVersion).equalToWhenPresent(row::getCudaVersion)
                .set(gpuDriverVersion).equalToWhenPresent(row::getGpuDriverVersion)
                .set(cudnnVersion).equalToWhenPresent(row::getCudnnVersion)
                .set(dataDiskList).equalToWhenPresent(row::getDataDiskList)
                .set(cpuThreadConfig).equalToWhenPresent(row::getCpuThreadConfig)
                .set(numaConfig).equalToWhenPresent(row::getNumaConfig);
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByPrimaryKey(AutoScalingDO row) {
        return update(c ->
                c.set(asId).equalTo(row::getAsId)
                        .set(asName).equalTo(row::getAsName)
                        .set(accountId).equalTo(row::getAccountId)
                        .set(createdTime).equalTo(row::getCreatedTime)
                        .set(updatedTime).equalTo(row::getUpdatedTime)
                        .set(clusterId).equalTo(row::getClusterId)
                        .set(clusterName).equalTo(row::getClusterName)
                        .set(queueId).equalTo(row::getQueueId)
                        .set(queueName).equalTo(row::getQueueName)
                        .set(zoneName).equalTo(row::getZoneName)
                        .set(subnetId).equalTo(row::getSubnetId)
                        .set(securityGroupId).equalTo(row::getSecurityGroupId)
                        .set(maxNodesInQueue).equalTo(row::getMaxNodesInQueue)
                        .set(minNodesInQueue).equalTo(row::getMinNodesInQueue)
                        .set(enableAutoGrow).equalTo(row::getEnableAutoGrow)
                        .set(enableAutoShrink).equalTo(row::getEnableAutoShrink)
                        .set(spec).equalTo(row::getSpec)
                        .set(systemDiskSize).equalTo(row::getSystemDiskSize)
                        .set(systemDiskType).equalTo(row::getSystemDiskType)
                        .set(excludeNodes).equalTo(row::getExcludeNodes)
                        .set(status).equalTo(row::getStatus)
                        .set(imageId).equalTo(row::getImageId)
                        .set(maxScalePerCycle).equalTo(row::getMaxScalePerCycle)
                        .set(hostnamePrefix).equalTo(row::getHostnamePrefix)
                        .set(cudaVersion).equalTo(row::getCudaVersion)
                        .set(gpuDriverVersion).equalTo(row::getGpuDriverVersion)
                        .set(cudnnVersion).equalTo(row::getCudnnVersion)
                        .set(dataDiskList).equalTo(row::getDataDiskList)
                        .set(cpuThreadConfig).equalTo(row::getCpuThreadConfig)
                        .set(numaConfig).equalTo(row::getNumaConfig)
                        .where(id, isEqualTo(row::getId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByAsIdSelective(AutoScalingDO row) {
        return update(c ->
                c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(asName).equalToWhenPresent(row::getAsName)
                        .set(accountId).equalToWhenPresent(row::getAccountId)
                        .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(clusterId).equalToWhenPresent(row::getClusterId)
                        .set(clusterName).equalToWhenPresent(row::getClusterName)
                        .set(queueName).equalToWhenPresent(row::getQueueName)
                        .set(zoneName).equalToWhenPresent(row::getZoneName)
                        .set(subnetId).equalToWhenPresent(row::getSubnetId)
                        .set(securityGroupId).equalToWhenPresent(row::getSecurityGroupId)
                        .set(maxNodesInQueue).equalToWhenPresent(row::getMaxNodesInQueue)
                        .set(minNodesInQueue).equalToWhenPresent(row::getMinNodesInQueue)
                        .set(enableAutoGrow).equalToWhenPresent(row::getEnableAutoGrow)
                        .set(enableAutoShrink).equalToWhenPresent(row::getEnableAutoShrink)
                        .set(spec).equalToWhenPresent(row::getSpec)
                        .set(systemDiskSize).equalToWhenPresent(row::getSystemDiskSize)
                        .set(systemDiskType).equalToWhenPresent(row::getSystemDiskType)
                        .set(excludeNodes).equalToWhenPresent(row::getExcludeNodes)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(imageId).equalToWhenPresent(row::getImageId)
                        .set(maxScalePerCycle).equalToWhenPresent(row::getMaxScalePerCycle)
                        .set(hostnamePrefix).equalToWhenPresent(row::getHostnamePrefix)
                        .set(cudaVersion).equalToWhenPresent(row::getCudaVersion)
                        .set(gpuDriverVersion).equalToWhenPresent(row::getGpuDriverVersion)
                        .set(cudnnVersion).equalToWhenPresent(row::getCudnnVersion)
                        .set(dataDiskList).equalToWhenPresent(row::getDataDiskList)
                        .set(deleted).equalToWhenPresent(row::getDeleted)
                        .set(cpuThreadConfig).equalToWhenPresent(row::getCpuThreadConfig)
                        .set(numaConfig).equalToWhenPresent(row::getNumaConfig)
                        .where(asId, isEqualTo(row::getAsId))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int updateByAsIdAndStatusSelective(AutoScalingDO row, String oldStatus) {
        return update(c ->
                c.set(updatedTime).equalToWhenPresent(row::getUpdatedTime)
                        .set(asName).equalToWhenPresent(row::getAsName)
                        .set(accountId).equalToWhenPresent(row::getAccountId)
                        .set(createdTime).equalToWhenPresent(row::getCreatedTime)
                        .set(clusterId).equalToWhenPresent(row::getClusterId)
                        .set(clusterName).equalToWhenPresent(row::getClusterName)
                        .set(queueName).equalToWhenPresent(row::getQueueName)
                        .set(zoneName).equalToWhenPresent(row::getZoneName)
                        .set(subnetId).equalToWhenPresent(row::getSubnetId)
                        .set(securityGroupId).equalToWhenPresent(row::getSecurityGroupId)
                        .set(maxNodesInQueue).equalToWhenPresent(row::getMaxNodesInQueue)
                        .set(minNodesInQueue).equalToWhenPresent(row::getMinNodesInQueue)
                        .set(enableAutoGrow).equalToWhenPresent(row::getEnableAutoGrow)
                        .set(enableAutoShrink).equalToWhenPresent(row::getEnableAutoShrink)
                        .set(spec).equalToWhenPresent(row::getSpec)
                        .set(systemDiskSize).equalToWhenPresent(row::getSystemDiskSize)
                        .set(systemDiskType).equalToWhenPresent(row::getSystemDiskType)
                        .set(excludeNodes).equalToWhenPresent(row::getExcludeNodes)
                        .set(status).equalToWhenPresent(row::getStatus)
                        .set(imageId).equalToWhenPresent(row::getImageId)
                        .set(maxScalePerCycle).equalToWhenPresent(row::getMaxScalePerCycle)
                        .set(hostnamePrefix).equalToWhenPresent(row::getHostnamePrefix)
                        .set(cudaVersion).equalToWhenPresent(row::getCudaVersion)
                        .set(gpuDriverVersion).equalToWhenPresent(row::getGpuDriverVersion)
                        .set(cudnnVersion).equalToWhenPresent(row::getCudnnVersion)
                        .set(dataDiskList).equalToWhenPresent(row::getDataDiskList)
                        .set(cpuThreadConfig).equalToWhenPresent(row::getCpuThreadConfig)
                        .set(numaConfig).equalToWhenPresent(row::getNumaConfig)
                        .where(asId, isEqualTo(row::getAsId))
                        .and(status, isEqualTo(oldStatus))
        );
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    default int deleteByAsId(String tarAsId) {
        return delete(c ->
                c.where(asId, isEqualTo(tarAsId))
        );
    }
}