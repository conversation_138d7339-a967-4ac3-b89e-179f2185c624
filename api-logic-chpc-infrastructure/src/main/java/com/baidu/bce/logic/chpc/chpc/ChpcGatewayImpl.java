package com.baidu.bce.logic.chpc.chpc;

import jakarta.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.chpc.model.PushAutoScalingBctEventRequest;
import com.baidu.bce.logic.chpc.chpc.model.PushAutoScalingBctEventResponse;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.chpc.client.ChpcNioClient;
import com.baidu.bce.logic.chpc.chpc.gateway.ChpcGateway;
import com.baidu.bce.logic.core.iam.service.IamLogicService;

import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ChpcGatewayImpl implements ChpcGateway {
    @Resource
    IamLogicService iamLogicService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private ChpcNioClient chpcNioClient;

    @Override
    public PushAutoScalingBctEventResponse pushAutoShrinkBctEvent(PushAutoScalingBctEventRequest request) {
        return chpcNioClient.pushAutoScalingBctEvent(request, request.getUserId(), ChpcConstant.BCT_ACTION_SHRINK);
    }

    @Override
    public PushAutoScalingBctEventResponse pushAutoExpandBctEvent(PushAutoScalingBctEventRequest request) {
        return chpcNioClient.pushAutoScalingBctEvent(request, request.getUserId(), ChpcConstant.BCT_ACTION_EXPAND);
    }
}
