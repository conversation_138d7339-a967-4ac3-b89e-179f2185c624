package com.baidu.bce.logic.chpc.database.dataobject;

import java.time.LocalDateTime;

import jakarta.annotation.Generated;

public class ChargeDataDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String orderId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String serviceType;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String instanceId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String chargeItem;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String chargeAmount;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime chargeMonth;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime chargeTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean chargeTotal;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean valid;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getOrderId() {
        return orderId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setOrderId(String orderId) {
        this.orderId = orderId == null ? null : orderId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId == null ? null : accountId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getServiceType() {
        return serviceType;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setServiceType(String serviceType) {
        this.serviceType = serviceType == null ? null : serviceType.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getInstanceId() {
        return instanceId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setInstanceId(String instanceId) {
        this.instanceId = instanceId == null ? null : instanceId.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getChargeItem() {
        return chargeItem;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setChargeItem(String chargeItem) {
        this.chargeItem = chargeItem == null ? null : chargeItem.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getChargeAmount() {
        return chargeAmount;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setChargeAmount(String chargeAmount) {
        this.chargeAmount = chargeAmount == null ? null : chargeAmount.trim();
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getChargeMonth() {
        return chargeMonth;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setChargeMonth(LocalDateTime chargeMonth) {
        this.chargeMonth = chargeMonth;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getChargeTime() {
        return chargeTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setChargeTime(LocalDateTime chargeTime) {
        this.chargeTime = chargeTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getChargeTotal() {
        return chargeTotal;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setChargeTotal(Boolean chargeTotal) {
        this.chargeTotal = chargeTotal;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getValid() {
        return valid;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setValid(Boolean valid) {
        this.valid = valid;
    }
}