package com.baidu.bce.logic.chpc.autoscaling.gatewayimpl;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.autoscaling.gateway.IAutoScalingDAOGateway;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.OrderByType;
import com.baidu.bce.logic.chpc.database.dataobject.AutoScalingDO;
import com.baidu.bce.logic.chpc.database.mapper.AutoScalingMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mybatis.dynamic.sql.SortSpecification;
import org.mybatis.dynamic.sql.SqlBuilder;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

import static com.baidu.bce.logic.chpc.database.mapper.local.AutoScalingDynamicSqlSupport.autoScaling;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualToWhenPresent;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.select;

@Slf4j
@Service
public class AutoScalingDAOGatewayImpl implements IAutoScalingDAOGateway {

    @Resource
    AutoScalingMapper autoScalingMapper;

    @Override
    public Long count(String asId) {
        SelectStatementProvider selectStatement = select(SqlBuilder.count())
                .from(autoScaling)
                .where(autoScaling.asId, isEqualTo(asId))
                .build()
                .render(RenderingStrategies.MYBATIS3);

        return autoScalingMapper.count(selectStatement);
    }

    @Override
    public Boolean insert(AutoScaling autoScaling) {
        AutoScalingDO autoScalingDO = new AutoScalingDO();
        BeanCopyUtil.copyProperties(autoScaling, autoScalingDO);
        autoScalingDO.setCreatedTime(LocalDateTime.now());
        autoScalingDO.setUpdatedTime(autoScalingDO.getCreatedTime());
        autoScalingDO.setStatus(autoScaling.getStatus().name());
        if (CollectionUtils.isNotEmpty(autoScaling.getExcludeNodes())) {
            autoScalingDO.setExcludeNodes(StringUtils.join(autoScaling.getExcludeNodes(), ","));
        }
        if (CollectionUtils.isNotEmpty(autoScaling.getDataDiskList())) {
            autoScalingDO.setDataDiskList(JacksonUtil.toJson(autoScaling.getDataDiskList()));
        }

        return autoScalingMapper.insertSelective(autoScalingDO) != 0;
    }

    @Override
    public Boolean update(AutoScaling autoScaling) {
        AutoScalingDO autoScalingDO = new AutoScalingDO();
        BeanCopyUtil.copyProperties(autoScaling, autoScalingDO);
        autoScalingDO.setUpdatedTime(LocalDateTime.now());
        if (autoScaling.getStatus() != null) {
            autoScalingDO.setStatus(autoScaling.getStatus().name());
        }
        if (autoScaling.getExcludeNodes() != null) {
            autoScalingDO.setExcludeNodes(StringUtils.join(autoScaling.getExcludeNodes(), ","));
        }
        if (CollectionUtils.isNotEmpty(autoScaling.getDataDiskList())) {
            autoScalingDO.setDataDiskList(JacksonUtil.toJson(autoScaling.getDataDiskList()));
        }
        log.debug("xxxxx update auto scaling, accountIdIn: {} autoScalingIn: {}", autoScaling.getAccountId(), autoScaling);
        log.debug("xxxxx update auto scaling, accountIdDO: {} autoScalingDO: {}", autoScalingDO.getAccountId(), autoScalingDO);
        return autoScalingMapper.updateByAsIdSelective(autoScalingDO) != 0;
    }

    @Override
    public Boolean delete(String asId) {
//        LocalDateTime now = LocalDateTime.now();
//        AutoScalingDO autoScalingDO = new AutoScalingDO();
//        autoScalingDO.setUpdatedTime(now);
//        autoScalingDO.setAsId(asId);
//        autoScalingDO.setDeleted(true);
//        return autoScalingMapper.updateByAsIdSelective(autoScalingDO) != 0;
        return autoScalingMapper.deleteByAsId(asId) != 0;
    }

    @Override
    public Boolean updateByStatus(AutoScaling autoScaling, AutoScalingStatus oldStatus) {
        AutoScalingDO autoScalingDO = new AutoScalingDO();
        BeanCopyUtil.copyProperties(autoScaling, autoScalingDO);
        autoScalingDO.setUpdatedTime(LocalDateTime.now());
        if (autoScaling.getStatus() != null) {
            autoScalingDO.setStatus(autoScaling.getStatus().name());
        }
        if (autoScaling.getExcludeNodes() != null) {
            autoScalingDO.setExcludeNodes(StringUtils.join(autoScaling.getExcludeNodes(), ","));
        }
        if (CollectionUtils.isNotEmpty(autoScaling.getDataDiskList())) {
            autoScalingDO.setDataDiskList(JacksonUtil.toJson(autoScaling.getDataDiskList()));
        }
        return autoScalingMapper.updateByAsIdAndStatusSelective(autoScalingDO, oldStatus.name()) != 0;
    }

    @Override
    public List<AutoScaling> getAll(String accountId, String orderByType) {
        SelectStatementProvider selectStatement =
                select(AutoScalingMapper.selectList)
                        .from(autoScaling)
                        .where(autoScaling.accountId, isEqualToWhenPresent(accountId))
                        .and(autoScaling.deleted, isEqualTo(false))
                        .orderBy(resolveOrderByType(orderByType))
                        .build().render(RenderingStrategies.MYBATIS3);

        List<AutoScalingDO> autoScalingDOS = autoScalingMapper.selectMany(selectStatement);
        List<AutoScaling> autoScalingList = BeanCopyUtil.copyListProperties(autoScalingDOS, AutoScaling::new);
        for (AutoScalingDO autoScalingDO : autoScalingDOS) {
            if (StringUtils.isNotEmpty(autoScalingDO.getExcludeNodes())) {
                for (AutoScaling autoScaling : autoScalingList) {
                    if (autoScaling.getAsId().equals(autoScalingDO.getAsId())) {
                        List<String> excludeInstances = Arrays.asList(autoScalingDO.getExcludeNodes().split(","));
                        autoScaling.setExcludeNodes(excludeInstances);
                        break;
                    }
                }
            }
            if (StringUtils.isNotEmpty(autoScalingDO.getDataDiskList())) {
                for (AutoScaling autoScaling : autoScalingList) {
                    if (autoScaling.getAsId().equals(autoScalingDO.getAsId())) {
                        autoScaling.setDataDiskList(JacksonUtil.decodeToList(autoScalingDO.getDataDiskList(), DiskInfo.class));
                        break;
                    }
                }
            }
        }
        return autoScalingList;
    }

    // 根据 orderByType 返回对应的排序方式
    private SortSpecification resolveOrderByType(String orderByType) {
        if (OrderByType.UPDATED_TIME_ASC.getName().equalsIgnoreCase(orderByType)) {
            return autoScaling.updatedTime;
        } else if (OrderByType.UPDATED_TIME_DESC.getName().equalsIgnoreCase(orderByType)) {
            return autoScaling.updatedTime.descending();
        } else if (OrderByType.CREATED_TIME_ASC.getName().equalsIgnoreCase(orderByType)) {
            return autoScaling.createdTime;
        } else {
            return autoScaling.createdTime.descending();  // 默认降序
        }
    }

    @Override
    public AutoScaling getByQueueId(String queueId, String accountId) {

        SelectStatementProvider selectStatement =
                select(AutoScalingMapper.selectList)
                        .from(autoScaling)
                        .where(autoScaling.queueId, isEqualTo(queueId))
                        .and(autoScaling.accountId, isEqualToWhenPresent(accountId))
                        .and(autoScaling.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);


        AutoScalingDO autoScalingDO = autoScalingMapper.selectOne(selectStatement).orElse(null);
        if (autoScalingDO == null) {
            return null;
        }
        AutoScaling autoScaling = autoScalingDO.toEntity();
        if (autoScalingDO != null && StringUtils.isNotEmpty(autoScalingDO.getDataDiskList())) {
            autoScaling.setDataDiskList(JacksonUtil.decodeToList(autoScalingDO.getDataDiskList(), DiskInfo.class));
        }
        return autoScaling;
    }

    @Override
    public List<AutoScaling> getByQueueIds(List<String> queueIds, String status) {

        SelectStatementProvider selectStatement =
                select(AutoScalingMapper.selectList)
                        .from(autoScaling)
                        .where(autoScaling.queueId, isIn(queueIds))
                        .and(autoScaling.status, isEqualToWhenPresent(status))
                        .and(autoScaling.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);


        List<AutoScalingDO> autoScalingDOS = autoScalingMapper.selectMany(selectStatement);

        List<AutoScaling> autoScalingList = BeanCopyUtil.copyListProperties(autoScalingDOS, AutoScaling::new);
        for (AutoScalingDO autoScalingDO : autoScalingDOS) {
            if (StringUtils.isNotEmpty(autoScalingDO.getExcludeNodes())) {
                for (AutoScaling autoScaling : autoScalingList) {
                    if (autoScaling.getAsId().equals(autoScalingDO.getAsId())) {
                        List<String> excludeInstances = Arrays.asList(autoScalingDO.getExcludeNodes().split(","));
                        autoScaling.setExcludeNodes(excludeInstances);
                        break;
                    }
                }
            }
            if (StringUtils.isNotEmpty(autoScalingDO.getDataDiskList())) {
                for (AutoScaling autoScaling : autoScalingList) {
                    if (autoScaling.getAsId().equals(autoScalingDO.getAsId())) {
                        autoScaling.setDataDiskList(JacksonUtil.decodeToList(autoScalingDO.getDataDiskList(), DiskInfo.class));
                        break;
                    }
                }
            }
        }
        return autoScalingList;

    }

    @Override
    public List<AutoScaling> getByClusterId(String clusterId, String accountId) {

        SelectStatementProvider selectStatement =
                select(AutoScalingMapper.selectList)
                        .from(autoScaling)
                        .where(autoScaling.clusterId, isEqualTo(clusterId))
                        .and(autoScaling.accountId, isEqualToWhenPresent(accountId))
                        .and(autoScaling.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);


        List<AutoScalingDO> autoScalingDOS = autoScalingMapper.selectMany(selectStatement);
        List<AutoScaling> autoScalingList = BeanCopyUtil.copyListProperties(autoScalingDOS, AutoScaling::new);
        for (AutoScalingDO autoScalingDO : autoScalingDOS) {
            if (StringUtils.isNotEmpty(autoScalingDO.getDataDiskList())) {
                for (AutoScaling autoScaling : autoScalingList) {
                    if (autoScaling.getAsId().equals(autoScalingDO.getAsId())) {
                        autoScaling.setDataDiskList(JacksonUtil.decodeToList(autoScalingDO.getDataDiskList(), DiskInfo.class));
                        break;
                    }
                }
            }
        }
        return autoScalingList;
    }

    @Override
    public AutoScaling getByAsId(String asId) {
        SelectStatementProvider selectStatement =
                select(AutoScalingMapper.selectList)
                        .from(autoScaling)
                        .where(autoScaling.asId, isEqualTo(asId))
                        .and(autoScaling.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);

        AutoScalingDO autoScalingDO = autoScalingMapper.selectOne(selectStatement).orElse(null);
        if (autoScalingDO == null) {
            return null;
        }
        AutoScaling autoScaling = autoScalingDO.toEntity();
        if (autoScalingDO != null && StringUtils.isNotEmpty(autoScalingDO.getDataDiskList())) {
            autoScaling.setDataDiskList(JacksonUtil.decodeToList(autoScalingDO.getDataDiskList(), DiskInfo.class));
        }
        return autoScaling;
    }

    @Override
    public AutoScaling getByClusterIdAndQueueId(String clusterId, String queueId, String accountId) {
        SelectStatementProvider selectStatement =
                select(AutoScalingMapper.selectList)
                        .from(autoScaling)
                        .where(autoScaling.clusterId, isEqualTo(clusterId))
                        .and(autoScaling.queueId, isEqualTo(queueId))
                        .and(autoScaling.accountId, isEqualToWhenPresent(accountId))
                        .and(autoScaling.deleted, isEqualTo(false))
                        .build().render(RenderingStrategies.MYBATIS3);


        AutoScalingDO autoScalingDO = autoScalingMapper.selectOne(selectStatement).orElse(null);
        if (autoScalingDO == null) {
            return null;
        }
        AutoScaling autoScaling = autoScalingDO.toEntity();
        if (autoScalingDO != null && StringUtils.isNotEmpty(autoScalingDO.getDataDiskList())) {
            autoScaling.setDataDiskList(JacksonUtil.decodeToList(autoScalingDO.getDataDiskList(), DiskInfo.class));
        }
        if (StringUtils.isNotEmpty(autoScalingDO.getExcludeNodes())) {
            autoScaling.setExcludeNodes(Arrays.asList(autoScalingDO.getExcludeNodes().split(",")));
        } else {
            autoScaling.setExcludeNodes(new ArrayList<>());
        }
        return autoScaling;
    }
}
