package com.baidu.bce.logic.chpc.gatewayimpl.external;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.common.ChpcClientFactory;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.client.BackendNioClient;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AuthDomainUserRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AutoScalingProbeRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AutoScalingProbeResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionScriptResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendTaskResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ComputeNode;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ComputeResource;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.CreateQueueRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.CreateWorkflowRunRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.GetClusterResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.GetComputeNodeResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListAvailableTagsResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListComputeNodesResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListQueuesResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ModifyClusterRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ModifyTagsRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.Queue;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ShrinkNodesRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ShrinkNodesResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.StopClusterRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunResResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunStatusResponse;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.tag.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Service
@Slf4j
public class BackendGatewayImpl implements BackendGateway {

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Value("${helix.helixfold3.cluster}")
    private String helixfold3Cluster;

    @Value("${helix.helixvs.cluster}")
    private String helixvsCluster;

    @Resource
    private InstanceDAOGateway instanceDAOGateway;

    @Resource
    private ChpcClientFactory chpcClientFactory;

    @Override
    public BackendCommonResponse startCluster(String clusterId) {

        BackendCommonResponse backendCommonResponse = this.createBackendNioClient(clusterId).startCluster();

        log.debug("[Backend StartCluster] cluster id:{}, responseBody:{}", clusterId, backendCommonResponse);

        return backendCommonResponse;
    }

    /**
     * {@inheritDoc}
     * 停止集群，并返回后端响应结果。
     *
     * @param clusterId 集群ID，不能为空
     * @param force     是否强制停止，true表示强制停止，false表示非强制停止
     * @return 后端通用响应对象，包含状态码和消息
     * @throws IllegalArgumentException 如果集群ID为空，则抛出此异常
     */
    @Override
    public BackendCommonResponse stopCluster(String clusterId, Boolean force) {

        StopClusterRequest request = new StopClusterRequest();
        request.setForce(force);

        BackendCommonResponse backendCommonResponse = this.createBackendNioClient(clusterId).stopCluster(force);

        log.debug("[Backend StopCluster] cluster id:{}, responseBody:{}", clusterId, backendCommonResponse);

        return backendCommonResponse;
    }

    @Override
    public BackendCommonResponse modifyCluster(String clusterId, int maxCpus, int maxNodes) {
        ModifyClusterRequest request = new ModifyClusterRequest();
        request.setMaxCpus(maxCpus);
        request.setMaxNodes(maxNodes);

        BackendCommonResponse backendCommonResponse = this.createBackendNioClient(clusterId).modifyCluster(request);

        log.debug("[Backend ModifyCluster] cluster id:{}, responseBody:{}", clusterId, backendCommonResponse);

        return backendCommonResponse;
    }

    /**
     * {@inheritDoc}
     * 获取集群详情，包括集群ID、集群名称、集群状态等信息。
     *
     * @param clusterId 集群ID，不能为空
     * @return {@link GetClusterResponse} 返回一个包含集群详情的对象
     * @throws IllegalArgumentException 如果集群ID为空，则抛出此异常
     */
    @Override
    public GetClusterResponse getClusterDetail(String clusterId) {

        GetClusterResponse getClusterResponse = this.createBackendNioClient(clusterId).getClusterDetail();

        log.debug("[Backend ClusterDetail] cluster id:{}, responseBody:{}", clusterId, getClusterResponse);

        return getClusterResponse;
    }

    private BackendNioClient createBackendNioClient(String clusterId) {

        String endpoint = this.getRequestEndpoint(clusterId);

        BackendNioClient backendNioClient;

        // Helix集群使用SaaS服务，所以使用chpc资源账号的accountId
        if (helixfold3Cluster.equals(clusterId) || helixvsCluster.equals(clusterId)) {
            backendNioClient = chpcClientFactory.createHelixBackendClient(endpoint);
        } else {
            // 其他集群使用自己的accountId
            backendNioClient = chpcClientFactory.createBackendClient(endpoint);
        }
        return backendNioClient;
    }

    private BackendNioClient createWorkflowClient(String clusterId) {

        String endpoint = this.getWorkflowEndpoint(clusterId);

        BackendNioClient backendNioClient = chpcClientFactory.createBackendClient(endpoint);
        return backendNioClient;
    }

    /**
     * {@inheritDoc}
     * 向指定集群添加队列，并返回响应结果。
     *
     * @param clusterId 集群ID，不能为空
     * @param queue     需要添加的队列对象，不能为空
     * @return 包装了响应信息的BackendCommonResponse对象
     * @throws IllegalArgumentException 如果clusterId或queue为空，则抛出此异常
     */
    @Override
    public BackendCommonResponse addQueue(String clusterId, Queue queue) {

        CreateQueueRequest createQueueRequest = new CreateQueueRequest();
        createQueueRequest.setQueue(queue);

        BackendCommonResponse backendCommonResponse = this.createBackendNioClient(clusterId).addQueue(createQueueRequest);

        log.debug("[Backend AddQueue] cluster id:{}, queue name:{}, responseBody:{}",
                clusterId, queue.getQueueName(), backendCommonResponse);

        return backendCommonResponse;
    }

    /**
     * {@inheritDoc}
     * 从队列中移除指定的队列，并返回一个包含删除结果的响应对象。
     * 如果队列不存在或者删除失败，则抛出异常。
     *
     * @param clusterId 集群ID，不能为空
     * @param queueName 要移除的队列名称，不能为空
     * @param force     是否强制移除队列，默认值为false
     * @param check     是否进行检查，默认值为true
     * @return 包含删除结果的响应对象，包含状态码和消息
     * @throws IllegalArgumentException 当clusterId、queueName或force、check参数为null时抛出此异常
     */
    @Override
    public BackendCommonResponse removeQueue(String clusterId, String queueName,
                                             Boolean force, Boolean check) {

        BackendCommonResponse backendCommonResponse = this.createBackendNioClient(clusterId).removeQueue(queueName, force, check);

        log.debug("[Backend RemoveQueue] cluster id:{}, queue name:{}, responseBody:{}",
                clusterId, queueName, backendCommonResponse);

        return backendCommonResponse;
    }

    /**
     * {@inheritDoc}
     * 更新队列的自动扩缩容配置，并返回后端响应。
     * 如果指定了不参与自动扩缩容的节点主机名，则将其添加到队列中。
     *
     * @param autoScaling          包含自动扩缩容配置的对象
     * @param excludeNodeHostnames 不参与自动扩缩容的节点主机名列表（可选）
     * @return 后端响应对象，包含状态码和消息
     * @throws RestClientException 发生REST客户端错误时抛出
     */
    @Override
    public BackendCommonResponse updateQueueAutoScaling(AutoScaling autoScaling,
                                                        List<String> excludeNodeHostnames) {
        CreateQueueRequest updateQueueSpecRequest = new CreateQueueRequest();
        ComputeResource computeResource = new ComputeResource();
        computeResource.setSpec(autoScaling.getSpec());
        Queue queue = new Queue();
        queue.setComputeResources(Collections.singletonList(computeResource));
        queue.setEnableAutoGrow(autoScaling.getEnableAutoGrow());
        queue.setEnableAutoShrink(autoScaling.getEnableAutoShrink());
        queue.setMaxNodes(autoScaling.getMaxNodesInQueue());
        queue.setMinNodes(autoScaling.getMinNodesInQueue());
        queue.setMaxScalePerCycle(autoScaling.getMaxScalePerCycle());
        queue.setExcludeNodeHostnames(excludeNodeHostnames);
        queue.setQueueName(autoScaling.getQueueName());
        queue.setQueueName(autoScaling.getQueueName());
        updateQueueSpecRequest.setQueue(queue);

        return createBackendNioClient(autoScaling.getClusterId()).updateQueueAutoScaling(updateQueueSpecRequest, autoScaling.getQueueName());
    }

    /**
     * {@inheritDoc}
     * 根据集群ID获取队列列表，并返回一个包含所有队列信息的ListQueuesResponse对象。
     *
     * @param clusterId 集群ID，不能为空
     * @return ListQueuesResponse类型，包含所有队列信息
     * @throws RestClientException 如果请求失败，则抛出RestClientException异常
     */
    @Override
    public ListQueuesResponse listQueue(String clusterId) {

        ListQueuesResponse listQueuesResponse = this.createBackendNioClient(clusterId).listQueue();

        log.debug("[Backend ListQueue] cluster id:{}, responseBody:{}", clusterId, listQueuesResponse);

        return listQueuesResponse;
    }

    /**
     * {@inheritDoc}
     * 获取指定队列中的计算节点列表。
     *
     * @param clusterId 集群ID，不能为空
     * @param queueName 队列名称，不能为空
     * @return 返回一个包含计算节点列表的ListComputeNodesResponse对象
     * @throws IllegalArgumentException 如果clusterId或queueName为空字符串时抛出此异常
     */
    @Override
    public ListComputeNodesResponse listComputeNodes(String clusterId, String queueName) {

        ListComputeNodesResponse listQueuesResponse = this.createBackendNioClient(clusterId).listComputeNodes(queueName);

        log.debug("[Backend ListComputeNodes] cluster id:{}, queue name:{}, responseBody:{}",
                clusterId, queueName, listQueuesResponse);

        return listQueuesResponse;
    }

    /**
     * {@inheritDoc}
     * 获取计算节点详情，包括集群ID、队列名称、主机名和实例ID。返回一个GetComputeNodeResponse对象，其中包含了请求的结果信息。
     *
     * @param clusterId  集群ID，不能为空
     * @param queueName  队列名称，不能为空
     * @param hostName   主机名，可以为空，为空时将忽略该参数
     * @param instanceId 实例ID，不能为空
     * @return GetComputeNodeResponse类型对象，包含请求的结果信息
     * @throws IllegalArgumentException 如果集群ID、队列名称或实例ID为空，则抛出此异常
     */
    @Override
    public GetComputeNodeResponse getComputeNodeDetail(String clusterId, String queueName,
                                                       String hostName, String instanceId) {

        GetComputeNodeResponse getComputeNodeResponse = this.createBackendNioClient(clusterId).
                getComputeNodeDetail(queueName, hostName, instanceId);

        log.debug("[Backend GetComputeNode] cluster id:{}, queue name:{}, responseBody:{}",
                clusterId, queueName, getComputeNodeResponse);

        return getComputeNodeResponse;
    }

    /**
     * {@inheritDoc}
     * 从指定的队列中移除节点，并返回响应结果。
     * 如果节点是自动扩缩容节点，则需要设置参数autoScaling为true；如果是强制删除节点，则需要设置参数force为true。
     *
     * @param clusterId   集群ID
     * @param queueName   队列名称
     * @param hostName    节点主机名称
     * @param instanceId  实例ID
     * @param autoScaling 是否是自动扩缩容节点（默认false）
     * @param force       是否是强制删除节点（默认false）
     * @return BackendCommonResponse 后端通用响应对象
     */
    @Override
    public BackendCommonResponse removeNode(String clusterId, String queueName, String hostName,
                                            String instanceId, Boolean autoScaling, Boolean force) {

        BackendCommonResponse backendCommonResponse = this.createBackendNioClient(clusterId).
                removeNode(queueName, hostName, instanceId, autoScaling, force);

        log.debug("[Backend RemoveNode] cluster id:{}, queue name:{}, responseBody:{}",
                clusterId, queueName, backendCommonResponse);

        return backendCommonResponse;
    }

    /**
     * {@inheritDoc}
     * 添加节点到队列中，返回一个BackendCommonResponse对象。
     *
     * @param clusterId String 集群ID，不能为空
     * @param queueName String 队列名称，不能为空
     * @param node      ComputeNode 计算节点信息，不能为空
     * @return BackendCommonResponse 后端通用响应对象，包含状态码和消息
     * @throws IOException IO异常
     */
    @Override
    public BackendCommonResponse addNode(String clusterId, String queueName, ComputeNode node) {

        BackendCommonResponse backendCommonResponse = this.createBackendNioClient(clusterId).addNode(queueName, node);

        log.debug("[Backend AddQueue] cluster id:{}, queue name:{}, responseBody:{}",
                clusterId, queueName, backendCommonResponse);

        return backendCommonResponse;
    }

    /**
     * {@inheritDoc}
     * 获取任务详情，包括任务ID和集群ID。返回值为BackendTaskResponse类型。
     *
     * @param clusterId 集群ID，不能为空
     * @param taskId    任务ID，不能为空
     * @return BackendTaskResponse 后端任务响应对象，包含任务详情信息
     * @throws IllegalArgumentException 当集群ID或任务ID为空时抛出此异常
     */
    @Override
    public BackendTaskResponse getTaskDetail(String clusterId, String taskId) {

        BackendTaskResponse backendTaskResponse = this.createBackendNioClient(clusterId).getTaskDetail(taskId);

        log.debug("[Backend GetTaskDetail] cluster id:{}, queue name:{}, responseBody:{}",
                clusterId, taskId, backendTaskResponse);

        return backendTaskResponse;
    }

    /**
     * {@inheritDoc}
     * <p>
     * 实现AutoScalingService接口的autoScalingProbe方法，向指定集群发送自动扩缩容请求。
     *
     * @param clusterId 集群ID，不能为空
     * @param request   自动扩缩容请求对象，不能为空
     * @return 包含自动扩缩容结果的AutoScalingProbeResponse对象
     * @throws IllegalArgumentException 如果clusterId或request为null时抛出此异常
     */
    @Override
    public AutoScalingProbeResponse autoScalingProbe(String clusterId, AutoScalingProbeRequest request) {
        return this.createBackendNioClient(clusterId).autoScalingProbe(request);
    }

    /**
     * {@inheritDoc}
     * 获取工作流运行状态，返回一个WorkflowRunStatusResponse对象。
     * 请求路径为：/api/workflows/v1/{runUuid}/status，其中{runUuid}是工作流的运行ID。
     * 请求方法为GET，返回类型为WorkflowRunStatusResponse。
     *
     * @param clusterId 集群ID，字符串类型，不能为空
     * @param runUuid   工作流运行ID，字符串类型，不能为空
     * @return WorkflowRunStatusResponse 工作流运行状态，包括工作流运行结果和错误信息等，不能为null
     * @throws RestClientException REST客户端异常
     */
    @Override
    public WorkflowRunStatusResponse getWorkflowRunStatus(String clusterId, String runUuid) {
        WorkflowRunStatusResponse workflowRunStatusResponse = this.createWorkflowClient(clusterId).getWorkflowRunStatus(runUuid);

        log.debug("[getWorkflowRunStatus] cluster id:{}, runUuid:{}, responseBody:{}",
                clusterId, runUuid, workflowRunStatusResponse);

        return workflowRunStatusResponse;
    }

    /**
     * {@inheritDoc}
     * 中止工作流程，并返回工作流运行状态响应对象。
     *
     * @param clusterId 集群ID
     * @param runUuid   工作流运行ID
     * @return 工作流运行状态响应对象{@link WorkflowRunStatusResponse}
     * @throws IOException 网络通信异常
     */
    @Override
    public WorkflowRunStatusResponse abortWorkflow(String clusterId, String runUuid) {
        WorkflowRunStatusResponse workflowRunStatusResponse = this.createWorkflowClient(clusterId).abortWorkflow(runUuid);

        log.debug("[abortWorkflow] cluster id:{}, runUuid:{}, responseBody:{}",
                clusterId, runUuid, workflowRunStatusResponse);

        return workflowRunStatusResponse;
    }

    /**
     * {@inheritDoc}
     * 创建工作流运行实例，并返回运行状态的响应对象。
     *
     * @param clusterId 集群ID，不能为空
     * @param request   创建工作流运行请求对象，不能为null
     * @return 包含工作流运行状态的响应对象，不能为null
     * @throws IllegalArgumentException 如果clusterId或request为空，则抛出此异常
     */
    @Override
    public WorkflowRunStatusResponse createWorkflowRun(String clusterId, CreateWorkflowRunRequest request) {
        return this.createBackendNioClient(clusterId).createWorkflowRun(request);
    }

    /**
     * {@inheritDoc}
     * 获取工作流运行结果，包括输出信息。
     *
     * @param clusterId 集群ID，不能为空
     * @param runUuid   运行实例的UUID，不能为空
     * @return WorkflowRunResResponse 工作流运行结果响应对象，包含输出信息
     * @throws IllegalArgumentException 如果clusterId或runUuid为空字符串或null时抛出此异常
     */
    @Override
    public WorkflowRunResResponse getWorkflowRunRes(String clusterId, String runUuid) {
        WorkflowRunResResponse workflowRunResResponse = this.createWorkflowClient(clusterId).getWorkflowRunRes(runUuid);

        log.debug("[getWorkflowRunRes] cluster id:{}, runUuid:{}, responseBody:{}",
                clusterId, runUuid, workflowRunResResponse);

        return workflowRunResResponse;
    }

    /**
     * {@inheritDoc}
     * 实现了BackendActionProxy的actionProxy方法，用于代理后端服务的请求。
     * 在调用此方法时，会将请求发送到指定的集群上，并返回相应的响应结果。
     * 如果请求超时或者出现其他异常，则抛出CommonException.RelatedServiceException或BceException。
     *
     * @param clusterId 集群ID，不能为空
     * @param action    要执行的动作，不能为空
     * @param arguments 动作参数，可以为空
     * @return BackendActionProxyResponse 包含后端服务响应信息的对象
     * @throws CommonException.RelatedServiceException 当调度器连接失败时抛出该异常
     * @throws BceException                            当请求超时或者出现其他异常时抛出该异常
     */
    @Override
    public BackendActionProxyResponse actionProxy(String clusterId, String action, String arguments) {
        log.debug("[actionProxy] cluster id:{}, action:{}, arguments:{}", 
            clusterId, action, arguments);
        BackendActionProxyRequest req = new BackendActionProxyRequest();
        req.setAction(action);
        req.setArguments(arguments);
        req.setNoRetry(false);
        BackendActionProxyResponse resp = new BackendActionProxyResponse();
        try {
            BackendNioClient backendNioClient = this.createBackendNioClient(clusterId);
            resp = backendNioClient.actionProxy(req);
            log.debug("[actionProxy] cluster id:{}, action:{}, arguments:{}, responseBody:{}",
                    clusterId, action, arguments, resp);
        } catch (WebClientResponseException e) {
            log.warn("[actionProxy] WebClientResponseException: {}", 
                e.getResponseBodyAsString());
            if (e.getResponseBodyAsString().contains("Read timed out") || e.getResponseBodyAsString().contains("Retries exhausted")) {
                throw new CommonException.RelatedServiceException("调度器连接失败", "scheduler");
            } else if (e.getResponseBodyAsString().contains("MessageBodyReader not found")) {
                throw new CommonException.RelatedServiceException("调度器节点连接失败", "cluster-api");
            } else if (e.getResponseBodyAsString().contains("InvokeScript fail")) {
                throw new CommonException.RelatedServiceException("调度器插件脚本调用失败", "scheduler plugin");
            } else {
                throw new BceException(e.getResponseBodyAsString());
            }
        } catch (Exception e) {
            log.warn("[actionProxy] Exception: {}", 
                e.getMessage());
            if (e.getMessage().contains("Read timed out") || e.getMessage().contains("Retries exhausted")) {
                throw new CommonException.RelatedServiceException("调度器连接失败", "scheduler");
            } else if (e.getMessage().contains("MessageBodyReader not found")) {
                throw new CommonException.RelatedServiceException("调度器节点连接失败", "cluster-api");
            } else if (e.getMessage().contains("InvokeScript fail")) {
                throw new CommonException.RelatedServiceException("调度器插件脚本调用失败", "scheduler plugin");
            } else {
                throw new BceException(e.getMessage());
            }
        }
        log.info("[actionProxy] actionProxy success");
        return resp;
    }

    @Override
    public BackendActionProxyResponse actionProxy(String clusterId, String action, String arguments, Boolean noRetry) {
        log.debug("[actionProxy] cluster id:{}, action:{}, arguments:{}, noRetry: {}", 
            clusterId, action, arguments, noRetry);
        BackendActionProxyRequest req = new BackendActionProxyRequest();
        req.setAction(action);
        req.setArguments(arguments);
        req.setNoRetry(noRetry);
        BackendActionProxyResponse resp = new BackendActionProxyResponse();
        try {
            BackendNioClient backendNioClient = this.createBackendNioClient(clusterId);
            resp = backendNioClient.actionProxy(req);
            log.debug("[actionProxy] cluster id:{}, action:{}, arguments:{}, responseBody:{}",
                    clusterId, action, arguments, resp);
        } catch (WebClientResponseException e) {
            log.warn("[actionProxy] WebClientResponseException: {}", 
                e.getResponseBodyAsString());
            if (e.getResponseBodyAsString().contains("Read timed out") || e.getResponseBodyAsString().contains("Retries exhausted")) {
                throw new CommonException.RelatedServiceException("调度器连接失败", "scheduler");
            } else if (e.getResponseBodyAsString().contains("MessageBodyReader not found")) {
                throw new CommonException.RelatedServiceException("调度器节点连接失败", "cluster-api");
            } else if (e.getResponseBodyAsString().contains("InvokeScript fail")) {
                throw new CommonException.RelatedServiceException("调度器插件脚本调用失败", "scheduler plugin");
            } else {
                throw new BceException(e.getResponseBodyAsString());
            }
        } catch (Exception e) {
            log.warn("[actionProxy] Exception: {}", 
                e.getMessage());
            if (e.getMessage().contains("Read timed out") || e.getMessage().contains("Retries exhausted")) {
                throw new CommonException.RelatedServiceException("调度器连接失败", "scheduler");
            } else if (e.getMessage().contains("MessageBodyReader not found")) {
                throw new CommonException.RelatedServiceException("调度器节点连接失败", "cluster-api");
            } else if (e.getMessage().contains("InvokeScript fail")) {
                throw new CommonException.RelatedServiceException("调度器插件脚本调用失败", "scheduler plugin");
            } else {
                throw new BceException(e.getMessage());
            }
        }
        log.info("[actionProxy] actionProxy success");
        return resp;
    }

    /**
     * {@inheritDoc}
     * <p>
     * 执行脚本操作，并返回响应。
     *
     * @param clusterId 集群ID
     * @param action    操作名称
     * @param arguments 参数列表
     * @return BackendActionScriptResponse 后端动作脚本响应对象
     * @throws Exception 可能会抛出异常，如网络连接错误等
     */
    @Override
    public BackendActionScriptResponse actionScript(String clusterId, String action, String arguments) {
        log.debug("[actionScript] cluster id:{}, action:{}, arguments:{}", clusterId, action, arguments);
        BackendActionProxyRequest req = new BackendActionProxyRequest();
        req.setAction(action);
        req.setArguments(arguments);
        BackendActionScriptResponse resp = new BackendActionScriptResponse();
        try {
            resp = this.createBackendNioClient(clusterId).actionScript(req);
            } catch (WebClientResponseException e) {
                    resp.setCode(500);
                    resp.setMessage(e.getResponseBodyAsString());
                    e.printStackTrace();
            } catch (Exception e) {
                    resp.setCode(500);
                    resp.setMessage(e.getMessage());
                    e.printStackTrace();
            }
        log.debug("[actionScript] cluster id:{}, action:{}, arguments:{}, responseBody:{}",
                clusterId, action, arguments, resp);
        return resp;
    }

    @Override
    public BackendCommonResponse authDomainUser(String clusterId, String userName, String password) {
        AuthDomainUserRequest req = new AuthDomainUserRequest();
        req.setUsername(userName);
        req.setPassword(password);
        return this.createBackendNioClient(clusterId).authDomainUser(req);
    }

    @Override
    public ListAvailableTagsResponse listAvailableTags(String clusterId) {

        ListAvailableTagsResponse listAvailableTagsResponse = this.createBackendNioClient(clusterId).listAvailableTags();

        log.debug("[ listAvailableTags] cluster id:{}, responseBody:{}", clusterId, listAvailableTagsResponse);

        return listAvailableTagsResponse;
    }

    @Override
    public BackendCommonResponse modifyTags(String clusterId, String jobId, List<Tag> tags) {
        ModifyTagsRequest request = new ModifyTagsRequest();
        request.setJobId(jobId);
        request.setTags(tags);

        BackendCommonResponse backendCommonResponse = this.createBackendNioClient(clusterId).modifyTags(request);

        log.debug("[Backend modifyTags] cluster id:{}, responseBody:{}", clusterId, backendCommonResponse);
        return backendCommonResponse;
    }

    @Override
    public ShrinkNodesResponse shrinkNodes(String clusterId, String queueName, List<String> hostNames,
                                           int shrinkNum) {
        ShrinkNodesRequest req = new ShrinkNodesRequest();
        req.setQueueName(queueName);
        req.setNodes(hostNames);
        req.setShrinkNum(shrinkNum);
        return this.createBackendNioClient(clusterId).shrinkNodes(req);
    }

    private String getRequestEndpoint(String clusterId) {

        // 优化todo 增加缓存
        Instance instance = instanceDAOGateway.findMasterInstance(clusterId);
        log.debug("=====>instance: {}", instance);
        if (instance == null) {
            throw new CommonException.RelatedServiceException(clusterId + ": No master instance",
                    "cluster-api");
        }
        return String.format("http://%s:8100", instance.getFloatingIp());
    }

    private String getWorkflowEndpoint(String clusterId) {

        Instance instance = instanceDAOGateway.findMasterInstance(clusterId);
        return String.format("http://%s:8000", instance.getFloatingIp());
    }


}
