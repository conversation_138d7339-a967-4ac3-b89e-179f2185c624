package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.WorkflowfileDO;
import com.baidu.bce.logic.chpc.database.mapper.WorkflowfileMapper;
import com.baidu.bce.logic.chpc.gateway.WorkflowfileDAOGateway;
import com.baidu.bce.logic.chpc.model.Workflowfile;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;
import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowfileDynamicSqlSupport.workflowfile;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
@Service
public class WorkflowfileDAOGatewayImpl implements WorkflowfileDAOGateway {

    @Resource
    WorkflowfileMapper workflowfileMapper;

    @Override
    public Boolean createWorkflowfile(List<Workflowfile> workflowfileList) {
        // 批量插入工作流文件
        List<WorkflowfileDO> workflowfileDOS = BeanCopyUtil.copyListProperties(workflowfileList, WorkflowfileDO::new)
                .stream()
                .peek(ins -> {
                    ins.setCreatedTime(LocalDateTime.now());
                    ins.setUpdatedTime(LocalDateTime.now());
                    ins.setDeleted(false);
                }).collect(Collectors.toList());
        return workflowfileMapper.insertMultiple(workflowfileDOS) != 0;
    }

    @Override
    public List<Workflowfile> getWorkflowfile(String workflowId) {
        SelectStatementProvider selectStatement =
                select(WorkflowfileMapper.selectList)
                        .from(workflowfile)
                        .where(workflowfile.deleted, isEqualTo(false))
                        .and(workflowfile.workflowId, isEqualTo(workflowId))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);

        List<WorkflowfileDO> workflowDO = workflowfileMapper.selectMany(selectStatement);

        return BeanCopyUtil.copyListProperties(workflowDO, Workflowfile::new);
    }


    @Override
    public List<Workflowfile> getWorkflowfileByVersion(String workflowId, Long version) {
        SelectStatementProvider selectStatement =
                select(WorkflowfileMapper.selectList)
                        .from(workflowfile)
                        .where(workflowfile.deleted, isEqualTo(false))
                        .and(workflowfile.workflowId, isEqualTo(workflowId))
                        .and(workflowfile.version, isEqualTo(version))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);

        List<WorkflowfileDO> workflowDO = workflowfileMapper.selectMany(selectStatement);

        return BeanCopyUtil.copyListProperties(workflowDO, Workflowfile::new);
    }

    @Override
    public Boolean deleteWorkflowfile(String workflowId) {
        WorkflowfileDO workflowfileDO = new WorkflowfileDO();
        workflowfileDO.setDeleted(true);
        workflowfileDO.setDeletedTime(LocalDateTime.now());
        workflowfileDO.setWorkflowId(workflowId);
        return workflowfileMapper.updateByWorkflowId(workflowfileDO) != 0;
    }

    @Override
    public Boolean deleteWorkflowByfileType(String workflowId, String fileType) {
        WorkflowfileDO workflowfileDO = new WorkflowfileDO();
        workflowfileDO.setDeleted(true);
        workflowfileDO.setDeletedTime(LocalDateTime.now());
        workflowfileDO.setWorkflowId(workflowId);
        workflowfileDO.setType(fileType);
        return workflowfileMapper.updateByWorkflowId(workflowfileDO) != 0;
    }
}
