package com.baidu.bce.logic.chpc.cfs.client;

import com.baidu.bce.logic.chpc.cfs.MountTarget;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CfsMountPointResponse {
    private Boolean isTruncated;
    private String marker;
    private Integer maxKeys;
    @JsonProperty("mountTargetList")
    private List<MountTarget> mountTargetList;

}
