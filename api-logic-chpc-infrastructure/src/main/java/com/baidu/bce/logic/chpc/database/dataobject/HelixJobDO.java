package com.baidu.bce.logic.chpc.database.dataobject;

import java.time.LocalDateTime;

import jakarta.annotation.Generated;

public class HelixJobDO {
    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Long id;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime createdTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime updatedTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private LocalDateTime deleteTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private Boolean deleted;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Long getId() {
        return id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getUpdatedTime() {
        return updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public LocalDateTime getDeleteTime() {
        return deleteTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleteTime(LocalDateTime deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public Boolean getDeleted() {
        return deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getClusterId() {
        return clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setClusterId(String clusterId) {
        this.clusterId = clusterId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAccountId() {
        return accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getOrderId() {
        return orderId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getTaskId() {
        return taskId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobId() {
        return jobId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobProduct() {
        return jobProduct;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobProduct(String jobProduct) {
        this.jobProduct = jobProduct;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobStatus() {
        return jobStatus;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobStatus(String jobStatus) {
        this.jobStatus = jobStatus;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobRunTime() {
        return jobRunTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobRunTime(String jobRunTime) {
        this.jobRunTime = jobRunTime;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobChargeAmount() {
        return jobChargeAmount;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobChargeAmount(String jobChargeAmount) {
        this.jobChargeAmount = jobChargeAmount;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getAction() {
        return action;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setAction(String action) {
        this.action = action;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobPrice() {
        return jobPrice;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobPrice(String jobPrice) {
        this.jobPrice = jobPrice;
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobCouponPrice() {
        return jobCouponPrice;
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobCouponPrice(String jobCouponPrice) {
        this.jobCouponPrice = jobCouponPrice;
    }


    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobTokenLength() {
        return jobTokenLength;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobTokenLength(String jobTokenLength) {
        this.jobTokenLength = jobTokenLength;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getJobPackageDeduction() {
        return jobPackageDeduction;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setJobPackageDeduction(String jobPackageDeduction) {
        this.jobPackageDeduction = jobPackageDeduction;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getExtra() {
        return extra;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setExtra(String extra) {
        this.extra = extra;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String clusterId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String accountId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String platformId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public String getPlatformId() {
        return platformId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String orderId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String taskId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobId;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobProduct;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobStatus;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String action;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobRunTime;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobChargeAmount;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobPrice;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobCouponPrice;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobTokenLength;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String jobPackageDeduction;

    @Generated("org.mybatis.generator.api.MyBatisGenerator")
    private String extra;


}