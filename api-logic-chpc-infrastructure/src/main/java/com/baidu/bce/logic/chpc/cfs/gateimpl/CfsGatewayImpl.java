package com.baidu.bce.logic.chpc.cfs.gateimpl;

import com.baidu.bce.logic.chpc.cfs.FileSystem;
import com.baidu.bce.logic.chpc.cfs.MountTarget;
import com.baidu.bce.logic.chpc.cfs.client.CfsNioClient;
import com.baidu.bce.logic.chpc.cfs.client.CfsDetailResponse;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import org.springframework.stereotype.Service;

import java.util.List;

import jakarta.annotation.Resource;

@Service
public class CfsGatewayImpl implements CfsGateway {

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private CfsNioClient cfsNioClient;

    @Override
    public List<FileSystem> getCfsFileSystem(String cfsId) {
        CfsDetailResponse cfsFileSystems = cfsNioClient.getCfsFileSystem(cfsId, LogicUserService.getAccountId());
        return cfsFileSystems.getFileSystemList();
    }

    @Override
    public List<MountTarget> getCfsMountPoint(String cfsId) {
        return cfsNioClient.getCfsMountPoint(cfsId, LogicUserService.getAccountId()).getMountTargetList();
    }

}
