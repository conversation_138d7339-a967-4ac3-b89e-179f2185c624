package com.baidu.bce.logic.chpc.gatewayimpl.database;

import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.database.dataobject.WorkflowDO;
import com.baidu.bce.logic.chpc.database.mapper.WorkflowMapper;
import com.baidu.bce.logic.chpc.gateway.WorkflowDAOGateway;
import com.baidu.bce.logic.chpc.model.Workflow;
import org.mybatis.dynamic.sql.render.RenderingStrategies;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import java.util.Comparator;

import static com.baidu.bce.logic.chpc.database.mapper.local.WorkflowDynamicSqlSupport.workflow;
import static org.mybatis.dynamic.sql.SqlBuilder.select;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isLikeWhenPresent;

@Service
public class WorkflowDAOGatewayImpl implements WorkflowDAOGateway {

    @Resource
    private WorkflowMapper workflowMapper;

    @Override
    public Boolean createWorkflow(Workflow workflow) {
        WorkflowDO workflowDO = new WorkflowDO();
        BeanUtils.copyProperties(workflow, workflowDO);
        workflowDO.setDeleted(false);
        workflowDO.setCreatedTime(LocalDateTime.now());
        workflowDO.setUpdatedTime(LocalDateTime.now());
        return workflowMapper.insert(workflowDO) != 0;
    }

    @Override
    public List<Workflow> listWorkflow(String workspaceId, String name, String accountId) {
        SelectStatementProvider selectStatement =
                select(WorkflowMapper.selectList)
                        .from(workflow)
                        .where(workflow.deleted, isEqualTo(false))
                        .and(workflow.workspaceId, isEqualTo(workspaceId))
                        .and(workflow.accountId, isEqualTo(accountId))
                        .and(workflow.name, isLikeWhenPresent("%" + name + "%"))
                        .orderBy(workflow.updatedTime.descending())
                        .build()
                        .render(RenderingStrategies.MYBATIS3);

        List<WorkflowDO> workflowDOList = workflowMapper.selectMany(selectStatement);

        Map<String, WorkflowDO> workflowMap = new HashMap<>();

        for (WorkflowDO workflow : workflowDOList) {
            String workflowId = workflow.getWorkflowId();
            if (!workflowMap.containsKey(workflowId) || workflowMap.get(workflowId).getVersion() < workflow.getVersion()) {
                workflowMap.put(workflowId, workflow);
            }
        }

        List<WorkflowDO> workflowDO = new ArrayList<>(workflowMap.values());

        Collections.sort(workflowDO, new Comparator<WorkflowDO>() {
            @Override
            public int compare(WorkflowDO o1, WorkflowDO o2) {
                return o2.getUpdatedTime().compareTo(o1.getUpdatedTime());
            }
        });

        return BeanCopyUtil.copyListProperties(workflowDO, Workflow::new);
    }

    @Override
    public Workflow getWorkflow(String workflowId, String accountId) {
        SelectStatementProvider selectStatement =
                select(WorkflowMapper.selectList)
                        .from(workflow)
                        .where(workflow.workflowId, isEqualTo(workflowId))
                        .and(workflow.deleted, isEqualTo(false))
                        .and(workflow.accountId, isEqualTo(accountId))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
        List<WorkflowDO> workflowDOList = workflowMapper.selectMany(selectStatement);
        WorkflowDO workflowDO = null;
        Collections.sort(workflowDOList, (o1, o2) -> Long.compare(o2.getVersion(), o1.getVersion()));
        if (workflowDOList.size() > 0) {
            workflowDO = workflowDOList.get(0);
        }
        return BeanCopyUtil.copyObject(workflowDO, Workflow::new);
    }


    @Override
    public Workflow getWorkflowByVersion(String workflowId, String accountId, Long version) {
        SelectStatementProvider selectStatement =
                select(WorkflowMapper.selectList)
                        .from(workflow)
                        .where(workflow.workflowId, isEqualTo(workflowId))
                        .and(workflow.deleted, isEqualTo(false))
                        .and(workflow.accountId, isEqualTo(accountId))
                        .and(workflow.version, isEqualTo(version))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
        WorkflowDO workflowDO = workflowMapper.selectOne(selectStatement).orElse(null);
        return BeanCopyUtil.copyObject(workflowDO, Workflow::new);
    }

    @Override
    public List<Workflow> getWorkflowByName(String workspaceId, String workflowName, String accountId) {
        SelectStatementProvider selectStatement =
                select(WorkflowMapper.selectList)
                        .from(workflow)
                        .where(workflow.name, isEqualTo(workflowName))
                        .and(workflow.deleted, isEqualTo(false))
                        .and(workflow.accountId, isEqualTo(accountId))
                        .and(workflow.workspaceId, isEqualTo(workspaceId))
                        .build()
                        .render(RenderingStrategies.MYBATIS3);
        List<WorkflowDO> workflowDOList = workflowMapper.selectMany(selectStatement);
        return BeanCopyUtil.copyListProperties(workflowDOList, Workflow::new);
    }

    @Override
    public Boolean updateWorkflow(Workflow workflow, String accountId) {
        WorkflowDO workflowDO = new WorkflowDO();
        workflowDO.setWorkflowId(workflow.getWorkflowId());
        workflowDO.setDescription(workflow.getDescription());
        workflowDO.setVersion(workflow.getVersion());
        workflowDO.setIntroduction(workflow.getIntroduction());
        workflowDO.setDocument(workflow.getDocument());
        workflowDO.setInputs(workflow.getInputs());
        workflowDO.setAccountId(accountId);
        workflowDO.setUpdatedTime(LocalDateTime.now());
        return workflowMapper.updateByWorkflowId(workflowDO) != 0;
    }

    @Override
    public Boolean deleteWorkflow(String workflowId, String accountId) {
        WorkflowDO workflowDO = new WorkflowDO();
        workflowDO.setDeleted(true);
        workflowDO.setDeletedTime(LocalDateTime.now());
        workflowDO.setWorkflowId(workflowId);
        workflowDO.setAccountId(accountId);
        return workflowMapper.updateByWorkflowId(workflowDO) != 0;
    }
}
