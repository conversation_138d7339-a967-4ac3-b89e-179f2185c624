package com.baidu.bce.logic.chpc.bcc.client;

import com.baidu.bce.internalsdk.bcc.model.DeleteServerRequest;
import com.baidu.bce.internalsdk.bcc.model.RebootServerRequest;
import com.baidu.bce.internalsdk.bcc.model.ServerDetail;
import com.baidu.bce.internalsdk.bcc.model.StartServerRequest;
import com.baidu.bce.internalsdk.bcc.model.StopServerRequest;
import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.bcc.BccInstanceDetailResponse;
import com.baidu.bce.logic.chpc.bcc.ListBccDetailRequest;
import com.baidu.bce.logic.chpc.bcc.ListBccDetailResponse;
import com.baidu.bce.logic.chpc.bcc.ListBccInstancesRequest;
import com.baidu.bce.logic.chpc.bcc.ListBccInstancesResponse;
import com.baidu.bce.logic.chpc.bcc.RebuildBatchInstanceRequest;

import java.util.Map;

import org.springframework.web.reactive.function.client.WebClient;

public class LogicBccNioClient extends BceNioClient {

    private static final String DEFAULT_FROM = "logic-chpc";

    public LogicBccNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public ListBccDetailResponse getInstanceDetails(ListBccDetailRequest request, String accountId) {
        return webClient.post()
                .uri(uriBuilder -> uriBuilder.path("/v1/bcc/instance/byInstanceIds").build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .header("from", DEFAULT_FROM)
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(ListBccDetailResponse.class).block();
    }

    public ListBccInstancesResponse listBccInstances(ListBccInstancesRequest request, String accountId) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder.path("/v2/instance")
                .queryParam("marker", request.getMarker())
                .queryParam("status", request.getStatus())
                .queryParam("maxKeys", request.getMaxKeys())
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .retrieve()
                .bodyToMono(ListBccInstancesResponse.class).block();
    }

    public BccInstanceDetailResponse getBccInstanceDetail(String instanceId, String accountId) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder.path("/v2/instance" + instanceId)
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .retrieve()
                .bodyToMono(BccInstanceDetailResponse.class).block();
    }

    public Void rebuildBatchInstance(RebuildBatchInstanceRequest request, String ak, String accountId) {
        return webClient.put()
                .uri(uriBuilder -> uriBuilder.path("/v2/instance/rebuild")
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .header("encrypted-key", ak)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(Void.class).block();
    }

    public ServerDetail getBccServerDetail(String serverId, String accountId) {
        return webClient.get()
                .uri(uriBuilder -> uriBuilder.path("/api/logical/bcc/v1/instance/" + serverId)
                .queryParam("from", "api")
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .retrieve()
                .bodyToMono(ServerDetail.class).block();
    }

    public Void rebootServer(RebootServerRequest rebootRequest, String accountId) {
        return webClient.put()
                .uri(uriBuilder -> uriBuilder.path("/api/logical/bcc/v1/instance/reboot")
                .queryParam("from", "api")
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(rebootRequest)
                .retrieve()
                .bodyToMono(Void.class).block();
    }

    public Void stopServer(StopServerRequest stopRequest, String accountId) {
        return webClient.put()
                .uri(uriBuilder -> uriBuilder.path("/api/logical/bcc/v1/instance/stop")
                .queryParam("from", "api")
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(stopRequest)
                .retrieve()
                .bodyToMono(Void.class).block();
    }

    public Void startServer(StartServerRequest startRequest, String accountId) {
        return webClient.put()
                .uri(uriBuilder -> uriBuilder.path("/api/logical/bcc/v1/instance/start")
                .queryParam("from", "api")
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(startRequest)
                .retrieve()
                .bodyToMono(Void.class).block();
    }


    public Void deleteServer(DeleteServerRequest deleteRequest, String accountId) {
       return webClient.post()
                .uri(uriBuilder -> uriBuilder.path("/api/logical/bcc/v1/instance/delete")
                .queryParam("from", "api")
                .build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(deleteRequest)
                .retrieve()
                .bodyToMono(Void.class).block();
    }
}