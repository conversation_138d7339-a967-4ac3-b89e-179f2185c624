SET NAMES utf8mb4;
SET
FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for t_chpc_cfs
-- ----------------------------
DROP TABLE IF EXISTS `t_chpc_cfs`;
CREATE TABLE `t_chpc_cfs`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT,
    `created_time`     datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '创建时间',
    `updated_time`     datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `deleted`          tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识: 0-未删除， 1-删除',
    `cluster_id`       varchar(128) NOT NULL DEFAULT '' COMMENT '集群id',
    `cfs_id`           varchar(255) NOT NULL DEFAULT '' COMMENT 'cfs id',
    `cfs_type`         varchar(32)  NOT NULL DEFAULT '' COMMENT 'cfs类型',
    `name`             varchar(128)          DEFAULT NULL COMMENT 'cfs名称',
    `storage_protocol` varchar(255) NOT NULL DEFAULT '' COMMENT '存储协议',
    `mount_target`     varchar(128) NOT NULL DEFAULT '' COMMENT '挂载地址',
    `mount_dir`        varchar(128) NOT NULL DEFAULT '' COMMENT '实例id',
    PRIMARY KEY (`id`),
    KEY                `udx_cluster_id_cfs_id` (`cluster_id`,`cfs_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for t_chpc_cluster
-- ----------------------------
DROP TABLE IF EXISTS `t_chpc_cluster`;
CREATE TABLE `t_chpc_cluster`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT,
    `created_time`      datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '创建时间',
    `updated_time`      datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `deleted`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识: 0-未删除， 1-删除',
    `cluster_id`        varchar(128) NOT NULL DEFAULT '' COMMENT '集群名称',
    `name`              varchar(255) NOT NULL DEFAULT '' COMMENT '集群名称',
    `description`       varchar(255) NOT NULL DEFAULT '' COMMENT '集群描述',
    `vpc_id`            varchar(255) NOT NULL DEFAULT '' COMMENT 'vpc id',
    `subnet_id`         varchar(128)          DEFAULT '' COMMENT '子网id',
    `security_group_id` varchar(128)          DEFAULT NULL COMMENT '安全组id',
    `status`            varchar(64)  NOT NULL DEFAULT '' COMMENT '集群状态',
    `logical_zone`      varchar(64)           DEFAULT '' COMMENT '集群所属的逻辑地域',
    `scheduler_type`    varchar(32)  NOT NULL DEFAULT '' COMMENT '调度器类型',
    `enable_ha`         tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启高可用  0-未开启， 1-开启',
    `account_id`        varchar(128) NOT NULL COMMENT '账户ID',
    `extra`             text COMMENT '保存一些额外信息',
    `enable_monitor`    tinyint(1) DEFAULT NULL COMMENT '是否开启集群监控预',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_cluster_id` (`cluster_id`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for t_chpc_group
-- ----------------------------
DROP TABLE IF EXISTS `t_chpc_group`;
CREATE TABLE `t_chpc_group`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT,
    `cluster_id`        varchar(128) NOT NULL DEFAULT '' COMMENT '集群id',
    `group_id`          varchar(128) NOT NULL DEFAULT '' COMMENT '队列id',
    `name`              varchar(255) NOT NULL DEFAULT '' COMMENT '队列名称',
    `is_default`        tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为默认队列  0-不是， 1-是',
    `status`            varchar(64)           DEFAULT NULL COMMENT '状态',
    `deleted`           tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识: 0-未删除， 1-删除',
    `created_time`      datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '创建时间',
    `updated_time`      datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `default_spec`      varchar(64)           DEFAULT NULL COMMENT 'bcc实例规格',
    `default_image_id`  varchar(64)           DEFAULT NULL COMMENT 'bcc实例镜像',
    `default_user_data` varchar(64)           DEFAULT NULL COMMENT '默认的user data',
    `is_auto_scale`     tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否开启自动伸缩  0-不是， 1-是',
    `has_auto_scale`    tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有自动伸缩  0-没有， 1-有',
    PRIMARY KEY (`id`),
    UNIQUE KEY `udx_cluster_id_name_deleted` (`cluster_id`, `name`, `deleted`) USING BTREE,
    KEY                 `udx_cluster_id_group_id` (`cluster_id`,`group_id`),
    KEY                 `idx_group_id` (`group_id`)
) ENGINE=InnoDB AUTO_INCREMENT=78 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for t_chpc_instance
-- ----------------------------
DROP TABLE IF EXISTS `t_chpc_instance`;
CREATE TABLE `t_chpc_instance`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT,
    `created_time`         datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '创建时间',
    `updated_time`         datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `deleted`              tinyint(2) NOT NULL DEFAULT '0' COMMENT '删除标识: 0-未删除， 1-删除',
    `cluster_id`           varchar(128) NOT NULL DEFAULT '' COMMENT '集群id',
    `instance_id`          varchar(255) NOT NULL DEFAULT '' COMMENT '实例id',
    `instance_uuid`        varchar(255)          DEFAULT NULL COMMENT '实例长id',
    `group_id`             varchar(128)          DEFAULT '' COMMENT '队列id',
    `instance_type`        varchar(64)  NOT NULL DEFAULT '' COMMENT '是否为主节点',
    `eip_network_capacity` int(10) NOT NULL DEFAULT '0' COMMENT '实例对应的eip带宽，为0代表不开启',
    `cos_stack_id`         varchar(64)  NOT NULL COMMENT 'cos对应的资源栈',
    `host_name`            varchar(64)           DEFAULT NULL COMMENT 'host name',
    `oos_execution_id`     varchar(64)           DEFAULT NULL COMMENT 'oos对应执行任务Id',
    `status`               varchar(64)           DEFAULT NULL COMMENT '集群实例状态',
    `spec`                 varchar(64)           DEFAULT NULL COMMENT '实例规格',
    `private_ip`           varchar(128)          DEFAULT NULL COMMENT '实例私网IP',
    `public_ip`            varchar(128)          DEFAULT NULL COMMENT '实例公网IP',
    `floating_ip`          varchar(128)          DEFAULT NULL COMMENT '实例浮动IP',
    `subnet_id`            varchar(128)          DEFAULT NULL COMMENT '实例子网ID',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_instance_id` (`instance_id`) USING BTREE,
    UNIQUE KEY `udx_cluster_id_group_id_instance_id` (`cluster_id`,`group_id`,`instance_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=62 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for t_chpc_task
-- ----------------------------
DROP TABLE IF EXISTS `t_chpc_task`;
CREATE TABLE `t_chpc_task`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `created_time` datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '创建时间',
    `updated_time` datetime     NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT '更新时间',
    `deleted`      tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标识: 0-未删除， 1-删除',
    `status`       varchar(64)           DEFAULT '' COMMENT '任务状态',
    `task_id`      varchar(255) NOT NULL DEFAULT '' COMMENT '任务ID',
    `task_type`    varchar(255) NOT NULL COMMENT '任务类型',
    `cluster_id`   varchar(128) NOT NULL COMMENT '集群id',
    `group_id`     varchar(128)          DEFAULT NULL COMMENT '队列id',
    `source`       varchar(255)          DEFAULT NULL COMMENT '任务来源',
    `extra`        text COMMENT '保存一些额外信息',
    PRIMARY KEY (`id`),
    KEY            `idx_task_id` (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=111 DEFAULT CHARSET=utf8;


