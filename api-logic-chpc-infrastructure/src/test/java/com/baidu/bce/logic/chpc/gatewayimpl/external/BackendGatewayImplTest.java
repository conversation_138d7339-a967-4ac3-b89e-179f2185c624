package com.baidu.bce.logic.chpc.gatewayimpl.external;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.logic.chpc.common.ChpcClientFactory;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.client.BackendNioClient;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.model.Instance;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {
        "helix.helixvs.cluster=test-cluster",
        "helix.helixfold3.cluster=test-cluster"
})
public class BackendGatewayImplTest {

    @Mock
    private ChpcClientFactory chpcClientFactory;

    @Mock
    private InstanceDAOGateway instanceDAOGateway;

    @Mock
    private ClusterDAOGateway clusterDAOGateway;

    @Mock
    private BackendNioClient backendNioClient;

    @InjectMocks
    private BackendGatewayImpl backendGateway;

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(backendGateway, "helixfold3Cluster", "helixfold3-cluster1234567890");
        ReflectionTestUtils.setField(backendGateway, "helixvsCluster", "helixvs-cluster1234567890");
        Instance instance = new Instance();
        instance.setFloatingIp("test");
        when(instanceDAOGateway.findMasterInstance(anyString())).thenReturn(instance);
        when(chpcClientFactory.createBackendClient(anyString())).thenReturn(backendNioClient);
    }

    @Test
    public void testActionProxyRetry() {
        BackendActionProxyResponse resp = new BackendActionProxyResponse();
        resp.setCode(200);
        resp.setData("\"jobId123\"");

        try {
            backendGateway.actionProxy("test-cluster", "test-action", "test-arguments");
        } catch (Exception e) {
        }

        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenReturn(resp);

        BackendActionProxyResponse result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments");
        assertEquals(resp, result);

        WebClientResponseException exception = new WebClientResponseException(500, "test", null, "Read timed out".getBytes(), null);
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(exception);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments");
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器连接失败");
        }

        exception = new WebClientResponseException(500, "test", null, "MessageBodyReader not found".getBytes(), null);
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(exception);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments");
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器节点连接失败");
        }

        exception = new WebClientResponseException(500, "test", null, "InvokeScript fail".getBytes(), null);
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(exception);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments");
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器插件脚本调用失败");
        }

        Exception excpt = new BceException("Read timed out");
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(excpt);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments");
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器连接失败");
        }

        excpt = new BceException("MessageBodyReader not found");
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(excpt);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments");
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器节点连接失败");
        }

        excpt = new BceException("InvokeScript fail");
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(excpt);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments");
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器插件脚本调用失败");
        }
    }

    @Test
    public void testActionProxyNoRetry() {
        BackendActionProxyRequest req = new BackendActionProxyRequest();
        req.setAction("test-action");
        req.setArguments("test-arguments");
        req.setNoRetry(true);

        BackendActionProxyResponse resp = new BackendActionProxyResponse();
        resp.setCode(200);
        resp.setData("\"jobId123\"");

        try {
            backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        } catch (Exception e) {
        }

        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenReturn(resp);

        BackendActionProxyResponse result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        assertEquals(resp, result);

        WebClientResponseException exception = new WebClientResponseException(500, "test", null, "Read timed out".getBytes(), null);
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(exception);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器连接失败");
        }

        exception = new WebClientResponseException(500, "test", null, "MessageBodyReader not found".getBytes(), null);
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(exception);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器节点连接失败");
        }

        exception = new WebClientResponseException(500, "test", null, "InvokeScript fail".getBytes(), null);
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(exception);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器插件脚本调用失败");
        }

        exception = new WebClientResponseException(500, "test", null, "test".getBytes(), null);
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(exception);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "test");
        }


        Exception excpt = new BceException("Read timed out");
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(excpt);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器连接失败");
        }

        excpt = new BceException("MessageBodyReader not found");
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(excpt);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器节点连接失败");
        }

        excpt = new BceException("InvokeScript fail");
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(excpt);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "调度器插件脚本调用失败");
        }

        excpt = new BceException("test");
        when(backendNioClient.actionProxy(any(BackendActionProxyRequest.class))).thenThrow(excpt);
        try {
            result = backendGateway.actionProxy("test-cluster", "test-action", "test-arguments", true);
        } catch (Exception e) {
            assertEquals(e.getMessage(), "test");
        }
        
    }
}