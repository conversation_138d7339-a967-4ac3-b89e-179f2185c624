#!/usr/bin/env bash

##########################################################################################
## Do something after maven build.
##
## Include the following content:
## - Change directory
## - Delete some temporary files or directories
## - Create a output directory
## - Move files
##########################################################################################

PROJECT_NAME=api-logic-chpc-start-version
WORK_DIR=$(dirname $0)
WORK_DIR=$(
  cd ${WORK_DIR}
  pwd
)
echo "Work dir: ${WORK_DIR}"

echo "Prepare package structure"
OUTPUT_DIR=${WORK_DIR}/output
if [[ -d ${OUTPUT_DIR} ]]; then
  echo "Remove output dir ..."
  rm -rf ${OUTPUT_DIR}
fi
echo "Make output dir ..."
mkdir -p ${OUTPUT_DIR}
echo "Switch to output dir"
cd ${OUTPUT_DIR}
mkdir logs
mkdir tmp
mkdir data

echo "Copy ${PROJECT_NAME}.jar to output dir..."
cp ${WORK_DIR}/api-logic-chpc-start/target/${PROJECT_NAME}.jar ${OUTPUT_DIR}

# 提取里面指定的文件到指定的位置
unzip -jo ${WORK_DIR}/api-logic-chpc-start/target/${PROJECT_NAME}.jar BOOT-INF/lib/transmittable-thread-local-2.13.2.jar -d ${OUTPUT_DIR}/agent

# 下载skywaling-agent-java
wget -O output.tar.gz --no-check-certificate --header "IREPO-TOKEN:3ab9e9cb-5a97-4662-a4e8-47a1159129e7" "https://irepo.baidu-int.com/rest/prod/v3/baidu/bce-api/skywalking-agent-java/releases/********/files"
tar zxvf output.tar.gz
mv output/skywalking-agent ${OUTPUT_DIR}
mv ${OUTPUT_DIR}/skywalking-agent skywalking-agent-10
rm -rf output.tar.gz
rm -rf output

for folder in ls $(ls ${WORK_DIR}/build/); do
  rm -rf ${OUTPUT_DIR}/${folder}
done
cp -rf ${WORK_DIR}/build/* ${OUTPUT_DIR}
find ${OUTPUT_DIR}/bin -name '*.sh' | xargs chmod 755

echo $(ls)" build success at dir "${OUTPUT_DIR}
exit 0
