Global:
  version: 2.0
  group_email: <EMAIL>
Default:
  profile : [dev]
Profiles:
  - profile:
    name: dev
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      resourceType: SMALL
      tools:
        - jdk: 21.0
        - maven: 3.8.6
    build:
      command: sh build.sh -q
      cache:
        enable: true
        type: REPO
        paths:
          - ~/.gradle
          - ~/.m2
    check:
      - reuse: TASK
        enable: true
        strategy: [ MULTI_BRANCH, CODE_REVIEW ]
    artifacts:
      release: true
  - profile:
    name:  publish
    mode: AGENT
    environment:
      image: DECK_STD_CENTOS7
      resourceType: SMALL
      tools:
        - jdk: 21.0
        - maven: 3.8.6
    build:
      command: sh publish.sh $AGILE_RELEASE_VERSION
      cache:
        enable: true
        type: REPO
        paths:
          - ~/.gradle
          - ~/.m2
    check:
      - reuse: TASK
        enable: true
        strategy: [ MULTI_BRANCH, CODE_REVIEW ]
    artifacts:
      release: false