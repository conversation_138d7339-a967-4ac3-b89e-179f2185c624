package com.baidu.bce.logic.chpc;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.ApplicationPidFileWriter;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.web.context.WebServerPortFileWriter;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * @Author: lilu24
 * @Date: 2022-12-01
 */
@ConfigurationPropertiesScan
@EnableTransactionManagement
@SpringBootApplication(scanBasePackages = "com.baidu.bce")
@EnableScheduling
public class LogicChpcApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(LogicChpcApplication.class);
        application.addListeners(new ApplicationPidFileWriter());
        application.addListeners(new WebServerPortFileWriter());
        application.run(args);
    }
}
