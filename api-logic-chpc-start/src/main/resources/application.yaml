#该属性置为true的时候，核心Logger（包含嵌入式容器、hibernate、spring）会输出更多内容，但是你自己应用的日志并不会输出为DEBUG级别。
debug: true
info:
  app:
    encoding: '@project.build.sourceEncoding@'
    java:
      source: '@java.version@'
      target: '@java.version@'
    name: '@name@'
    version: '@project.version@'
#日志相关配置
logging:
  config: classpath:logback-spring.xml
  file:
    name: ${logging.file.path}/debug/${spring.application.name}.debug.log
    path: ${HOME}/${spring.application.name}/logs
  level:
    com:
      baidu: debug
    root: info
    tomcat: debug
    web: debug
  register-shutdown-hook: false
  # exception-conversion-word: "%wEx"
  logback:
    rollingpolicy:
      max-file-size: 100MB
      clean-history-on-start: false
      max-history: 180
      # total-size-cap: 0B
      # file-name-pattern:
    # pattern:
    # console:
    # dateformat:
    # file:
    # level: "%5p"
  charset:
    console: utf-8
    file: UTF-8
management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
    shutdown:
      enabled: false
  endpoints:
    web:
      base-path: /admin
      exposure:
        include:
          - health
          - metrics
          - prometheus
          - info
    enabled-by-default: true
  info:
    git:
      mode: full
  server:
    add-application-context-header: false
    port: ${server.port}
  metrics:
    export:
      jmx:
        enabled: true
      simple:
        enabled: true
    distribution:
      sla:
        http:
          server:
            requests: 10ms,30ms,50ms,100ms,200ms,500ms,1s,5s
    tags:
      application: ${spring.application.name}
      region: bj
      stack: sandbox
server:
  port: 8990
  servlet:
    context-path: /
    session:
      timeout: 30m
    encoding:
      # HTTP 请求和响应的字符集。如果未明确设置，则添加到“Content-Type”标头
      charset: UTF-8
      #是否启用http编码支持
      enabled: true
      #是否在HTTP请求和响应上强制编码到已配置的字符集
      force: true
      # 是否在HTTP请求中强制编码到配置的字符集。如果未指定“force”，则默认为true
      force-request: true
      # 是否在HTTP响应中强制编码到配置的字符集
      force-response: true
  server-header: BWS
  compression:
    # 是否启用了响应压缩
    enabled: false
    min-response-size: 2048KB
  tomcat:
    mbeanregistry:
      # Tomcat Metrics
      enabled: true
    accesslog:
      enabled: true
      #打开Tomcat的Access日志，并可以设置日志格式的方法：
      request-attributes-enabled: true
      #accesslog目录，默认在basedir/logs
      #directory: /tmp/logs
      encoding: UTF-8
      #是否缓冲输出，使其仅定期刷新
      buffered: true
      max-days: 30
    #存放Tomcat的日志、Dump等文件的临时文件夹，默认为系统的tmp文件夹（如：C:\Users\<USER>\AppData\Local\Temp）
    basedir: ${HOME}/${spring.application.name}
    uri-encoding: UTF-8
    connection-timeout: 20s
    relaxed-path-chars: <,>,[,\,],^,`,{,|,}
    relaxed-query-chars: <,>,[,\,],^,`,{,|,}
    # 当所有可能的请求处理线程都在使用时，传入连接请求的最大队列长度
    accept-count: 100
    # 服务器在任何给定时间接受和处理的最大连接数
    max-connections: 10000
    threads:
      # 最小工作线程数
      min-spare: 10
      # 最大工作线程数
      max: 800
    max-keep-alive-requests: 100
  #开启优雅停机
  shutdown: graceful
# 应用名称
spring:
  banner:
    charset: UTF-8
  aop:
    # 是否要创建基于子类的（CGLIB）代理（true），而不是基于标准Java接口的代理（false）
    proxy-target-class: true
    auto: true
  application:
    name: api-logic-chpc
    admin:
      # 是否为应用程序启用管理功能
      enabled: true
  mvc:
    contentnegotiation:
      # 是否应使用请求参数（默认为“format”）来确定请求的媒体类型
      favor-parameter: false
    # 是否将TRACE请求分派给FrameworkServlet doService方法
    dispatch-trace-request: false
    # 是否将OPTIONS请求分派给FrameworkServlet doService方法
    dispatch-options-request: true
    # 是否启用Spring的FormContentFilter
    formcontent:
      filter:
        enabled: true
    # 是否启用Spring的HiddenHttpMethodFilter
    hiddenmethod:
      filter:
        enabled: true
    # 在重定向场景中是否应忽略“默认”模型的内容
    ignore-default-model-on-redirect: true
    # 是否启用由“HandlerExceptionResolver”解析的异常的警告日志记录，“DefaultHandlerExceptionResolver”除外
    log-resolved-exception: false
    servlet:
      # 加载调度程序servlet的启动优先级
      load-on-startup: -1
    # 如果没有找到Handler来处理请求，是否应该抛出“NoHandlerFoundException”
    throw-exception-if-no-handler-found: false
    converters:
      #converters 默认jackson 用于HTTP消息转换的首选JSON映射器。默认情况下，根据环境自动检测
      preferred-json-mapper: jackson
    # 是否允许在DEBUG和TRACE级别记录（可能敏感的）请求详细信息
    log-request-details: true
    async:
      request-timeout: 30s
  jackson:
    #日期格式化
    date-format: yyyy-MM-dd'T'HH:mm:ss'Z'
    #设置空如何序列化 控制序列化期间包含的属性。配置了Jackson的JsonInclude.Include枚举中的一个值
    default-property-inclusion: non_null
    # 枚举类DeserializationFeature中的枚举属性为key，值为boolean设置jackson反序列化特性,具体key请看DeserializationFeature源码
    deserialization:
      #允许对象忽略json中不存在的属性,默认true
      fail-on-unknown-properties: false
    # 常规默认,枚举类SerializationFeature中的枚举属性为key，值为boolean设置jackson序列化特性,具体key请看SerializationFeature源码
    serialization:
      #忽略无法转换的对象,默认true
      fail-on-empty-beans: false
      # 返回的java.util.date转换成timestamp
      write-dates-as-timestamps: true
      #格式化输出
      indent-output: false
    # 设置全局时区
    # time-zone: GMT+8
    time-zone: GMT
    visibility:
      # DEFAULT ：默认，public_only
      field: default
    # 设置属性命名策略,对应jackson下PropertyNamingStrategy中的常量值，SNAKE_CASE-返回的json驼峰式转下划线，json body下划线传到后端自动转驼峰式
    # property-naming-strategy: UPPER_CAMEL_CASE
    # 当地时区
    locale: zh
    # 枚举类MapperFeature中的枚举属性为key，值为boolean设置jackson ObjectMapper特性
    # ObjectMapper在jackson中负责json的读写、json与pojo的互转、json tree的互转,具体特性请看MapperFeature,常规默认即可
    mapper:
      # 使用getter取代setter探测属性，如类中含getName()但不包含name属性与setName()，传输的vo json格式模板中依旧含name属性
      #默认false
      use-getters-as-setters: false
    # 枚举类JsonParser.Feature枚举类中的枚举属性为key，值为boolean设置jackson JsonParser特性
    # JsonParser在jackson中负责json内容的读取,具体特性请看JsonParser.Feature，一般无需设置默认即可
    parser:
      # 是否允许出现单引号,默认false
      allow-single-quotes: false
      #允许出现特殊字符和转义符
      allow-unquoted-control-chars: true
    # 枚举类JsonGenerator.Feature枚举类中的枚举属性为key，值为boolean设置jackson JsonGenerator特性，一般无需设置默认即可
    # JsonGenerator在jackson中负责编写json内容,具体特性请看JsonGenerator.Feature
  main:
    banner-mode: console
    # 是否允许通过注册与现有定义同名的定义来覆盖bean定义
    allow-bean-definition-overriding: false
    register-shutdown-hook: true
  messages:
    basename: messages
    encoding: UTF-8
    # ＃是否使用消息代码作为默认消息而不是抛出“NoSuchMessageException”。仅在开发期间推荐
    use-code-as-default-message: true
    # 如果找不到特定区域设置的文件，是否回退到系统区域设置
    fallback-to-system-locale: true
  # 国际化显示英文提示信息
  web:
    locale: en_US
  info:
    build:
      encoding: UTF-8
    git:
      encoding: UTF-8
  jmx:
    enabled: true
  pid:
    # 如果使用ApplicationPidFileWriter但它无法写入PID文件，则失败
    fail-on-write-error: true
  lifecycle:
    #设置缓冲时间 默认30s
    timeout-per-shutdown-phase: 30s
  task:
    execution:
      pool:
        queue-capacity: 10
        core-size: 10
        max-size: 200
        allow-core-thread-timeout: true
        keep-alive: 60s
      shutdown:
        await-termination: true
        await-termination-period: 20s
      thread-name-prefix: asyncTaskThreadPool-
    scheduling:
      pool:
        size: 10
      shutdown:
        await-termination: true
        await-termination-period: 20s
      thread-name-prefix: task-scheduling-
  thymeleaf:
    cache: true
    check-template: true
    servlet:
      content-type: text/html
    enabled: true
    encoding: utf-8
    check-template-location: true
  security:
    #安全部分，roles必填否则会造成鉴权失败或者启动失败
    user:
      name: logic-chpc
      password: ae^S3mJ6
      roles: ENDPOINT_ADMIN
  sql:
    init:
      # 创建表的MySql语句位置
      # schema-locations:
      #  - classpath:TableCreationDDL.sql
      # 插入数据的MySql语句的位置
      #      data-locations:
      #        - classpath:data.sql
      mode: ALWAYS
      continue-on-error: true

#pagehelper
pagehelper:
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
  offset-as-page-num: true
  row-bounds-with-count: true
  helper-dialect: mysql
springdoc:
  show-actuator: true
  use-management-port: false
  api-docs:
    enabled: true
    version: OPENAPI_3_0
  swagger-ui:
    enabled: true
    display-request-duration: true
    groups-order: DESC
    operationsSorter: method
    disable-swagger-default-url: true
    use-root-path: true
  group-configs:
    - group: stores
      paths-to-match: /store/**
swagger:
  application-description: springdoc openapi with chpc
  application-name: ${spring.application.name}
  application-version: 1.0
  try-host: http://localhost:${server.port}
logback:
  access:
    enabled: true
    config: classpath:logback-access-spring.xml
    local-port-strategy: server
    tee-filter:
      enabled: true
    tomcat:
      request-attributes-enabled: true
bce:
  web:
    endpoint:
      config: classpath:endpoint.json
      default-region-name: bj
    idgen:
      data-center-id: 1
      worker-id: 2
      enabled: true
    log:
      detail: true
      enabled: true
      request-id-url-pattern: /*
      via: console
    trace:
      log-module-name: bce-chpc
      log-format-request-in: true
      log-format-request-out: true
    commons:
      region:
        current-region: bj
      idempotent:
        enabled: true
      iam:
        console-username: chpc
        console-password: CDyftmfwBhm8cwJSt2aW9nYOLF7nju96
        accessKey: ALTAKRdaQc8i1vNPaCPVmlyG5g
        secretKey: 328324bff01b4411b6cdb98d720e6042
        token-check-paths-excluded: /doc.html;/swagger**/**;/v2/api-docs;/favicon.ico;/error;/webjars/**;/v3/api-docs/**
        sts-role-name: BceServiceRole_chpc
      userlog:
        is-async: true

# chpc custom config
chpc:
  sts:
    policy:
      id: accf409402cf40458363708dffa8ced3
    service:
      account:
        id: f1ada9c007844f71a3302b12d4774c9e
  task:
    wait-times: 40
mybatis:
  configuration:
    map-underscore-to-camel-case: true
app:
  id: ${spring.application.name}
apollo:
  # 配置远程地址
  meta: http://gzbh-sandbox12-6271.gzbh.baidu.com:8180,http://gzbh-sandbox13-6271.gzbh.baidu.com:8180,http://gzbh-sandbox14-6271.gzbh.baidu.com:8180
  cache-dir: ${HOME}/data
  cluster: local
  label: local
  override-system-properties: true
  property:
    order:
      enable: false
    names:
      cache:
        enable: false
  access-key:
    secret:
  bootstrap:
    # 和 @EnableApolloConfig 注解等效
    enabled: true
    eagerLoad:
      enabled: true
    # 使用 application,和datasource的命名空间配置
    namespaces: application,application.yaml

