<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- <contextListener class="com.ofpay.logback.TtlMdcListener"/> -->
    <statusListener class="ch.qos.logback.core.status.OnConsoleStatusListener" />
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%25.25t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} [%X{x-bce-request-id}][%X{sw_ctx}][%X{currentUser}] %line %X %.-10240m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="CONSOLE_LOG_CHARSET" value="${CONSOLE_LOG_CHARSET:-${file.encoding:-UTF-8}}"/>
    <property name="FILE_LOG_PATTERN"
              value="${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] %-40.40logger{39} : [%X{x-bce-request-id}][%X{sw_ctx}][%X{currentUser}] %line %X %.-10240m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="FILE_LOG_CHARSET" value="${FILE_LOG_CHARSET:-${file.encoding:-UTF-8}}"/>

    <springProperty scope="context" name="appName" source="spring.application.name" defaultValue="spring_access.log"/>

    <property name="ACCESS_LOG_FILE" value="${ACCESS_LOG_FILE:-${LOG_PATH:-${LOG_TEMP:-${java.io.tmpdir:-/tmp}}}/access_debug/${appName}.access_debug.log}"/>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <!--<pattern>${CONSOLE_LOG_PATTERN}</pattern>-->
            <!--<pattern>common</pattern>-->
            <!--<pattern>"%h|%t{yyyy/MM/dd HH:mm:ss}|%D|%requestContent|%responseContent"</pattern>-->
            <!--<pattern>logging uri: %requestURL | status code: %statusCode | bytes: %bytesSent | elapsed time: %elapsedTime | request-log: %magenta(%requestContent) | response-log: %cyan(%responseContent)</pattern>-->
            <pattern>%i{ClientIp} %h %l %u [%t{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai}] [%I] "%r" %s %b %D [%reqAttribute{skywalking.traceId}] [%reqAttribute{requestId}] "%i{Referer}" "%i{User-Agent}" %i{x-ssl-header}
                %n[请求]======&gt;%n%fullRequest%n[响应]&lt;======%n%.-10240fullResponse
            </pattern>
            <charset>${CONSOLE_LOG_CHARSET}</charset>
        </encoder>
    </appender>

    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder>
            <!--<pattern>${FILE_LOG_PATTERN}</pattern>-->
            <!--<pattern>combined</pattern>-->
            <pattern>%i{ClientIp} %h %l %u [%t{yyyy-MM-dd HH:mm:ss.SSS,Asia/Shanghai}] [%I] "%r" %s %b %D [%reqAttribute{skywalking.traceId}] [%reqAttribute{requestId}] "%i{Referer}" "%i{User-Agent}" %i{x-ssl-header}
                %n[请求]======&gt;%n%fullRequest%n[响应]&lt;======%n%.-10240fullResponse
            </pattern>
            <charset>${FILE_LOG_CHARSET}</charset>
        </encoder>
        <file>${ACCESS_LOG_FILE}</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!--<fileNamePattern>${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz}</fileNamePattern>-->
            <fileNamePattern>${LOGBACK_ROLLINGPOLICY_FILE_NAME_PATTERN:-${ACCESS_LOG_FILE}-%d{yyyy-MM-dd}/%d{yyyy-MM-dd}.%i.access.debug.log}</fileNamePattern>
            <!--<fileNamePattern>access.%d{yyyy-MM-dd}.log.zip</fileNamePattern>-->
            <cleanHistoryOnStart>${LOGBACK_ROLLINGPOLICY_CLEAN_HISTORY_ON_START:-false}</cleanHistoryOnStart>
            <maxFileSize>${LOGBACK_ROLLINGPOLICY_MAX_FILE_SIZE:-10MB}</maxFileSize>
            <totalSizeCap>${LOGBACK_ROLLINGPOLICY_TOTAL_SIZE_CAP:-0}</totalSizeCap>
            <maxHistory>${LOGBACK_ROLLINGPOLICY_MAX_HISTORY:-7}</maxHistory>
        </rollingPolicy>
    </appender>
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="FILE"/>

    <!--<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
        <evaluator> &lt;!&ndash; 默认为 ch.qos.logback.classic.boolex.JaninoEventEvaluator &ndash;&gt;
            <expression>(event.getRequestURI().contains("fileUpload"))</expression>
        </evaluator>
        <OnMatch>DENY</OnMatch>
        <OnMismatch>ACCEPT</OnMismatch>
    </filter>
    <encoder>
        <pattern>[请求]%n%fullRequest%n[请求时间]%date%n%n[响应]%n%fullResponse</pattern>
    </encoder>
</appender>-->
</configuration>