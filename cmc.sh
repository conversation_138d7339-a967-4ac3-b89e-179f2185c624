#!/usr/bin/env bash

# Be sure your script exits whenever encounter errors
set -e

# Be sure your charset is correct. eg: zh_CN.UTF-8
export LC_ALL=en_US.UTF-8
export LANG=en_US.UTF-8
export LANGUAGE=en_US.UTF-8

# Use DECK Environment Variables to set yours
# See http://buildcloud.baidu.com/submitter/5-env_declare#DECK_CENTOS6U3_K3 to find more.
#export PATH=$MAVEN_3_6_3_HOME:$ORACLEJDK_1_8_0_BIN:$PATH
#export PATH=$MAVEN_3_6_3_HOME:$ORACLEJDK_11_0_7_BIN:$PATH

#export PATH=$M2_BIN_V353:$ORACLEJDK_11_0_7_BIN:$PATH

#export JAVA_HOME=$ORACLEJDK_11_0_7_HOME

#export M2_HOME=$MAVEN_3_6_3_HOME