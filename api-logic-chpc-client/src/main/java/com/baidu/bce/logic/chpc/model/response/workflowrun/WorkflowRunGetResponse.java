package com.baidu.bce.logic.chpc.model.response.workflowrun;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class WorkflowRunGetResponse extends BaseResponse {
    private String name;
    private String inputs;
    private String outputs;
    private String runId;
    private Boolean callCaching;
    private String failureMode;
    private String workflowId;
    private String workflowName;
    private String status;
    private Long version;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;
}
