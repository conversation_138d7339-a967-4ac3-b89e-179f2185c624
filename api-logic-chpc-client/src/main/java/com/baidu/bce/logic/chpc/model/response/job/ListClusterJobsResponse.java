package com.baidu.bce.logic.chpc.model.response.job;

import java.util.List;

import com.baidu.bce.logic.chpc.model.Job;
import com.baidu.bce.logic.chpc.model.response.ListResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.EqualsAndHashCode;
import lombok.Data;

@EqualsAndHashCode(callSuper = true)
@Data
public class ListClusterJobsResponse extends ListResponse{

    @JsonProperty("jobs")
    List<Job> jobs;
}
