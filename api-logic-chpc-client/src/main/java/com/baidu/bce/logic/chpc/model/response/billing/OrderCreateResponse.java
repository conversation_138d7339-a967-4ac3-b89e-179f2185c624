package com.baidu.bce.logic.chpc.model.response.billing;

import java.sql.Timestamp;

import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(Include.NON_NULL)
public class OrderCreateResponse {
    private String orderId;
    private String accountId;    
    private String reason;
    private String itemKey;
    private OrderStatus status;
    private String subServiceType;
    private String productType;
    private String flavor;
    private String orderInfo;
    private Timestamp createTime;
    private Timestamp deleteTime;
    private Timestamp updateTime;
}
