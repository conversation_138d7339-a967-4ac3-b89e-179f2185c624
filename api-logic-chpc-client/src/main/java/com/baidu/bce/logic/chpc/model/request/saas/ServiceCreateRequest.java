package com.baidu.bce.logic.chpc.model.request.saas;

import jakarta.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ServiceCreateRequest {

        @Pattern(regexp = "^(SkyForm)$", message = "is invalid.")
        String resourceType; // 服务类型

        String accountId;

        String pfsId;

        String filesetId;

}
