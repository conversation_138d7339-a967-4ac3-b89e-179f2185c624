package com.baidu.bce.logic.chpc.model.request.instance;


import com.baidu.bce.logic.chpc.model.request.cluster.CdsDiskForCreate;
import lombok.Data;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Data
public class InstanceAddRequest {


    private Integer computeNodeNum = 0;

    private Integer rootDiskSizeInGb = 40;

    private String rootDiskStorageType = "ssd";

    private List<CdsDiskForCreate> cdsDisks;

    private String paymentTiming = "Postpaid";

    private String subnetId;

    private String spec;
}
