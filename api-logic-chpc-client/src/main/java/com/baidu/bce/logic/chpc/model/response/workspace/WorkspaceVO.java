package com.baidu.bce.logic.chpc.model.response.workspace;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WorkspaceVO {
    private String workspaceId;
    private String name;
    private String description;
    private String clusterId;
    private String bosBucket;
    private String status;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;
}
