package com.baidu.bce.logic.chpc.model.response.workflow;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.cromwell.DepFile;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class WorkflowGetResponse extends BaseResponse {

    private String name;
    private String description;
    private Long version;
    private String language;
    private String languageVersion;
    private String workspaceId;
    private String mainFileContent;
    private String mainFilePath;
    private List<DepFile> depFiles;
    private String configFileContent;
    private String configFilePath;
    private String introduction;
    private String document;
    private String inputs;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;
}
