package com.baidu.bce.logic.chpc.model.response.workflowrun;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WorkflowRunVO {
    private String name;
    private String inputs;
    private String outputs;
    private String workflowId;
    private String workflowName;
    private String runId;
    private String status;
    private Long version;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;
}
