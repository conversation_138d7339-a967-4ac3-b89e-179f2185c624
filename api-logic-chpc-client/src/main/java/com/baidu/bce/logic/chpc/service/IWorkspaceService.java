package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.model.Workspace;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceCreateResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceDeleteResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceGetResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.AvailableCluster;
import com.baidu.bce.logic.chpc.model.response.workspace.NameisExistResponse;
import com.baidu.bce.logic.chpc.cromwell.model.WorkspaceCreateRequest;

import java.util.List;

public interface IWorkspaceService {

    WorkspaceCreateResponse createWorkspace(WorkspaceCreateRequest workspaceAddRequest);

    List<Workspace> listWorkspace(String name);

    List<AvailableCluster> listAvailableCluster();

    WorkspaceGetResponse getWorkspace(String workspaceId);

    NameisExistResponse workspaceNameisExist(String workspaceName);

    WorkspaceDeleteResponse deleteWorkspace(String workspaceId);


}
