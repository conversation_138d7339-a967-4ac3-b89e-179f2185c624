package com.baidu.bce.logic.chpc.model.response.workflow;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class WorkflowCreateResponse extends BaseResponse {

    private String name;
    private String description;
    private String language;
    private String languageVersion;
    private String workflowId;
    private Long version;
    private String introduction;
    private String document;
    private String inputs;

}
