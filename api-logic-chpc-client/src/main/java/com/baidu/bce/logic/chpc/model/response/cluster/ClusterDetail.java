package com.baidu.bce.logic.chpc.model.response.cluster;

import java.time.LocalDateTime;
import java.util.List;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.response.cfs.CfsVO;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceVO;
import com.baidu.bce.logic.chpc.model.response.queue.QueueVO;
import com.baidu.bce.logic.chpc.model.response.user.User;
import com.baidu.bce.logic.chpc.software.model.SoftwareVersion;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ClusterDetail extends BaseResponse {

    ClusterInfo cluster;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ClusterInfo {

        String clusterId;

        String clusterName;

        String description;

        @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
        LocalDateTime createdTime;

        @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
        LocalDateTime updatedTime;

        String zoneName;

        String chargeType;

        String vpcId;

        String vpcName;

        String vpcCidr;

        String subnetsName;

        String subnetsCidr;

        String subnetId;

        String securityGroupName;

        String securityGroupId;

        String schedulerType;

        String schedulerVersion;

        boolean enableHa;

        String imageId;

        String errorMessage;

        String clusterType;

        String status;

        List<BccInfo> bccInfo;

        List<CfsVO> cfsInfo;

        List<QueueVO> queueInfo;

        Long[] jobPriorityRange;

        int runningNodeNum;

        int totalNodeNum;

        int errorNodeNum;

        Double cpuUsed12h;

        List<SoftwareVersion> softwareList;

        List<User> userList;

        String schedulerIp;

        String schedulerHost;

        boolean createdDone;

        private Integer maxNodes;

        private Integer maxCpus;

        private String domainAccount;

        private Boolean forbidDelete;

        private Instance loginNode;

        private Instance managerNode;

        private List<Tag> tags;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    public static class BccInfo {
        String nodeType;

        List<SpecCount> specCountList;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @AllArgsConstructor
    public static class SpecCount {
        String spec;
        int count;

    }

}
