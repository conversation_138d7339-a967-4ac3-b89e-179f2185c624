package com.baidu.bce.logic.chpc.model.response.instance;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author: lilu24
 * @Date: 2022-12-26
 */
@Data
public class InstanceVO {


    private String instanceId;

    private String queueName;

    private String nodeType;

    private Integer eipNetworkCapacity;

    private String cosStackId;

    private String privateIp;

    private String spec;

    private String imageId;

    private String hostName;

    private String subnetId;

    private String publicIp;

    private String status;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;


}
