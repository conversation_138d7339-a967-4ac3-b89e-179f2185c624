package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.software.model.InstallSoftwareRequest;
import com.baidu.bce.logic.chpc.software.model.InstallSoftwareResponse;
import com.baidu.bce.logic.chpc.software.model.QuerySoftwareForNodeRequest;
import com.baidu.bce.logic.chpc.software.model.QuerySoftwareForNodeResponse;
import com.baidu.bce.logic.chpc.software.model.SoftwareQueryResponse;
import com.baidu.bce.logic.chpc.software.model.UnInstallSoftwareRequest;
import com.baidu.bce.logic.chpc.software.model.UnInstallSoftwareResponse;

public interface ISoftwareService {

    SoftwareQueryResponse getSoftwareList(String clusterId, String name);

    InstallSoftwareResponse installSoftware(String clusterId, InstallSoftwareRequest request);

    UnInstallSoftwareResponse unInstallSoftware(String clusterId, UnInstallSoftwareRequest request);

    QuerySoftwareForNodeResponse getSoftwareRecordByInstance(String clusterId, QuerySoftwareForNodeRequest request);

}
