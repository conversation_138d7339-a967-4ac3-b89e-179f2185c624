package com.baidu.bce.logic.chpc.model.request.saas;

import jakarta.validation.constraints.Pattern;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResourceCreateRequest {

        @Pattern(regexp = "^(SkyForm)$", message = "is invalid.")
        String resourceType; // 服务类型

        @Pattern(regexp = "^.{0,250}$", message = "is invalid")
        String resourceData; // 数据目录

        String userId;

        String accountId;

}
