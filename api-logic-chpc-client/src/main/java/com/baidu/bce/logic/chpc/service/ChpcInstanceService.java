package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.model.request.instance.InstanceAddRequest;
import com.baidu.bce.logic.chpc.model.request.instance.InstanceOperationRequest;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceAddResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceOperationResponse;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */

public interface ChpcInstanceService {


    InstanceAddResponse addInstanceToCluster(String clusterId, String groupId,
                                             InstanceAddRequest request);

    InstanceOperationResponse removeInstanceFromCluster(String clusterId, String groupId, String instanceId,
                                                        InstanceOperationRequest request);

    InstanceOperationResponse stopInstanceFromCluster(String clusterId, String groupId, String instanceId,
                                                      InstanceOperationRequest request);


    InstanceOperationResponse startInstanceFromCluster(String clusterId, String groupId, String instanceId);


    InstanceOperationResponse rebootInstanceFromCluster(String clusterId, String groupId, String instanceId,
                                                        InstanceOperationRequest request);

}
