package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.model.request.user.UserModifyRequest;
import com.baidu.bce.logic.chpc.model.response.user.User;
import com.baidu.bce.logic.chpc.model.response.user.UserCommonResponse;

import java.util.List;

public interface UserService {
    List<User> getDomainUserList(String clusterId, String userName, String authority);

    UserCommonResponse addDomainUser(String clusterId, String userName, String userPassword, String authority);

    UserCommonResponse updateDomainUser(String clusterId, UserModifyRequest request);

    UserCommonResponse deleteDomainUser(String clusterId, String userName);

    BaseResponse changeDomainUserQos(String clusterID, String username, String qos);
}