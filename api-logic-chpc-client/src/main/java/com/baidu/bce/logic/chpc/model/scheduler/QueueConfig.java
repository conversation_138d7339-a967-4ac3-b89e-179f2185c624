package com.baidu.bce.logic.chpc.model.scheduler;
import java.util.Comparator;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;
@Data
public class QueueConfig {
    private List<UserMaxRunLimit> userMaxRunLimitList;
    private String queueName;

    public void sortUserList() {
        if (userMaxRunLimitList != null) {
            userMaxRunLimitList.sort(Comparator.comparing(UserMaxRunLimit::getUserName));
        }
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserMaxRunLimit {
        private String userName;
        private String mem;
        private String cpus;
        private String nodes;
        private String maxJobs;
    }

}
