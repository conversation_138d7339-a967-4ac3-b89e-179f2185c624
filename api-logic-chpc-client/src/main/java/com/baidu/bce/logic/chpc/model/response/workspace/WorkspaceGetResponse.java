package com.baidu.bce.logic.chpc.model.response.workspace;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class WorkspaceGetResponse extends BaseResponse {

    private String workspaceId;
    private String name;
    private String description;
    private String clusterId;
    private String clusterName;
    private String bosBucket;
    private String status;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;
}
