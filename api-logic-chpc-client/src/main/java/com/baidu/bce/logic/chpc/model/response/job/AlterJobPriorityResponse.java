package com.baidu.bce.logic.chpc.model.response.job;

import java.util.List;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.EqualsAndHashCode;
import lombok.Data;

@EqualsAndHashCode(callSuper = true)
@Data
public class AlterJobPriorityResponse extends BaseResponse{
    @JsonProperty("succeededList")
    List<AlterJobPriorityResult> succeededList;

    @JsonProperty("failedList")
    List<AlterJobPriorityResult> failedList;

    @JsonProperty("asyncOperation")
    boolean asyncOperation;

}
