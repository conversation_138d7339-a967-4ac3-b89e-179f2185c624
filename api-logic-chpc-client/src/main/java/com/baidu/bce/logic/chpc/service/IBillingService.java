package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.chpc.billing.LegacyChargeDataRequest;
import com.baidu.bce.logic.chpc.model.ChargeData;
import com.baidu.bce.logic.chpc.model.request.billing.BillingActiveRequest;
import com.baidu.bce.logic.chpc.model.response.billing.ActiveResourceResponse;
import com.baidu.bce.logic.chpc.model.response.billing.OrderCreateResponse;
import com.baidu.bce.logic.chpc.model.response.billing.OrderExecutorResponse;
import com.baidu.bce.logic.chpc.model.response.billing.OrderInfoResponse;
import com.baidu.bce.logic.chpc.model.response.billing.QueryResourceResponse;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;

public interface IBillingService {

    // 创建订单
    OrderCreateResponse newOrder(CreateOrderRequest<CreateNewTypeOrderItem> orderRequest, String userId);

    // 订单执行器
    // 执行订单，开始创建资源
    OrderExecutorResponse execute(String orderId);

    // 查询订单是否执行完成
    OrderExecutorResponse check(String orderId);

    // 查询订单信息
    OrderInfoResponse queryOrder(String orderId);

    // 订单详情页
    Order queryOrderDetail(String orderId);

    // 取消订单
    OrderExecutorResponse terminate(String orderId, String status, String serviceType);

    // billing 回调
    // 查询资源状态
    QueryResourceResponse queryResource(String resourceUuid, String accountId);

    // 启停资源
    ActiveResourceResponse activateResource(String resourceUuid, String accountId,
            String action, BillingActiveRequest billingActiveForm);

    // 推送计费用量
    Boolean chargeData(String accountId, LegacyChargeDataRequest request);

    // 查询计费数据
    Boolean resourceUsageTrail(ChargeData chargeData, String startTime, String endTime, String region);

    // 构建计费数据
    LegacyChargeDataRequest makeChargeData(String userId, String serviceName, String chargeItem,
                                           String instanceId,
                                           String sum, String unit, Long chargeDataTime);

}
