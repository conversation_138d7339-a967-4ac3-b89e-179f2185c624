package com.baidu.bce.logic.chpc.model.response.job;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class BatchJobResponse extends BaseResponse {

    private List<JobItem> successJobItems;

    private List<JobItem> failedJobItems;


    @Data
    @NoArgsConstructor
    public static class JobItem {
        private String jobId;
        private String msg;
    }
}
