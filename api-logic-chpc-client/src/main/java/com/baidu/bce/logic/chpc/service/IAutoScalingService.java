package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.autoscaling.model.DeleteAutoScalingRequest;
import com.baidu.bce.logic.chpc.autoscaling.model.ExecuteAutoScalingInfo;
import com.baidu.bce.logic.chpc.autoscaling.model.SetAutoScalingRequest;
import com.baidu.bce.logic.chpc.autoscaling.model.UpdateAutoScalingTagsRequest;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.response.autoscale.SetAutoScalingResponse;
import com.baidu.bce.logic.chpc.model.response.autoscale.UpdateAutoScalingResponse;

import java.util.List;

public interface IAutoScalingService {

    SetAutoScalingResponse setAutoScaling(SetAutoScalingRequest request);

    UpdateAutoScalingResponse updateAutoScaling(UpdateAutoScalingTagsRequest request);

    void deleteAutoScaling(String clusterId, String queueName);

    void deleteAutoScaling(DeleteAutoScalingRequest request);

    List<AutoScaling> getAutoScaling(String clusterId);

    List<AutoScaling> getAutoScalingByOrder(String orderByType);

    AutoScaling getAutoScaling(String clusterId, String queueName);

    void executeAutoScalingTask(AutoScaling autoScaling, Queue queue);

    void executeHeartbeatAutoScalingTask(AutoScaling autoScaling, Queue queue, ExecuteAutoScalingInfo info);

    AutoScaling generateAutoScaling(SetAutoScalingRequest request, Queue queue);

    void updateAutoScalerToClusterManager(AutoScaling autoScaling);

    List<AutoScaling> getAutoScalingTags(List<AutoScaling> autoScalers);
}
