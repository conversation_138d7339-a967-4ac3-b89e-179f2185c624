package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.model.response.event.ClusterEventList;
import com.baidu.bce.logic.chpc.model.scheduler.SchedulerCommonResponse;
import com.baidu.bce.logic.chpc.sheduler.MountConfigUpdateRequest;
import com.baidu.bce.logic.chpc.sheduler.SchedulerConfigUpdateRequest;

public interface IClusterEventService {
    ClusterEventList getClusterEvent(String clusterId);

    SchedulerCommonResponse modifyClusterSchedulerConfig(String clusterId, SchedulerConfigUpdateRequest request);

    SchedulerCommonResponse modifyClusterNfsConfig(String clusterId, MountConfigUpdateRequest request);
}
