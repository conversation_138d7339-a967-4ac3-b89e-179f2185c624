package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.model.request.queue.QueueAddRequest;
import com.baidu.bce.logic.chpc.model.response.queue.QueueAddResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueDetailResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueRemoveResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueTagsResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueUpdateResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueVO;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.baidu.bce.logic.chpc.tag.UpdateTagsRequest;

import java.util.List;


/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */

public interface IQueueService {

    QueueAddResponse addQueue(String clusterId, QueueAddRequest request);

    QueueRemoveResponse removeQueue(String clusterId, String queueName, Boolean check, Boolean force);

    QueueDetailResponse getQueueDetail(String clusterId, String queueName);

    QueueVO getQueueUsers(String clusterId, String queueName);

    List<QueueVO> listQueues(String clusterId);

    QueueTagsResponse listTags();

    QueueUpdateResponse updateQueueTags(String clusterId, String queueName, UpdateTagsRequest request);

}
