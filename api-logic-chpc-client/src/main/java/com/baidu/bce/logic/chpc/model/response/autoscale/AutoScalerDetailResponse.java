package com.baidu.bce.logic.chpc.model.response.autoscale;

import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 */

@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class AutoScalerDetailResponse extends BaseResponse {

    private AutoScaling autoScaler;

    public AutoScaling getAutoScaler() {
        return autoScaler;
    }

    public void setAutoScaler(AutoScaling autoScaler) {
        this.autoScaler = autoScaler;
    }

}
