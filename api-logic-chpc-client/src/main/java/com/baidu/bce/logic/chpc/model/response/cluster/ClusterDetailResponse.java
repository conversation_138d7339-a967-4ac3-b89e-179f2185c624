package com.baidu.bce.logic.chpc.model.response.cluster;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.response.cfs.CfsVO;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceVO;
import com.baidu.bce.logic.chpc.model.response.queue.QueueVO;
import com.baidu.bce.logic.chpc.model.response.user.User;
import com.baidu.bce.logic.chpc.software.model.SoftwareVersion;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * @Author: lilu24
 * @Date: 2022-12-26
 */
@Data
public class ClusterDetailResponse {

    private String clusterId;

    private String clusterName;

    private String description;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;

    private String vpcId;

    private String vpcName;

    private String vpcCidr;

    private String subnetsName;

    private String subnetsCidr;

    private String securityGroupId;

    String securityGroupName;

    private String subnetId;

    private String schedulerType;

    private String schedulerVersion;

    private Boolean enableHa;

    private String chargeType;

    private String logicalZone;

    private String imageId;

    private List<InstanceVO> instanceList = Collections.emptyList();

    private List<CfsVO> cfsList = Collections.emptyList();

    private List<QueueVO> queueList = Collections.emptyList();

    private String status;

    private String errorMessage;

    private String clusterType;

    private int runningNodeNum;

    private int totalNodeNum;

    private int errorNodeNum;

    private Double cpuUsed12h;

    private List<SoftwareVersion> softwareList;
    
    private List<User> userList;

    private Boolean forbidDelete;

    private String schedulerIp;

    private String schedulerHost;

    private Boolean createdDone;

    private Integer maxNodes;

    private Integer maxCpus;

    private String domainAccount;

    private Instance loginNode;

    private Instance managerNode;

    private List<Tag> tags;

}
