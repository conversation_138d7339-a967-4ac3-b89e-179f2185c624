package com.baidu.bce.logic.chpc.model.response.instance;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class InstanceResponse extends BaseResponse {

    private String clusterId;

    private String queueName;

    private String status;

    @JsonIgnore
    private String taskId;


}
