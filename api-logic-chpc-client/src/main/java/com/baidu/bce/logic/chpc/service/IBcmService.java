package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.bcm.model.BatchQueryRequest;
import com.baidu.bce.logic.chpc.bcm.model.BatchQueryResponse;
import com.baidu.bce.logic.chpc.bcm.model.PartialQueryRequest;
import com.baidu.bce.logic.chpc.bcm.model.PartialQueryResponse;

public interface IBcmService {

    BatchQueryResponse batchQuery(BatchQueryRequest request);

    PartialQueryResponse partialQuery(PartialQueryRequest request);

}
