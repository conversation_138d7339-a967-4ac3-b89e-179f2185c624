package com.baidu.bce.logic.chpc.model.request.cluster;


import com.baidu.bce.logic.chpc.model.request.Tag;
import lombok.Data;

import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2022-12-15
 */
@Data
public class ClusterCreateRequest {

    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){1,14}[a-zA-Z0-9]$")
    private String clusterName = "";

    private String description = "";

    private String vpcId;

    private String schedulerType = "slurm";

    private Boolean openedHa = false;

    private Boolean enableMonitor = false;

    private String securityGroupId;

    private Integer networkCapacityInMbps = 0;

    private String imageId;

    private List<ClusterInstanceForCreate> clusterInstanceForCreates;

    private List<ClusterCfsForCreate> clusterCfsForCreates;

    private List<Tag> globalTags;


}
