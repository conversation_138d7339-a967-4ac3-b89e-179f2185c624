package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.cromwell.model.WorkflowCreateRequest;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowParseRequest;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowUpdateRequest;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowCreateResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowVO;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowGetResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowUpdateResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowParseResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowDeleteResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.NameisExistResponse;

import java.util.List;

public interface IWorkflowService {

    WorkflowCreateResponse createWorkflow(WorkflowCreateRequest workflowCreateRequest);

    List<WorkflowVO> listWorkflow(String workspaceId, String name);

    WorkflowGetResponse getWorkflow(String workflowId, Long version);

    WorkflowDeleteResponse deleteWorkflow(String workflowId);

    WorkflowUpdateResponse updateWorkflow(String workflowId, WorkflowUpdateRequest workflowUpdateRequest);

    WorkflowParseResponse parseWorksflow(WorkflowParseRequest request);

    NameisExistResponse workflowNameisExist(String workspaceId, String workflowName);
}
