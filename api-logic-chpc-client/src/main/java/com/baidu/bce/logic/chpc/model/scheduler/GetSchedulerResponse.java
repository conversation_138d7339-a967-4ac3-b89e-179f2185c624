package com.baidu.bce.logic.chpc.model.scheduler;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class GetSchedulerResponse extends BaseResponse {
    private String maxQueuedJobs;
    private List<AclInfo> aclInfo;
    private String jobHistory;
    private String schedIteration;
    private List<QueueConfig> queueConfigList;
    private String maxJobs;
    private String scheduler;
    private String clusterName;
    private String clusterId;

}

