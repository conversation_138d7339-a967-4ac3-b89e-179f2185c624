package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.model.request.cluster.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterOperationRequest;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterUpdateRequest;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterDetailResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterListResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */

public interface ChpcClusterService {


    ClusterResponse createCluster(ClusterCreateRequest clusterCreateRequest);


    ClusterDetailResponse getClusterDetail(String clusterId);


    ClusterListResponse listCluster();

    ClusterResponse deleteCluster(String clusterId, boolean force);

    ClusterResponse updateCluster(String clusterId, ClusterUpdateRequest request);

    ClusterResponse startCluster(String clusterId, ClusterOperationRequest request);

    ClusterResponse stopCluster(String clusterId, ClusterOperationRequest request);

}
