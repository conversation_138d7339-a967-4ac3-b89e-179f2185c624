package com.baidu.bce.logic.chpc.model.response.queue;

import java.util.List;

import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonIgnore;

import lombok.Data;

/**
 * @Author: lilu24
 * @Date: 2022-12-30
 */


@Data
public class QueueVO {
    @Data
    public class QueueResource {
        private long cpu;
        private String memory;
    }
    @JsonIgnore
    private String queueId;

    private String queueName;

    private String description;

    private Boolean isDefault;

    private Boolean isAutoScale;

    private Boolean hasAutoScale;

    private String status;

    private Integer instanceCount;

    private QueueResource allocatedResource;

    private QueueResource totalResource;

    private List<String> userList;

    private List<Tag> tags;
}
