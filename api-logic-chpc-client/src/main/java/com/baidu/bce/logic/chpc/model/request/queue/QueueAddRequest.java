package com.baidu.bce.logic.chpc.model.request.queue;


import com.baidu.bce.logic.chpc.model.request.instance.InstanceAddRequest;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueueAddRequest extends InstanceAddRequest {
    // todo 去除GroupAddRequest和InstanceAddRequest的耦合

    private String clientToken;

    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){0,13}[a-zA-Z0-9]$",
            message = "The specified parameter queueName is invalid.")
    @NotEmpty
    private String queueName;

    @Pattern(regexp = "^.{0,250}$", message = "is invalid")
    private String description;

    @Valid
    private List<Tag> tags;

}
