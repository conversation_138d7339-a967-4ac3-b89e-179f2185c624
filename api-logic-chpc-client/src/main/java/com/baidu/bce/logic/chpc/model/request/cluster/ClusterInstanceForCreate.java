package com.baidu.bce.logic.chpc.model.request.cluster;

import com.baidu.bce.logic.chpc.model.request.Tag;
import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2022-12-15
 */
@Data
public class ClusterInstanceForCreate {

    private String name;

    @NotEmpty(message = "instanceNodeType is required")
    private String instanceNodeType;

    private String subnetId;

    private String spec;

    private Integer rootDiskSizeInGb = 40;

    private String rootDiskStorageType;

    List<CdsDiskForCreate> cdsDisks;

    private Integer purchaseCount = 1;

    private String paymentTiming = "Postpaid";

    private Integer reservationLength;

    private String reservationTimeUnit;

    private List<Tag> tags = new ArrayList<>();


}
