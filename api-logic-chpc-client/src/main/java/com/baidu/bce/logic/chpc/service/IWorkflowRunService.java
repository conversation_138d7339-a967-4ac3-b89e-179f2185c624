package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.cromwell.model.WorkflowRunRequest;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunDeleteResponse;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunVO;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunGetResponse;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.NameisExistResponse;

import java.util.List;

public interface IWorkflowRunService {

    WorkflowRunResponse workflowRun(WorkflowRunRequest workflowRunRequest);

    List<WorkflowRunVO> listWorkflowRun(String workspaceId, String workflowId, String name, String status);

    WorkflowRunGetResponse getWorkflowRun(String runId);

    WorkflowRunDeleteResponse deleteWorkflowRun(String runId);

    NameisExistResponse workflowRunNameisExist(String workflowId, String workflowRunName);

    WorkflowRunResponse abortWorkflow(String runId);

}
