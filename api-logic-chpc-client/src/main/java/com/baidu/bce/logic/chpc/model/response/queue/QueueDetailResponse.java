package com.baidu.bce.logic.chpc.model.response.queue;


import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceVO;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Collections;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class QueueDetailResponse extends BaseResponse {


    QueueInfo queue;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class QueueInfo {

        private String queueName;

        private String description;

        private Boolean isDefault;

        private Boolean isAutoScale;

        private Boolean hasAutoScale;

        private String status;

        private Integer instanceCount;

        @JsonIgnore
        private List<InstanceVO> instanceList = Collections.emptyList();

        private List<Tag> tags;


    }
}
