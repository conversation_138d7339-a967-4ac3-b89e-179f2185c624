package com.baidu.bce.logic.chpc.model.response.job;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class JobTemplatesResponse extends BaseResponse {

    private List<JobTemplateResponse> templateList;

    @Data
    @NoArgsConstructor
    public static class JobTemplateResponse{

        private String name;

        private String clusterId;

        private String jobName;

        private String jobCmd;

        private String queue;

        private String postCmd;

        private Integer nhosts;

        private Integer ncpus;

        private Integer limitTimeInMinutes;

        private String stdoutPath;

        private String stderrPath;

        private Map<String, String> envVars;

        private String bosFilePath;

        private String decompressCmd;
    }
}
