package com.baidu.bce.logic.chpc.model.request.cluster;

import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2022-12-30
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ClusterUpdateRequest {


    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){1,14}[a-zA-Z0-9]$")
    private String clusterName;

    @Pattern(regexp = "^.{0,250}$", message = "is invalid")
    private String clusterDescription;

    private Boolean forbidDelete;

    private Integer maxNodes;

    private Integer maxCpus;

    @Valid
    private List<Tag> tags;

    private Boolean associatedResources = false;

}
