package com.baidu.bce.logic.chpc.model.response.workflow;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class WorkflowVO {
    private String name;
    private String description;
    private String language;
    private String languageVersion;
    private String workflowId;
    private String introduction;
    private String document;
    private Long version;
    private String inputs;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;
}
