package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.storage.model.BatchUpdateStorageRequest;
import com.baidu.bce.logic.chpc.storage.model.BatchUpdateStorageResponse;
import com.baidu.bce.logic.chpc.storage.model.StorageQueryResponse;
import com.baidu.bce.logic.chpc.storage.model.UpdateStorageRequest;

public interface IStorageService {

    StorageQueryResponse getStorageList(String clusterId);

    void updateStorage(String clusterId, UpdateStorageRequest request, String action);

    BatchUpdateStorageResponse batchUpdateStorage(String clusterId, BatchUpdateStorageRequest request, String action);

}
