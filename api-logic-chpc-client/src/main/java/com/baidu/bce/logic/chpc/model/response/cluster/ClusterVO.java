package com.baidu.bce.logic.chpc.model.response.cluster;

import java.time.LocalDateTime;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * @Author: lilu24
 * @Date: 2022-12-29
 */
@Data
public class ClusterVO {

    private String clusterId;

    private String clusterName;

    private String description;

    private String status;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;

    private String chargeType;

    private String imageId;

    private String schedulerType;   
    
    private String schedulerVersion;

    private int schedulePlugin;

    private String zoneName;

    private String errorMessage;

    private String clusterType;

    private Long[] jobPriorityRange;

    /**
     * FE console根据此字段决定是否封禁集群列表【释放】集群按钮
     * openApi接口不返回此接口
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean containsPrepayInstances;

    private Boolean forbidDelete;

}
