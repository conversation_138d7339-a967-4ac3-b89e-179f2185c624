package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.model.response.services.ServiceAddResponse;
import com.baidu.bce.logic.chpc.model.response.services.ServiceGetResponse;

import java.util.List;

public interface IServicesService {

    ServiceAddResponse subscribeService(Long serviceId);

    ServiceAddResponse subscribeServiceSaas(String accountId, String pfsId, String filesetId);

    List<ServiceGetResponse> getService(String name);
}
