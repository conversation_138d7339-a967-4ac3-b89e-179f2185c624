package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.model.response.task.TaskDetailResponse;
import com.baidu.bce.logic.chpc.model.response.usercallbacktask.UserCallbackTaskDetailResponse;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */

public interface ChpcTaskService {
    TaskDetailResponse getClusterTaskDetail(String clusterId, String taskId);

    UserCallbackTaskDetailResponse getUserCallbackTaskDetail(String clusterId, String taskId);

    UserCallbackTaskDetailResponse updateUserCallbackTaskStatus(String clusterId, String taskId, String status);
}
