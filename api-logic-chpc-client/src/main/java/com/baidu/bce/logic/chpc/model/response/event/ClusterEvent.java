package com.baidu.bce.logic.chpc.model.response.event;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
public class ClusterEvent extends BaseResponse {
    private String clusterId;
    private String name;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;
    private String status;
    private String errMsg;
}
