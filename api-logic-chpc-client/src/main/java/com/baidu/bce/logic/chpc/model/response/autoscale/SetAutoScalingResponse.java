package com.baidu.bce.logic.chpc.model.response.autoscale;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.EqualsAndHashCode;
import lombok.Data;

@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SetAutoScalingResponse extends BaseResponse {
    String asId;

    String message;
    // 带参数的构造函数
    public SetAutoScalingResponse(String asId, String message) {
        this.asId = asId;
        this.message = message;
    }
}
