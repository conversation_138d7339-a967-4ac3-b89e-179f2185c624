package com.baidu.bce.logic.chpc.model.request.usercallbacktask;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import jakarta.validation.constraints.Pattern;

/**
 * @Author: qiansheng01
 * @Date: 2024-03-24
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserCallbackTaskUpdateRequest {
    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){1,31}[a-zA-Z0-9]$")
    private String taskId;

    private String status;

}
