package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.job.model.BatchJobRequest;
import com.baidu.bce.logic.chpc.job.model.JobTemplateRequest;
import com.baidu.bce.logic.chpc.job.model.SubmitJobRequest;
import com.baidu.bce.logic.chpc.model.response.job.AlterJobPriorityResponse;
import com.baidu.bce.logic.chpc.model.response.job.BatchJobResponse;
import com.baidu.bce.logic.chpc.model.response.job.CommonHelixJobResponse;
import com.baidu.bce.logic.chpc.model.response.job.JobTemplatesResponse;
import com.baidu.bce.logic.chpc.model.response.job.ListAvailableTagsResponse;
import com.baidu.bce.logic.chpc.model.response.job.ListClusterJobsResponse;
import com.baidu.bce.logic.chpc.model.response.job.SubmitJobResponse;
import com.baidu.bce.logic.chpc.tag.UpdateTagsRequest;

import java.util.List;


public interface JobService {
    ListClusterJobsResponse listClusterJob(String clusterId, Integer stateFilter, Long timeStart, Long timeEnd,
                                           String jobId, String user, String queue, Boolean orderDesc, Integer page, Integer size);

    AlterJobPriorityResponse alterClusterJobPriority(String clusterId, List<String> jobId, Long priority, String reason);

    SubmitJobResponse submitJob(String clusterId, SubmitJobRequest request);

    CommonHelixJobResponse submitHelixJob(SubmitJobRequest request);

    CommonHelixJobResponse cancelHelixJob(String taskId, String action);

    BaseResponse createJobTemplate(String clusterId, JobTemplateRequest jobTemplateRequest);

    JobTemplatesResponse queryTemplateList(String clusterId);

    BaseResponse modifyJobTemplate(String clusterId, JobTemplateRequest jobTemplateRequest);

    BaseResponse deleteJobTemplate(String clusterId, String templateName);

    BatchJobResponse batchProcessJob(String clusterId, String action, BatchJobRequest batchJobRequest);

    ListAvailableTagsResponse listAvailableTags(String clusterId);

    BaseResponse mdifyTags(String clusterId, String jobId, UpdateTagsRequest request);
}
