package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.ca.model.CreateActionRequest;
import com.baidu.bce.logic.chpc.ca.model.CreateActionResponse;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceDeleteRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceMoveRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceOfflineRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceRebootRequest;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.response.instance.DeleteInstancesResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceAddResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceTagsResponse;
import com.baidu.bce.logic.chpc.tag.UpdateInstanceTagsRequest;

import java.util.LinkedHashMap;
import java.util.List;

public interface IInstanceService {

    InstanceAddResponse addInstanceToCluster(String clusterId,
                                             String queueName,
                                             InstanceAddRequest request);

    InstanceAddResponse addInstanceToCluster(String clusterId,
                                             String queueName,
                                             InstanceAddRequest request,
                                             AutoScaling autoScaling);


    DeleteInstancesResponse deleteInstanceFromCluster(String clusterId,
                                                      InstanceDeleteRequest request);

    DeleteInstancesResponse updateInstanceTag(String clusterId,
                                              UpdateInstanceTagsRequest request);

    BaseResponse deleteInstanceFromCluster(String clusterId,
                                           InstanceDeleteRequest request,
                                           AutoScaling autoScaling);

    BaseResponse shrinkInstanceFromCluster(AutoScaling autoScaling, List<String> hostnames, int shrinkNum, int shrinkCycle);


    BaseResponse moveInstance(String clusterId, InstanceMoveRequest request);

    BaseResponse instanceOffline(String clusterId, InstanceOfflineRequest request);

    BaseResponse rebootInstance(InstanceRebootRequest request);

    CreateActionResponse instanceCreateAction(CreateActionRequest request);

    List<Instance> getClusterInstances(String clusterId, String nodeType, String hostName, String queueName, String hostIP, String status, Boolean syncBccInfo);

    List<String> getContainsPrepayInstanceClusterId(List<String> clusterIds);

    Instance getClusterManagerInstanceOrNull(String clusterId);

    LinkedHashMap<String, Instance> getClusterManagerInstances(List<String> clusterIds);

    List<Instance> getQueueInstances(String clusterId,
                                     String queueName,
                                     int offset,
                                     int limit);

    List<Instance> getAllQueueInstances(String clusterId,
                                        String queueName);

    List<Instance> getAllCloudInstances(String zoneName,
                                        String subnetId,
                                        String instanceNames,
                                        String instanceIds
    );

    Long countInstances(String clusterId,
                        String queueName);

    List<Instance> encapsulateBccInfo(List<Instance> instances, Boolean excludeNotExist);

    List<Instance> getInstancesTags(List<Instance> instances);

    InstanceTagsResponse listTags();


}
