package com.baidu.bce.logic.chpc.model.request.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import jakarta.validation.constraints.Pattern;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserModifyRequest {

    private String authority;
    @Pattern(regexp = "^[a-z][a-z0-9_-]{2,29}$", message = "Invalid username")
    private String userName;

    private String userPassword;
}