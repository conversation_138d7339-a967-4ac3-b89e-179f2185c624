package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.cluster.model.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.ClusterHeartbeatRequest;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterUpdateRequest;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterDetailResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterVO;

import java.util.List;

public interface IChpcClusterService {

    ClusterResponse createCluster(ClusterCreateRequest clusterCreateRequest);

    ClusterResponse deleteCluster(String clusterId, boolean force);

    ClusterDetailResponse getCluster(String clusterId);

    List<ClusterVO> getClusters(String clusterName);

    ClusterResponse updateCluster(String clusterId, ClusterUpdateRequest request);

    ClusterResponse resetPassword(String clusterId, String password);

    ClusterResponse updateClusterPassword(String clusterId, String rsaPassword);

    BaseResponse heartbeat(ClusterHeartbeatRequest clusterHeartbeatRequest);
}
