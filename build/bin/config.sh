#!/bin/bash

readonly PROJECT=api-logic-chpc-start-version

# Define the main port
MAIN_PORT=8990

# Define the main jar file
readonly MAIN_CLASS=${PROJECT}.jar
# Get the current timestamp
readonly DATE=$(date +%Y-%m-%d'T'%H:%M:%S)
# DEFINE THE JVM ARGS
readonly JVM_MEM="-Xms6G -Xmx6G -XX:MaxDirectMemorySize=4G -XX:MaxMetaspaceSize=4G"
readonly JVM_GC="-XX:+UseG1GC -XX:-HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${BASE_DIR}/tmp -XX:+UseCompressedOops -XX:+UseCompressedClassPointers -XX:+PrintCommandLineFlags"
readonly JVM_GC_LOG="-Xlog:gc*=info:file=${BASE_DIR}/logs/gc_%p.log:utctime,level,tags:filecount=50,filesize=100M"
readonly JVM_ARGS="${JVM_MEM} ${JVM_GC} ${JVM_GC_LOG}"
MODE=debug
function printMsg() {
  if [ "${MODE}" == "debug" ]; then
    echo $*
  fi
}

function prepareJavaEnv() {
  [[ -f "/home/<USER>/deck/etc/bashrc" ]] && source /home/<USER>/deck/etc/bashrc
  export PATH=/home/<USER>/deck/client/bin:$PATH
  if [ -f /home/<USER>/deck/etc/deck-default-version ]; then
    DECK_VERSION=$(cat /home/<USER>/deck/etc/deck-default-version)
  else
    DECK_VERSION=$(cat /home/<USER>/deck/etc/deck-repo.conf | head -n 1 | awk -F '\t' '{print $1}')
  fi
  if [ -f /home/<USER>/deck/${DECK_VERSION}/etc/bashrc ]; then
    source /home/<USER>/deck/${DECK_VERSION}/etc/bashrc
  fi
  [[ -f "/home/<USER>/.deck/etc/bashrc" ]] && source /home/<USER>/.deck/etc/bashrc
  export PATH=/home/<USER>/.deck/client/bin:$PATH
  if [ -f /home/<USER>/.deck/etc/deck-default-version ]; then
    DECK_VERSION=$(cat /home/<USER>/.deck/etc/deck-default-version)
  else
    DECK_VERSION=$(cat /home/<USER>/.deck/etc/deck-repo.conf | head -n 1 | awk -F '\t' '{print $1}')
  fi
  if [ -f /home/<USER>/.deck/${DECK_VERSION}/etc/bashrc ]; then
    source /home/<USER>/.deck/${DECK_VERSION}/etc/bashrc
  fi
}

function prepareEnv() {
  if [ $# -lt 1 ]; then
    echo "Base directory must be provided."
    exit 1
  fi
  echo "Preparing to start project with baseDir '"${1}"'"
  echo "env is '${2}'"

  prepareJavaEnv

  if ! command -v deck &>/dev/null; then
    # 执行如下命令，并根据提示执行source 命令，生效环境变量
    bash -c "$(curl -s -L -k https://bcloud-baseenv-bj.bj.bcebos.com/BaseEnv/etc/install_deck.sh)"
    prepareJavaEnv
    JDK_VERSION=$(deck list | grep oraclejdk-17 | cut -d ' ' -f 1)
    if [ ! ${JDK_VERSION} ] || [[ ! (${JDK_VERSION} =~ '17') ]]; then
      echo "not exist jdk17"
      deck install oraclejdk-17.0.1
      prepareJavaEnv
    else
      echo "exist jdk17"
    fi
  else
    echo "exist deck command."
    JDK_VERSION=$(deck list | grep oraclejdk-17 | cut -d ' ' -f 1)
    if [ ! ${JDK_VERSION} ] || [[ ! (${JDK_VERSION} =~ '17') ]]; then
      echo "not exist jdk17"
      deck install oraclejdk-17.0.1
      prepareJavaEnv
    else
      echo "exist jdk17"
    fi
  fi
  export LC_ALL=en_US.UTF-8
  export LANG=en_US.UTF-8
  export LANGUAGE=en_US.UTF-8
  export PATH=$ORACLEJDK_17_0_1_BIN:$PATH
  export JAVA_HOME=$ORACLEJDK_17_0_1_HOME
  if [ "$ORACLEJDK_17_0_1_BIN" != "" ]; then
    JAVA=$ORACLEJDK_17_0_1_BIN/java
  elif [ -f /home/<USER>/.deck/1.0/oraclejdk/17.0.1/bin/java ]; then
    JAVA=/home/<USER>/.deck/1.0/oraclejdk/17.0.1/bin/java
  else
    JAVA=java
  fi
  echo "java env is '${JAVA}'"
  process_port=$(cat ${1}/bin/application.port)
  if [ "$process_port" != "" ]; then
    MAIN_PORT=$process_port
  fi
  echo "process_port=${process_port}"
  echo "MAIN_PORT=${MAIN_PORT}"
  hostname=$(hostname)
  instance_name="${hostname}:${MAIN_PORT}"
  echo ${instance_name}
  JAVA_AGENT="-javaagent:${1}/agent/transmittable-thread-local-2.13.2.jar"
  # 从icode发布产出下载，理论上只需要首次部署时下载
  BUILD_JDK_VERSION=$(unzip -qc ${1}/${MAIN_CLASS} META-INF/MANIFEST.MF | grep Build-Jdk | cut -d ':' -f 2)
  echo "BUILD_JDK_VERSION=${BUILD_JDK_VERSION}"
  rm -rf ${1}/skywalking-agent

  JAVA_AGENT_APM=""
  JAVA_D_PARAM_APM=""
  if [ -f ${1}/skywalking-agent-10/skywalking-agent.jar ] && [ -f ${1}/conf/agent.config ] && [[ ${BUILD_JDK_VERSION} =~ '17' ]]; then
    JAVA_AGENT_APM="-javaagent:${1}/skywalking-agent-10/skywalking-agent.jar"
    JAVA_D_PARAM_APM="-Dskywalking_config=${1}/conf/agent.config -Dskywalking.agent.instance_name=${instance_name} -Dskywalking.logging.dir=${1}/logs -Dskywalking.trace.ignore_path=GET:/actuator/**,GET:/admin/**"
  else
    echo "not exist skywalking agent or agent.config or BUILD_JDK_VERSION is not 17. "
  fi
  JAVA_D_PARAM="-Dspring.config.additional-location=${1}/conf/application-addition.yaml -Dapollo.cache-dir=${1}/data -Dapollo.path.server.properties=${1}/conf/server.properties -Dbce.web.endpoint.config=file:${1}/conf/endpoint.json -Dfile.encoding=UTF-8 -Duser.timezone=UTC -Djava.security.egd=file:/dev/./urandom -Djava.io.tmpdir=${1}/tmp"
  JAVA__PARAM="--server.tomcat.basedir=${1} --server.port=${MAIN_PORT} --logging.file.path=${1}/logs"
  START_COMMAND="${JAVA} ${JAVA_AGENT} ${JAVA_AGENT_APM} ${JAVA_D_PARAM} ${JAVA_D_PARAM_APM} ${JVM_ARGS} -jar ${1}/${MAIN_CLASS} ${JAVA__PARAM}"
  echo "StartCommand '${START_COMMAND}'"
  return 0
}
