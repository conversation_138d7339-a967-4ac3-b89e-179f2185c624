#!/bin/bash

init() {
  # Get the current bin folder
  BIN_DIR=$(dirname $0)
  readonly BIN_DIR=$(
    cd ${BIN_DIR}
    pwd
  )
  # Get the base directory folder.
  BASE_DIR=$(
    cd ${BIN_DIR}/..
    pwd
  )
  readonly BASE_DIR=${BASE_DIR}
  echo "Application base directory is : ${BASE_DIR}"
  echo "Application bin directory is : ${BIN_DIR}"
  return 0
}
if [ -n ${BASE_DIR} ]; then
  init
  source ${BASE_DIR}/bin/config.sh
  source ${BASE_DIR}/bin/control.sh
  prepareEnv ${BASE_DIR} $2
fi
if [[ $# != "1" && $# != "2" ]]; then
  usage
fi
ACTION=${1}
case "${1}" in
*restart)
  stopApp
  sleep 1
  startApp
  ;;
*start)
  startApp
  ;;
*stop)
  stopApp
  ;;
*check)
  checkApp
  ;;
*)
  usage
  ;;
esac