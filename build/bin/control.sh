#!/bin/bash

usage() {
  echo "Usage: "${PROJECT}".sh [start|stop|restart|check]"
  return 0
}
checkULimit() {
  printMsg "enter checkULimit function"
  local U_LIMIT=$(ulimit -n)
  if [ ${U_LIMIT} -lt 10000 ]; then
    printMsg "ULimit is ${U_LIMIT}, less than 10000"
    printMsg "checkULimit fail"
    return 1
  else
    printMsg "checkULimit success"
    return 0
  fi
}
findAppProcess() {
  printMsg "enter findProcess function"
  RET_PROCESS=$(ps aux | grep ${BASE_DIR}/${MAIN_CLASS} | grep -v "grep")
  return 0
}
writePidFile() {
  findAppProcess
  if [ -n "${RET_PROCESS}" ]; then
    echo ${RET_PROCESS} | awk '{print $2}' >${BASE_DIR}/${PROJECT}.pid
    return 0
  fi
  return 1
}
cleanPidFile() {
  rm -f ${BASE_DIR}/${PROJECT}.pid
  return 0
}
checkAppProcessExist() {
  printMsg "enter checkProcess function"
  findAppProcess
  if [ -n "${RET_PROCESS}" ]; then
    printMsg "checkProcess success"
    return 0
  else
    printMsg "checkProcess fail"
    return 1
  fi
}
checkPortListen2() {
  printMsg "enter checkPort function"
  echo "quit" | /usr/bin/nc localhost ${MAIN_PORT} 2>/dev/null
  return $?
}
checkPortListen3() {
  printMsg "enter checkPort function"
  # 判断端口是否被监听
  ss -nltp | grep -qw ${MAIN_PORT}
  return $?
}
checkPortListen() {
  printMsg "enter checkPort function"
  # 进程的健康检查接口是否正常
  requestId=$(cat /proc/sys/kernel/random/uuid)
  echo ${requestId}
  resp=$(curl -w %{http_code} --location --request GET 'http://localhost:'${MAIN_PORT}'/admin/health' \
    --header 'x-bce-request-id: '${requestId}'' \
    --header 'Authorization: Basic bG9naWMtY2hwYzphZV5TM21KNg==' \
    --header 'Accept: application/json')
  echo "check result=${resp}"
  echo ${resp:0-3:3}
  #if [[ ! (${resp} =~ '{"status":"UP"') ]]; then
  if [[ ! (${resp:0-3:3} == '200') ]]; then
    echo "check checkPort fail"
    return 1
  fi
  echo "check checkPort success"
  return 0
}
checkPortOpened() {
  for i in {1..60}; do
    printMsg "enter checkPort function"
    checkPortListen
    result1="$?"
    if [ "$result1" -eq 0 ]; then
      echo "Port ${MAIN_PORT} opened successfully!"
      return 0
    else
      sleep 10
      if [ "$i" -eq 60 ]; then
        echo "[Fatal] Port ${MAIN_PORT} open failed!"
        exit 1
      fi
    fi
  done
}
postStartApp() {
  checkAppProcessExist && checkPortOpened
  if [ $? -eq 0 ]; then
    echo ${ACTION} ${PROJECT} "success!"
    return 0
  else
    echo ${ACTION} ${PROJECT} "fail!"
    cleanPidFile
    return 2
  fi
}
startApp() {
  ACTION=start
  checkAppProcessExist
  if [ $? -eq 0 ]; then
    echo "${PROJECT} is already started!"
    return 1
  fi
  checkPortListen
  if [ $? -eq 0 ]; then
    echo "Port ${MAIN_PORT} has already been used!"
    return 1
  fi
  checkULimit
  if [ $? -eq 1 ]; then
    echo "ULimit is not enough!"
    return 1
  fi
  printMsg ${ACTION} ${PROJECT} "begin!"
  cleanPidFile
  if [ -f ${BASE_DIR}/log/gc.log ]; then
    mv ${BASE_DIR}/log/gc.log ${BASE_DIR}/log/gc.log.${DATE}
  fi
  cd ${BASE_DIR}/bin
  nohup ${START_COMMAND} >/dev/null 2>&1 &
  sleep 30
  process_pid=$(cat ${BASE_DIR}/bin/application.pid)
  process_port=$(cat ${BASE_DIR}/bin/application.port)
  echo "start process_pid=${process_pid}"
  echo "start process_port=${process_port}"
  if [ "$process_pid" == "" ]; then
    process_pid=$(ps ux | grep ${BASE_DIR}/${MAIN_CLASS} | grep -v grep | grep -v stop.sh | cut -c 9-15)
  fi
  if [ "$process_port" != "" ]; then
    MAIN_PORT=$process_port
  fi
  echo "start process_pid=${process_pid}"
  echo "start process_port=${process_port}"
  postStartApp
}
forceStopApp() {
  ACTION=forceStop
  printMsg "enter stop"
  printMsg ${DATE} ${PROJECT} ${ACTION} "begin!"
  checkAppProcessExist
  if [ $? -eq 1 ]; then
    echo "${PROJECT} is already stopped!"
    return 0
  fi
  SERVER_PIDS=$(echo ${RET_PROCESS} | awk '{print $2}')
  for id in ${SERVER_PIDS}; do
    kill -9 ${id}
    printMsg "kill process,pid:${id}"
  done
  checkAppProcessExist
  if [ $? -eq 0 ]; then
    echo ${ACTION} ${PROJECT} "fail!"
    return 2
  else
    echo ${ACTION} ${PROJECT} "success!"
    return 0
  fi
}

stopApp() {
  ACTION=stop
  printMsg "enter stop"
  printMsg ${DATE} ${PROJECT} ${ACTION} "begin!"
  process_pid=$(cat ${BASE_DIR}/bin/application.pid)
  process_port=$(cat ${BASE_DIR}/bin/application.port)
  echo "stop process_pid=${process_pid}"
  echo "stop process_port=${process_port}"
  if [ "$process_pid" == "" ]; then
    process_pid=$(ps ux | grep ${BASE_DIR}/${MAIN_CLASS} | grep -v grep | grep -v stop.sh | cut -c 9-15)
  fi
  if [ "$process_port" != "" ]; then
    MAIN_PORT=$process_port
  fi
  echo "stop process_pid=${process_pid}"
  echo "stop process_port=${process_port}"
  checkAppProcessExist
  if [ $? -eq 1 ]; then
    # 首次上线需要强制kill一次
    kill -9 ${process_pid}
    echo "kill -9 ${process_pid}"
    echo "${PROJECT} is already stopped!"
    return 0
  fi
  process_num=$(ps -ef | grep ${BASE_DIR}/${MAIN_CLASS} | grep -v 'grep' | wc -l)
  echo "process_num=${process_num}"
  if [ ${process_num} -ge 1 ]; then
    requestId=$(cat /proc/sys/kernel/random/uuid)
    echo ${requestId}
    curl --location --request POST 'http://localhost:'${MAIN_PORT}'/admin/shutdown' \
      --header 'x-bce-request-id: '${requestId}'' \
      --header 'Authorization: Basic bG9naWMtY2hwYzphZV5TM21KNg==' \
      --header 'Accept: application/json'
    echo -e "\n kill ${process_pid}"
    sleep 10
    process_num=$(ps -ef | grep ${BASE_DIR}/${MAIN_CLASS} | grep -v 'grep' | wc -l)
    if [ ${process_num} -ge 1 ]; then
      kill -9 ${process_pid}
      echo "kill -9 ${process_pid}"
    fi
  fi
  checkAppProcessExist
  if [ $? -eq 0 ]; then
    echo ${ACTION} ${PROJECT} "fail!"
    return 2
  else
    echo ${ACTION} ${PROJECT} "success!"
    return 0
  fi
}
checkApp() {
  ACTION=check
  checkAppProcessExist && checkPortListen
  if [ $? -eq 0 ]; then
    echo ${ACTION} ${PROJECT} "process Port ${MAIN_PORT} is started!"
    return 0
  else
    echo ${ACTION} ${PROJECT} "process Port ${MAIN_PORT} is fail!"
    return 2
  fi
}
