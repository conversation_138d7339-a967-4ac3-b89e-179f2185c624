package com.baidu.bce.logic.chpc.controller.consulehub;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import com.baidu.bce.logic.chpc.cluster.model.MultiQueueAndNodeClusterCreateRequest;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 * @Date 2023/9/14
 */
@WebMvcTest(ConsoleClusterOperateController.class)
public class ConsoleClusterOperateControllerTest {


    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private IChpcClusterService chpcClusterService;

    public void testRequestParm() throws Exception {
        // TODO IllegalStateException: Unable to find a @SpringBootConfiguration
        String clusterId = "c-abcdefg";
        MultiQueueAndNodeClusterCreateRequest request = ClusterCreateRequestBuilder.buildNormal();
        String content = objectMapper.writeValueAsString(request);


        // when(chpcClusterService.createCluster((IClusterCreateRequest) any())).thenReturn(buildClusterResponse(clusterId));
        // this.mockMvc.perform(
        //                 post("/v1/console/cluster")
        //                         .contentType(MediaType.APPLICATION_JSON_UTF8)
        //                         .content(content)
        //         )
        //         .andDo(print()).andExpect(status().isOk())
        //         .andExpect(content().string(containsString(clusterId)));

    }

    private static ClusterResponse buildClusterResponse(String clusterId) {
        ClusterResponse clusterResponse = new ClusterResponse();
        clusterResponse.setClusterId(clusterId);
        return clusterResponse;
    }
}
