package com.baidu.bce.logic.chpc.controller.consulehub;

import com.baidu.bce.logic.chpc.cluster.model.MultiQueueAndNodeClusterCreateRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.queue.QueueAddRequest;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/14
 */
public class ClusterCreateRequestBuilder {

    public static final MultiQueueAndNodeClusterCreateRequest buildNormal() {
        MultiQueueAndNodeClusterCreateRequest request = new MultiQueueAndNodeClusterCreateRequest();
        request.setClusterName("cluster1");
        request.setDescription("description");
        request.setVpcId("v-testvpcid");
        request.setSecurityGroupId("g-testsecuritygroupid");
        InstanceAddRequest manager = new InstanceAddRequest();
        manager.setCount(1);
        manager.setSpec("bcc.c4.c4m8");
        manager.setImageId("i-imageid");
        manager.setPassword("helloworld");
        manager.setSubnetId("s-subnetid");
        manager.setZoneName("cn-bj-d");
        request.setManagerNode(manager);

        QueueAddRequest queue = new QueueAddRequest();
        queue.setQueueName("queueA");
        queue.setDefault(true);
        queue.setDescription("queue description");
        InstanceAddRequest compute = new InstanceAddRequest();
        compute.setCount(2);
        compute.setSpec("bcc.c4.c4m8");
        compute.setImageId("i-imageid");
        compute.setPassword("helloworld");
        compute.setSubnetId("s-subnetid");
        compute.setZoneName("cn-bj-d");
        List<InstanceAddRequest> computeNodes = new ArrayList<>();
        computeNodes.add(compute);
        queue.setComputeNodes(computeNodes);

        List<QueueAddRequest> queues = new ArrayList<>();
        queues.add(queue);
        request.setQueues(queues);
        return request;
    }
}
