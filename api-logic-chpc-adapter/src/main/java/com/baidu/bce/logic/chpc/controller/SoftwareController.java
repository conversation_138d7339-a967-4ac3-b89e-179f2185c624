package com.baidu.bce.logic.chpc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.ISoftwareService;
import com.baidu.bce.logic.chpc.software.model.InstallSoftwareRequest;
import com.baidu.bce.logic.chpc.software.model.InstallSoftwareResponse;
import com.baidu.bce.logic.chpc.software.model.QuerySoftwareForNodeRequest;
import com.baidu.bce.logic.chpc.software.model.QuerySoftwareForNodeResponse;
import com.baidu.bce.logic.chpc.software.model.SoftwareQueryResponse;
import com.baidu.bce.logic.chpc.software.model.UnInstallSoftwareRequest;
import com.baidu.bce.logic.chpc.software.model.UnInstallSoftwareResponse;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/v1")
public class SoftwareController {

    @Autowired
    ISoftwareService softwareService;

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询软件列表")
    @RequestMapping(value = "cluster/{clusterId}/software/list", method = RequestMethod.GET)
    @PermissionVerify(resourceLocation = PermissionVerify.ResourceLocation.IN_STRING, permissions = {
            PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY })
    public SoftwareQueryResponse getSoftwareList(@PathVariable @PermissionResourceID String clusterId,
            @RequestParam(required = false, defaultValue = "") String name) {

        return softwareService.getSoftwareList(clusterId, name);

    }

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询可安装软件列表")
    @RequestMapping(value = "software", method = RequestMethod.GET)
    @PermissionVerify(resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID, permissions = {
            PermissionConstant.CHPC_CONTROL, PermissionConstant.CLUSTER_MODIFY })
    public SoftwareQueryResponse getAvailSoftwareList(@RequestParam(required = false, defaultValue = "") String name) {

        return softwareService.getSoftwareList("", name);

    }

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询节点上指定软件列表信息")
    @RequestMapping(value = "cluster/{clusterId}/software/record/byInstance", method = RequestMethod.POST)
    @PermissionVerify(resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID, permissions = {
            PermissionConstant.CHPC_CONTROL, PermissionConstant.CLUSTER_MODIFY })
    public QuerySoftwareForNodeResponse getSoftwareRecordByInstance(@PathVariable @PermissionResourceID String clusterId,
            @Validated @RequestBody QuerySoftwareForNodeRequest request) {

        return softwareService.getSoftwareRecordByInstance(clusterId, request);
        
    }

    /**
     * @Description 批量安装软件
     * @Param clusterId String - 集群ID，需要有权限操作的集群ID，用于确定安装软件的集群环境
     * @Param request InstallSoftwareRequest - 安装软件请求对象，包含软件名称、版本号等信息
     * @Return InstallSoftwareResponse - 安装软件返回结果，包含软件安装状态和错误信息
     * @Throws PermissionDeniedException 如果当前用户没有操作集群的权限
     * @See com.tencent.bk.job.manage.common.consts.permission.PermissionConstant#CHPC_OPERATE
     * @See com.tencent.bk.job.manage.common.consts.permission.PermissionConstant#CLUSTER_MODIFY
     */
    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "安装软件")
    @RequestMapping(value = "cluster/{clusterId}/software/installation/batch", method = RequestMethod.POST)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING, permissions = {
                    PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY })
    public InstallSoftwareResponse installSoftware(@PathVariable @PermissionResourceID String clusterId,
            @Validated @RequestBody InstallSoftwareRequest request) {

        return softwareService.installSoftware(clusterId, request);

    }

    /**
     * @Description 批量卸载软件
     * @Param clusterId String 集群ID，需要操作的集群ID，需要具有集群修改权限
     * @Param request UnInstallSoftwareRequest 卸载软件请求参数，包含软件名称、版本号等信息
     * @Return UnInstallSoftwareResponse 卸载软件返回结果，包含卸载状态和错误码等信息
     * @Throws PermissionDeniedException 如果调用者没有相应的权限进行操作
     * @See com.alibaba.nacos.console.model.response.UnInstallSoftwareResponse
     */
    @Idempotent
    @RequestIdAdapter
    @Operation(description = "卸载软件")
    @RequestMapping(value = "cluster/{clusterId}/software/uninstallation/batch", method = RequestMethod.POST)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING, permissions = {
                    PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY })
    public UnInstallSoftwareResponse unInstallSoftware(@PathVariable @PermissionResourceID String clusterId,
            @Validated @RequestBody UnInstallSoftwareRequest request) {

        return softwareService.unInstallSoftware(clusterId, request);

    }

}
