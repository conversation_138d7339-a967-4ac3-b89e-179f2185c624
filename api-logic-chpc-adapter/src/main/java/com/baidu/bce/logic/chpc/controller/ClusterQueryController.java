package com.baidu.bce.logic.chpc.controller;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Resource;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.convertor.ClusterConvertor;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterDetail;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterDetailResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterVO;
import com.baidu.bce.logic.chpc.model.response.cluster.ListClustersResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1")
public class ClusterQueryController {


    @Resource
    private IChpcClusterService chpcClusterService;


    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群详情")
    @RequestMapping(value = "/cluster/{clusterId}", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY}
    )
    public ClusterDetail getCluster(@PathVariable("clusterId") @PermissionResourceID String clusterId) {

        ClusterDetailResponse cluster = chpcClusterService.getCluster(clusterId);

        return new ClusterConvertor().convert(cluster);

    }

    /**
     * @Description: 查询集群列表，支持分页和按名称过滤。
     * @param clusterName 集群名称（可选），默认为空字符串。
     * @param pageNo 当前页码，默认为1，必须大于等于1。
     * @param pageSize 每页记录数，默认为10，最大不超过1000。
     * @return ListClustersResponse 包含集群列表、总记录数、当前页码、每页记录数的响应对象。
     * @throws IllegalArgumentException 如果pageNo小于1或pageSize超出限制。
     */
    @RequestIdAdapter
    @Operation(description = "查询集群列表")
    @RequestMapping(value = "/clusters", method = RequestMethod.GET)
    public ListClustersResponse getClusters(@RequestParam(required = false, defaultValue = "") String clusterName,
                                            @RequestParam(required = false, defaultValue = "1")
                                            @Min(1) Integer pageNo,
                                            @RequestParam(required = false, defaultValue = "10")
                                            @Max(1000) Integer pageSize) {
        ListClustersResponse response = new ListClustersResponse();
        List<ClusterVO> clusters = chpcClusterService.getClusters(clusterName);
        response.setTotalCount(clusters.size());

        List<ClusterVO> clusterVOS = clusters.stream().skip((long) (pageNo - 1) * pageSize).limit(pageSize).
                collect(Collectors.toList());

        response.setClusters(clusterVOS);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);

        return response;
    }
}
