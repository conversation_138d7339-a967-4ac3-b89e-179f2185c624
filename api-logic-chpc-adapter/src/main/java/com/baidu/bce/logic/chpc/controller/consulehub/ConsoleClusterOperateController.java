package com.baidu.bce.logic.chpc.controller.consulehub;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.cluster.model.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.job.model.SubmitJobRequest;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterCreateResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.model.response.job.SubmitJobResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;
import com.baidu.bce.logic.chpc.service.JobService;
import com.baidu.bce.logic.chpc.service.util.AesEncryptUtil;
import com.baidu.bce.logic.chpc.service.util.PasswordUtil;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.PathVariable;


import jakarta.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */

@Slf4j
@RestController
@RequestMapping("/v1/console/cluster")
public class ConsoleClusterOperateController {

    @Resource
    private IChpcClusterService chpcClusterService;

    @Resource
    PasswordUtil passwordUtil;

    @Resource
    JobService jobService;

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "创建集群")
    @RequestMapping(method = RequestMethod.POST)
    @PermissionVerify(resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID, permissions = {
            PermissionConstant.CHPC_CONTROL, PermissionConstant.CLUSTER_MODIFY})

    public ClusterCreateResponse createCluster(@Validated @RequestBody ClusterCreateRequest clusterCreateRequest) {
        // 解析前端Rsa为密码原文
        String password = passwordUtil.convertRsa2Pwd(clusterCreateRequest.getPassword());
        // 加密存入db
        String aesPassword = AesEncryptUtil.encrypt(password);
        clusterCreateRequest.setPassword(aesPassword);
        ClusterResponse clusterResponse = chpcClusterService.createCluster(clusterCreateRequest);
        ClusterCreateResponse response = new ClusterCreateResponse();
        response.setClusterId(clusterResponse.getClusterId());
        response.setMessage(clusterResponse.getMessage());
        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "提交作业")
    @RequestMapping(value = "/{clusterId}/job", method = RequestMethod.POST)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_JOB_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.JOB_ALL}
    )
    public SubmitJobResponse submitJob(@PathVariable String clusterId,
                                       @Validated @RequestBody SubmitJobRequest request) {

        String plainTextPassword = passwordUtil.convertRsa2Pwd(request.getPassword());
        // 将密文修改为明文
        request.setPassword(plainTextPassword);
        SubmitJobResponse response = jobService.submitJob(clusterId, request);

        return response;
    }

}
