package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.request.user.UserModifyRequest;
import com.baidu.bce.logic.chpc.model.response.user.GetUserList;
import com.baidu.bce.logic.chpc.model.response.user.User;
import com.baidu.bce.logic.chpc.model.response.user.UserCommonResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/cluster")
public class DomainUserController {
    @Autowired
    UserService userService;

    /**
     * @Description:
     * 查询集群的域账号，包括用户名和权限。
     * 支持分页、过滤和排序功能。
     *
     * @Param {String} clusterId - 必选参数，类型为String，用于指定集群ID，需要具有资源访问权限。
     * @Param {Integer} pageNo - 可选参数，默认值为1，类型为Integer，表示当前页码。
     * @Param {Integer} pageSize - 可选参数，默认值为10，类型为Integer，表示每页显示的条数。
     * @Param {String} userName - 可选参数，类型为String，表示过滤用户名的关键字。
     * @Param {String} authority - 可选参数，类型为String，表示过滤用户权限的关键字。
     *
     * @Return {GetUserList} - 返回一个GetUserList对象，包含了集群域账号列表、总记录数、当前页码和每页显示条数等信息。
     *
     * @Throws {PermissionDeniedException} - 如果调用者没有相应的权限，则会抛出该异常。
     */
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群的域账号")
    @RequestMapping(value = "/{clusterId}/userList", method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY}
    )
    public GetUserList getClusterDomainUserList(@PathVariable @PermissionResourceID String clusterId,
                                                @RequestParam(required = false, defaultValue = "1")
                                                @Min(1) Integer pageNo,
                                                @RequestParam(required = false, defaultValue = "10")
                                                @Max(10000) Integer pageSize,
                                                @RequestParam(required = false)
                                                        String userName,
                                                @RequestParam(required = false)
                                                        String authority) {

        List<User> userList = userService.getDomainUserList(clusterId, userName, authority);
        int totalCount = userList.size();
        List<User> pagedList = userList.stream().skip((long) (pageNo - 1) * pageSize).limit(pageSize).
                collect(Collectors.toList());

        GetUserList getUserListResponse = new GetUserList();
        getUserListResponse.setTotalCount(totalCount);
        getUserListResponse.setPageNo(pageNo);
        getUserListResponse.setPageSize(pageSize);
        getUserListResponse.setUserList(pagedList);
        return getUserListResponse;
    }


    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "新增集群域账号")
    @RequestMapping(value = "/{clusterId}/user", method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_READONLY}
    )
    public UserCommonResponse addClusterDomainUser(@PathVariable @PermissionResourceID String clusterId,
                                                   @Validated @RequestBody UserModifyRequest request) {

        UserCommonResponse addClusterDomainUserResponse = userService.addDomainUser(clusterId, request.getUserName(), request.getUserPassword(), request.getAuthority());

        return addClusterDomainUserResponse;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "更新集群域账号信息")
    @RequestMapping(value = "/{clusterId}/modifyUser", method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_READONLY}
    )
    public UserCommonResponse updateClusterDomainUser(@PathVariable @PermissionResourceID String clusterId,
                                                      @Validated @RequestBody UserModifyRequest request) {

        UserCommonResponse updateClusterDomainUserResponse = userService.updateDomainUser(clusterId, request);

        return updateClusterDomainUserResponse;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "删除集群域账号信息")
    @RequestMapping(value = "/{clusterId}/user/{userName}", method = RequestMethod.DELETE)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_READONLY}
    )
    public UserCommonResponse deleteClusterDomainUser(@PathVariable @PermissionResourceID String clusterId,
                                                      @PathVariable @PermissionResourceID String userName) {

        UserCommonResponse deleteClusterDomainUserResponse = userService.deleteDomainUser(clusterId, userName);

        return deleteClusterDomainUserResponse;
    }
}
