package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.logic.chpc.bct.model.BctConstants;
import com.baidu.bce.logic.chpc.bct.model.Event;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.opencsv.CSVWriter;
import com.opencsv.bean.StatefulBeanToCsv;
import com.opencsv.bean.StatefulBeanToCsvBuilder;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.intellij.lang.annotations.Pattern;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.bct.model.BctQueryEventsRequest;
import com.baidu.bce.logic.chpc.bct.model.BctQueryEventsResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.BctEventService;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.Max;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1")
public class BctEventController {
    @Autowired
    BctEventService bctEventService;

    @Autowired
    private ObjectMapper jacksonObjectMapper;

    @RequestIdAdapter
    @Operation(description = "查询BCT事件")
    @RequestMapping(value = "/bctevents", method = RequestMethod.POST)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_READ}
    )
    @ValidateAuthentication
    public BctQueryEventsResponse queryBctEvents(@Validated @RequestBody BctQueryEventsRequest request) {

        BctQueryEventsResponse response = bctEventService.queryEvents(request);
        if (response.getData() != null){
            response.setData(response.getData().stream().map(event -> {
                if (event.getUserAgent().contains("Mozilla")){
                    event.setOperationMethod("控制台");
                }else{
                    event.setOperationMethod("API");
                }
                return event;
            }).collect(Collectors.toList()));
        }
        return response;
    }

    @RequestIdAdapter
    @Operation(description = "查询CHPC-BCT过滤字段")
    @RequestMapping(value = "/bctevents/filterFields", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_READ}
    )
    @ValidateAuthentication
    public BctConstants queryBctFilterFields() {

        return new BctConstants();
    }

    @RequestIdAdapter
    @Operation(description = "下载BCT日志csv or json文件")
    @RequestMapping(value = "/bctevents/download", method = RequestMethod.GET)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
            permissions = {PermissionConstant.CHPC_READ}
    )
    @ValidateAuthentication
    @Validated
    public void downloadBctEvents(@RequestParam(name = "fileType") @Pattern("csv|json") String fileType,
                                  @RequestParam(name = "pageNo") Integer pageNo,
                                  @RequestParam(name = "pageSize") @Max(1000) Integer pageSize,
                                  @RequestParam(name = "eventSource") String eventSource,
                                  @RequestParam(name = "resourceType") String resourceType,
                                  @RequestParam(name = "resourceName") String resourceName,
                                  @RequestParam(name = "eventName", required = false) String eventName,
                                  @RequestParam(name = "operationMethod", required = false) String operationMethod,
                                  @RequestParam(name = "startTime") @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'") Date startTime,
                                  @RequestParam(name = "endTime") @DateTimeFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'") Date endTime,
                                  HttpServletResponse response) {
        BctQueryEventsRequest request = new BctQueryEventsRequest();
        request.setPageNo(pageNo);
        request.setPageSize(pageSize);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        BctQueryEventsRequest.FieldFilter source = new BctQueryEventsRequest.FieldFilter("eventSource", eventSource);
        BctQueryEventsRequest.FieldFilter resType = new BctQueryEventsRequest.FieldFilter("resourceType", resourceType);
        BctQueryEventsRequest.FieldFilter resName = new BctQueryEventsRequest.FieldFilter("resourceName", resourceName);
        List<BctQueryEventsRequest.FieldFilter> filters = new ArrayList<>();
        filters.add(source);
        filters.add(resType);
        filters.add(resName);
        if (StringUtils.isNotEmpty(eventName)){
            BctQueryEventsRequest.FieldFilter eName = new BctQueryEventsRequest.FieldFilter("eventName", eventName);
            filters.add(eName);
        }
        if (StringUtils.isNotEmpty(operationMethod)){
            BctQueryEventsRequest.FieldFilter operateMethod = new BctQueryEventsRequest.FieldFilter("operationMethod", operationMethod);
            filters.add(operateMethod);
        }
        request.setFilters(filters);
        BctQueryEventsResponse bctQueryEventsResponse = bctEventService.queryEvents(request);
        if (bctQueryEventsResponse.getData() == null){
            log.error("no data can download");
            throw new BceException("当前日志页为空，不执行下载操作");
        }
        bctQueryEventsResponse.setData(bctQueryEventsResponse
                .getData()
                .stream()
                .map(event -> {
                    if (event.getUserAgent().contains("Mozilla")) {
                        event.setOperationMethod("控制台");
                    } else {
                        event.setOperationMethod("API");
                    }
                    return event;
                }).collect(Collectors.toList()));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");
        if ("csv".equals(fileType)){
            log.info("start downloadBctEvents csv");
            // 下载数据为csv文件
            String fileName = String.format("trail_%s_%s_%d_%d.csv", sdf.format(request.getStartTime()),
                    sdf.format(request.getEndTime()), request.getPageNo(), request.getPageSize());
            response.setContentType("text/csv");
            // response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            try {
                StatefulBeanToCsv<Event> beanToCsv = new StatefulBeanToCsvBuilder<Event>(response.getWriter())
                    .withSeparator(CSVWriter.DEFAULT_SEPARATOR).build();
                // withQuotechar(CSVWriter.NO_QUOTE_CHARACTER)
                beanToCsv.write(bctQueryEventsResponse.getData());
            }catch (Exception e){
                log.error("csv file download error", e);
                throw new BceException("csv文件下载异常");
            }
        } else if ("json".equals(fileType)){
            // 下载数据为json文件
            log.info("start downloadBctEvents json");
            String fileName = String.format("trail_%s_%s_%d_%d.json", sdf.format(request.getStartTime()),
                    sdf.format(request.getEndTime()), request.getPageNo(), request.getPageSize());
            response.setContentType("application/json");
            // response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
            try {
                jacksonObjectMapper.writeValue(response.getWriter(), bctQueryEventsResponse.getData());
            }catch (Exception e){
                log.error("json file download error", e);
                throw new BceException("json文件下载异常");
            }
        }
    }
}
