package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.service.ITagService;
import com.baidu.bce.logic.chpc.tag.TagListResourcesRequest;
import com.baidu.bce.logic.chpc.tag.TagListResourcesResponse;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/tag")
public class TagController {

    @Resource
    ITagService tagService;

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "tag可绑定的资源")
    @RequestMapping(value = "/resources", method = RequestMethod.POST)
    public TagListResourcesResponse tagListResources(@RequestBody TagListResourcesRequest request) {
        TagListResourcesResponse response = tagService.tagListResources(request);
        return response;
    }
}
