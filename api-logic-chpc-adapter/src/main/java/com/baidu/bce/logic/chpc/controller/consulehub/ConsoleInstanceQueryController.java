package com.baidu.bce.logic.chpc.controller.consulehub;


import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.response.instance.GetMasterSpecResponse;
import com.baidu.bce.logic.chpc.model.response.instance.ListClusterInstancesResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IInstanceService;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/console/cluster")
public class ConsoleInstanceQueryController {

    @Autowired
    IInstanceService instanceService;
    @Autowired
    InstanceDAOGateway instanceDAOGateway;

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群中队列的全部节点")
    @RequestMapping(value = "/{clusterId}/queue/{queueName}/instances", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.QUEUE_READONLY}
    )
    public ListClusterInstancesResponse getQueueInstances(
            @PathVariable @PermissionResourceID String clusterId,
            @PathVariable @PermissionResourceID String queueName
    ) {
        ListClusterInstancesResponse response = new ListClusterInstancesResponse();
        List<Instance> instances = instanceService.getAllQueueInstances(clusterId, queueName);
        int totalCount = instances.size();
        response.setInstances(instances);
        response.setPageNo(1);
        response.setPageSize(totalCount);
        response.setTotalCount(totalCount);

        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群中队列的全部节点")
    @RequestMapping(value = "/masterSpec", method = RequestMethod.GET)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY}
    )
    public GetMasterSpecResponse getMasterSpec(
            @RequestParam()String clusterId
    ) {
        List<Instance> instances = instanceDAOGateway.findByClusterId(clusterId);
        if (instances == null || instances.size() == 0) {
            throw new CommonExceptions.RequestInvalidException("cluster: " + clusterId + " do not have Node");

        }
        Instance master = null;
        for (Instance instance : instances) {
            if ("master".equals(instance.getNodeType())) {
                master = instance;
                break;
            }
        }

        if (master == null ) {
            throw new CommonExceptions.RequestInvalidException("cluster: " + clusterId + " do not have masterNode");
        }

        int lastIndex = -1;
        for (int i = master.getSpec().length() - 1; i >= 0; i--) {
            char c = master.getSpec().charAt(i);
            if (Character.isLetter(c)) {
                lastIndex = i;
                break;
            }
        }
        String letterAndNumber = master.getSpec().substring(lastIndex + 1);

        GetMasterSpecResponse getMasterSpecResponse =new GetMasterSpecResponse();
        getMasterSpecResponse.setMasterSpec(Integer.valueOf(letterAndNumber));
        return getMasterSpecResponse;
    }

}
