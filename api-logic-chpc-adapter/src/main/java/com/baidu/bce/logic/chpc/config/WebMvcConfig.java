package com.baidu.bce.logic.chpc.config;

import com.baidu.bce.logic.core.validation.web.CustomWebAttributeMethodProcessor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.hibernate.validator.HibernateValidatorConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.context.request.async.TimeoutCallableProcessingInterceptor;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import jakarta.annotation.PostConstruct;
import java.util.List;

@Configuration
@Slf4j
public class WebMvcConfig implements WebMvcConfigurer {

    private MessageSource messageSource;

    private LocalValidatorFactoryBean localValidatorFactoryBean;

    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @Autowired
    public void setLocalValidatorFactoryBean(LocalValidatorFactoryBean localValidatorFactoryBean) {
        this.localValidatorFactoryBean = localValidatorFactoryBean;
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(new CustomWebAttributeMethodProcessor());
    }

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer configurer) {
        // 默认超时时间 60s
        configurer.setDefaultTimeout(60000);
        // 设置异步Callable请求拦截器
        configurer.registerCallableInterceptors(timeoutInterceptor());
        // 自定义异步请求的线程池，设置可重用，默认使用SimpleAsyncTaskExecutor
        // configurer.setTaskExecutor(taskExecutor);
    }

    @Bean
    public TimeoutCallableProcessingInterceptor timeoutInterceptor() {
        return new TimeoutCallableProcessingInterceptor();
    }

    /**
     * HibernateValidator整合spring及国际化
     */
    @PostConstruct
    public void init() {
        localValidatorFactoryBean.setValidationMessageSource(messageSource);
        localValidatorFactoryBean.getValidationPropertyMap().put(HibernateValidatorConfiguration.FAIL_FAST
                , BooleanUtils.toStringTrueFalse(true));
    }

}