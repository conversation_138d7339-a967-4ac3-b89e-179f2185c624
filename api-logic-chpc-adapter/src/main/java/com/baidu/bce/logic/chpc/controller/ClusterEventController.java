package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.response.event.ClusterEventList;
import com.baidu.bce.logic.chpc.model.scheduler.SchedulerCommonResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IClusterEventService;
import com.baidu.bce.logic.chpc.sheduler.MountConfigUpdateRequest;
import com.baidu.bce.logic.chpc.sheduler.SchedulerConfigUpdateRequest;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import jakarta.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/v1/cluster")
public class ClusterEventController {
    @Resource
    private IClusterEventService clusterEventService;


    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查看集群创建进度")
    @RequestMapping(value = "/{clusterId}/install/event", method = RequestMethod.GET)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
            permissions = {PermissionConstant.CHPC_CONTROL, PermissionConstant.CLUSTER_MODIFY}
    )
    public ClusterEventList getClusterEvent(@PathVariable("clusterId") @PermissionResourceID String clusterId) {

        ClusterEventList clusterEventList = clusterEventService.getClusterEvent(clusterId);
        return clusterEventList;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "更新混合云集群的调度服务配置:调度器IP和调度器host")
    @RequestMapping(value = "/{clusterId}/config/scheduler", method = RequestMethod.PUT)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public SchedulerCommonResponse modifyClusterSchedulerConfig(@PathVariable @PermissionResourceID String clusterId,
                                                                @RequestBody SchedulerConfigUpdateRequest request) {

        SchedulerCommonResponse response = clusterEventService.modifyClusterSchedulerConfig(clusterId, request);

        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "更新集群的共享存储配置")
    @RequestMapping(value = "/{clusterId}/config/nfs", method = RequestMethod.PUT)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public SchedulerCommonResponse modifyClusterNfsConfig(@PathVariable @PermissionResourceID String clusterId,
                                                          @RequestBody MountConfigUpdateRequest request) {

        SchedulerCommonResponse response = clusterEventService.modifyClusterNfsConfig(clusterId, request);

        return response;
    }
}
