package com.baidu.bce.logic.chpc.controller.consulehub;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.cluster.model.ResetPasswordRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceAddResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;
import com.baidu.bce.logic.chpc.service.IInstanceService;
import com.baidu.bce.logic.chpc.service.util.AesEncryptUtil;
import com.baidu.bce.logic.chpc.service.util.PasswordUtil;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/console/cluster")
public class ConsoleInstanceOperateController {

    @Autowired
    IInstanceService instanceService;

    @Resource
    PasswordUtil passwordUtil;

    @Resource
    private IChpcClusterService chpcClusterService;

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "扩容节点到集群")
    @RequestMapping(value = "/{clusterId}/queue/{queueName}/instances", method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.QUEUE_MODIFY}
    )
    public InstanceAddResponse addInstance(@PathVariable @PermissionResourceID String clusterId,
                                           @PathVariable @PermissionResourceID String queueName,
                                           @Validated @RequestBody InstanceAddRequest request) {

        if (StringUtils.isNotEmpty(request.getPassword())) {
            String skEncryptedPwd = passwordUtil.convertRsa2Pwd(request.getPassword());
            String aesPassword = AesEncryptUtil.encrypt(skEncryptedPwd);
            request.setPassword(aesPassword);
        }

        return instanceService.addInstanceToCluster(clusterId, queueName, request);

    }

    @RequestIdAdapter
    @Operation(description = "重置集群密码")
    @RequestMapping(value = "/{clusterId}/resetPassword", method = RequestMethod.PUT)
    public ClusterResponse resetPassword(@PathVariable("clusterId") @PermissionResourceID String clusterId,
                                         @Validated @RequestBody ResetPasswordRequest resetPasswordRequest) {
        // 解析前端Rsa为密码原文
        String password = passwordUtil.convertRsa2Pwd(resetPasswordRequest.getPassword());
        return chpcClusterService.resetPassword(clusterId, password);
    }

}
