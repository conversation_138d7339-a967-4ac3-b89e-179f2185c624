package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.job.model.AlterJobPriorityRequest;
import com.baidu.bce.logic.chpc.job.model.BatchJobRequest;
import com.baidu.bce.logic.chpc.job.model.JobTemplateRequest;
import com.baidu.bce.logic.chpc.job.model.SubmitJobRequest;
import com.baidu.bce.logic.chpc.model.response.job.AlterJobPriorityResponse;
import com.baidu.bce.logic.chpc.model.response.job.BatchJobResponse;
import com.baidu.bce.logic.chpc.model.response.job.JobTemplatesResponse;
import com.baidu.bce.logic.chpc.model.response.job.SubmitJobResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.JobService;
import com.baidu.bce.logic.chpc.service.util.PasswordUtil;
import com.baidu.bce.logic.chpc.tag.UpdateTagsRequest;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/v1/cluster")
public class JobOperateController {
    @Autowired
    JobService jobService;

    @Resource
    PasswordUtil passwordUtil;

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "修改作业优先级")
    @RequestMapping(value = "/{clusterId}/job/{jobId}/priority", method = RequestMethod.PUT)
    public BaseResponse alterClusterJobPriority(@PathVariable String clusterId,
                                                @PathVariable String jobId,
                                                @RequestBody AlterJobPriorityRequest request) {
        List<String> jobIds = List.of(jobId);
        BaseResponse response = jobService.alterClusterJobPriority(clusterId, jobIds,
                request.getPriority(), request.getReason());

        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "批量修改作业优先级")
    @RequestMapping(value = "/{clusterId}/jobs/priority", method = RequestMethod.PUT)
    public AlterJobPriorityResponse batchAlterClusterJobPriority(@PathVariable String clusterId,
                                                                 @RequestBody AlterJobPriorityRequest request) {
        if (request.getJobIds() == null || request.getJobIds().isEmpty()) {
            return new AlterJobPriorityResponse();
        }

        AlterJobPriorityResponse response = jobService.alterClusterJobPriority(clusterId, request.getJobIds(),
                request.getPriority(), request.getReason());
        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "提交作业")
    @RequestMapping(value = "/{clusterId}/job", method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_JOB_LIST,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.JOB_ALL}
    )
    public SubmitJobResponse submitJob(@PathVariable String clusterId,
                                       @Validated @RequestBody SubmitJobRequest request) {
        // openApi解密
        String plainTextPassword = passwordUtil.convertSkEncryptedPwd2Pwd(request.getPassword());
        // 密文修改为明文
        request.setPassword(plainTextPassword);

        SubmitJobResponse response = jobService.submitJob(clusterId, request);

        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "批量处理作业")
    @RequestMapping(value = "/{clusterId}/job/batchJob", method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_JOB_LIST,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.JOB_ALL}
    )
    public BatchJobResponse batchProcessJob(@PathVariable String clusterId,
                                            @RequestParam(required = true, name = "action") String action,
                                            @RequestBody BatchJobRequest batchJobRequest) {
        return jobService.batchProcessJob(clusterId, action, batchJobRequest);
    }


    @RequestIdAdapter
    @Idempotent
    @ValidateAuthentication
    @Operation(description = "创建作业提交模版")
    @RequestMapping(value = "/{clusterId}/job/template", method = RequestMethod.POST)
    public BaseResponse createJobTemplate(@PathVariable String clusterId, @Validated @RequestBody JobTemplateRequest jobTemplateRequest) {
        BaseResponse response = jobService.createJobTemplate(clusterId, jobTemplateRequest);
        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群模版列表")
    @RequestMapping(value = "/{clusterId}/job/template", method = RequestMethod.GET)
    public JobTemplatesResponse queryTemplateList(@PathVariable String clusterId) {
        JobTemplatesResponse jobTemplatesResponse = jobService.queryTemplateList(clusterId);
        return jobTemplatesResponse;
    }

    @RequestIdAdapter
    @Idempotent
    @ValidateAuthentication
    @Operation(description = "编辑作业模版")
    @RequestMapping(value = "/{clusterId}/job/template", method = RequestMethod.PUT)
    public BaseResponse modifyJobTemplate(@PathVariable String clusterId, @Validated @RequestBody JobTemplateRequest jobTemplateRequest) {
        return jobService.modifyJobTemplate(clusterId, jobTemplateRequest);
    }

    @RequestIdAdapter
    @Idempotent
    @ValidateAuthentication
    @Operation(description = "删除作业模版")
    @RequestMapping(value = "/{clusterId}/job/template", method = RequestMethod.DELETE)
    public BaseResponse deleteJobTemplate(@PathVariable String clusterId, @RequestParam(required = true, name = "templateName") String templateName) {
        return jobService.deleteJobTemplate(clusterId, templateName);
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "更新作业标签")
    @RequestMapping(value = "/{clusterId}/job/{jobId}", method = RequestMethod.PUT)
    public BaseResponse mdifyTags(@PathVariable @PermissionResourceID String clusterId,
                                  @PathVariable String jobId,
                                  @Validated @RequestBody UpdateTagsRequest request) {

        BaseResponse response = jobService.mdifyTags(clusterId, jobId, request);

        return response;
    }
}
