package com.baidu.bce.logic.chpc.controller;

import java.util.List;
import java.util.stream.Collectors;

import com.baidu.bce.logic.chpc.autoscaling.model.UpdateAutoScalingTagsRequest;
import com.baidu.bce.logic.chpc.model.response.autoscale.UpdateAutoScalingResponse;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

import org.apache.hadoop.hbase.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.autoscaling.model.DeleteAutoScalingRequest;
import com.baidu.bce.logic.chpc.autoscaling.model.PushAutoScalingBctEventRequest;
import com.baidu.bce.logic.chpc.autoscaling.model.PushAutoScalingBctEventResponse;
import com.baidu.bce.logic.chpc.autoscaling.model.SetAutoScalingRequest;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.common.OrderByType;
import com.baidu.bce.logic.chpc.model.response.autoscale.AutoScalerDetailResponse;
import com.baidu.bce.logic.chpc.model.response.autoscale.ListClusterAutoScaleResponse;
import com.baidu.bce.logic.chpc.model.response.autoscale.SetAutoScalingResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IAutoScalingService;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1")
public class AutoScalingController {


    @Autowired
    IAutoScalingService autoScalingService;

    @Idempotent
    @ValidateAuthentication
    @RequestIdAdapter
    @Operation(description = "设置自动伸缩规则")
    @RequestMapping(value = "/cluster/{clusterId}/queue/{queueName}/autoscaler", method = RequestMethod.POST)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.QUEUE_MODIFY}
    )
    public SetAutoScalingResponse setAutoScaling(@PathVariable @PermissionResourceID String clusterId,
                                       @PathVariable @PermissionResourceID String queueName,
                                       @Validated @RequestBody SetAutoScalingRequest request) {
        if (Strings.isEmpty(request.getAsName())) {
            request.setAsName(ServiceUtil.generateCustomString(clusterId, queueName));
        } else if (!ServiceUtil.isValidString(request.getAsName())) {
            throw new CommonExceptions.RequestInvalidException(String.format(
                "The autoscaler name %s is invalid. The name only supports English letters, numbers, " + 
                "underscores (_), or hyphens (-). It must start with a letter and be no longer than 63 characters.", 
                request.getAsName()));
        }
        request.setClusterId(clusterId);
        request.setQueueName(queueName);
        return autoScalingService.setAutoScaling(request);
    }

    @Idempotent
    @ValidateAuthentication
    @RequestIdAdapter
    @Operation(description = "读取自动伸缩规则")
    @RequestMapping(value = "/cluster/{clusterId}/queue/{queueName}/autoscaler", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.QUEUE_READONLY}
    )
    public AutoScalerDetailResponse getAutoScaling(@PathVariable @PermissionResourceID String clusterId,
                                                   @PathVariable @PermissionResourceID String queueName) {
        AutoScalerDetailResponse autoScalerDetailResponse = new AutoScalerDetailResponse();
        autoScalerDetailResponse.setAutoScaler(autoScalingService.getAutoScaling(clusterId, queueName));
        return autoScalerDetailResponse;
    }

    @RequestIdAdapter
    @Operation(description = "查询自动伸缩规则")
    @RequestMapping(value = "/cluster/{clusterId}/autoscalers", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY}
    )
    @ValidateAuthentication
    public ListClusterAutoScaleResponse getAutoScaling(@PathVariable @PermissionResourceID String clusterId,
                                                       @RequestParam(required = false, defaultValue = "1")
                                                       @Min(1) Integer pageNo,
                                                       @RequestParam(required = false, defaultValue = "10")
                                                       @Max(100) Integer pageSize) {
        ListClusterAutoScaleResponse response = new ListClusterAutoScaleResponse();

        List<AutoScaling> autoScales = autoScalingService.getAutoScaling(clusterId);
        response.setTotalCount(autoScales.size());
        List<AutoScaling> autoScalingList = autoScales.stream().skip((long) (pageNo - 1) * pageSize).
                limit(pageSize).collect(Collectors.toList());
        response.setAutoScalers(autoScalingService.getAutoScalingTags(autoScalingList));
        response.setPageSize(autoScalingList.size());
        response.setPageNo(pageNo);
        return response;
    }

    @Idempotent
    @ValidateAuthentication
    @RequestIdAdapter
    @Operation(description = "批量删除自动伸缩规则")
    @RequestMapping(value = "/autoscaler", method = RequestMethod.POST)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.QUEUE_MODIFY}
    )
    public BaseResponse batchDeleteAutoScaling(@RequestBody DeleteAutoScalingRequest request,
                                                @RequestParam(required = true, defaultValue = "") String action) {
        if (Strings.isEmpty(action)) {
            throw new IllegalArgumentException("action is required");
        }
        if ("batchDelete".equals(action)) {
            autoScalingService.deleteAutoScaling(request);
        }
        return new BaseResponse();
    }

    @RequestIdAdapter
    @Operation(description = "查询自动伸缩规则列表")
    @RequestMapping(value = "/autoscalers", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_READ}
    )
    @ValidateAuthentication
    public ListClusterAutoScaleResponse getAutoScaling(@RequestParam(required = false, defaultValue = "1")
                                                       @Min(1) Integer pageNo,
                                                       @RequestParam(required = false, defaultValue = "10")
                                                       @Max(100) Integer pageSize,
                                                       @RequestParam(required = false, defaultValue = "")
                                                       String keywordType,
                                                       @RequestParam(required = false, defaultValue = "")
                                                       String keyword,
                                                       @RequestParam(required = false, defaultValue = "")
                                                       String orderBy,
                                                       @RequestParam(required = false, defaultValue = "")
                                                       String order) {
                                                        
        log.debug("[AutoScalingController] getAutoScaling, pageNo: {}, pageSize: {}", pageNo, pageSize);
        ListClusterAutoScaleResponse response = new ListClusterAutoScaleResponse();
        log.debug("[AutoScalingController] getAutoScaling, response: {}", response);

        String asNameKeyword = "";
        if ("asName".equals(keywordType)) {
            asNameKeyword = keyword.trim();
        }
        String orderByType = "";
        if ("createdTime".equals(orderBy)) {
            if ("asc".equals(order)) {
                orderByType = OrderByType.CREATED_TIME_ASC.getName();
            } else {
                orderByType = OrderByType.CREATED_TIME_DESC.getName();
            }
        } else if ("updatedTime".equals(orderBy)) {
            if ("asc".equals(order)) {
                orderByType = OrderByType.UPDATED_TIME_ASC.getName();
            } else {
                orderByType = OrderByType.UPDATED_TIME_DESC.getName();
            }
        }

        List<AutoScaling> autoScales = autoScalingService.getAutoScalingByOrder(orderByType);
        response.setTotalCount(autoScales.size());
        String tmpAsName = asNameKeyword;
        List<AutoScaling> autoScalingList = autoScales.stream()
                    .filter(autoScaling -> Strings.isEmpty(tmpAsName) || Strings.isEmpty(autoScaling.getAsName()) || autoScaling.getAsName().contains(tmpAsName))
                    .skip((long) (pageNo - 1) * pageSize)
                    .limit(pageSize)
                    .collect(Collectors.toList());
        response.setAutoScalers(autoScalingService.getAutoScalingTags(autoScalingList));
        response.setPageSize(autoScalingList.size());
        response.setPageNo(pageNo);
        response.setOrderBy(orderBy.isEmpty() ? OrderByType.DEFAULT_ORDER_BY.getName() : orderBy);
        response.setOrder(order.isEmpty() ? OrderByType.DEFAULT_ORDER.getName() : order);
        return response;
    }

    @RequestIdAdapter
    @Operation(description = "用于推送BCT事件")
    @RequestMapping(value = "/autoscaler/bctevent", method = RequestMethod.POST)
    public PushAutoScalingBctEventResponse pushAutoScalingBctEvent(
                                        @RequestBody PushAutoScalingBctEventRequest pushBctEventRequest) {
        
        if (!pushBctEventRequest.getSuccess()) {
            throw new IllegalArgumentException(pushBctEventRequest.getMessage());
        }

        PushAutoScalingBctEventResponse resp = new PushAutoScalingBctEventResponse();
        resp.setSuccess(pushBctEventRequest.getSuccess());
        resp.setMessage(pushBctEventRequest.getMessage());
        
        return resp;
    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "更新自动伸缩规则tags")
    @RequestMapping(value = "/autoscaler", method = RequestMethod.PUT)
    public UpdateAutoScalingResponse updateAutoScaling(@Validated @RequestBody UpdateAutoScalingTagsRequest request) {
        return autoScalingService.updateAutoScaling(request);
    }

}
