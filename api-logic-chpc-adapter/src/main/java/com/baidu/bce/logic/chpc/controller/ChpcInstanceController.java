package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.model.request.instance.InstanceAddRequest;
import com.baidu.bce.logic.chpc.model.request.instance.InstanceOperationRequest;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceAddResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceOperationResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.ChpcInstanceService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * @Author: lilu24
 * @Date: 2022-12-07
 */
@Slf4j
@RestController
@RequestMapping("/chpc/v1/instance")
@Deprecated
public class ChpcInstanceController {

    @Resource
    private ChpcInstanceService chpcInstanceService;

    @RequestMapping(value = "/add/{clusterId}/{groupId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "扩容节点到集群")
    @Idempotent
    public InstanceAddResponse addInstance(@RequestParam(required = false) String clientToken,
                                           @PathVariable String clusterId,
                                           @PathVariable String groupId,
                                           @RequestBody InstanceAddRequest request) {

        try {

            return chpcInstanceService.addInstanceToCluster(clusterId, groupId, request);
        } catch (Exception e) {
            log.error("failed to add instance to cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }

    }

    @RequestMapping(value = "/remove/{clusterId}/{groupId}/{instanceId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "释放集群中的节点")
    @Idempotent
    public InstanceOperationResponse removeInstance(@RequestParam(required = false) String clientToken,
                                                    @PathVariable String clusterId,
                                                    @PathVariable String groupId,
                                                    @PathVariable String instanceId,
                                                    @RequestBody InstanceOperationRequest request) {

        try {

            return chpcInstanceService.removeInstanceFromCluster(clusterId, groupId, instanceId, request);
        } catch (Exception e) {
            log.error("failed to remove instance from cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }

    }

    @RequestMapping(value = "/stop/{clusterId}/{groupId}/{instanceId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "停止集群中的节点")
    @Idempotent
    public InstanceOperationResponse stopInstance(@RequestParam(required = false) String clientToken,
                                                  @PathVariable String clusterId,
                                                  @PathVariable String groupId,
                                                  @PathVariable String instanceId,
                                                  @RequestBody InstanceOperationRequest request) {

        try {

            return chpcInstanceService.stopInstanceFromCluster(clusterId, groupId, instanceId, request);
        } catch (Exception e) {
            log.error("failed to stop instance from cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }

    }


    @RequestMapping(value = "/start/{clusterId}/{groupId}/{instanceId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "启动集群中的节点")
    @Idempotent
    public InstanceOperationResponse startInstance(@RequestParam(required = false) String clientToken,
                                                   @PathVariable String clusterId,
                                                   @PathVariable String groupId,
                                                   @PathVariable String instanceId) {

        try {

            return chpcInstanceService.startInstanceFromCluster(clusterId, groupId, instanceId);
        } catch (Exception e) {
            log.error("failed to start instance from cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }

    }


    @RequestMapping(value = "/reboot/{clusterId}/{groupId}/{instanceId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "重启集群中的节点")
    @Idempotent
    public InstanceOperationResponse rebootInstance(@RequestParam(required = false) String clientToken,
                                                    @PathVariable String clusterId,
                                                    @PathVariable String groupId,
                                                    @PathVariable String instanceId,
                                                    @RequestBody InstanceOperationRequest request) {

        try {

            return chpcInstanceService.rebootInstanceFromCluster(clusterId, groupId, instanceId, request);
        } catch (Exception e) {
            log.error("failed to reboot instance from cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }

    }


}
