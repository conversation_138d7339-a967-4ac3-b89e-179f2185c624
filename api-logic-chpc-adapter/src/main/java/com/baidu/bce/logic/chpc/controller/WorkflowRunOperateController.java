package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowRunRequest;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunGetResponse;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunResponse;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunListResponse;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunDeleteResponse;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunVO;
import com.baidu.bce.logic.chpc.model.response.workspace.NameisExistResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.IWorkflowRunService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/v1/service/GeneWorks")
public class WorkflowRunOperateController {
    @Autowired
    private IWorkflowRunService workflowRunService;


    @RequestIdAdapter
    @RequestMapping(value = "/run", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: OP
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "执行工作流")
    @ValidateAuthentication
    @Idempotent
    public WorkflowRunResponse createWorkflowRun(@Validated @RequestBody WorkflowRunRequest request) {

        return workflowRunService.workflowRun(request);

    }

    @RequestIdAdapter
    @RequestMapping(value = "/run/{runId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: OP
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "中止工作流")
    @ValidateAuthentication
    @Idempotent
    public WorkflowRunResponse abortWorkflowRun(@PathVariable("runId") String runId) {

        return workflowRunService.abortWorkflow(runId);

    }

    /**
     * @Description:
     * 获取工作流执行列表，支持按名称、状态、工作流ID进行过滤。
     * 返回的结果分页展示，每页包含指定数量的记录。
     *
     * @Param workspaceId string - 可选参数，工作空间ID，默认为空字符串
     * @Param name string - 可选参数，工作流名称，默认为空字符串
     * @Param status string - 可选参数，工作流运行状态，可选值："RUNNING"、"SUCCESS"、"FAILED"、"STOPPED"，默认为空字符串
     * @Param workflowId string - 可选参数，工作流ID，默认为空字符串
     * @Param pageNo integer - 可选参数，页码，默认为1
     * @Param pageSize integer - 可选参数，每页记录数，默认为10，最大不超过100
     *
     * @Return WorkflowRunListResponse - 工作流执行列表响应对象，包含总记录数、分页后的工作流执行列表、每页记录数和当前页码
     *
     * @Throws PermissionDeniedException - 如果用户没有查看工作流执行列表的权限
     *
     * @see WorkflowRunService#listWorkflowRun(String, String, String, String) 获取工作流执行列表方法
     */
    @RequestIdAdapter
    @RequestMapping(value = "/runs", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "获取工作流执行列表")
    @ValidateAuthentication
    public WorkflowRunListResponse listWorkflowRun(
            @RequestParam(required = false)
                    String workspaceId,
            @RequestParam(required = false, defaultValue = "")
                    String name,
            @RequestParam(required = false)
                    String status,
            @RequestParam(required = false)
                    String workflowId,
            @RequestParam(required = false, defaultValue = "1")
            @Min(1) Integer pageNo,
            @RequestParam(required = false, defaultValue = "10")
            @Max(100) Integer pageSize) {
        List<WorkflowRunVO> list = workflowRunService.listWorkflowRun(workspaceId, workflowId, name, status);
        int totalCount = list.size();
        List<WorkflowRunVO> pagedList = list.stream().skip((long) (pageNo - 1) * pageSize).limit(pageSize).
                collect(Collectors.toList());
        WorkflowRunListResponse response = new WorkflowRunListResponse();
        response.setTotalCount(totalCount);
        response.setRuns(pagedList);
        response.setPageSize(pageSize);
        response.setPageNo(pageNo);
        return response;

    }

    @RequestIdAdapter
    @RequestMapping(value = "/run/{runId}", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "查询工作流执行详情")
    @ValidateAuthentication
    public WorkflowRunGetResponse getWorkflowRun(@PathVariable("runId") String runId) {
        return workflowRunService.getWorkflowRun(runId);
    }


    @RequestIdAdapter
    @RequestMapping(value = "/run/{runId}", method = RequestMethod.DELETE)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "删除工作流执行记录")
    @ValidateAuthentication
    public WorkflowRunDeleteResponse deleteWorkflowRun(@PathVariable("runId") String runId) {
        return workflowRunService.deleteWorkflowRun(runId);
    }


    @RequestIdAdapter
    @RequestMapping(value = "/run/{workflowRunName}/isExist", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "查询工作流执行是否重复")
    @ValidateAuthentication
    public NameisExistResponse workflowRunNameisExist(@PathVariable("workflowRunName") String workflowRunName,
                                                      @RequestParam(required = true) String workflowId) {
        return workflowRunService.workflowRunNameisExist(workflowId, workflowRunName);
    }
}
