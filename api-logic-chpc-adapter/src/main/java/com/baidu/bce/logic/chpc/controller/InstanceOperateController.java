package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.ca.model.CreateActionRequest;
import com.baidu.bce.logic.chpc.ca.model.CreateActionResponse;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceDeleteRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceMoveRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceOfflineRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceRebootRequest;
import com.baidu.bce.logic.chpc.model.response.instance.DeleteInstancesResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceAddResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IInstanceService;
import com.baidu.bce.logic.chpc.tag.UpdateInstanceTagsRequest;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/v1/cluster")
public class InstanceOperateController {

    @Autowired
    IInstanceService instanceService;

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "扩容节点到集群")
    @RequestMapping(value = "/{clusterId}/queue/{queueName}/instances", method = RequestMethod.POST)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.QUEUE_MODIFY}
    )
    public InstanceAddResponse addInstance(@PathVariable @PermissionResourceID String clusterId,
                                           @PathVariable @PermissionResourceID String queueName,
                                           @Validated @RequestBody InstanceAddRequest request) {

        return instanceService.addInstanceToCluster(clusterId, queueName, request);

    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "删除集群中的节点")
    @RequestMapping(value = "/{clusterId}/instances", method = RequestMethod.DELETE)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public BaseResponse removeInstance(@PathVariable @PermissionResourceID String clusterId,
                                       @RequestBody InstanceDeleteRequest request) {

        return this.batchRemoveInstances(clusterId, request);

    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "批量删除集群中的节点")
    @RequestMapping(value = "/{clusterId}/instances/batch-delete", method = RequestMethod.POST)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public DeleteInstancesResponse batchRemoveInstances(@PathVariable @PermissionResourceID String clusterId,
                                                        @RequestBody InstanceDeleteRequest request) {

        return instanceService.deleteInstanceFromCluster(clusterId, request);

    }


    @Idempotent
    @RequestIdAdapter
    @Operation(description = "更新节点标签")
    @RequestMapping(value = "/{clusterId}/instances", method = RequestMethod.PUT)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public DeleteInstancesResponse updateInstanceTag(@PathVariable @PermissionResourceID String clusterId,
                                                     @Validated @RequestBody UpdateInstanceTagsRequest request) {
        return instanceService.updateInstanceTag(clusterId, request);

    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "批量移动集群中的节点")
    @RequestMapping(value = "/{clusterId}/instances/batch-move", method = RequestMethod.POST)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public BaseResponse batchMoveInstances(@PathVariable @PermissionResourceID String clusterId,
                                           @RequestBody InstanceMoveRequest request) {

        return instanceService.moveInstance(clusterId, request);

    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "批量设置集群中的节点为不可调度")
    @RequestMapping(value = "/{clusterId}/instances/batch-offline", method = RequestMethod.PUT)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public BaseResponse batchOfflineInstances(@PathVariable @PermissionResourceID String clusterId,
                                              @RequestBody InstanceOfflineRequest request) {

        return instanceService.instanceOffline(clusterId, request);

    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "批量重启集群中的节点")
    @RequestMapping(value = "/{clusterId}/instances/batch-reboot", method = RequestMethod.POST)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public BaseResponse batchRebootInstances(@PathVariable @PermissionResourceID String clusterId,
                                            @RequestBody InstanceRebootRequest request) {

        return instanceService.rebootInstance(request);

    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "给集群中的节点发送命令")
    @RequestMapping(value = "/{clusterId}/instances/action", method = RequestMethod.POST)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public CreateActionResponse instancesCreateAction(@PathVariable @PermissionResourceID String clusterId,
                                                        @RequestBody CreateActionRequest request) {

        return instanceService.instanceCreateAction(request);

    }

}
