package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.request.service.ServiceSubscribeRequest;
import com.baidu.bce.logic.chpc.model.request.service.ServiceSubscribeSaasRequest;
import com.baidu.bce.logic.chpc.model.response.services.ServiceAddResponse;
import com.baidu.bce.logic.chpc.model.response.services.ServiceGetResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.IServicesService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: jiazhongxiang
 * @Date: 2023-12-09
 */
@Slf4j
@RestController
@RequestMapping("/v1/service")
public class ServicesOperateController {

    @Autowired
    private IServicesService serviceService;


    @RequestIdAdapter
    @RequestMapping(value = "/subscribe", method = RequestMethod.POST)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "开通服务")
    @ValidateAuthentication
    @Idempotent
    public ServiceAddResponse subscribeService(@RequestBody ServiceSubscribeRequest request) {

        return serviceService.subscribeService(request.getServiceId());

    }


    @RequestIdAdapter
    @RequestMapping(value = "/subscribe/saas", method = RequestMethod.POST)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "手动开通天云服务")
    @ValidateAuthentication
    @Idempotent
    public ServiceAddResponse subscribeSaasService(@RequestBody ServiceSubscribeSaasRequest request) {
        // 当前使用白名单形式开通:主账号ID,pfs的ID,FileSet的ID
        return serviceService.subscribeServiceSaas(request.getAccountId(), request.getPfsId(), request.getFilesetId());

    }

    @RequestIdAdapter
    @RequestMapping(method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "获取服务列表")
    @ValidateAuthentication
    @Idempotent
    public List<ServiceGetResponse> getServices(@RequestParam(required = false, defaultValue = "")
                                                        String name) {

        return serviceService.getService(name);
    }

}
