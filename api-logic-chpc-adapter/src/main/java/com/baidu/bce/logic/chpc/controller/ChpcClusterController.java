package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterDeleteRequest;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterOperationRequest;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterUpdateRequest;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterDetailResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterListResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.model.response.task.TaskDetailResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.ChpcClusterService;
import com.baidu.bce.logic.chpc.service.ChpcTaskService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

/**
 * @Author: lilu24
 * @Date: 2022-12-07
 */
@Slf4j
@RestController
@RequestMapping("/chpc/v1/cluster")
@Deprecated
public class ChpcClusterController {

    @Resource
    private ChpcClusterService chpcClusterService;

    @Resource
    private ChpcTaskService chpcTaskService;

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "创建CHPC集群")
    @Idempotent
    public ClusterResponse createCluster(@RequestParam(required = false) String clientToken,
                                         @RequestBody ClusterCreateRequest clusterCreateRequest) {

        try {
            return chpcClusterService.createCluster(clusterCreateRequest);
        } catch (Exception e) {
            log.error("failed to create  cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }

    }

    @RequestMapping(value = "/detail/{clusterId}", method = RequestMethod.GET)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_READ}
    )
    @Operation(description = "通过集群ID获取CHPC集群详情")
    @ValidateAuthentication
    public ClusterDetailResponse getClusterDetail(@PathVariable("clusterId") String clusterId) {

        try {
            return chpcClusterService.getClusterDetail(clusterId);
        } catch (Exception e) {
            log.error("failed to get  cluster detail, error is: ", e);
            throw new BceException(e.getMessage());
        }
    }

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_READ}
    )
    @Operation(description = "查询当前账户下CHPC集群列表")
    public ClusterListResponse listCluster() {

        try {
            return chpcClusterService.listCluster();
        } catch (Exception e) {
            log.error("failed to list  cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }

    }

    @RequestMapping(value = "/delete/{clusterId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "删除CHPC集群")
    @Idempotent
    @ValidateAuthentication
    public ClusterResponse deleteCluster(@RequestParam(required = false) String clientToken,
                                         @PathVariable("clusterId") String clusterId,
                                         @RequestBody ClusterDeleteRequest request) {

        try {
            return chpcClusterService.deleteCluster(clusterId, request.getForce());
        } catch (Exception e) {
            log.error("failed to delete cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }
    }

    @RequestMapping(value = "/update/{clusterId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "更新CHPC集群")
    @Idempotent
    public ClusterResponse updateCluster(@RequestParam(required = false) String clientToken,
                                         @PathVariable("clusterId") String clusterId,
                                         @RequestBody ClusterUpdateRequest request) {

        try {
            return chpcClusterService.updateCluster(clusterId, request);
        } catch (Exception e) {
            log.error("failed to delete cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }
    }

    @RequestMapping(value = "/start/{clusterId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "启动CHPC集群")
    @Idempotent
    public ClusterResponse startCluster(@RequestParam(required = false) String clientToken,
                                        @PathVariable("clusterId") String clusterId,
                                        @RequestBody(required = false) ClusterOperationRequest request) {

        try {
            return chpcClusterService.startCluster(clusterId, request);
        } catch (Exception e) {
            log.error("failed to delete cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }
    }


    @RequestMapping(value = "/stop/{clusterId}", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "停止CHPC集群")
    @Idempotent
    public ClusterResponse stopCluster(@RequestParam(required = false) String clientToken,
                                       @PathVariable("clusterId") String clusterId,
                                       @RequestBody(required = false) ClusterOperationRequest request) {

        try {
            return chpcClusterService.stopCluster(clusterId, request);
        } catch (Exception e) {
            log.error("failed to delete cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }
    }

    @RequestMapping(value = "/task/{clusterId}/{taskId}", method = RequestMethod.GET)
    @PermissionVerify(
        // TODO: 需要精细化资源粒度 {clusterId}
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_CONTROL}
    )
    @Operation(description = "获取集群异步任务的处理状态")
    public TaskDetailResponse getClusterTaskDetail(
            @PathVariable("clusterId") String clusterId,
            @PathVariable("taskId") String taskId) {

        try {
            return chpcTaskService.getClusterTaskDetail(clusterId, taskId);
        } catch (Exception e) {
            log.error("failed to delete cluster, error is: ", e);
            throw new BceException(e.getMessage());
        }
    }
}
