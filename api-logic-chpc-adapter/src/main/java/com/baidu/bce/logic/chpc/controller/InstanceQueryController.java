package com.baidu.bce.logic.chpc.controller;


import java.util.List;
import java.util.stream.Collectors;

import com.baidu.bce.logic.chpc.model.response.instance.InstanceTagsResponse;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceDetail;
import com.baidu.bce.logic.chpc.model.response.instance.ListClusterInstancesResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IInstanceService;
import com.baidu.bce.logic.core.exception.CommonExceptions;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/cluster")
public class InstanceQueryController {

    @Autowired
    IInstanceService instanceService;

    /**
     * @Description:
     * 查询集群中的节点，包括分页、过滤条件等功能。
     *
     * @Param clusterId string - 必选参数，路径变量，集群ID，用于标识需要查询的集群。
     * @Param pageNo integer - 可选参数，默认值为1，表示当前页码，从1开始计数。
     * @Param pageSize integer - 可选参数，默认值为10，表示每页显示的节点数量，取值范围为1到100。
     * @Param nodeType string - 可选参数，字符串类型，表示节点类型，空字符串表示不进行过滤。
     * @Param scheduleStatus string - 可选参数，字符串类型，表示调度状态，空字符串表示不进行过滤。
     * @Param keywordType string - 可选参数，字符串类型，表示关键字类型，空字符串表示不进行过滤。
     * @Param queueName string - 可选参数，字符串类型，表示队列名称，空字符串表示不进行过滤。
     * @Param keyword string - 可选参数，字符串类型，表示关键字，空字符串表示不进行过滤。
     *
     * @Return ListClusterInstancesResponse - 返回类型，包含节点列表、分页信息和总记录数。
     *
     * @Throws PermissionDeniedException - 如果没有查看集群权限或者只读权限，则抛出该异常。
     */
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群中的节点")
    @RequestMapping(value = "/{clusterId}/instances", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY}
    )
    public ListClusterInstancesResponse getClusterInstances(@PathVariable @PermissionResourceID String clusterId,
                                                            @RequestParam(required = false, defaultValue = "1")
                                                            @Min(1) Integer pageNo,
                                                            @RequestParam(required = false, defaultValue = "10")
                                                            @Max(100) Integer pageSize,
                                                            @RequestParam(required = false, defaultValue = "")
                                                            String nodeType,
                                                            @RequestParam(required = false, defaultValue = "")
                                                            String scheduleStatus,
                                                            @RequestParam(required = false, defaultValue = "")
                                                            String keywordType,
                                                            @RequestParam(required = false, defaultValue = "")
                                                            String queueName,
                                                            @RequestParam(required = false, defaultValue = "")
                                                            String keyword) {
        ListClusterInstancesResponse response = new ListClusterInstancesResponse();
        String hostname = "";
        String hostIP = "";
        if ("hostName".equals(keywordType)) {
            hostname = keyword.trim();
        } else if ("privateIp".equals(keywordType)) {
            hostIP = keyword.trim();
        }
        
        List<Instance> instances = instanceService.getClusterInstances(clusterId, nodeType, hostname, queueName, hostIP, scheduleStatus, true);
        int totalCount = instances.size();
        List<Instance> instanceList = instances.stream().skip((long) (pageNo - 1) * pageSize).
                limit(pageSize).collect(Collectors.toList());
        response.setInstances(instanceService.getInstancesTags(instanceList));
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount(totalCount);

        return response;
    }
    // TODO: 前端没有调用该接口
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群中队列的节点")
    @RequestMapping(value = "/{clusterId}/queue/{queueName}/instances", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.QUEUE_READONLY}
    )
    public ListClusterInstancesResponse getQueueInstances(@PathVariable @PermissionResourceID String clusterId,
                                                          @PathVariable @PermissionResourceID String queueName,
                                                          @RequestParam(required = false, defaultValue = "1")
                                                          @Min(1) Integer pageNo,
                                                          @RequestParam(required = false, defaultValue = "10")
                                                          @Max(100) Integer pageSize) {
        ListClusterInstancesResponse response = new ListClusterInstancesResponse();
        int offset = (pageNo - 1) * pageSize;
        List<Instance> instances = instanceService.getQueueInstances(clusterId, queueName, offset, pageSize);
        long totalCount = instanceService.countInstances(clusterId, queueName);
        response.setInstances(instanceService.getInstancesTags(instances));
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);
        response.setTotalCount((int) totalCount);

        return response;
    }

    // TODO：前端没有调用该接口
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群中的管理节点")
    @RequestMapping(value = "/{clusterId}/manager/instance", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY}
    )
    public InstanceDetail getManagerInstance(
            @PathVariable @PermissionResourceID String clusterId
    ) {
        InstanceDetail response = new InstanceDetail();
        Instance instance = instanceService.getClusterManagerInstanceOrNull(clusterId);
        if (instance == null) {
            throw new CommonExceptions.ResourceNotExistException("can not found manager instance;");
        }
        response.setInstance(instance);
        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群中的全部可用云上节点")
    @RequestMapping(value = "/available/instances", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY}
    )
    public ListClusterInstancesResponse getAllAvaliableInstances(
            @RequestParam(required = true, defaultValue = "")
            String zoneName,
            @RequestParam(required = true, defaultValue = "")
            String subnetId,
            @RequestParam(required = false, defaultValue = "")
            String instanceIds,
            @RequestParam(required = false, defaultValue = "")
            String instanceNames
    ) {
        ListClusterInstancesResponse response = new ListClusterInstancesResponse();
        List<Instance> instances = instanceService.getAllCloudInstances(zoneName, subnetId, instanceIds, instanceNames);
        response.setInstances(instances);
        response.setTotalCount(instances.size());

        return response;
    }

    @RequestIdAdapter
    @RequestMapping(value = "/instance/tags", method = RequestMethod.GET)
    @Operation(description = "查询混合云节点可用标签")
    @ValidateAuthentication
    public InstanceTagsResponse getInstanceTags() {

        return instanceService.listTags();

    }

}
