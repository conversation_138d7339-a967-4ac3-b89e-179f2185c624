package com.baidu.bce.logic.chpc.controller.cluster;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.model.request.usercallbacktask.UserCallbackTaskUpdateRequest;
import com.baidu.bce.logic.chpc.model.response.usercallbacktask.UserCallbackTaskDetailResponse;
import com.baidu.bce.logic.chpc.service.ChpcTaskService;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/cluster")
public class UserCallbackTaskController {

    
    @Resource
    private ChpcTaskService chpcTaskService;

    
    @Idempotent
    @RequestIdAdapter
    @RequestMapping(value = "/usercallbacktask/{clusterId}/{taskId}", method = RequestMethod.GET)
    @Operation(description = "获取用户自定义回调任务的处理状态")
    public UserCallbackTaskDetailResponse getUserCallbackTaskDetail(
            @PathVariable("clusterId") @PermissionResourceID String clusterId,
            @PathVariable("taskId") String taskId) {

        try {
            return chpcTaskService.getUserCallbackTaskDetail(clusterId, taskId);
        } catch (Exception e) {
            log.error("failed to get user callback task detail, error is: ", e);
            throw new BceException(e.getMessage());
        }
    }

    
    @Idempotent
    @RequestIdAdapter
    @RequestMapping(value = "/usercallbacktask/{clusterId}/{taskId}", method = RequestMethod.POST)
    @Operation(description = "更新用户自定义回调任务的处理状态")
    public UserCallbackTaskDetailResponse updateUserCallbackTaskStatus(
            @PathVariable("clusterId") @PermissionResourceID String clusterId,
            @PathVariable("taskId") String taskId,
            @RequestBody UserCallbackTaskUpdateRequest request) {

        try {
            return chpcTaskService.updateUserCallbackTaskStatus(clusterId, taskId, request.getStatus());
        } catch (Exception e) {
            log.error("failed to update user callback task, error is: ", e);
            throw new BceException(e.getMessage());
        }
    }
}