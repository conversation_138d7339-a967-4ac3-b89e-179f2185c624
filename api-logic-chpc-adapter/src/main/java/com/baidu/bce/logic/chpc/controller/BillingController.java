package com.baidu.bce.logic.chpc.controller;

import jakarta.annotation.Resource;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.billing.LegacyChargeDataRequest;
import com.baidu.bce.logic.chpc.model.ChargeData;
import com.baidu.bce.logic.chpc.model.request.billing.BillingActiveRequest;
import com.baidu.bce.logic.chpc.model.request.billing.ResourceUsageRequest;
import com.baidu.bce.logic.chpc.model.response.billing.ActiveResourceResponse;
import com.baidu.bce.logic.chpc.model.response.billing.QueryResourceResponse;
import com.baidu.bce.logic.chpc.service.IBillingService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/v1/billing")
public class BillingController {

    @Resource
    private IBillingService billingService;

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "查询资源")
    @RequestMapping(value = "/{accountId}/{service}/resources/{resourceId}", method = RequestMethod.GET)
    public QueryResourceResponse queryResource(@PathVariable("accountId") String accountId,
            @PathVariable("service") String service,
            @PathVariable("resourceId") String resourceId,
            @RequestParam("region") String region) {

        log.debug("billing query accountId:{} service:{} region:{} resourceId:{}", accountId, service, region, resourceId);
        return billingService.queryResource(resourceId, accountId);
    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "启停资源")
    @RequestMapping(value = "/{accountId}/{service}/resources/{resourceId}", method = RequestMethod.PUT)
    public ActiveResourceResponse activateResource(@PathVariable("accountId") String accountId,
            @PathVariable("service") String service,
            @PathVariable("resourceId") String resourceId,
            @RequestParam("action") String action,
            @RequestBody BillingActiveRequest bbeBillingActiveForm) {

        log.debug("billing query accountId:{} service:{} action:{} resourceId:{}", accountId, service, action, resourceId);

        return billingService.activateResource(resourceId, accountId, action, bbeBillingActiveForm);
    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "推送计费用量")
    @RequestMapping(value = "/charge/data/{orderId}", method = RequestMethod.POST)
    public Boolean chargeData(@PathVariable("orderId") String orderId,
            @RequestBody LegacyChargeDataRequest request) {
        return billingService.chargeData(orderId, request);
    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "查询计费用量")
    @RequestMapping(value = "/resource/usage", method = RequestMethod.POST)
    public Boolean resourceUsage(@RequestBody ResourceUsageRequest resourceUsage) {
        ChargeData request = new ChargeData();
        request.setAccountId(resourceUsage.getAccountId());
        request.setInstanceId(resourceUsage.getInstanceId());
        request.setServiceType(resourceUsage.getServiceType());
        request.setChargeItem(resourceUsage.getChargeItem());
        request.setChargeAmount(resourceUsage.getChargeAmount());
        request.setChargeTime(resourceUsage.getChargeTime());

        return billingService.resourceUsageTrail(request, resourceUsage.getStartTime(), resourceUsage.getEndTime(),
                resourceUsage.getRegion());
    }

}
