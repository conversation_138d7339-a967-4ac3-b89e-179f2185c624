package com.baidu.bce.logic.chpc.controller.consulehub;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Resource;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterVO;
import com.baidu.bce.logic.chpc.model.response.cluster.ListClustersResponse;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;
import com.baidu.bce.logic.chpc.service.IInstanceService;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/console")
public class ConsoleClusterQueryController {

    @Resource
    IChpcClusterService chpcClusterService;

    @Autowired
    IInstanceService instanceService;

    /**
     * @Description:
     * 查询集群列表，支持按集群名称过滤。返回分页结果，默认每页显示10条记录。
     * 如果包含预付费实例的集群ID在返回的集群列表中，则对应集群将标记为包含预付费实例。
     *
     * @Param clusterName string 可选参数，默认值为空字符串，用于按集群名称进行过滤。
     * @Param pageNo int 可选参数，默认值为1，用于指定当前页码。
     * @Param pageSize int 可选参数，默认值为10，用于指定每页显示的记录数。最大不超过1000。
     *
     * @Return ListClustersResponse 返回一个ListClustersResponse类型的对象，包含集群列表、总记录数、当前页码和每页显示的记录数等信息。
     *
     * @Throws 无特定异常抛出。
     */
    @RequestIdAdapter
    @Operation(description = "查询集群列表")
    @RequestMapping(value = "/clusters", method = RequestMethod.GET)
    public ListClustersResponse getClusters(@RequestParam(required = false, defaultValue = "") String clusterName,
                                            @RequestParam(required = false, defaultValue = "") String clusterType,
                                            @RequestParam(required = false, defaultValue = "") String schedulerType,
                                            @RequestParam(required = false, defaultValue = "1")
                                            @Min(1) Integer pageNo,
                                            @RequestParam(required = false, defaultValue = "10")
                                            @Max(1000) Integer pageSize) {
        ListClustersResponse response = new ListClustersResponse();
        List<ClusterVO> clusters = chpcClusterService.getClusters(clusterName).stream()
                    .filter(cluster -> (clusterType.isEmpty() || clusterType.equals(cluster.getClusterType())) &&
                            (schedulerType.isEmpty() || schedulerType.equals(cluster.getSchedulerType())))
                    .collect(Collectors.toList());

        List<ClusterVO> clusterVOS = clusters.stream()
                    .skip((long) (pageNo - 1) * pageSize)
                    .limit(pageSize)
                    .collect(Collectors.toList());

        List<String> clusterIds = clusterVOS.stream().map(ClusterVO::getClusterId).collect(Collectors.toList());
        List<String> containsPrepayInstanceClusterId = instanceService.getContainsPrepayInstanceClusterId(clusterIds);
        clusterVOS.forEach(clusterVO -> {
            if (containsPrepayInstanceClusterId.contains(clusterVO.getClusterId())) {
                clusterVO.setContainsPrepayInstances(true);
            }
        });

        response.setTotalCount(clusters.size());
        response.setClusters(clusterVOS);
        response.setPageNo(pageNo);
        response.setPageSize(pageSize);

        return response;
    }

}
