package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.cluster.model.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.ResetPasswordRequest;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterUpdateRequest;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterCreateResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;
import com.baidu.bce.logic.chpc.service.util.AesEncryptUtil;
import com.baidu.bce.logic.chpc.service.util.PasswordUtil;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/v1/cluster")
public class ClusterOperateController {

    @Resource
    private IChpcClusterService chpcClusterService;

    @Resource
    PasswordUtil passwordUtil;

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "创建集群")
    @RequestMapping(method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
            permissions = {PermissionConstant.CHPC_CONTROL, PermissionConstant.CLUSTER_MODIFY}
    )
    public ClusterCreateResponse createCluster(@Validated @RequestBody ClusterCreateRequest clusterCreateRequest) {

        if (StringUtils.isNotEmpty(clusterCreateRequest.getPassword())) {
            // SkEncrypted解析为密码原文
            String password = passwordUtil.convertSkEncryptedPwd2Pwd(clusterCreateRequest.getPassword());
            // 加密存入db
            String aesPassword = AesEncryptUtil.encrypt(password);
            clusterCreateRequest.setPassword(aesPassword);
        }

        ClusterResponse clusterResponse = chpcClusterService.createCluster(clusterCreateRequest);
        ClusterCreateResponse response = new ClusterCreateResponse();
        response.setClusterId(clusterResponse.getClusterId());
        response.setMessage(clusterResponse.getMessage());
        return response;
    }


    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "释放集群")
    @RequestMapping(value = "/{clusterId}", method = RequestMethod.DELETE)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_CONTROL, PermissionConstant.CLUSTER_MODIFY}
    )
    public ClusterResponse deleteCluster(@PathVariable("clusterId") @PermissionResourceID String clusterId) {

        return chpcClusterService.deleteCluster(clusterId, true);

    }

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "更新集群信息")
    @RequestMapping(value = "/{clusterId}", method = RequestMethod.PUT)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public ClusterResponse updateCluster(@PathVariable("clusterId") @PermissionResourceID String clusterId,
                                         @Validated @RequestBody ClusterUpdateRequest request) {

        return chpcClusterService.updateCluster(clusterId, request);
    }

    @RequestIdAdapter
    @Operation(description = "重置集群密码")
    @RequestMapping(value = "/{clusterId}/resetPassword", method = RequestMethod.PUT)
    public ClusterResponse resetPassword(@PathVariable("clusterId") @PermissionResourceID String clusterId,
                                         @Validated @RequestBody ResetPasswordRequest resetPasswordRequest) {
        // SkEncrypted解析为密码原文
        String password = passwordUtil.convertSkEncryptedPwd2Pwd(resetPasswordRequest.getPassword());

        return chpcClusterService.resetPassword(clusterId, password);
    }
}
