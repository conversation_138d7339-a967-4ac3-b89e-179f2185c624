package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.scheduler.GetSchedulerResponse;
import com.baidu.bce.logic.chpc.model.scheduler.SchedulerCommonResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.SchedulerService;
import com.baidu.bce.logic.chpc.sheduler.SchedulerAddRequest;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/cluster")
public class SchedulerController {
    @Autowired
    SchedulerService schedulerService;

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群的调度器配置")
    @RequestMapping(value = "/{clusterId}/scheduler", method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY}
    )
    public GetSchedulerResponse getClusterScheduler(@PathVariable @PermissionResourceID String clusterId) {

        GetSchedulerResponse response = schedulerService.getScheduler(clusterId);

        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "更新集群的调度器配置")
    @RequestMapping(value = "/{clusterId}/scheduler", method = RequestMethod.PUT)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    public SchedulerCommonResponse modifyClusterScheduler(@PathVariable @PermissionResourceID String clusterId,
                                                          @RequestBody SchedulerAddRequest request) {

        SchedulerCommonResponse response = schedulerService.modifyScheduler(clusterId, request);

        return response;
    }
}
