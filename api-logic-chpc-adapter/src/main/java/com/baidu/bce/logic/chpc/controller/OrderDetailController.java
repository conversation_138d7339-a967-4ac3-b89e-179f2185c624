package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.billing.OrderDetailRequest;
import com.baidu.bce.logic.chpc.service.IBillingService;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;



@Slf4j
@Validated
@RestController
@RequestMapping("/order")
public class OrderDetailController {
    @Resource
    private IBillingService billingService;


    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "订单详情接口")
    @RequestMapping(value = "/detail", method = RequestMethod.POST)
    public Order getOrderDetail(@RequestBody OrderDetailRequest request) {
        Order order = billingService.queryOrderDetail(request.getUuid());
        return order;
    }
}
