package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.cromwell.model.WorkspaceCreateRequest;
import com.baidu.bce.logic.chpc.model.Workspace;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceListResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceCreateResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceGetResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceDeleteResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.AvailableCluster;
import com.baidu.bce.logic.chpc.model.response.workspace.AvailableClusterListResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.NameisExistResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.IWorkspaceService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: jiazhongxiang
 * @Date: 2023-12-09
 */
@Slf4j
@RestController
@RequestMapping("/v1/service/GeneWorks")
public class WorkspaceOperateController {

    @Autowired
    private IWorkspaceService workspaceService;


    @RequestIdAdapter
    @RequestMapping(value = "/workspace", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: OP
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "新建工作空间")
    @ValidateAuthentication
    @Idempotent
    public WorkspaceCreateResponse createWorkflow(@Validated @RequestBody WorkspaceCreateRequest request) {

        return workspaceService.createWorkspace(request);

    }

    /**
     * @Description:
     * 获取工作空间列表，支持分页和按名称搜索。
     * 需要校验用户有读取ChPC的权限或只读权限。
     * 返回值包含工作空间列表、总数量、分页大小和当前页码。
     *
     * @Param pageNo Integer 可选，默认为1，页码从1开始，不能为0，最大不超过100
     * @Param pageSize Integer 可选，默认为10，每页条数，不能为0，最大不超过100
     * @Param name String 可选，工作空间名称，支持模糊查询
     *
     * @Return WorkspaceListResponse 工作空间列表响应对象，包含工作空间列表、总数量、分页大小和当前页码
     *
     * @Throws PermissionDeniedException 如果用户没有读取ChPC的权限或只读权限时抛出此异常
     *
     * @see com.tencent.bk.codecc.chpcg.service.WorkspaceService#listWorkspace(String)
     */
    @RequestIdAdapter
    @RequestMapping(value = "/workspace", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "获取工作空间列表")
    @ValidateAuthentication
    public WorkspaceListResponse listWorkspace(
            @RequestParam(required = false, defaultValue = "1")
            @Min(1) Integer pageNo,
            @RequestParam(required = false, defaultValue = "10")
            @Max(100) Integer pageSize,
            @RequestParam(required = false, defaultValue = "")
                    String name) {
        List<Workspace> list = workspaceService.listWorkspace(name);
        int totalCount = list.size();
        List<Workspace> pagedList = list.stream().skip((long) (pageNo - 1) * pageSize).limit(pageSize).
                collect(Collectors.toList());
        WorkspaceListResponse response = new WorkspaceListResponse();
        response.setTotalCount(totalCount);
        response.setWorkspaces(pagedList);
        response.setPageSize(pageSize);
        response.setPageNo(pageNo);
        return response;

    }

    @RequestIdAdapter
    @RequestMapping(value = "/workspace/{workspaceId}", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "查询工作空间详情")
    @ValidateAuthentication
    public WorkspaceGetResponse getWorkspace(@PathVariable("workspaceId") String workspaceId) {
        return workspaceService.getWorkspace(workspaceId);
    }


    @RequestIdAdapter
    @RequestMapping(value = "/workspace/{workspaceId}", method = RequestMethod.DELETE)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "删除指定工作空间")
    @ValidateAuthentication
    @Idempotent
    public WorkspaceDeleteResponse deleteWorkspace(@PathVariable("workspaceId") String workspaceId) {

        return workspaceService.deleteWorkspace(workspaceId);
    }

    @RequestIdAdapter
    @RequestMapping(value = "/workspace/{workspaceName}/isExist", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "查询工作空间名称是否重复")
    @ValidateAuthentication
    public NameisExistResponse workspaceNameisExist(@PathVariable("workspaceName") String workspaceName) {
        return workspaceService.workspaceNameisExist(workspaceName);
    }


    /**
     * @Description: 获取可绑定的集群列表，支持分页查询
     * @Param pageNo 当前页码，默认为1，从1开始计数，必须大于等于1
     * @Param pageSize 每页条目数，默认为1000，最多不超过1000
     * @Return AvailableClusterListResponse 包含可绑定集群列表、总条目数和分页信息的响应对象
     * @Throws None
     */
    @RequestIdAdapter
    @RequestMapping(value = "/workspace/availableClusters", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "获取可绑定的集群列表")
    @ValidateAuthentication
    public AvailableClusterListResponse listAvailableCluster(
            @RequestParam(required = false, defaultValue = "1")
            @Min(1) Integer pageNo,
            @RequestParam(required = false, defaultValue = "1000")
            @Max(1000) Integer pageSize) {
        List<AvailableCluster> list = workspaceService.listAvailableCluster();
        int totalCount = list.size();
        List<AvailableCluster> pagedList = list.stream().skip((long) (pageNo - 1) * pageSize).limit(pageSize).
                collect(Collectors.toList());
        AvailableClusterListResponse response = new AvailableClusterListResponse();
        response.setTotalCount(totalCount);
        response.setAvailableClusterList(pagedList);
        response.setPageSize(pageSize);
        response.setPageNo(pageNo);
        return response;

    }
}
