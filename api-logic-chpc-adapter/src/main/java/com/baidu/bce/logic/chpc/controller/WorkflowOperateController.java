package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowCreateRequest;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowParseRequest;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowUpdateRequest;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowCreateResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowListResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowGetResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowDeleteResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowUpdateResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowParseResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowVO;
import com.baidu.bce.logic.chpc.model.response.workspace.NameisExistResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.IWorkflowService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;


import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/v1/service/GeneWorks")
public class WorkflowOperateController {
    @Autowired
    private IWorkflowService workflowService;


    @RequestIdAdapter
    @RequestMapping(value = "/workflow", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: OP
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "新建工作流")
    @ValidateAuthentication
    @Idempotent
    public WorkflowCreateResponse createWorkflow(@Validated @RequestBody WorkflowCreateRequest request) {

        return workflowService.createWorkflow(request);

    }

    /**
     * @Description: 获取工作流列表，包括分页和过滤功能。
     * @Param workspaceId String 工作空间ID，必填参数，不可为空字符串。
     * @Param pageNo Integer 当前页码，默认值为1，最小值为1。
     * @Param pageSize Integer 每页记录数，默认值为10，最大值为1000。
     * @Param name String 工作流名称，默认值为空字符串。
     * @Return WorkflowListResponse 返回一个WorkflowListResponse对象，包含工作流列表、总记录数、每页记录数和当前页码等信息。
     * @Throws Exception 无异常抛出。
     */
    @RequestIdAdapter
    @RequestMapping(value = "/{workspaceId}/workflow", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "获取工作流列表")
    @ValidateAuthentication
    public WorkflowListResponse listWorkflow(
            @PathVariable("workspaceId") String workspaceId,
            @RequestParam(required = false, defaultValue = "1")
            @Min(1) Integer pageNo,
            @RequestParam(required = false, defaultValue = "10")
            @Max(1000) Integer pageSize,
            @RequestParam(required = false, defaultValue = "")
                    String name) {
        List<WorkflowVO> list = workflowService.listWorkflow(workspaceId, name);
        int totalCount = list.size();
        List<WorkflowVO> pagedList = list.stream().skip((long) (pageNo - 1) * pageSize).limit(pageSize).
                collect(Collectors.toList());
        WorkflowListResponse response = new WorkflowListResponse();
        response.setTotalCount(totalCount);
        response.setWorkflows(pagedList);
        response.setPageSize(pageSize);
        response.setPageNo(pageNo);
        return response;

    }

    @RequestIdAdapter
    @RequestMapping(value = "/workflow/{workflowId}", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "查询工作流详情")
    @ValidateAuthentication
    public WorkflowGetResponse getWorkflow(@PathVariable("workflowId") String workspaceId,
                                           @RequestParam(required = false) Long version) {
        return workflowService.getWorkflow(workspaceId, version);
    }


    @RequestIdAdapter
    @RequestMapping(value = "/workflow/{workflowId}", method = RequestMethod.DELETE)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "删除指定工作流")
    @ValidateAuthentication
    @Idempotent
    public WorkflowDeleteResponse deleteWorkflow(@PathVariable String workflowId) {

        return workflowService.deleteWorkflow(workflowId);
    }


    @RequestIdAdapter
    @RequestMapping(value = "/workflow/{workflowId}", method = RequestMethod.PUT)
    @PermissionVerify(
        // TODO: OP
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "更新工作流")
    @ValidateAuthentication
    @Idempotent
    public WorkflowUpdateResponse updateWorkflow(@PathVariable String workflowId,
                                                  @Validated @RequestBody WorkflowUpdateRequest request) {

        return workflowService.updateWorkflow(workflowId, request);

    }


    @RequestIdAdapter
    @RequestMapping(value = "/workflow/parseWorkflow", method = RequestMethod.POST)
    @PermissionVerify(
        // TODO: OP
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.SERVICE_MODIFY}
    )
    @Operation(description = "解析工作流输入")
    @ValidateAuthentication
    @Idempotent
    public WorkflowParseResponse parseWorksflow(@Validated @RequestBody WorkflowParseRequest request) {

        return workflowService.parseWorksflow(request);

    }

    @RequestIdAdapter
    @RequestMapping(value = "/workflow/{workflowName}/isExist", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.SERVICE_READONLY}
    )
    @Operation(description = "查询工作流执行是否重复")
    @ValidateAuthentication
    public NameisExistResponse workflowNameisExist(@PathVariable("workflowName") String workflowName,
                                                   @RequestParam(required = true) String workspaceId) {
        return workflowService.workflowNameisExist(workspaceId, workflowName);
    }
}
