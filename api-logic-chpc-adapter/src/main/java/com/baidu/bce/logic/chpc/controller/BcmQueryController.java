package com.baidu.bce.logic.chpc.controller;

import jakarta.annotation.Resource;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.bcm.model.BatchQueryRequest;
import com.baidu.bce.logic.chpc.bcm.model.BatchQueryResponse;
import com.baidu.bce.logic.chpc.bcm.model.PartialQueryRequest;
import com.baidu.bce.logic.chpc.bcm.model.PartialQueryResponse;
import com.baidu.bce.logic.chpc.service.IBcmService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/v1/cluster")
public class BcmQueryController {

    @Resource
    private IBcmService bcmService;

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "批量查询（汇聚）")
    @RequestMapping(value = "/bcm/data/metricAllData/batch/converge", method = RequestMethod.POST)
    public BatchQueryResponse batchQuery(@Validated @RequestBody BatchQueryRequest request) {

        BatchQueryResponse queryResponse = bcmService.batchQuery(request);
        return queryResponse;
    }

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "批量查询（汇聚）")
    @RequestMapping(value = "/bcm/data/metricData/PartialDimension/converge", method = RequestMethod.POST)
    public PartialQueryResponse partialQuery(@Validated @RequestBody PartialQueryRequest request) {

        PartialQueryResponse queryResponse = bcmService.partialQuery(request);
        return queryResponse;
    }

}
