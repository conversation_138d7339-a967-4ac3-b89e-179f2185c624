package com.baidu.bce.logic.chpc.controller.cluster;

import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.cluster.model.ClusterHeartbeatRequest;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/cluster")
public class ClusterHeartbeatController {
    @Resource
    private IChpcClusterService chpcClusterService;

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "心跳请求")
    @RequestMapping(value = "/heartbeat", method = RequestMethod.POST)
    public BaseResponse clusterHeartbeat(@Validated @RequestBody ClusterHeartbeatRequest clusterHeartbeatRequest) {

        return chpcClusterService.heartbeat(clusterHeartbeatRequest);
    }
}
