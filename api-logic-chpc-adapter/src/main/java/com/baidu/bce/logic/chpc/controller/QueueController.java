package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.request.queue.QueueAddRequest;
import com.baidu.bce.logic.chpc.model.response.queue.QueueAddResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueDetailResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueListResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueRemoveResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueTagsResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueUpdateResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueUsersResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueVO;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IQueueService;
import com.baidu.bce.logic.chpc.tag.UpdateTagsRequest;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: lilu24
 * @Date: 2022-12-07
 */
@Slf4j
@RestController
@RequestMapping("/v1/cluster")
public class QueueController {

    @Resource
    private IQueueService queueService;


    @RequestIdAdapter
    @RequestMapping(value = "/{clusterId}/queue", method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    @Operation(description = "为指定集群新建一个队列")
    @ValidateAuthentication
    @Idempotent
    public QueueAddResponse addQueue(@PathVariable @PermissionResourceID String clusterId,
                                     @Validated @RequestBody QueueAddRequest request) {

        return queueService.addQueue(clusterId, request);

    }

    @RequestIdAdapter
    @RequestMapping(value = "/{clusterId}/queue/{queueName}", method = RequestMethod.DELETE)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.QUEUE_MODIFY}
    )
    @Operation(description = "删除指定集群的一个指定队列")
    @ValidateAuthentication
    @Idempotent
    public QueueRemoveResponse removeQueue(@PathVariable @PermissionResourceID String clusterId,
                                           @PathVariable @PermissionResourceID String queueName,
                                           @RequestParam(required = false) Boolean check,
                                           @RequestParam(required = false) Boolean force) {

        return queueService.removeQueue(clusterId, queueName, check, force);
    }


    @RequestIdAdapter
    @RequestMapping(value = "/{clusterId}/queue/{queueName}", method = RequestMethod.GET)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_READ, PermissionConstant.QUEUE_READONLY}
    )
    @Operation(description = "查询指定集群的一个队列详情")
    @ValidateAuthentication
    public QueueDetailResponse getQueueDetail(@PathVariable("clusterId") @PermissionResourceID String clusterId,
                                              @PathVariable("queueName") @PermissionResourceID String queueName) {

        return queueService.getQueueDetail(clusterId, queueName);

    }

    @RequestIdAdapter
    @RequestMapping(value = "/{clusterId}/queue/{queueName}/userlist", method = RequestMethod.GET)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING,
            permissions = {PermissionConstant.CHPC_READ, PermissionConstant.QUEUE_READONLY}
    )
    @Operation(description = "查询指定集群的一个队列的用户列表")
    @ValidateAuthentication
    public QueueUsersResponse getQueueUsers(@PathVariable("clusterId") @PermissionResourceID String clusterId,
                                            @PathVariable("queueName") @PermissionResourceID String queueName) {
        QueueVO queue = queueService.getQueueUsers(clusterId, queueName);
        QueueUsersResponse response = new QueueUsersResponse();
        response.setQueue(queue);
        return response;
    }

    /**
     * @Description: 查询指定集群的队列列表，支持分页。
     * @Param {String} clusterId 集群ID，必填参数，不能为空字符串。
     * @Param {Integer} pageNo 当前页码，可选参数，默认值为1，取值范围为[1,100]。
     * @Param {Integer} pageSize 每页显示条目数，可选参数，默认值为10，取值范围为[1,100]。
     * @Return {QueueListResponse} 返回一个包含队列列表、总条目数、当前页码、每页显示条目数等信息的QueueListResponse对象。
     * @Throws 无异常抛出。
     */
    @RequestIdAdapter
    @RequestMapping(value = "/{clusterId}/queues", method = RequestMethod.GET)
    @Operation(description = "查询指定集群的队列列表")
    @ValidateAuthentication
    public QueueListResponse listQueues(@PathVariable("clusterId") String clusterId,
                                        @RequestParam(required = false) Boolean hasAutoScale,
                                        @RequestParam(required = false, defaultValue = "1")
                                        @Min(1) Integer pageNo,
                                        @RequestParam(required = false, defaultValue = "10")
                                        @Max(10000) Integer pageSize) {
        List<QueueVO> queues = queueService.listQueues(clusterId).stream()
                .filter(queue -> {
                    if (hasAutoScale == null) {
                        return true; // 当 hasAutoScale 为 null 时，不进行过滤
                    }
                    Boolean realHasAutoScale = queue.getHasAutoScale();
                    return hasAutoScale ? (realHasAutoScale != null && realHasAutoScale) : (realHasAutoScale != null && !realHasAutoScale);
                })
                .collect(Collectors.toList());
        int totalCount = queues.size();

        List<QueueVO> pagedList = queues.stream()
                .skip((long) (pageNo - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());
        QueueListResponse response = new QueueListResponse();
        response.setTotalCount(totalCount);
        response.setQueueList(pagedList);
        response.setPageSize(pageSize);
        response.setPageNo(pageNo);
        return response;

    }

    @RequestIdAdapter
    @RequestMapping(value = "/queue/tags", method = RequestMethod.GET)
    @Operation(description = "查询队列可用标签")
    @ValidateAuthentication
    public QueueTagsResponse getQueueTags() {

        return queueService.listTags();

    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "修改队列标签")
    @RequestMapping(value = "/{clusterId}/queue/{queueName}", method = RequestMethod.PUT)
    public QueueUpdateResponse updateQueueTags(@PathVariable String clusterId,
                                               @PathVariable String queueName,
                                               @Validated @RequestBody UpdateTagsRequest request) {
        QueueUpdateResponse response = queueService.updateQueueTags(clusterId, queueName, request);

        return response;

    }
}
