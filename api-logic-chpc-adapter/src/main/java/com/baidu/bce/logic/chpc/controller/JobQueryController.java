package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.response.job.ListAvailableTagsResponse;
import com.baidu.bce.logic.chpc.model.response.job.ListClusterJobsResponse;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.JobService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;


@Validated
@RestController
@RequestMapping("/v1/cluster")
public class JobQueryController {

    @Autowired
    JobService jobService;

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群中的作业")
    @RequestMapping(value = "/{clusterId}/jobs", method = RequestMethod.GET)
    @PermissionVerify(
        resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_JOB_LIST,
        permissions = {PermissionConstant.CHPC_READ, PermissionConstant.JOB_ALL}
    )
    public ListClusterJobsResponse listClusterJobs( @PathVariable @PermissionResourceID String clusterId,
                                                    @RequestParam(required = false, defaultValue = "1")
                                                    @Min(1) Integer pageNo,
                                                    @RequestParam(required = false, defaultValue = "10")
                                                    @Max(100) Integer pageSize,
                                                    @RequestParam(required = false, defaultValue = "")
                                                    Long timeStart,
                                                    @RequestParam(required = false, defaultValue = "")
                                                    Long timeEnd,
                                                    @RequestParam(required = false, defaultValue = "")
                                                    String keywordType,
                                                    @RequestParam(required = false, defaultValue = "")
                                                    String keyword,
                                                    @RequestParam(required = false, defaultValue = "0")
                                                    Integer stateFilter,
                                                    @RequestParam(required = false, defaultValue = "false")
                                                    Boolean orderDesc) {
        String user = "";
        String queue = "";
        String jobId = "";
        if ("jobId".equals(keywordType)) {
            jobId = keyword.trim();
        } else if ("jobOwner".equals(keywordType)) {
            user = keyword.trim();
        } else if ("queue".equals(keywordType)) {
            queue = keyword.trim();
        }
        ListClusterJobsResponse response = jobService.listClusterJob(clusterId, stateFilter, timeStart, timeEnd, jobId, user,
            queue, orderDesc, pageNo, pageSize);

        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询集群中可用的作业标签")
    @RequestMapping(value = "/{clusterId}/job/tags", method = RequestMethod.GET)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_JOB_LIST,
            permissions = {PermissionConstant.CHPC_READ, PermissionConstant.JOB_ALL}
    )
    public ListAvailableTagsResponse listAvailableTags(@PathVariable @PermissionResourceID String clusterId) {

        ListAvailableTagsResponse response = jobService.listAvailableTags(clusterId);

        return response;
    }

}
