package com.baidu.bce.logic.chpc.controller;

import jakarta.annotation.Resource;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.response.saas.ServiceLoginResponse;
import com.baidu.bce.logic.chpc.service.ISaasService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/v1/saas")
public class SaasController {

    @Resource
    private ISaasService saasService;

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "登录 SaaS 系统")
    @RequestMapping(value = "/service/login", method = RequestMethod.GET)
    public ServiceLoginResponse loginService() {
        return saasService.loginService();
    }
}
