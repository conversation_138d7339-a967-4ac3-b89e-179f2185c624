package com.baidu.bce.logic.chpc.convertor;

import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.SchedulerPriorityRange;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterDetail;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterDetailResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ClusterConvertor {

    /**
     * {@inheritDoc}
     * 将原始的ClusterDetailResponse对象转换为ClusterDetail对象，包括ClusterInfo、BccInfo等信息。
     *
     * @param origin 原始的ClusterDetailResponse对象
     * @return 转换后的ClusterDetail对象
     * @throws Exception 如果发生任何异常，则抛出该异常
     */
    public ClusterDetail convert(ClusterDetailResponse origin) {
        ClusterDetail clusterDetail = new ClusterDetail();
        ClusterDetail.ClusterInfo clusterInfo = new ClusterDetail.ClusterInfo();

        clusterInfo.setClusterId(origin.getClusterId());
        clusterInfo.setClusterName(origin.getClusterName());
        clusterInfo.setDescription(origin.getDescription());
        clusterInfo.setCreatedTime(origin.getCreatedTime());
        clusterInfo.setUpdatedTime(origin.getUpdatedTime());

        clusterInfo.setZoneName(origin.getLogicalZone());
        clusterInfo.setStatus(origin.getStatus());
        clusterInfo.setErrorMessage(origin.getErrorMessage());
        clusterInfo.setChargeType(origin.getChargeType());
        clusterInfo.setImageId(origin.getImageId());

        clusterInfo.setVpcId(origin.getVpcId());

        clusterInfo.setVpcName(origin.getVpcName());

        clusterInfo.setVpcCidr(origin.getVpcCidr());

        clusterInfo.setSubnetsName(origin.getSubnetsName());
        clusterInfo.setSubnetsCidr(origin.getSubnetsCidr());

        clusterInfo.setSecurityGroupName(origin.getSecurityGroupName());
        clusterInfo.setSecurityGroupId(origin.getSecurityGroupId());
        clusterInfo.setSubnetId(origin.getSubnetId());

        clusterInfo.setSchedulerType(origin.getSchedulerType());

        clusterInfo.setSchedulerVersion(origin.getSchedulerVersion());

        clusterInfo.setCfsInfo(origin.getCfsList());
        clusterInfo.setQueueInfo(origin.getQueueList());

        clusterInfo.setClusterType(origin.getClusterType());

        clusterInfo
                .setJobPriorityRange(SchedulerPriorityRange.schedulerPriorityRangeMap.get(origin.getSchedulerType()));

        clusterInfo.setRunningNodeNum(origin.getRunningNodeNum());
        clusterInfo.setTotalNodeNum(origin.getTotalNodeNum());
        clusterInfo.setErrorNodeNum(origin.getErrorNodeNum());
        clusterInfo.setCpuUsed12h(origin.getCpuUsed12h());
        clusterInfo.setSoftwareList(origin.getSoftwareList());
        clusterInfo.setUserList(origin.getUserList());
        clusterInfo.setMaxCpus(origin.getMaxCpus());
        clusterInfo.setMaxNodes(origin.getMaxNodes());
        clusterInfo.setDomainAccount(origin.getDomainAccount());
        clusterInfo.setForbidDelete(origin.getForbidDelete());

        Map<String, List<InstanceVO>> nodeType2Instance = origin.getInstanceList().stream()
                .collect(Collectors.groupingBy(InstanceVO::getNodeType));

        List<ClusterDetail.BccInfo> bccInfoList = new ArrayList<>();
        // 在返回值信息中新增管理节点和登陆节点的信息
        clusterInfo.setManagerNode(origin.getManagerNode());
        clusterInfo.setLoginNode(origin.getLoginNode());
        for (Map.Entry<String, List<InstanceVO>> entry : nodeType2Instance.entrySet()) {
            String nodeType = entry.getKey();

            Map<String, List<InstanceVO>> spec2Instances = entry.getValue().stream()
                    .collect(Collectors.groupingBy(InstanceVO::getSpec));
            List<ClusterDetail.SpecCount> specCountList = new ArrayList<>();

            for (Map.Entry<String, List<InstanceVO>> spec2Instance : spec2Instances.entrySet()) {
                specCountList.add(new ClusterDetail.SpecCount(spec2Instance.getKey(), spec2Instance.getValue().size()));
            }

            bccInfoList.add(new ClusterDetail.BccInfo(nodeType, specCountList));
        }

        clusterInfo.setBccInfo(bccInfoList);
        clusterInfo.setTags(origin.getTags());
        clusterInfo.setSchedulerIp(origin.getSchedulerIp());
        clusterInfo.setSchedulerHost(origin.getSchedulerHost());
        clusterInfo.setCreatedDone(origin.getCreatedDone());
        clusterDetail.setCluster(clusterInfo);

        return clusterDetail;
    }
}
