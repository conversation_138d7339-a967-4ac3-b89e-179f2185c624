package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.model.response.os.OsListResponse;
import com.baidu.bce.logic.chpc.os.OsListRequest;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.IOsService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import jakarta.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/v1/os")
public class OsController {
    @Resource
    private IOsService osService;


    @RequestIdAdapter
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID,
            permissions = {PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY}
    )
    @Operation(description = "调度器查询可用的操作系统版本")
    @ValidateAuthentication
    @Idempotent
    public OsListResponse osList(@Validated @RequestBody OsListRequest request) {

        return osService.osList(request);

    }
}
