package com.baidu.bce.logic.chpc.controller;

import jakarta.annotation.Resource;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.model.request.billing.OrderExecutorRequest;
import com.baidu.bce.logic.chpc.model.response.billing.OrderExecutorResponse;
import com.baidu.bce.logic.chpc.model.response.billing.OrderInfoResponse;
import com.baidu.bce.logic.chpc.service.IBillingService;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/v1/order_executor")
public class OrderExecutorController {

    @Resource
    private IBillingService billingService;

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "执行订单，开始创建资源")
    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public OrderExecutorResponse executeOrder(@RequestBody OrderExecutorRequest orderExecutorRequest) {
        return billingService.execute(orderExecutorRequest.getOrderId());
    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "查询订单是否执行完成")
    @RequestMapping(value = "/check", method = RequestMethod.GET)
    public OrderExecutorResponse checkOrder(@RequestParam String orderId) {
        return billingService.check(orderId);
    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "查询订单信息")
    @RequestMapping(value = "/query/{orderId}", method = RequestMethod.GET)
    public OrderInfoResponse queryOrder(@PathVariable("orderId") String orderId) {
        return billingService.queryOrder(orderId);
    }

    @Idempotent
    @RequestIdAdapter
    @Operation(description = "取消订单")
    @RequestMapping(value = "/terminate", method = RequestMethod.PUT)
    public OrderExecutorResponse terminateOrder(@RequestParam("orderId") String orderId,
            @RequestParam("status") String status,
            @RequestParam("serviceType") String serviceType) {
        return billingService.terminate(orderId, status, serviceType);
    }

}
