package com.baidu.bce.logic.chpc.controller;

import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.job.model.SubmitJobRequest;
import com.baidu.bce.logic.chpc.model.response.job.CommonHelixJobResponse;
import com.baidu.bce.logic.chpc.service.JobService;
import com.baidu.bce.logic.chpc.service.UserService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequestMapping("/v1/helix")
public class HelixController {
    
    @Value("${helix.helixfold3.cluster}")
    private String helixfold3Cluster;

    @Autowired
    JobService jobService;

    @Autowired
    UserService userService;

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "helix提交作业")
    @RequestMapping(value = "/job", method = RequestMethod.POST)
    public CommonHelixJobResponse submitJob(@RequestBody SubmitJobRequest request) {
        CommonHelixJobResponse response = jobService.submitHelixJob(request);
        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "helix取消作业")
    @RequestMapping(value = "/job/{taskId}", method = RequestMethod.PUT)
    public CommonHelixJobResponse cancelJob(@PathVariable("taskId") String taskId,
                                            @RequestParam(value = "action", required = true) String action) {
        CommonHelixJobResponse response = jobService.cancelHelixJob(taskId, action);
        return response;
    }

    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "helix更改用户qos")
    @RequestMapping(value = "/user/qos", method = RequestMethod.PUT)
    public BaseResponse changeUserPriority(@RequestParam(value = "qos", required = true) String qos) {
        BaseResponse response = userService.changeDomainUserQos(helixfold3Cluster, LogicUserService.getUserId(), qos);
        return response;
    }
}
