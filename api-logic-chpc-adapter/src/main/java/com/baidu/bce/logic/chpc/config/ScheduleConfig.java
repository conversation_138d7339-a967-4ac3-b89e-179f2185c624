package com.baidu.bce.logic.chpc.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.task.TaskSchedulerBuilder;
import org.springframework.boot.task.TaskSchedulerCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration(proxyBeanMethods = false)
@EnableScheduling
public class ScheduleConfig implements SchedulingConfigurer {

    private TaskSchedulerBuilder taskSchedulerBuilder;

    @Autowired
    public void setTaskSchedulerBuilder(TaskSchedulerBuilder taskSchedulerBuilder) {
        this.taskSchedulerBuilder = taskSchedulerBuilder;
    }

    @Override
    public void configureTasks(ScheduledTaskRegistrar scheduledTaskRegistrar) {
        ThreadPoolTaskScheduler taskScheduler = taskSchedulerBuilder.build();
        taskScheduler.initialize();
        scheduledTaskRegistrar.setScheduler(taskScheduler);
    }

}