package com.baidu.bce.logic.chpc.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.annotation.RequestIdAdapter;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IStorageService;
import com.baidu.bce.logic.chpc.storage.model.BatchUpdateStorageRequest;
import com.baidu.bce.logic.chpc.storage.model.BatchUpdateStorageResponse;
import com.baidu.bce.logic.chpc.storage.model.StorageQueryResponse;
import com.baidu.bce.logic.chpc.storage.model.UpdateStorageRequest;
import com.baidu.bce.logic.core.idempotent.annotation.Idempotent;

import io.swagger.v3.oas.annotations.Operation;

@RestController
@RequestMapping("/v1")
public class StorageController {

    @Autowired
    IStorageService storageService;

    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "查询存储列表")
    @RequestMapping(value = "/cluster/{clusterId}/storages", method = RequestMethod.GET)
    @PermissionVerify(resourceLocation = PermissionVerify.ResourceLocation.IN_STRING, permissions = {
            PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY })
    public StorageQueryResponse getStorageList(@PathVariable @PermissionResourceID String clusterId) {

        return storageService.getStorageList(clusterId);

    }

    /**
     * {@literal}
     * 挂载存储。
     * 该接口需要经过身份验证，并携带有效的权限令牌。返回值为UpdateStorageResponse类型，包含更新结果。
     * 当前操作需要管理员（OP）或者集群管理员（CLUSTER_MODIFY）权限。
     *
     * @param clusterId 集群ID，需要经过身份验证和权限控制，格式为字符串，不能为空
     * @param request   更新请求，包含多个存储信息，需要通过@Validated进行校验，不能为null
     * @return UpdateStorageResponse 更新结果，包含更新状态码、消息和错误码等信息
     * @throws PermissionDeniedException 如果调用者没有相应的权限，将抛出此异常
     * @since 2022-03-16
     */
    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "挂载存储")
    @RequestMapping(value = "/cluster/{clusterId}/storage", method = RequestMethod.POST)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING, permissions = {
                    PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY })
    public void createStorage(@PathVariable @PermissionResourceID String clusterId,
            @Validated @RequestBody UpdateStorageRequest request) {

        storageService.updateStorage(clusterId, request, "create");

    }

    /**
     * {@literal}
     * 挂载存储。
     * 该接口需要经过身份验证，并携带有效的权限令牌。返回值为UpdateStorageResponse类型，包含更新结果。
     * 当前操作需要管理员（OP）或者集群管理员（CLUSTER_MODIFY）权限。
     *
     * @param clusterId 集群ID，需要经过身份验证和权限控制，格式为字符串，不能为空
     * @param request   更新请求，包含多个存储信息，需要通过@Validated进行校验，不能为null
     * @return UpdateStorageResponse 更新结果，包含更新状态码、消息和错误码等信息
     * @throws PermissionDeniedException 如果调用者没有相应的权限，将抛出此异常
     * @since 2022-03-16
     */
    @Idempotent
    @RequestIdAdapter
    @ValidateAuthentication
    @Operation(description = "修改存储")
    @RequestMapping(value = "/cluster/{clusterId}/batch/storage", method = RequestMethod.POST)
    @PermissionVerify(
            // TODO: OP
            resourceLocation = PermissionVerify.ResourceLocation.IN_STRING, permissions = {
                    PermissionConstant.CHPC_OPERATE, PermissionConstant.CLUSTER_MODIFY })
    public BatchUpdateStorageResponse updateStorage(@PathVariable @PermissionResourceID String clusterId,
            @RequestParam(required = true, defaultValue = "") String action,
            @Validated @RequestBody BatchUpdateStorageRequest request) {

        return storageService.batchUpdateStorage(clusterId, request, action);

    }
}
