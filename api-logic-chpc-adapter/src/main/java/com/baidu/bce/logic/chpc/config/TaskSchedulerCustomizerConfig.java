package com.baidu.bce.logic.chpc.config;

import org.springframework.boot.task.ThreadPoolTaskSchedulerCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.ThreadPoolExecutor;

@Configuration(proxyBeanMethods = false)
public class TaskSchedulerCustomizerConfig {

    @Bean
    public ThreadPoolTaskSchedulerCustomizer taskSchedulerCustomizer() {
        return taskScheduler ->
                taskScheduler.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    }

}
