package com.baidu.bce.logic.chpc.config;

import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest;
import org.springframework.boot.autoconfigure.security.SecurityProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.util.ClassUtils;

import static org.springframework.security.config.Customizer.withDefaults;

@Configuration(proxyBeanMethods = false)
public class ActuatorSecurity {

    @Bean
    @Order(SecurityProperties.BASIC_AUTH_ORDER)
    public SecurityFilterChain managementSecurityFilterChain(HttpSecurity http) throws Exception {
        // 确保所有端点都具有ENDPOINT_ADMIN角色,针对所有Actuator路径做鉴权处理
        // 如果只鉴权特定的endpoint 可以调整matcher和 EndpointRequest
        http.securityMatcher(EndpointRequest.toAnyEndpoint());
        http.authorizeHttpRequests((requests) -> {
            requests.requestMatchers(EndpointRequest.to("prometheus", "info", "health")).permitAll();
            requests.requestMatchers(EndpointRequest.toAnyEndpoint().excluding("prometheus", "info", "health"))
                    .hasRole("ENDPOINT_ADMIN");
            requests.anyRequest().hasRole("ENDPOINT_ADMIN");
        });
        if (ClassUtils.isPresent("org.springframework.web.servlet.DispatcherServlet", null)) {
            http.cors(withDefaults());
        }
        http.formLogin(withDefaults());
        // 禁用csrf 否则无法通过curl执行发密码
        http.httpBasic(withDefaults()).csrf(AbstractHttpConfigurer::disable);
        return http.build();
    }
}
