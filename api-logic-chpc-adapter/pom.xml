<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baidu.bce</groupId>
        <artifactId>api-logic-chpc-parent</artifactId>
        <version>${api-logic-chpc-version}</version>
    </parent>

    <artifactId>api-logic-chpc-adapter</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>api-logic-chpc-app</artifactId>
        </dependency>
         <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-spring</artifactId>
        </dependency>
        <dependency>
            <groupId>net.javacrumbs.shedlock</groupId>
            <artifactId>shedlock-provider-redis-spring</artifactId>
        </dependency>
    </dependencies>

</project>