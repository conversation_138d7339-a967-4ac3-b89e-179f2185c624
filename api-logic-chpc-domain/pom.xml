<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baidu.bce</groupId>
        <artifactId>api-logic-chpc-parent</artifactId>
        <version>${api-logic-chpc-version}</version>
    </parent>

    <artifactId>api-logic-chpc-domain</artifactId>


    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-internal-sdk-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>api-logic-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-internal-sdk-bcc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-internal-sdk-cos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-internal-sdk-cfs</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-internal-sdk-oos</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-plat-servicecatalog-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidubce</groupId>
            <artifactId>bce-java-sdk</artifactId>
            <version>0.10.315</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-reload4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>jdk.tools</groupId>
            <artifactId>jdk.tools</artifactId>
            <version>1.6</version>
        </dependency>
        <dependency>
            <groupId>io.projectreactor</groupId>
            <artifactId>reactor-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce</groupId>
            <artifactId>bce-logical-tag-sdk</artifactId>
            <version>1.0.240.1</version>
            <exclusions>
                <exclusion>
                    <groupId>com.baidu.bce</groupId>
                    <artifactId>bce-internal-sdk-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


</project>