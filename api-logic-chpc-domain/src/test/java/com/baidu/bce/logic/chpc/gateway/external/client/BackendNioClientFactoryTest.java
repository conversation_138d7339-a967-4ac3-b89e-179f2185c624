package com.baidu.bce.logic.chpc.gateway.external.client;

import static org.mockito.Mockito.*;

import com.baidu.bce.logic.core.webclient.SignByStsExchangeFilter;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Map;

public class BackendNioClientFactoryTest {

    @Mock
    private RegionConfiguration regionConfiguration; // mock regionConfiguration

    @Mock
    private Map<String, SignByStsExchangeFilter> signByStsExchangeFilterMap; // mock exchange filter map

    @InjectMocks
    private BackendNioClientFactory backendNioClientFactory; // 被测试的对象

    @BeforeEach
    void setUp() {
        // 初始化 Mockito 注解的 mock 对象
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateClientNewClient() {
        String endpoint = "http://example.com";
        String accountId = "account123";

        // 模拟 RegionConfiguration 的行为
        when(regionConfiguration.getCurrentRegion()).thenReturn("cn-bj");

        // 模拟 SignByStsExchangeFilter 的行为
        when(signByStsExchangeFilterMap.get(any())).thenReturn(mock(SignByStsExchangeFilter.class));
        backendNioClientFactory.regionConfiguration = regionConfiguration;
        // 创建客户端
        try {
            BackendNioClient client = backendNioClientFactory.createClient(endpoint, accountId);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
