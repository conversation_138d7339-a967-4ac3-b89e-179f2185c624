package com.baidu.bce.logic.chpc.utils;

import com.baidu.bce.internalsdk.iam.model.Token;

/**
 * <AUTHOR>
 * @Date 2023/9/14
 */
public class TestUtil {

    public static Token.TokenResult buildToken() {
        Token.TokenResult.User user = new Token.TokenResult.User();
        Token.TokenResult.User.Domain domain = new Token.TokenResult.User.Domain();
        domain.setId("domainId");
        user.setDomain(domain);
        Token.TokenResult tokenResult = new Token.TokenResult();
        tokenResult.setUser(user);

        return tokenResult;
    }

}
