package com.baidu.bce.logic.chpc.cluster;

import com.baidu.bce.logic.chpc.cfs.CfsAddRequest;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.chpc.cluster.domainservice.ClusterCreateRequestValidatorFactory;
import com.baidu.bce.logic.chpc.cluster.domainservice.IClusterCreateRequestValidator;
import com.baidu.bce.logic.chpc.cluster.domainservice.MultiQueueAndNodeClusterCreateRequestValidator;
import com.baidu.bce.logic.chpc.cluster.model.IClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.MultiQueueAndNodeClusterCreateRequest;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.WhitelistGateway;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.queue.QueueAddRequest;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.chpc.utils.TestUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.function.Executable;
import org.mockito.Mockito;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * <AUTHOR>
 * @Date 2023/9/14
 */
public class MultiQueueAndNodeClusterCreateRequestValidatorTest {

    @Configuration
    public static class InnerBeanConfigure {
        @Bean(name = "multiQueueAndNodeClusterCreateRequestValidator")
        public MultiQueueAndNodeClusterCreateRequestValidator multiQueueAndNodeClusterCreateRequestValidator() {
            return new MultiQueueAndNodeClusterCreateRequestValidator();
        }


        @Bean
        public CfsGateway cfsGateway() {
            return Mockito.mock(CfsGateway.class);
        }

        @Bean
        public SubnetGateway subnetGateway() {
            return Mockito.mock(SubnetGateway.class);
        }

        @Bean
        public ClusterDAOGateway clusterDAOGateway() {
            return Mockito.mock(ClusterDAOGateway.class);
        }

        @Bean
        public WhitelistGateway whitelistGateway() {
            return Mockito.mock(WhitelistGateway.class);
        }

    }

    static AnnotationConfigApplicationContext applicationContext = new AnnotationConfigApplicationContext();

    @BeforeAll
    static void setUp() {
        // 注册 Configuration Clas (配置类）
        applicationContext.register(InnerBeanConfigure.class);
        applicationContext.refresh();
    }

    @Test
    public void testValidatorNotNull() {
        IClusterCreateRequest request = new MultiQueueAndNodeClusterCreateRequest();
        IClusterCreateRequestValidator validator = ClusterCreateRequestValidatorFactory.getValidator(request);
        assertEquals(MultiQueueAndNodeClusterCreateRequestValidator.class, validator.getClass());
    }

    @Test
    public void testValidatorIsNull() {
        IClusterCreateRequest request = new IClusterCreateRequest() {
            @Override
            public String getClusterName() {
                return null;
            }

            @Override
            public String getDescription() {
                return null;
            }

            @Override
            public String getDefaultQueueName() {
                return null;
            }

            @Override
            public String getSchedulerType() {
                return null;
            }

            @Override
            public String getChargeType() {
                return null;
            }

            @Override
            public int getPeriod() {
                return 0;
            }

            @Override
            public String getPeriodUnit() {
                return null;
            }

            @Override
            public boolean isAutoRenew() {
                return false;
            }

            @Override
            public Integer getAutoRenewPeriod() {
                return null;
            }

            @Override
            public String getAutoRenewPeriodUnit() {
                return null;
            }

            @Override
            public String getZoneName() {
                return null;
            }

            @Override
            public boolean isEnableHa() {
                return false;
            }

            @Override
            public InstanceAddRequest getManagerNode() {
                return null;
            }

            @Override
            public List<CfsAddRequest> getCfsVolumes() {
                return null;
            }

            @Override
            public List<QueueAddRequest> getQueues() {
                return null;
            }

            @Override
            public String getVpcId() {
                return null;
            }

            @Override
            public String getSecurityGroupId() {
                return null;
            }
        };
        IClusterCreateRequestValidator validator = ClusterCreateRequestValidatorFactory.getValidator(request);
        assertNull(validator);
    }

    @Test
    public void testClusterCreateRequestValidatorPass() {
        MultiQueueAndNodeClusterCreateRequest request = ClusterCreateRequestBuilder.buildNormal();
        IClusterCreateRequestValidator validator = ClusterCreateRequestValidatorFactory.getValidator(request);
        LogicUserService.setSubjectToken(TestUtil.buildToken());
        assertDoesNotThrow(new Executable() {
            @Override
            public void execute() throws Throwable {
                validator.valid(request);
            }
        });

    }

    @Test
    public void testClusterCreateRequestValidatorNotPass() {
        MultiQueueAndNodeClusterCreateRequest request = ClusterCreateRequestBuilder.buildNormal(700);
        IClusterCreateRequestValidator validator = ClusterCreateRequestValidatorFactory.getValidator(request);
        LogicUserService.setSubjectToken(TestUtil.buildToken());
        Exception exception = assertThrows(CommonException.QuotaException.class, () -> validator.valid(request));
        String expectedMessage = "Exceed compute node quota";
        String actualMessage = exception.getMessage();

        assertTrue(actualMessage.contains(expectedMessage));


        final MultiQueueAndNodeClusterCreateRequest request2 = ClusterCreateRequestBuilder.buildMultiDefaultQueue(2);
        exception = assertThrows(CommonExceptions.RequestInvalidException.class, () -> validator.valid(request2));
        expectedMessage = "too many default queue";
        actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage));

        final MultiQueueAndNodeClusterCreateRequest request3 = ClusterCreateRequestBuilder.buildNormal();
        request3.setQueues(null);
        exception = assertThrows(CommonExceptions.RequestInvalidException.class, () -> validator.valid(request3));
        expectedMessage = "queues is empty";
        actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage));

        final MultiQueueAndNodeClusterCreateRequest request4 = ClusterCreateRequestBuilder.buildNormal();
        request4.getQueues().get(0).setDefault(false);
        exception = assertThrows(CommonExceptions.RequestInvalidException.class, () -> validator.valid(request4));
        expectedMessage = "no default queue";
        actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage), actualMessage);

        final MultiQueueAndNodeClusterCreateRequest request5 = ClusterCreateRequestBuilder.buildNormal();
        request5.getQueues().get(0).getComputeNodes().get(0).setImageId(null);
        exception = assertThrows(CommonExceptions.RequestInvalidException.class, () -> validator.valid(request5));
        expectedMessage = "compute spec or imageId is empty";
        actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage), actualMessage);

        final MultiQueueAndNodeClusterCreateRequest request6 = ClusterCreateRequestBuilder.buildNormal();
        request6.getQueues().get(0).getComputeNodes().get(0).setSpec(null);
        exception = assertThrows(CommonExceptions.RequestInvalidException.class, () -> validator.valid(request6));
        expectedMessage = "compute spec or imageId is empty";
        actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage), actualMessage);

        final MultiQueueAndNodeClusterCreateRequest request7 = ClusterCreateRequestBuilder.buildNormal();
        request7.getQueues().get(0).getComputeNodes().get(0).setZoneName("hah");
        exception = assertThrows(CommonExceptions.RequestInvalidException.class, () -> validator.valid(request7));
        expectedMessage = "zoneName";
        actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage), actualMessage);

        final MultiQueueAndNodeClusterCreateRequest request8 = ClusterCreateRequestBuilder.buildNormal();
        request8.getQueues().get(0).getComputeNodes().get(0).setZoneName("");
        exception = assertThrows(CommonExceptions.RequestInvalidException.class, () -> validator.valid(request8));
        expectedMessage = "zoneName";
        actualMessage = exception.getMessage();
        assertTrue(actualMessage.contains(expectedMessage), actualMessage);

    }


}
