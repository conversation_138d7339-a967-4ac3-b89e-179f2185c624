package com.baidu.bce.logic.chpc.cluster.domainservice;

import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.chpc.cluster.domainservice.ClusterServiceV2;
import com.baidu.bce.logic.chpc.cluster.model.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.cluster.model.NodeInfo;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.GlobalUuidUtil;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.common.charge.EipChargeType;
import com.baidu.bce.logic.chpc.common.charge.PeriodUnit;
import com.baidu.bce.logic.chpc.common.validator.AesDecryptUtil;
import com.baidu.bce.logic.chpc.cos.gateway.CosGateway;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterEventDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareDAOGateway;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.WhitelistGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag;
import com.baidu.bce.logic.chpc.iam.IamGateway;
import com.baidu.bce.logic.chpc.securitygroup.gateway.SecurityGroupGateway;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.chpc.user.Const;
import com.baidu.bce.logic.chpc.vpc.gateway.VpcGateway;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class ClusterServiceV2Test {

    @InjectMocks
    private ClusterServiceV2 clusterServiceV2;

    @Mock
    private IamGateway iamGateway;

    @Mock
    private VpcGateway vpcGateway;

    @Mock
    private SoftwareDAOGateway softwareDAOGateway;

    @Mock
    private SecurityGroupGateway securityGroupGateway;

    @Mock
    private BccGateway bccGateway;

    @Mock
    private ClusterGenerator clusterGenerator;

    @Mock
    private SubnetGateway subnetGateway;

    @Mock
    private ClusterEventDAOGateway clusterEventDAOGateway;

    @Mock
    private InstanceDAOGateway instanceDAOGateway;

    @Mock
    private BackendGateway backendGateway;

    @Mock
    private TaskService taskService;

    @Mock
    private GlobalUuidUtil globalUuidUtil;

    @Mock
    private WhitelistGateway whitelistGateway;

    @Mock
    private QueueDAOGateway queueDAOGateway;

    @Mock
    private TagsDAOGateway tagsDAOGateway;

    @Mock
    private ClusterDAOGateway clusterDAOGateway;

    @Mock
    private CfsDAOGateway cfsDAOGateway;

    @Mock
    private CfsGateway cfsGateway;

    @Mock
    private CosGateway cosGateway;

    @BeforeEach
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testGenerateTemplate() {
        // Arrange
        NodeInfo nodeSpec = new NodeInfo();
        nodeSpec.setCount(1);
        nodeSpec.setSpec("testSpec");
        nodeSpec.setSubnetId("testSubnetId");
        DiskInfo diskInfo = new DiskInfo();
        diskInfo.setSize(100);
        diskInfo.setStorageType("premium_ssd");
        nodeSpec.setSystemDisk(diskInfo);
        nodeSpec.setImageId("testImageId");
        nodeSpec.setChargeType(ChargeType.Postpaid.name());
        nodeSpec.setGpuDriverVersion("testGpuDriverVersion");
        nodeSpec.setCudaVersion("testCudaVersion");
        nodeSpec.setCudnnVersion("testCudnnVersion");
        nodeSpec.setCpuThreadConfig("2");
        nodeSpec.setNumaConfig("1");

        ClusterCreateRequest request = new ClusterCreateRequest();
        request.setZoneName("testZoneName");
        request.setSecurityGroupId("testSecurityGroupId");
        request.setSecurityGroupType(ChpcConstant.SECURITY_GROUP_TYPE_NORMAL);


        String nodeType = InstanceNodeType.COMPUTE.getType();
        int offsetIdx = 0;
        String queueName = "testQueueName";
        String queueId = "testQueueId";


        // Act
        Map<String, Object> result = clusterServiceV2.generateTemplate(nodeSpec, nodeType, request, offsetIdx, queueName, queueId);

        // Assert
        assertEquals(1, result.size());
        Map<String, Object> resource = (Map<String, Object>) result.get("bcc_compute_1");
        assertEquals("BCE::BCC::Instance", resource.get("type"));
        assertEquals(1, resource.get("count"));

        Map<String, Object> properties = (Map<String, Object>) resource.get("properties");
        assertEquals("testSpec", properties.get("spec"));
        assertEquals("testZoneName", properties.get("zoneName"));
        assertEquals("testSubnetId", properties.get("subnetId"));
        assertEquals(100, properties.get("rootDiskSizeInGb"));
        assertEquals("cloud_hp1", properties.get("rootDiskStorageType"));
        assertEquals("testImageId", properties.get("imageId"));
        List<Tag> tags = (List<Tag>) properties.get("tags");
        Map<String, String> tagKeyToVal = tags.stream().collect(Collectors.toMap(Tag::getTagKey, Tag::getTagValue));
        assertEquals("testGpuDriverVersion", tagKeyToVal.get("gpuDriverVersion"));
        assertEquals("testCudaVersion", tagKeyToVal.get("cudaVersion"));
        assertEquals("testCudnnVersion", tagKeyToVal.get("cudnnVersion"));
    }
}