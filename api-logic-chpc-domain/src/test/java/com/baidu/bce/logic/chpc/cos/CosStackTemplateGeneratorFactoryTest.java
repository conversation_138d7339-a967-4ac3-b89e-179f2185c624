package com.baidu.bce.logic.chpc.cos;

import com.baidu.bce.logic.chpc.cfs.CfsAddRequest;
import com.baidu.bce.logic.chpc.cluster.ClusterCreateRequestBuilder;
import com.baidu.bce.logic.chpc.cluster.model.IClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.MultiQueueAndNodeClusterCreateRequest;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.common.charge.PeriodUnit;
import com.baidu.bce.logic.chpc.common.holder.AccessKeyThreadHolder;
import com.baidu.bce.logic.chpc.cos.service.CosStackTemplateGeneratorFactory;
import com.baidu.bce.logic.chpc.cos.service.ICosStackTemplateGenerator;
import com.baidu.bce.logic.chpc.cos.service.MultiQueueAndNodeCosStackTemplateGenerator;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.queue.QueueAddRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;


/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
public class CosStackTemplateGeneratorFactoryTest {

    @Configuration
    public static class InnerBeanConfigure {

        @Bean(name = "multiQueueAndNodeCosStackTemplateGenerator")
        public MultiQueueAndNodeCosStackTemplateGenerator MultiQueueAndNodeCosStackTemplateGenerator() {
            return new MultiQueueAndNodeCosStackTemplateGenerator();
        }
    }

    static AnnotationConfigApplicationContext applicationContext = new AnnotationConfigApplicationContext();

    @BeforeAll
    static void setUp() {
        // 注册 Configuration Clas (配置类）
        applicationContext.register(InnerBeanConfigure.class);
        applicationContext.refresh();

    }

    @Test
    public void testGeneratorNotNull() {
        IClusterCreateRequest request = new MultiQueueAndNodeClusterCreateRequest();
        ICosStackTemplateGenerator generator = CosStackTemplateGeneratorFactory.getCosStackTemplateGenerator(request);
        assertEquals(MultiQueueAndNodeCosStackTemplateGenerator.class, generator.getClass());
    }

    @Test
    public void testGeneratorIsNull() {
        IClusterCreateRequest request = new IClusterCreateRequest() {
            @Override
            public String getClusterName() {
                return null;
            }

            @Override
            public String getDescription() {
                return null;
            }

            @Override
            public String getDefaultQueueName() {
                return null;
            }

            @Override
            public String getSchedulerType() {
                return null;
            }

            @Override
            public String getChargeType() {
                return null;
            }

            @Override
            public int getPeriod() {
                return 0;
            }

            @Override
            public String getPeriodUnit() {
                return null;
            }

            @Override
            public boolean isAutoRenew() {
                return false;
            }

            @Override
            public Integer getAutoRenewPeriod() {
                return null;
            }

            @Override
            public String getAutoRenewPeriodUnit() {
                return null;
            }

            @Override
            public String getZoneName() {
                return null;
            }

            @Override
            public boolean isEnableHa() {
                return false;
            }

            @Override
            public InstanceAddRequest getManagerNode() {
                return null;
            }

            @Override
            public List<CfsAddRequest> getCfsVolumes() {
                return null;
            }

            @Override
            public List<QueueAddRequest> getQueues() {
                return null;
            }

            @Override
            public String getVpcId() {
                return null;
            }

            @Override
            public String getSecurityGroupId() {
                return null;
            }
        };
        ICosStackTemplateGenerator generator = CosStackTemplateGeneratorFactory.getCosStackTemplateGenerator(request);
        assertNull(generator);
    }

    @Test
    public void testNormalRequest() {

        AccessKeyThreadHolder.setAccessKey("mockak");

        MultiQueueAndNodeClusterCreateRequest request = ClusterCreateRequestBuilder.buildNormal();
        assertEquals(1, countManagerInstanceNum(request), "manager node num must be 1");
        assertEquals(1, countDefaultQueueNum(request), "default queue num must be 1");
        ICosStackTemplateGenerator generator = CosStackTemplateGeneratorFactory.getCosStackTemplateGenerator(request);
        CreateStackRequest.Template template = generator.generate(request);
        try {
            JSONObject json = new JSONObject(template.getJson());
            JSONObject resources = json.getJSONObject("resources");

            checkProperties(request, resources);

        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    private void checkProperties(IClusterCreateRequest request, JSONObject resources) throws JSONException {
        int instanceNum = countManagerInstanceNum(request) + countComputerInstanceNum(request);
        assertEquals(resources.names().length(), instanceNum
                , String.format("cos instance num should equal to request,but cos:%d != request:%d"
                        , resources.names().length(), instanceNum
                )
        );
        String key = resources.names().getString(0);
        assertEquals(key, "bcc_" + InstanceNodeType.MASTER.getType() + "_0");
        assertInstanceMatchCos(
                InstanceNodeType.MASTER,
                request,
                request.getManagerNode(),
                resources.getJSONObject(key)
        );
        int count = 1;
        for (QueueAddRequest queue : request.getQueues()) {
            if (CollectionUtils.isEmpty(queue.getComputeNodes())) {
                continue;
            }
            for (InstanceAddRequest instance : queue.getComputeNodes()) {
                String expectedKey = "bcc_" + InstanceNodeType.COMPUTE.getType() + "_" + count;
                assertTrue(resources.has(expectedKey));
                assertInstanceMatchCos(
                        InstanceNodeType.COMPUTE,
                        request,
                        instance,
                        resources.getJSONObject(expectedKey)
                );
            }

        }
    }

    private void assertInstanceMatchCos(InstanceNodeType nodeType, IClusterCreateRequest request, InstanceAddRequest instance, JSONObject node) throws JSONException {

        assertEquals("BCE::BCC::CreateInstanceBySpec", node.optString("type"));
        JSONObject properties = node.getJSONObject("properties");
        assertEquals(properties.optString("subnetId"), instance.getSubnetId());
        assertEquals(properties.optString("securityGroupId"), request.getSecurityGroupId());
        assertEquals(
                properties.optString("imageId"),
                instance.getImageId(),
                String.format("imageId not match, cos:%s != ins:%s",
                        properties.optString("imageId"),
                        instance.getImageId())
        );

        assertEquals(
                properties.optString("rootDiskStorageType"),
                instance.getSystemDiskType(),
                String.format("system disk type not match, cos:%s != ins:%s",
                        properties.optString("rootDiskStorageType"),
                        instance.getSystemDiskType())
        );

        assertEquals(
                properties.optInt("rootDiskSizeInGb"),
                instance.getSystemDiskSize(),
                String.format("system disk size not match, cos:%d != ins:%d",
                        properties.optInt("rootDiskSizeInGb"),
                        instance.getSystemDiskSize())
        );

        assertEquals(properties.getInt("purchaseCount"), 1);

        assertEquals(properties.optString("paymentTiming"), request.getChargeType());
        assertEquals(
                properties.optString("reservationTimeUnit", null),
                ChargeType.Prepaid.name().equalsIgnoreCase(request.getChargeType()) ? PeriodUnit.Month.name() : null
        );
        assertEquals(
                properties.optInt("reservationLength"),
                ChargeType.Prepaid.name().equalsIgnoreCase(request.getChargeType()) ?
                        (PeriodUnit.Year.name().equalsIgnoreCase(request.getPeriodUnit()) ?
                                request.getPeriod() * 12 :
                                request.getPeriod()) :
                        0

        );
        assertEquals(properties.optBoolean("cdsAutoRenew"), request.isAutoRenew());
        assertEquals(properties.optInt("autoRenewTime"), request.isAutoRenew() ? request.getAutoRenewPeriod() : 0);
        assertEquals(
                properties.optString("autoRenewTimeUnit", null),
                request.isAutoRenew() ?
                        request.getAutoRenewPeriodUnit() :
                        null
        );
        assertEquals(properties.optString("zoneName"), instance.getZoneName());
        assertEquals(properties.optString("adminPass", null), instance.getPassword());
        if (properties.has("adminPass")) {
            assertNotNull(properties.optString("encrypted-key", null));
        }
        assertEquals(properties.optString("spec"), instance.getSpec());
        assertEquals(properties.optString("keypairId", null), instance.getKeypairId());

        assertEquals(properties.optInt("networkCapacityInMbps"), instance.getNetworkCapacityInMbps());
        assertEquals(properties.optString("internetChargeType", null), instance.getInternetChargeType());
        assertEquals(properties.optString("eipName", null), instance.getEipName());

        JSONArray tags = properties.getJSONArray("tags");
        boolean containsFromTag = false;
        boolean containsNodeTypeTag = false;
        for (int i = 0; i < tags.length(); i++) {
            JSONObject tag = tags.getJSONObject(i);
            assertTrue(tag.has("tagKey"));
            assertNotNull(tag.getString("tagKey"));
            assertTrue(tag.has("tagValue"));
            assertNotNull(tag.getString("tagValue"));
            if (tag.getString("tagKey").equals("from")) {
                containsFromTag = true;
                assertEquals(tag.getString("tagValue"), "chpc");
            } else if (tag.getString("tagKey").equals("nodeType")) {
                containsNodeTypeTag = true;
                assertEquals(tag.getString("tagValue"), nodeType.getType());
            }
        }
        assertTrue(containsFromTag);
        assertTrue(containsNodeTypeTag);

        boolean hasDataDisk = CollectionUtils.isNotEmpty(instance.getDataDiskList());
        assertEquals(properties.has("createCdsList"), hasDataDisk);
        if (hasDataDisk) {
            JSONArray cdsList = properties.getJSONArray("createCdsList");
            assertEquals(cdsList.length(), instance.getDataDiskList().size());
        }

    }


    int countManagerInstanceNum(IClusterCreateRequest request) {
        if (null == request.getManagerNode()) return 0;
        return request.getManagerNode().getCount();
    }

    private int countComputerInstanceNum(IClusterCreateRequest request) {
        if (null == request.getQueues()) return 0;
        int count = 0;
        for (QueueAddRequest queue : request.getQueues()) {
            if (CollectionUtils.isEmpty(queue.getComputeNodes())) {
                continue;
            }
            for (InstanceAddRequest instanceAddRequest : queue.getComputeNodes()) {
                count = count + instanceAddRequest.getCount();
            }
        }
        return count;
    }

    private int countQueueNum(IClusterCreateRequest request) {
        if (null == request.getQueues()) return 0;
        return request.getQueues().size();
    }

    private int countDefaultQueueNum(IClusterCreateRequest request) {
        if (null == request.getQueues()) return 0;
        return (int) request.getQueues().stream().filter(QueueAddRequest::isDefault).count();
    }


}
