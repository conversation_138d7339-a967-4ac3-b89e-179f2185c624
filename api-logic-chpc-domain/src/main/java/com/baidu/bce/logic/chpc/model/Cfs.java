package com.baidu.bce.logic.chpc.model;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@NoArgsConstructor
public class Cfs {

    /**
     * 集群id
     */
    private String clusterId;
    /**
     * cfs id
     */
    private String cfsId;
    /**
     * cfs name
     */
    private String name;
    /**
     * cfs类型
     */
    private String cfsType;
    /**
     * 存储协议
     */
    private String storageProtocol;
    /**
     * 挂载地址
     */
    private String mountTarget;
    /**
     * 实例id
     */
    private String mountDir;
    /**
     * 参数
     */
    private String mountOption;

}

