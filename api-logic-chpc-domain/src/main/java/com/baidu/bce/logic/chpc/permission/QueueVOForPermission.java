package com.baidu.bce.logic.chpc.permission;

import com.baidu.bce.logic.chpc.model.Queue;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.List;

@Data
public class QueueVOForPermission {
    @Data
    public class QueueResource {
        private long cpu;
        private String memory;
    }
    @JsonIgnore
    private String queueId;

    private String queueName;

    private String description;

    private Boolean isDefault;

    private Boolean isAutoScale;

    private Boolean hasAutoScale;

    private String status;

    private Integer instanceCount;

    private QueueResource allocatedResource;

    private QueueResource totalResource;

    private List<String> userList;

    public static QueueVOForPermission convertToGroupVOTest(Queue queue) {

        QueueVOForPermission queueVO = new QueueVOForPermission();
        queueVO.setQueueName(queue.getName());
        queueVO.setDescription(queue.getDescription());
        queueVO.setIsDefault(queue.getIsDefault());
        queueVO.setStatus(queue.getStatus());
        queueVO.setIsAutoScale(queue.getIsAutoScale());
        queueVO.setHasAutoScale(queue.getHasAutoScale());
        queueVO.setQueueId(queue.getQueueId());
        return queueVO;
    }
}
