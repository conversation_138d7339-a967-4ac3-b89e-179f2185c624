package com.baidu.bce.logic.chpc.common;

import java.util.Arrays;

import lombok.Getter;

/**
 * @Author: lilu24
 * @Date: 2022-12-26
 */
@Getter
public enum TaskType {

    GROUP_STARTED_TASK("group_started_task"),

    GROUP_CHECK_TASK("group_check_task"),

    GROUP_RESOURCE_SAVE_TASK("group_resource_save_task"),

    GROUP_CROMWELL_SAVE_TASK("group_cromwell_save_task"),

    RESET_PASSWORD_TASK("reset_password_task"),

    ADD_SKYFORM("add_skyform"),

    RUN_WORKFLOW_TASK("run_workflow_task"),

    GROUP_DELETED_TASK("group_deleted_task"),

    GROUP_SAVE_FAILED_TASK("group_save_failed_task"),

    GROUP_JOIN_TASK("group_join_task"),

    INSTANCE_STATUS_SYNC_TASK("instance_status_sync_task"),

    CLUSTER_DELETED_TASK("cluster_deleted_task"),

    BACKEND_ASYNC_TASK("backend_async_task"),

    SOFTWARE_OPERATION_TASK("software_operation_task"),

    SHRINK_TASK("shrink_task"),

    OOS_ASYNC_TASK("oos_async_task");

    private final String taskType;

    TaskType(String taskType) {
        this.taskType = taskType;
    }

    public static TaskType fromType(String input) {

        return Arrays.stream(TaskType.values())
                .filter(b -> b.taskType.equals(input))
                .findFirst()
                .orElse(null);
    }
}
