package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.model.Queue;

import java.util.Collection;
import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
public interface QueueDAOGateway {

    Long count(String clusterId, String queueId, String status);

    Boolean isExisted(String clusterId, String queueId, String status);

    Queue getByQueueId(String queueId);

    Queue getByQueueName(String queueName, String clusterId);

    List<Queue> getByQueueIds(Collection<String> queueIds);

    List<Queue> findAllByQueueIds(Collection<String> queueIds);

    List<Queue> getByIsAutoScaling(boolean isAutoScaling);

    Queue getByName(String clusterId, String queueName);

    Queue getClusterDefaultQueue(String clusterId);

    Boolean insert(Queue queue);

    List<Queue> listByClusterId(String clusterId);

    List<Queue> listByStatus(List<String> statusList);

    Boolean updateStatus(String groupId, String status);

    Boolean update(Queue queue);

    Boolean delete(String queueId);

    Boolean deleteWithStatus(String groupId, QueueStatus status);
}
