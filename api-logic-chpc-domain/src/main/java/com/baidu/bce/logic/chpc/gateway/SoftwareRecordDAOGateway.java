package com.baidu.bce.logic.chpc.gateway;

import java.util.List;

import com.baidu.bce.logic.chpc.model.SoftwareRecord;

public interface SoftwareRecordDAOGateway {

    // 插入软件安装、卸载记录
    Boolean insert(SoftwareRecord record);

    // 查询软件安装记录
    SoftwareRecord findSoftwareByNameAndVersion(String clusterId, String name, String version);

    // 查询软件安装记录
    SoftwareRecord findSoftwareByNameAndVersionAndInstanceId(String clusterId, String name, String version, String instanceId);

    // 查询指定节点上的软件安装记录
    List<SoftwareRecord>  findSoftwareByInstanceId(String clusterId, String instanceId);

    // 查询集群下所有的软件安装记录
    List<SoftwareRecord> findSoftwareByClusterId(String clusterId);

    // 更新软件记录状态
    Boolean update(SoftwareRecord record);

}
