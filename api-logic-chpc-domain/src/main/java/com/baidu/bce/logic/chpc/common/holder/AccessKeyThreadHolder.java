package com.baidu.bce.logic.chpc.common.holder;

import com.alibaba.ttl.TransmittableThreadLocal;

public class AccessKeyThreadHolder {

    /**
     * access key thread local
     */
    private static final ThreadLocal<String> ACCESS_KEY_HOLDER = new TransmittableThreadLocal<>();

    public static String getAccessKey() {
        return ACCESS_KEY_HOLDER.get();
    }

    public static void setAccessKey(String accessKey) {
        ACCESS_KEY_HOLDER.set(accessKey);
    }


    public static void clear() {
        ACCESS_KEY_HOLDER.remove();
    }

}
