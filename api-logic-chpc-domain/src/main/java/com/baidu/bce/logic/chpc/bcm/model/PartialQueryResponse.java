package com.baidu.bce.logic.chpc.bcm.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class PartialQueryResponse {
    String requestId;
    String code;
    String message;
    PageResponse result;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PageResponse {
        Integer pageNo;

        Integer pageSize;

        Integer totalCount;

        List<DataResult> result;
    }
}
