package com.baidu.bce.logic.chpc.common.interceptors;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AccessKeyThreadLocalInterceptor())
                .addPathPatterns("/v1/cluster", "/v1/cluster/*/queue/*/instances", "/v1/cluster/*/resetPassword",
                        "/v1/cluster/*/job");
    }
}
