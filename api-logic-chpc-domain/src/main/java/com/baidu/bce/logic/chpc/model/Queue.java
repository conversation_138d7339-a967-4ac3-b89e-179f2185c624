package com.baidu.bce.logic.chpc.model;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@NoArgsConstructor
public class Queue {

    /**
     * 集群id
     */
    private String clusterId;
    /**
     * 队列名称
     */
    private String name;
    /**
     * 队列描述
     */
    private String description;

    /**
     * 队列id
     */
    private String queueId;
    /**
     * 是否为默认队列
     */
    private Boolean isDefault;
    /**
     * 是否开启自动伸缩
     */
    private Boolean isAutoScale;
    /**
     * 是否包含自动伸缩策略
     */
    private Boolean hasAutoScale;
    /**
     * bcc实例规格
     */
    private String defaultSpec;
    /**
     * bcc实例镜像
     */
    private String defaultImageId;
    /**
     * 默认的user data
     */
    private String defaultUserData;

    /**
     * 状态
     */
    private String status;

    private String logicalZone;

    private String subnetId;

}

