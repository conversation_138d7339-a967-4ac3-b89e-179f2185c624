package com.baidu.bce.logic.chpc.cluster.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ClusterHeartbeatRequest {
    
    @JsonProperty("heartbeat_info")
    HeartbeatInfo heartbeatInfo;

    @JsonProperty("signature")
    String signature;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class HeartbeatInfo {
        @JsonProperty("cluster_info")
        ClusterInfo clusterInfo;

        @JsonProperty("auto_scaling_info")
        List<AutoScalingInfo> autoScalingInfo;

        @JsonProperty("timestamp")
        Long timestamp;
    }
    
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AutoScalingInfo {
        @JsonProperty("queue_name")
        String queueName;

        @JsonProperty("scale_count")
        Integer scaleCount;

        String message;

        List<Node> nodes;

        @JsonProperty("shrink_cycle")
        Integer shrinkCycle;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Node {
        String hostIP;

        String status;

        String nodeID;

        String nodeName;

        String spec;

        String queue;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class ClusterInfo {
        @JsonProperty("cluster_id")
        String clusterId;

        @JsonProperty("status")
        String status;
    }
}


