package com.baidu.bce.logic.chpc.gateway;

import java.util.List;

import com.baidu.bce.logic.chpc.model.Task;

/**
 * <AUTHOR>
 * @since 2023-01-04
 */
public interface TaskDAOGateway {


    Boolean insert(Task task);

    Boolean update(String taskId, String status, String extra);

    Boolean updateByTaskUuid(String taskUuid, String status, String extra);

    Boolean updateStackId(String taskId, String stackId);

    Boolean updateStackIdByTaskUuid(String taskUuid, String stackId);

    List<Task> listByStatus(List<String> statusList);

    List<Task> listByTypeAndStatus(String type, List<String> statusList);

    List<Task> listByTaskUuid(String clusterId, String taskUuid);

}
