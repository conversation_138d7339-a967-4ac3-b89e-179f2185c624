package com.baidu.bce.logic.chpc.bcm.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResultDimension {
    String name;

    String value;

    public ResultDimension() {
        this.name = "";
        this.value = "";
    }

    public ResultDimension(String name, String value) {
        this.name = name;
        this.value = value;
    }
}
