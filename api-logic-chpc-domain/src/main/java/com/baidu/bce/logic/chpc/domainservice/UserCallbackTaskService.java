package com.baidu.bce.logic.chpc.domainservice;

import com.baidu.bce.logic.chpc.common.UserCallbackTaskStatus;
import com.baidu.bce.logic.chpc.common.UserCallbackTaskType;
import com.baidu.bce.logic.chpc.gateway.UserCallbackTaskDAOGateway;
import com.baidu.bce.logic.chpc.model.UserCallbackTask;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * @Author: qiansheng01
 * @Date: 2023-03-24
 */
@Service
public class UserCallbackTaskService {

    @Resource
    private UserCallbackTaskDAOGateway taskDAOGateway;

    public List<UserCallbackTask> getByTaskId(String clusterId, String taskId) {
        if (StringUtils.isEmpty(taskId)) {
            return Collections.emptyList();
        }
        return taskDAOGateway.listByTaskId(clusterId, taskId);
    }

    public List<UserCallbackTask> getByJobIdAndStatus(String clusterId, List<String> jobIds, String status) {
        if (StringUtils.isEmpty(clusterId) || jobIds.isEmpty() || StringUtils.isEmpty(status)) {
            return Collections.emptyList();
        }
        return taskDAOGateway.listByJobIdAndStatus(clusterId, jobIds, status);
    }

    public Boolean insert(String clusterId, String queueName, String nodeName, String jobId,
                          String action, String parameters, String taskId, String userName,
                          String asyncOperationType) {
        UserCallbackTask task = new UserCallbackTask();
        String status = UserCallbackTaskStatus.WAITING.getValue();
        if (asyncOperationType != null && asyncOperationType.equals(UserCallbackTaskType.NON_BLOCKING.getValue())) {
            status = UserCallbackTaskStatus.WAITING_NON_BLOCKING.getValue();
        }
        task.setStatus(status);
        task.setTaskId(taskId);
        task.setClusterId(clusterId);
        task.setQueueName(queueName);
        task.setNodeName(nodeName);
        task.setJobId(jobId);
        task.setAction(action);
        task.setParameters(parameters);
        task.setUserName(userName);
        return taskDAOGateway.insert(task);
    }

    public Boolean updateStatus(String taskId, String status) {
        return taskDAOGateway.update(taskId, status);
    }


}


