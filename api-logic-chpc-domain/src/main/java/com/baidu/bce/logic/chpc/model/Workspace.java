package com.baidu.bce.logic.chpc.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class Workspace {


    private String workspaceId;

    private String name;

    private String description;

    private String accountId;

    private String clusterId;

    private String clusterName;

    private String bosBucket;

    private String status;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;


}
