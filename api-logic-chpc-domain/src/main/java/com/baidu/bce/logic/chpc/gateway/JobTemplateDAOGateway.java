package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.model.JobTemplate;

import java.util.List;

public interface JobTemplateDAOGateway {

        JobTemplate findByTemplateNameAndClusterId(String templateName, String clusterId);

        Boolean insertTemplate(JobTemplate template);

        List<JobTemplate> findTemplatesByCluster(String clusterId);

        Boolean updateTemplate(JobTemplate template);

        Boolean deleteTemplate(String clusterId, String templateName);

        void deleteAllJobTemplatesByCluster(String clusterId);
}
