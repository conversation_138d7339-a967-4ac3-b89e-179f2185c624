package com.baidu.bce.logic.chpc.cos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

/*
{
      "success": true,
      "msg": "",
      "code": 200,
      "result": {
          "id": "st-dJ7ihkEm",  // 资源栈id
          "name": "我的资源栈",  // 资源栈名称
          "template": {  // 模板
              "json": "",
              "terraform": "",
          },
          "description": "",  // 描述
          "timeout": 60,      // 资源创建超时时间
          "params": {},       // 资源栈输入参数
          "tags": {},         // 标签列表
          "disableRollback": false,  // 是否禁用回滚
          
          // 资源栈状态：
          // RUNNING          - 创建中
          // SUCCESS          - 成功
          // FAILED           - 失败
          // ROLLBACK         - 回滚中
          // ROLLBACK_SUCCESS - 回滚成功
          // ROLLBACK_FAILED  - 回滚失败
          "status": "SUCCESS",
          
          // 资源栈状态，即资源栈成功创建的资源列表
          // 1. 若status=SUCCESS/ROLLBACK_SUCCESS，该资源列表与模板保持一致（排除外部直接操作资源的情况）
          // 2. 若status=FAILED，该资源列表与模板可能不一致
          "state": {
              "bcc_0": {
                  // 资源类型
                  "type": "BCE::BCC::Instance",
                  // 资源id
                  "id": "i-cQy2O3sFbqCn",        
                  // 资源属性
                  "properties": {
                      "id": "i-cQy2O3sFbqCn",
                      "subnetId": "sbn-9m44hn3sbd5k",
                      "imageId": "m-5LqDsXSC",
                  },
              },     
          },
          
          // 对于未成功创建/更新的资源，该字段记录了失败原因
          "failedReasons": {
              "bcc_1": "The param securityGroup is invalid",
          },
          "createdTime": "2023-04-25T15:58:34Z",  // 资源栈创建时间
          "updatedTime": "2023-04-25T15:58:34Z",  // 资源栈更新时间
     }
}
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class StackDetail {

    Boolean success;

    String msg;

    Integer code;

    StackResult result;

}
