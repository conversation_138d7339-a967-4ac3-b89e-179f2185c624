package com.baidu.bce.logic.chpc.bct.model;

import java.util.List;

import com.opencsv.bean.CsvBindByName;
import com.opencsv.bean.CsvCustomBindByName;
import lombok.Data;

@Data
public class Event {

    @CsvBindByName(column = "EventType")
    private String eventType;

    @CsvBindByName(column = "OperationMethod")
    private String operationMethod;

    @CsvBindByName(column = "EventSource")
    private String eventSource;

    @CsvBindByName(column = "EventName")
    private String eventName;

    @CsvBindByName(column = "EventTimeInMilliseconds")
    private Long eventTimeInMilliseconds;

    @CsvBindByName(column = "EventTime")
    private String eventTime;

    @CsvBindByName(column = "UserIpAddress")
    private String userIpAddress;

    @CsvBindByName(column = "UserAgent")
    private String userAgent;

    @CsvBindByName(column = "RegionId")
    private String regionId;

    @CsvBindByName(column = "RequestId")
    private String requestId;

    @CsvBindByName(column = "OrderId")
    private String orderId;

    @CsvBindByName(column = "Description")
    private String description;

    @CsvBindByName(column = "ErrorCode")
    private String errorCode;

    @CsvBindByName(column = "ErrorMessage")
    private String errorMessage;

    @CsvBindByName(column = "Success")
    private boolean success;

    @CsvBindByName(column = "UserIdentity")
    private UserIdentity userIdentity;

    @CsvCustomBindByName(column = "Resources", converter = ResourceInfoListToStringConverter.class)
    private List<ResourceInfo> resources;

    @Data
    public static class UserIdentity {
    
        String iamDomainId;
    
        String iamUserId;

        String loginUserId;
    
        String userDisplayName;

        String accesskey;
    }
   
    @Data
    public static class ResourceInfo {
    
        String resourceType;

        String resourceName;

        @Override
        public String toString(){
            return "ResourceInfo(resourceType=" + resourceType + ", resourceName=" + resourceName +")";
        }
    }

}
