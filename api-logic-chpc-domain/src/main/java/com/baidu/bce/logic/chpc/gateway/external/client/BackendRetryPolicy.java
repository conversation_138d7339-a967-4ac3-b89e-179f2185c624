package com.baidu.bce.logic.chpc.gateway.external.client;

import java.net.SocketTimeoutException;

import jakarta.ws.rs.ProcessingException;

import org.apache.http.NoHttpResponseException;
import org.apache.http.conn.ConnectTimeoutException;
import org.apache.http.conn.HttpHostConnectException;

// package com.baidu.bce.internalsdk.core.ext.policy;

import com.baidu.bce.internalsdk.core.BceInternalResponse;
import com.baidu.bce.internalsdk.core.ext.policy.RetryInfo;
import com.baidu.bce.internalsdk.core.ext.policy.RetryPolicy;

public class BackendRetryPolicy implements RetryPolicy {

    public static final RetryInfo NO_RETRY = new RetryInfo().setRetry(false)
            .setRetryIntervalInMs(0).setRetryNum(1);
    public static final RetryInfo SLEEP_RETRY = new RetryInfo().setRetry(true)
            .setRetryIntervalInMs(100).setRetryNum(1);
    public static final RetryInfo NOSLEEP_RETRY = new RetryInfo().setRetry(true)
            .setRetryIntervalInMs(0).setRetryNum(1);

    /**
     * {@inheritDoc}
     * 重写父类的retryResponse方法，返回NO_RETRY，表示不需要重试。
     *
     * @param response BceInternalResponse对象，包含请求的相关信息和响应结果
     * @return RetryInfo对象，包含是否需要重试、重试次数等信息，此处为NO_RETRY，表示不需要重试
     * @see RetryInfo#NO_RETRY
     */
    @Override
    public RetryInfo retryResponse(BceInternalResponse response) {
        return NO_RETRY;
    }

    /**
     * {@inheritDoc}
     * 如果异常是 ProcessingException，且它的原因是 HttpHostConnectException，则返回 SLEEP_RETRY。
     * 否则，如果异常是 ProcessingException，且它的原因是 ConnectTimeoutException、SocketTimeoutException、NoHttpResponseException，则返回 NOSLEEP_RETRY。
     * 否则，返回 NO_RETRY。
     *
     * @param th 异常对象
     * @return RetryInfo 包含重试信息的实例，包括是否需要重试和重试策略等信息
     */
    @Override
    public RetryInfo retryException(Throwable th) {
        if (th instanceof ProcessingException) {
            ProcessingException pe = (ProcessingException) th;
            if (pe.getCause() instanceof HttpHostConnectException) {
                return SLEEP_RETRY;
            } else if (pe.getCause() instanceof ConnectTimeoutException
                    || pe.getCause() instanceof SocketTimeoutException
                    || pe.getCause() instanceof NoHttpResponseException) {
                return NOSLEEP_RETRY;
            }
        }
        return NO_RETRY;
    }
}
