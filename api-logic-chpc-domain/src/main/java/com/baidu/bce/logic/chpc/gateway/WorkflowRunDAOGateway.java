package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.model.WorkflowRun;

import java.util.List;

public interface WorkflowRunDAOGateway {

    Boolean createWorkflowRun(WorkflowRun workflowRun);


    WorkflowRun getWorkflowRun(String workflowRunId, String accountId);

    List<WorkflowRun> getWorkflowRunByName(String workflowId, String workflowRunName, String accountId);

    List<WorkflowRun> getWorkflowRunById(String workspaceId, String workflowRunId, String name, String status, String accountId);

    Boolean deleteWorkflowRun(String runId, String accountId);

    Boolean deleteWorkflowRunByWorkspaceId(String workspaceId, String accountId);

    Boolean updateWorkflowRunStatus(String workflowRunId, String status, String outputs);
}
