package com.baidu.bce.logic.chpc.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;


@Data
@NoArgsConstructor
public class HelixJob {
    /**
     * 账号id
     */
    private String accountId;
    /**
     * 平台id,如GitHub Id'
     */
    private String platformId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 任务id
     */
    private String taskId;
    /**
     * 作业id
     */
    private String jobId;
    /**
     * 集群id
     */
    private String clusterId;
    /**
     * 作业商品:helixvs、helixfold3
     */
    private String jobProduct;
    /**
     * job 状态
     */
    private String jobStatus;

    /**
     * 用户操作
     */
    private String action;
    /**
     * job 运行时间
     */
    private String jobRunTime;
    /**
     * job 计费次数
     */
    private String jobChargeAmount;
    /**
     * job 实际计费金额
     */
    private String jobPrice;
    /**
     * job 代金劵抵扣金额
     */
    private String jobCouponPrice;
    /**
     * job 的token长度
     */
    private String jobTokenLength;
    /**
     * job 量包抵扣量
     */
    private String jobPackageDeduction;

    /**
     * job 提交的参数
     */
    private String extra;
    /**
     * 是否删除
     */
    private Boolean deleted;

    /**
     * 作业创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 作业修改时间
     */


    private LocalDateTime updatedTime;
}
