package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.tag.DbTag;
import com.baidu.bce.logic.chpc.tag.Tag;

import java.util.List;

public interface TagsDAOGateway {
    Boolean insert(String accountId, String clusterId, String tagType, String name, String tagKey, String takValue);

    List<Tag> findTags(String accountId, String clusterId, String tagType, String name);

    List<Tag> findTagsByKey(String accountId, String tagType, String tagKey);

    List<DbTag> findDbTags(String accountId, String clusterId, String tagType);

    Boolean deleteByClusterId(String clusterId, String tagType);

    Boolean deleteByClusterIdAndName(String clusterId, String tagType, String name);

    Boolean deleteByClusterIdAndKey(String clusterId, String tagType, String tagKey);
}
