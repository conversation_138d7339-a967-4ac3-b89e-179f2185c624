package com.baidu.bce.logic.chpc.cos.gateway;

import com.baidu.bce.internalsdk.cos.model.ClusterResponse;
import com.baidu.bce.logic.chpc.cos.CreateStackRequest;
import com.baidu.bce.logic.chpc.cos.CreateStackResponse;
import com.baidu.bce.logic.chpc.cos.DeleteStackResponse;
import com.baidu.bce.logic.chpc.cos.GetStackListRequest;
import com.baidu.bce.logic.chpc.cos.GetStackListResponse;
import com.baidu.bce.logic.chpc.cos.StackDetail;

public interface CosGateway {

    CreateStackResponse createStack(CreateStackRequest createStackRequest);

    StackDetail getStack(String stackId);

    GetStackListResponse getStackList(GetStackListRequest request);

    DeleteStackResponse deleteStack(String stackId);

    ClusterResponse getClusterStatus(String stackId);
}
