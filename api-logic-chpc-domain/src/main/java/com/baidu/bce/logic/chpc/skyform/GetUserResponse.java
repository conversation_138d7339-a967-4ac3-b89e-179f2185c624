package com.baidu.bce.logic.chpc.skyform;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetUserResponse {
    private Integer code;
    private String msg;
    private List<RetObj> retObj;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RetObj {
        private String uuid;
        private String loginName;
        private String loginNameMapping;
        private String userName;
        private String userType;
        private String deptId;
        private String tenantId;
        private String token;
        private String tenantName;
        private String cpu;
        private String roleName;
        private String color;
        private String lastModifiedPwdTime;
        private String lastLoginTimeStr;
        private String tenantCode;
        private String lastLoginTime;
    }
}

