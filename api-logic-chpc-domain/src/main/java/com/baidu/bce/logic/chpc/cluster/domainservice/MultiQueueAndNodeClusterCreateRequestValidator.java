package com.baidu.bce.logic.chpc.cluster.domainservice;

import com.baidu.bce.logic.chpc.cfs.CfsAddRequest;
import com.baidu.bce.logic.chpc.cluster.model.IClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.MultiQueueAndNodeClusterCreateRequest;
import com.baidu.bce.logic.chpc.common.charge.ChargeUtil;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.queue.QueueAddRequest;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
@Slf4j
@Component
public class MultiQueueAndNodeClusterCreateRequestValidator extends CommonClusterCreateRequestValidator {
    @Override
    public void valid(IClusterCreateRequest clusterCreateRequest) {
        if (!(clusterCreateRequest instanceof MultiQueueAndNodeClusterCreateRequest)) {
            log.warn("unsupported request for parameter validator, " +
                            "expect MultiQueueAndNodeClusterCreateRequest but provide {}",
                    clusterCreateRequest.getClass().getName()
            );

            throw new CommonExceptions.RequestInvalidException(
                    "unsupported request for parameter validator"
            );
        }

        MultiQueueAndNodeClusterCreateRequest request = (MultiQueueAndNodeClusterCreateRequest) clusterCreateRequest;

        if (CollectionUtils.isEmpty(request.getQueues())) {
            log.warn("no default queue found in clusterCreateRequest");
            throw new CommonExceptions.RequestInvalidException(
                    "queues is empty");
        }

        List<QueueAddRequest> defaultQueues = request.getQueues().stream()
                .filter(QueueAddRequest::isDefault)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(defaultQueues)) {
            log.warn("no default queue found in clusterCreateRequest");
            throw new CommonExceptions.RequestInvalidException(
                    "no default queue");
        }

        if (defaultQueues.size() != 1) {
            log.warn("only support one default queue , but there are {} in clusterCreateRequest", defaultQueues.size());
            throw new CommonExceptions.RequestInvalidException(
                    "too many default queue");
        }

        int computeCount = 0;

        for (QueueAddRequest queueAddRequest : request.getQueues()) {
            if (CollectionUtils.isNotEmpty(queueAddRequest.getComputeNodes())) {
                for (InstanceAddRequest instanceAddRequest : queueAddRequest.getComputeNodes()) {
                    computeCount += instanceAddRequest.getCount();
                    if (StringUtils.isEmpty(instanceAddRequest.getSpec())
                            || StringUtils.isEmpty(instanceAddRequest.getImageId())) {
                        log.warn("compute spec or imageId is empty.");
                        throw new CommonExceptions.RequestInvalidException(
                                "compute spec or imageId is empty.");
                    }
                    this.validateZoneName(instanceAddRequest.getZoneName());
                }
            }
        }


        ChargeUtil.validateChargingTypeAndPeriod(request.getChargeType(), request.getPeriod(), request.getPeriodUnit());


        this.validateZoneName(request.getManagerNode().getZoneName());
        if (CollectionUtils.isNotEmpty(request.getCfsVolumes())) {
            for (CfsAddRequest cfsAddRequest : request.getCfsVolumes()) {
                this.validateCfsParameters(cfsAddRequest, request.getVpcId());
            }
        }


        this.validateQuota(computeCount);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ClusterCreateRequestValidatorFactory.register(MultiQueueAndNodeClusterCreateRequest.class, this);
    }

}
