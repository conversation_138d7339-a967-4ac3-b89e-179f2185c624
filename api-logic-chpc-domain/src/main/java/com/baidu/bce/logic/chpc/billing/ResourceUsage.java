package com.baidu.bce.logic.chpc.billing;

import java.time.LocalDateTime;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResourceUsage {

    private String amount;
    private String deductAmount;
    private String unit;
    private TimeSpan timeSpan;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TimeSpan {
         @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
        LocalDateTime startTime;
        @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
        LocalDateTime endTime;
    }

}
