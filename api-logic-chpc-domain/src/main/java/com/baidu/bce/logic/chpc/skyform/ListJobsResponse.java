package com.baidu.bce.logic.chpc.skyform;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListJobsResponse {
    Integer code;

    String msg;

    RetObj retObj;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RetObj {
        private Integer totalElements;
        private Integer totalPages;
        private List<Content> content;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Content {
        private String uuid;
        private String jobId;
        private String jobName;
        private String jobType;
        private String poolId;
        private String poolName;
        private String queue;
        private String submitHost;
        private String execHost;
        private Integer jobNum;
        private String jobStatus;
        private String dataStatus;
        private String user;
        private String project;
        private String projectDesc;
        private String projectSecretLevel;
        private String msg;
        private String createBy;
        private Date createTime;
        private Date submitTime;
        private Date startTime;
        private String startTimeStr;
        private Date endTime;
        private String requestResource;
        private Integer priority;
        private String dependency;
        private String endReason;
        private String jobSpec;
        private String extendInfo;
        private String updateBy;
        private String updateTime;
        private String shareNum;
        private String shareObjs;
        private String actualMaxRunTime;
        private String requestCpu;

        private String runCpu;
        private String runTime;
        private String userName;
        private String requestGpu;
        private String requestMem;
    }
}
