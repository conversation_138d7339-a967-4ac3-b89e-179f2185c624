package com.baidu.bce.logic.chpc.model;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@NoArgsConstructor
public class Cluster {

    /**
     * 集群名称
     */
    private String clusterId;
    /**
     * 集群名称
     */
    private String name;
    /**
     * 集群描述
     */
    private String description;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;


    /**
     * 付费类型
     */
    private String chargeType;

    /**
     * vpc id
     */
    private String vpcId;
    /**
     * 子网id
     */
    private String subnetId;

    /**
     * 安全组
     */
    private String securityGroupId;

    /**
     * 安全组类型
     */
    private String securityGroupType;

    /**
     * 集群状态
     */
    private String status;
    /**
     * 集群所属的逻辑地域
     */
    private String logicalZone;
    /**
     * 调度器类型
     */
    private String schedulerType;
    /**
     * 调度器版本
     */
    private String schedulerVersion;
    /**
     * 是否开启高可用  0-未开启， 1-开启
     */
    private boolean enableHa;

    /**
     * 集群监控预留字段,一期不开放
     */
    private Boolean enableMonitor = false;


    private String accountId;

    private String imageId;

    private String spec;

    /**
     * 创建集群时，cos对应的资源栈
     */
    private String cosStackId;

    private String extra;

    /**
     * 集群异常原因
     */
    private String errorMessage;

    /**
     * 软件安装目录
     */
    private String softwareDir;

    /**
     * 上次心跳时间
     */
    private Long heartbeat;

    /**
     * 使用 schedule plugin
     */
    private int schedulePlugin;
    /**
     * 插件版本
     */
    private String schedulePluginVersion;

    /**
     * 集群类型
     */
    private String clusterType;

    private Integer maxNodes;

    private Integer maxCpus;

    private Boolean forbidDelete;
    
    /**
     * 密码
     */
    private String password;

    /**
     * 密钥对ID
     */
    private String keypairId;

    private String schedulerIp;

    private String schedulerHost;

    private Boolean createdDone;

    private String domainAccount;

}

