package com.baidu.bce.logic.chpc.autoscaling.model;

import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.Valid;
import lombok.Data;

import jakarta.validation.constraints.Min;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SetAutoScalingRequest {

    /**
     * 集群ID
     */
    String clusterId;

    /**
     * 集群名称
     */
    String clusterName;

    /**
     * 队列名称
     */
    String queueName;

    /**
     * 自动伸缩策略ID
     */
    String asId;

    /**
     * 自动伸缩策略名称
     */
    String asName;

    /**
     * 队列中最大节点数
     */
    // @Max(500)
    // @Min(1)
    Integer maxNodesInQueue;

    /**
     * 队列中最小节点数，避免在缩容过程中将队列中所有节点都释放
     */
    @Min(value = 0)
    Integer minNodesInQueue;

    /**
     * 单次最大伸缩节点数
     */
    // @Max(50)
    // @Min(1)
    Integer maxScalePerCycle;

    /**
     * 是否开启自动扩容
     */
    Boolean enableAutoGrow;

    /**
     * 是否开启自动缩容
     */
    Boolean enableAutoShrink;

    /**
     * 空闲时不被释放的节点
     */
    List<String> excludeNodes;

    /**
     * 自动扩容的实例规格
     */
    String spec;

    /**
     * 系统盘大小
     */
    Integer systemDiskSize;

    /**
     * 系统盘类型
     */
    String systemDiskType;

    List<DiskInfo> dataDiskList;

    String zoneName;

    /**
     * 自动伸缩时使用的镜像ID
     */
    String imageId;

    /**
     * 子网ID
     */
    String subnetId;

    /**
     * 扩容节点主机名前缀
     */
    String hostNamePrefix;

    String cudaVersion;

    String gpuDriverVersion;

    String cudnnVersion;

    String securityGroupId;

    @Valid
    private List<Tag> tags;

    // ebc ht&numa 配置
    private String cpuThreadConfig;

    private String numaConfig;
}
