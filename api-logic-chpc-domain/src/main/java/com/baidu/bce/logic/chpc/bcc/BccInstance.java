package com.baidu.bce.logic.chpc.bcc;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BccInstance {
    private Long id = 0L;
    private String instanceId;
    private String instanceUuid;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp createdTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp updatedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp deletedTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp recycleTime; // 回收时间

    private String userId;
    private String resourceAccountId;

    private String tenantId;
    private String name;
    private String hostName;
    private String description;
    private String internalIp;
    private String floatingIp;
    private String ipv6;
    private String eipId; // 长id
    private String eipAllocationId; // 短id
    private String eip;
    private Integer eipSize = 0;
    private String eipStatus;
    private String eipGroupId;    // eip 所属的groupId
    private String eipType;      // "shared|normal"
    private Integer rootGb;
    private Integer ephemeralGb; // 临时数据盘大小
    private Integer vCpu = 0;
    private Integer memoryMb = 0; // 内存大小 (一般对应t_instance.memory_mb字段 该字段实际上存储单位是GB)
    private Boolean rocV2 = Boolean.FALSE; // 是否是rocv2虚机

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp expireTime; // 过期时间
    private String productType; // 支付方式
    private String imageUserId; // 镜像所属用户ID
    private String imageId; // 镜像
    private String imageUuid; // 镜像
    private String imageType;
    private String imageName;
    private String status;
    private boolean hasScheduleTask = false; // 是否设置定时快照
    private String orderUuid; // 订单id
    private String resourceUuid; // 资源id
    private String extra;
    private String source;
    private String tag = "";
    private String flavorId = "";

    private String flavor;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp releaseTime;

    private String osName;
    private String osVersion;
    private String osArch;
    private String osLang;
    private String osBuild;
    private String osType;
    private String taskStatus;
    private Integer instanceType;  // 0:普通 1：专属 2:物理机,具体类型见InstanceType类
    private String password;

    private String vpcName;
    private String vpcCidr;
    private String subnetName;
    private String subnetCidr;

    private String vpcId;
    // add by hsq
    private String vpcUuid;
    private String subnetId; // 短id
    private String subnetUuid;  // 长id
    // 子网类型
    private String subnetType;
    private String zoneId;
    private String logicalZone;
    private String region;
    // 订单状态，用预付费<==>后付费转换状态
    private String orderStatus = "";

    // 实例置放策略，取值default、dedicatedHost, 根据instanceType 0 : 普通 1 ：专属 2 : 物理机设置
    private String placementPolicy;

//    private List<VolumePO> volumeList = new ArrayList<>(); // 所挂载的CDS信息

//    private List<EipInstance> eipInstanceList; // 所绑定的EIp信息

    private List<Tag> tags; // 所绑定的tag信息

    private String gpuCard = "";

    private String gpuCardForWrapSpec;

    private Integer gpuCount = 0;
    // gpu显存
    private String gpuVideoMemory;

    /**
     * npu显存
     */
    private String npuVideoMemory;

    private String kunlunCard;

    private Integer kunlunCount = 0;

    private String isomerismCard;

    private Integer isomerismCount = 0;

    private int deleted = 0;
    // true开启了自动续费
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean autoRenew = Boolean.FALSE;
    private String renewTimeUnit;
    private int renewTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Date nextRenewTime;

    // 虚机用途，默认是bcc 可选值 bcc;cce;rds(专属实例用于部署rds);scs(专属实例用于部署scs)
    private String application = "bcc";

    // 创建来源,用户普通创建情况下为空,其余情况填写请求来源(一般结构为internal_xxx)
    private String createdFrom = "";

    // 虚机绑定的密钥对Id
    private String keypairId = "";

    // 虚机绑定的密钥对名称
    private String keypairName = "";

    // 元数据，Map<String, Object> Json序列化数据，
    // 创建BCC指定脚本地址，
    // 创建DCC专属RDS时, 调用发发来的请求参数. 待创建完成回调时需要携带
    private String metadata = "";

    private String rdsUuid; // bcc上部署的rds的Uuid(一般供FE使用)

    private String scsUuid; // bcc上部署的scs的Uuid(一般供FE使用)

    private String rdsId; // bcc上部署的rds的Id(一般供FE使用)

    private String scsId; // bcc上部署的scs的Id(一般供FE使用)

    // 使用的镜像的最大内存GB
    private int maxRamGb;

    private String hostEyeStatus = "alive";

    private String userShutdownTime;
    private String sysShutdownTime;

    private String chargeStatus;

    private int eniNum;

    private int eniExtNum;
    private String specId;

    private String specialVersion = "";

    private String spec;

    private String deploysetUuid = "";

    private String sysVolumeId;

    private String chainId;

    private String chainUuid;

    private String chainStatus;

    private int resizeCount;

    private String hostId = "";

    private String rackId = "";

    private String torId = "";

    private String switchId = "";

    private String roleName = "";

    private String deploysetId = "";

    private Integer vfsCounts;

    private String hosteyeType;

    /**
     * 混卖机型下的虚机支持挂载最大cds数据盘数量
     */
    private Integer cdsDataCount;

    /**
     * 混卖机型下的虚机支持最多挂载eni数量, 不包括主网卡
     */
    private Integer eniCount;

    private String sn = "";

    /**
     * 网卡队列数
     */
    private Integer netEthQueueCount;
    private String ehcClusterId = "";


    private int hotplugCnt;

    private String physicalZone;


    private String deploysetName = "";

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = BceConstant.DATETIME_FORMAT, timezone = "UTC")
    private Timestamp licenseExpire;

    private String capacityInfo = "";

    private Integer deletionProtection;

    private Integer rdmaType;

    private String rdmaIp;

    private List<String> rdmaIps;

    /**
     * 镜像所胡者类型 USER/PUBLIC/SERVICE/MKT
     */
    private String ownerType;

    private boolean isDedicatedSysVolume = false;

}
