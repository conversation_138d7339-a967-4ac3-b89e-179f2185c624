package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.model.ServiceStatus;

public interface ServiceStatusDAOGateway {

    Boolean insert(Long serviceId, String accountId, String status);

    Boolean delete(Long serviceId, String accountId);

    Boolean update(Long serviceId, String accountId, String status);

    Boolean isExisted(Long serviceId, String accountId);

    ServiceStatus findBy(Long serviceId, String accountId);

}
