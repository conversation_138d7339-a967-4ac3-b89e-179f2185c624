package com.baidu.bce.logic.chpc.sheduler;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
public class SchedulerAddRequest {
    private String maxQueuedJobs;
    private List<AclInfo> aclInfo;
    private String jobHistory;
    private String schedIteration;
    private List<QueueConfig> queueConfigList;
    private String maxJobs;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class AclInfo {
        private List<String> userList;
        private String queueName;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserMaxRunLimit {
        private String userName;
        private String mem;
        private String cpus;
        private String nodes;
        private String maxJobs;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class QueueConfig {
        private List<UserMaxRunLimit> userMaxRunLimitList;
        private String queueName;
    }
}
