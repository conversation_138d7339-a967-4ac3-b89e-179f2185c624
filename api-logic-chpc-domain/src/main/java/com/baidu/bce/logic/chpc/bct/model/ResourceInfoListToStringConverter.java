package com.baidu.bce.logic.chpc.bct.model;

import com.opencsv.bean.AbstractBeanField;
import com.opencsv.exceptions.CsvConstraintViolationException;
import com.opencsv.exceptions.CsvDataTypeMismatchException;

import java.util.List;
import java.util.stream.Collectors;

public class ResourceInfoListToStringConverter extends AbstractBeanField<List<Event.ResourceInfo>, String> {
    // 将csv格式的字符串转换成对象
    @Override
    protected Object convert(String s) throws CsvDataTypeMismatchException, CsvConstraintViolationException {
        return null;
    }

    // 将对象转换成csv格式的字符串，这里用来处理ResouceInfo类型
    @Override
    public String convertToWrite(Object object){
        if (object == null) {
            return "";
        }
        List<Event.ResourceInfo> resourceInfoList = (List<Event.ResourceInfo>) object;
        // 将 List<ResourceInfo> 转换为以逗号分隔的字符串
        return resourceInfoList.stream()
                    .map(Event.ResourceInfo::toString)
                    .collect(Collectors.joining(","));
    }

}
