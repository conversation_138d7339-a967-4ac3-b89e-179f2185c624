package com.baidu.bce.logic.chpc.cluster.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChpcCluster {

    /**
     * 集群名称
     */
    private String clusterId;
    /**
     * 集群名称
     */
    private String name;
    /**
     * 集群描述
     */
    private String description;
    /**
     * vpc id
     */
    private String vpcId;
    /**
     * 子网id
     */
    private String subnetId;
    /**
     * 集群状态
     */
    private String status;
    /**
     * 集群所属的逻辑地域
     */
    private String logicalZone;
    /**
     * 调度器类型
     */
    private String schedulerType;
    /**
     * 是否开启高可用  0-未开启， 1-开启
     */
    private Boolean enableHa;

    /**
     * 集群监控预留字段,一期不开放
     */
    private Boolean enableMonitor = false;


    private String accountId;

    private String securityGroupId;

    private String extra;
}
