package com.baidu.bce.logic.chpc.common;

import java.util.Arrays;

import lombok.Getter;

/**
 * @Author: lilu24
 * @Date: 2022-12-26
 */
@Getter
public enum TaskSourceType {

    INIT_CLUSTER("init_cluster"),

    GROUP_STARTED_TASK("group_started_task"),

    GROUP_CHECK_TASK("group_check_task"),

    DELETE_CLUSTER("delete_cluster"),

    STOP_CLUSTER("stop_cluster"),

    ADD_RESOURCE_TO_CLUSTER("add_resource_to_cluster"),

    ADD_CROMWELL_TO_CLUSTER("add_cromwell_to_cluster"),

    RESET_PASSWORD_TO_CLUSTER("reset_password_to_cluster"),

    ADD_SKYFORM("add_skyform"),

    RUN_WORKFLOW_TASK("run_workflow_task"),

    ADD_GROUP_TO_CLUSTER("add_group_to_cluster"),

    REMOVE_GROUP_FROM_CLUSTER("remove_group_from_cluster"),

    ADD_INSTANCE_TO_CLUSTER("add_instance_to_cluster"),

    REMOVE_INSTANCE_FROM_CLUSTER("remove_instance_from_cluster"),

    ADD_INSTANCE_TO_GROUP("add_instance_to_group"),

    INSTALL_SOFTWARE("install_software"),

    SHRINK_NODES("shrink_nodes"),

    MOVE_NODE_TO_QUEUE("move_node_to_queue"),

    UNINSTALL_SOFTWARE("uninstall_software");

    private final String taskSourceType;

    TaskSourceType(String taskSourceType) {
        this.taskSourceType = taskSourceType;
    }

    public static TaskSourceType fromType(String input) {

        return Arrays.stream(TaskSourceType.values())
                .filter(b -> b.taskSourceType.equals(input))
                .findFirst()
                .orElse(null);
    }
}
