package com.baidu.bce.logic.chpc.domainservice;


import com.baidu.bce.internalsdk.cos.model.ClusterCreateRequest;
import com.baidu.bce.internalsdk.cos.model.ClusterResponse;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.core.util.UuidUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2022-12-07
 */
@Service
public class ClusterService {


    @Resource
    private ClusterDAOGateway clusterDAOGateway;

    public ClusterResponse createClusterResourceWithCos(ClusterCreateRequest clusterCreateRequest) {
        return null;
    }

    public Boolean isExisted(String clusterId, String accountId, String status) {
        return clusterDAOGateway.findByClusterIdAll(clusterId, accountId) != null;
    }

    public Boolean insert(Cluster cluster) {
        return clusterDAOGateway.insert(cluster);
    }


    public Cluster findBy(String clusterId) {
        if (StringUtils.isEmpty(clusterId)) {
            return null;
        }

        return clusterDAOGateway.findByClusterId(clusterId, null);
    }

    public Cluster findByAll(String clusterId) {
        if (StringUtils.isEmpty(clusterId)) {
            return null;
        }

        return clusterDAOGateway.findByClusterIdAll(clusterId, null);
    }

    public List<Cluster> getClustersByStatus(String... status) {

        List<String> statusList = Lists.newArrayList(status);

        if (CollectionUtils.isNotEmpty(statusList)) {
            return clusterDAOGateway.listByStatus(statusList);
        }

        return Collections.emptyList();
    }


    public void updateClusterStatus(String clusterId, String status) {
        clusterDAOGateway.updateClusterStatus(clusterId, status);
    }

    public void updateClusterPassword(String clusterId, String password) {
        clusterDAOGateway.updateClusterPassword(clusterId, password);
    }


    public void updateClusterWithErrorMessage(String clusterId, String status, String message) {
        clusterDAOGateway.updateClusterWithErrorMessage(clusterId, status, message);
    }

    public void deleteWithStatus(String clusterId, ClusterStatus status) {
        clusterDAOGateway.deleteWithStatus(clusterId, status);
    }

    public String generateClusterId() {

        int retryCount = 0;
        while (retryCount < 5) {
            try {
                String clusterId = String.format("c-%s", UuidUtil.generateShortUuid());
                Boolean isExisted = this.isExisted(clusterId, null, null);
                if (!isExisted) {
                    return clusterId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate stack_id in database.");
    }

    public List<Cluster> findByAccountId(String accountId) {
        return clusterDAOGateway.findByAccountId(accountId);
    }

    /**
     * 更新集群名称或描述，是否禁止删除。
     *
     * @param clusterId          集群ID
     * @param clusterName        集群名称（可选），如果为空则不修改集群名称
     * @param clusterDescription 集群描述（可选），如果为空则不修改集群描述
     * @param forbidDelete       是否禁止删除（可选），默认值为false
     * @return Boolean 返回true表示更新成功，否则返回false
     */
    public Boolean updateCluster(String clusterId, String clusterName, Integer maxCpus, Integer maxNodes, String clusterDescription, Boolean forbidDelete) {

        Cluster updateCluster = new Cluster();
        updateCluster.setClusterId(clusterId);
        updateCluster.setDescription(clusterDescription);
        updateCluster.setForbidDelete(forbidDelete);
        if (maxCpus != null) {
            updateCluster.setMaxCpus(maxCpus);
        }
        if (maxNodes != null) {
            updateCluster.setMaxNodes(maxNodes);
        }
        updateCluster.setForbidDelete(forbidDelete);
        updateCluster.setName(clusterName);

        return clusterDAOGateway.update(updateCluster);
    }


    public Boolean delete(String clusterId) {
        return clusterDAOGateway.delete(clusterId);
    }
}
