package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.model.Order;

import java.util.List;

public interface OrderDAOGateway {

    Boolean insert(Order order);

    Boolean update(Order order);

    Boolean delete(String orderId);

    Order findByOrderId(String orderId);

    Order findByAccountIdAndServiceType(String accountId, String serviceType);

    List<Order> findAll(String serviceType);

}
