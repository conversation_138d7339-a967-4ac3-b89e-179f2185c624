package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Data
public class ComputeResource {

    @JsonProperty("Name")
    private String name;


    @JsonProperty("capacityType")
    private String capacityType;


    @JsonProperty("paymentMethod")
    private String paymentMethod;

    private String spec;

    @JsonProperty("autoscalingSettings")
    private AutoscalingSetting autoscalingSetting;

    @JsonProperty("computeNodes")
    private List<ComputeNode> computeNodes;


}
