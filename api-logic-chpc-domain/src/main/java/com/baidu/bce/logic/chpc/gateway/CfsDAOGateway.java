package com.baidu.bce.logic.chpc.gateway;

import java.util.List;

import com.baidu.bce.logic.chpc.model.Cfs;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
public interface CfsDAOGateway {

    Boolean insert(Cfs cfs);

    Long countByClusterId(String clusterId, String cfsId);

    Long countByCluster(String clusterId);

    List<Cfs> findBy(String clusterId, String cfsId);

    Boolean updateByClusterId(String clusterId, String mountTarget, String mountDir, String mountOption);

    Boolean update(Cfs cfs);

    Boolean delete(String clusterId, String cfsId);

    Boolean deleteByMountInfo(String clusterId, String cfsId, String mountTarget, String mountDir);
}
