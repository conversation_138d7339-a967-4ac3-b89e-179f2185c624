package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import lombok.Getter;

import java.util.Arrays;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Getter
public enum CapacityTypeEnum {
    STATIC("STATIC"),
    ONDEMAND("ONDEMAND");

    private final String value;

    CapacityTypeEnum(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static CapacityTypeEnum fromValue(String input) {
        return Arrays.stream(CapacityTypeEnum.values())
                .filter(b -> b.value.equals(input))
                .findFirst()
                .orElse(null);
    }
}
