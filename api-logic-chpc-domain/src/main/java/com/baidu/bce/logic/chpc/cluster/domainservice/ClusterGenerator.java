package com.baidu.bce.logic.chpc.cluster.domainservice;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.cluster.model.IClusterCreateRequest;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.GlobalUuidUtil;
import com.baidu.bce.logic.chpc.common.ZoneUtil;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
@Slf4j
@Component
public class ClusterGenerator {

    @Autowired
    GlobalUuidUtil globalUuidUtil;

    public Cluster genCluster(IClusterCreateRequest request) {
        Cluster cluster = new Cluster();
        cluster.setClusterId(globalUuidUtil.genClusterShortId());
        cluster.setName(request.getClusterName());
        cluster.setDescription(request.getDescription());
        cluster.setVpcId(request.getVpcId());
        cluster.setSubnetId(request.getManagerNode().getSubnetId());
        cluster.setSecurityGroupId(request.getSecurityGroupId());
        cluster.setSchedulerType(request.getSchedulerType());
        cluster.setStatus(ClusterStatus.CREATING.nameLowerCase());
        cluster.setAccountId(LogicUserService.getAccountId());
        cluster.setChargeType(request.getChargeType());
        cluster.setLogicalZone(ZoneUtil.getZoneNumberFromApiZone(request.getZoneName()));
        cluster.setImageId(request.getManagerNode().getImageId());
        cluster.setSpec(request.getManagerNode().getSpec());
        cluster.setClusterType("cloud");
        Map<String, String> extra = new HashMap<>();
        if (CollectionUtils.isNotEmpty(request.getCfsVolumes())) {
            extra.put("mountDirectory", request.getCfsVolumes().get(0).getMountDirectory());
        }
        cluster.setExtra(JacksonUtil.toJson(extra));
        return cluster;
    }

}
