package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class BackendActionProxyResponse {
    @JsonProperty("request_id")
    private String requestId;

    private Integer code;

    private String message;

    private String data;

    @JsonProperty("async_operation")
    private boolean asyncOperation;

    @JsonProperty("taskID")
    private String taskId;

    @JsonProperty("async_operation_type")
    private String asyncOperationType;
}
