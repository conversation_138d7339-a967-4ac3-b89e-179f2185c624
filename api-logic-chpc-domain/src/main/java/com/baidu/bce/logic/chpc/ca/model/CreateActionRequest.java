package com.baidu.bce.logic.chpc.ca.model;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateActionRequest {
    
    // 执行动作。枚举值：SAVE(仅保存），RUN（仅执行），SAVE_AND_RUN（保存并执行）
    private String execution;
    // 执行动作详情
    private Action action;
    // 执行命令时的参数值，仅在命令有参数且需要执行时需要
    private Map<String,String> parameters;
    // 实例选择器类型，仅在执行动作为执行或保存并执行时需要。默认值为INSTANCES_LIST。可选值：INSTANCES_LIST（实例列表），ALL_INSTANCES（全部实例），TAG_INSTANCES（实例标签选择），INSTANCES_IMPORT（实例列表导入）
    private String targetSelectorType;
    // 实例ID列表，仅在targetSelectorType为INSTANCES_LIST时需要
    private List<Target> targets;
    // 实例选择器
    private TargetSelector targetSelector;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Action {
        // 命令ID，仅被保存的命令拥有
        private String id;
        // Action类型。枚举值：COMMAND（命令），FILE_UPLOAD（上传文件）
        private String type;
        // 命令名称，仅被保存的命令拥有
        private String name;
        // 动作描述
        private String description;
        // 动作的超时时间（秒）
        private String timeoutSecond;
        // 命令详情
        private Command command;
        // 文件上传详情
        private FileUpload fileUpload;
        // 命令创建时间。unix时间戳（毫秒）
        private Long createdTimestamp;
        // 命令最后一次被修改时间。unix时间戳（毫秒）
        private Long updateTimestamp;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Command {
        // 脚本类型。枚举值：SHELL，POWERSHELL
        private String type;
        // 命令脚本内容
        private String content;
        // 命令可见范围。GLOBAL表示公共命令，INDIVIDUAL表示个人命令
        private String scope;
        // 命令是否包含参数
        private Boolean enableParameter;
        // 命令参数列表
        private List<Parameter> parameters;
        // 命令在虚机的执行用户
        private String user;
        // 命令在虚机的执行路径
        private String workDir;
        // 命令执行时的实际参数值
        private Map<String, String> execParams;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Parameter {
        // 参数名称
        private String name;
        // 参数描述
        private String desc;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FileUpload {
        // 操作系统。枚举值：LINUX，WINDOWS
        private String os;
        // 文件名称
        private String filename;
        // 目标路径
        private String filepath;
        // bos桶名称
        private String bosBucketName;
        // bos文件路径
        private String bosFilePath;
        // 文件唯一标识符
        private String bosEtag;
        // 是否覆盖目标路径下的同名文件
        private Boolean overwrite;
        // 用户，仅Linux文件上传需要
        private String user;
        // 用户组，仅Linux文件上传需要
        private String group;
        // 文件权限, 仅Linux文件上传需要
        private String mode;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Target {
        // 实例类型。枚举值：BCC，BBC
        private String instanceType;
        // 实例ID列表
        private String instanceId;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TargetSelector {
        // 实例类型。枚举值：BCC，BBC
        private String instanceType;
        // 实例标签列表
        private List<Tag> tags;
        // 实例标签列表
        private TargetImport importInstances;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Tag{
        // 标签Key
        private String tagKey;
        // 标签Value
        private String tagValue;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class TargetImport{
        // 实例列表导入类型。枚举值：instanceId（通过实例ID导入），internalIp表示（通过实例内网导入）
        private String keywordType;
        // 实例清单列表
        private List<String> instances;
    }

}