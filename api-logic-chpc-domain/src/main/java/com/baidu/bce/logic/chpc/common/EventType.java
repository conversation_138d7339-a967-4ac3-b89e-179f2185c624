package com.baidu.bce.logic.chpc.common;

public class EventType {

    // event

    public static final String CREATE_SECURITY_GRUOP = "createSecurityGroup";

    public static final String CREATE_RESOURCE = "createResource";

    public static final String START_PROXY_NODE = "startProxyNode";

    public static final String START_MANAGER_NODE = "startManagerNode";

    public static final String START_COMPUTE_NODE = "startComputeNode";

    public static final String START_LOGIN_NODE = "startLoginNode";

    public static final String CHECK_NETWORK = "checkNetwork";

    public static final String CHECK_SHARED_STORAGE = "checkSharedStorage";

    public static final String CHECK_SCHEDULER = "checkScheduler";

    // status

    public static final String READY_TO_START = "readyToStart";

    public static final String PROGRESSING = "progressing";

    public static final String SUCCEED = "succeed";

    public static final String FAILED = "failed";

    // action

    public static final String ADD_SCHEDULER_INFO = "add_scheduler_info";

    public static final String MOUNT_NFS = "mount_nfs";

    public static final String CHECK_PROXY_ROLE = "check_proxy_role";

    public static final String CHECK_SCHEDULER_INFO = "check_scheduler_info";

    public static final String CHECK_NFS = "check_nfs";

    public static final String CHECK_NETWORK_ACTION = "check_network";


}