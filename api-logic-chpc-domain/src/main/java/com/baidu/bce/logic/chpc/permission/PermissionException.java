package com.baidu.bce.logic.chpc.permission;

import com.baidu.bce.internalsdk.core.HttpStatus;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.internalsdk.exception.BceException;

public class PermissionException {
    /**
     * 无权限 401
     */
    public static class PermissionDenyException extends BceException {
        public PermissionDenyException(String errorMsg) {
            super("CHPC Permission Deny.\n" + errorMsg,
                    HttpStatus.ERROR_PERMISSION_DENY, "PermissionDeny");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class ResourceNotExistException extends BceException {
        public ResourceNotExistException(String errorMsg) {
            super("CHPC Resource Not Exist.\n" + errorMsg,
                    HttpStatus.ERROR_RESOURCE_NOT_EXIST, "ResourceNotExist");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class PermissionDenyExceptionWithEmptyResource extends BceException {
        public PermissionDenyExceptionWithEmptyResource() {
            super("CHPC Permission Deny With Empty Resource List.",
                    HttpStatus.ERROR_PERMISSION_DENY, "PermissionDenyWithEmptyResource");
            setRequestId(LogicUserService.getRequestId());
        }
    }
    public static class AccessNoSpecifiedResource extends BceException {
        public AccessNoSpecifiedResource() {
            super("CHPC Access No Specified Resource.",
                    HttpStatus.ERROR_PERMISSION_DENY, "AccessNoSpecifiedResource");
            setRequestId(LogicUserService.getRequestId());
        }
    }

    public static class AccessNonOwnedResource extends BceException {
        public AccessNonOwnedResource() {
            super("CHPC Access Non Owned Resource.",
                    HttpStatus.ERROR_PERMISSION_DENY, "AccessNonOwnedResource");
            setRequestId(LogicUserService.getRequestId());
        }
    }
}
