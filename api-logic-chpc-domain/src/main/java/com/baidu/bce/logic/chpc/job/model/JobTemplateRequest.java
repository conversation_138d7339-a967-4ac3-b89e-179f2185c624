package com.baidu.bce.logic.chpc.job.model;

import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.codehaus.jackson.annotate.JsonIgnoreProperties;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@NoArgsConstructor
public class JobTemplateRequest extends SubmitJobRequest{

    @Pattern(regexp = "^[0-9a-zA-Z][0-9a-zA-Z_\\-]{0,29}$", message = "name is invalid")
    private String name;

}
