package com.baidu.bce.logic.chpc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import com.baidu.bce.logic.chpc.permission.PermissionConstant;
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface PermissionVerify {
    // 资源ID所在的位置，方便鉴权模块获取资源对象
    ResourceLocation resourceLocation() default ResourceLocation.NO_RESOURCE_ID;

    // 服务号，格式如：bce:bcc, 处理同一接口属于多个产品的，比如：dcc_bcc
    String service() default PermissionConstant.SERVICE_CHPC;

    // 权限列表，如【“CreateCluster”】
    String[] permissions() default {};

    public enum ResourceLocation {
        NO_RESOURCE_ID, // Create/List类接口，资源ID为*

        NO_RESOURCE_ID_CLUSTER_LIST, // 针对集群列表接口

        NO_RESOURCE_ID_QUEUE_LIST, // 针对队列列表接口

        NO_RESOURCE_ID_SERVICE_LIST, // 针对报表接口

        NO_RESOURCE_ID_JOB_LIST, // 针对作业列表接口

        IN_STRING, // 资源ID在请求参数中，为String类型
    }
}
