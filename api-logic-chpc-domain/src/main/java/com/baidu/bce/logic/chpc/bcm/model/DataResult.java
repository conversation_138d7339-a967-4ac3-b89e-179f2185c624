package com.baidu.bce.logic.chpc.bcm.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataResult {
    String region;

    String scope;

    String userId;

    String resourceId;

    String metricName;

    List<ResultDimension> dimensions;

    List<DataPoint> dataPoints;
}
