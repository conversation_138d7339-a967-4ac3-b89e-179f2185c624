package com.baidu.bce.logic.chpc.autoscaling;

public class AutoScalingConst {

    public static final int DEFAULT_MAX_NODES_IN_CLUSTER = 0;
    public static final int DEFAULT_MAX_CPUS_IN_CLUSTER = 0;
    public static final int DEFAULT_MAX_NODES_IN_QUEUE = 0;
    public static final int DEFAULT_MIN_NODES_IN_QUEUE = 0;
    public static final int DEFAULT_MAX_SCALING_PER_CYCLE_IN_QUEUE = 0;
    public static final int DEFAULT_SYSTEM_DISK_SIZE = 40;
    public static final String DEFAULT_SYSTEM_DISK_TYPE = "cloud_hp1";
    public static final int DEFAULT_DATA_DISK_SIZE = 0;

    public static final boolean DEFAULT_ENABLE_AUTO_GROW = false;
    public static final boolean DEFAULT_ENABLE_AUTO_SHRINK = false;


}
