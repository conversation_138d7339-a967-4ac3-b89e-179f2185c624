package com.baidu.bce.logic.chpc.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class WorkflowRun {
    private String name;
    private Boolean callCaching;
    private String failureMode;
    private String inputs;
    private String outputs;
    private String runId;
    private String runUuid;
    private String accountId;
    private String workflowId;
    private String workspaceId;
    private String workflowName;
    private String status;
    private Long version;
    private LocalDateTime updatedTime;
    private LocalDateTime createdTime;

}
