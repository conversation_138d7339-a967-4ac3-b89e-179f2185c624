package com.baidu.bce.logic.chpc.model;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * saas 计费数据
 */
@Data
@NoArgsConstructor
public class ChargeData {
    /**
     * 订单 id
     */
    private String orderId;
    /**
     * 主账户 id
     */
    private String accountId;
    /**
     * 服务类型
     */
    private String serviceType;
    /**
     * 绑定的资源实例
     */
    private String instanceId;
    /**
     * 计费项
     */
    private String chargeItem;
    /**
     * 计费用量
     */
    private String chargeAmount;
    /**
     * 计费所属月份
     */
    private LocalDateTime chargeMonth;
    /**
     * 计费时间戳
     */
    private LocalDateTime chargeTime;
    /**
     * 资源状态
     */
    private Boolean chargeTotal;
    /**
     * 是否有效
     */
    private Boolean valid;
}
