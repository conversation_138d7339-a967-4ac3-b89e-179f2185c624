package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Data
public class ClusterDetail {

    @JsonProperty("queues")
    private List<Queue> queues = new ArrayList<>();

    @JsonProperty("name")
    private String name;


    /**
     * 后端集群状态RUNNING，STOPPING和STOPPED
     */
    @JsonProperty("status")
    private String status;

}
