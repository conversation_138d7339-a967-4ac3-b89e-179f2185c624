package com.baidu.bce.logic.chpc.common;

import lombok.Getter;

@Getter
public enum OrderByType {
    CREATED_TIME_ASC("created_time_asc"),
    CREATED_TIME_DESC("created_time_desc"),
    UPDATED_TIME_ASC("updated_time_asc"),
    UPDATED_TIME_DESC("updated_time_desc"),
    DEFAULT_ORDER_BY("createdTime"),
    DEFAULT_ORDER("desc");

    private final String name;

    OrderByType(String name) {
        this.name = name;
    }
}
