package com.baidu.bce.logic.chpc.billing;

import java.time.LocalDateTime;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResourceUsageRequest {

    private String accountId;
    private String instanceId;
    private String serviceName;
    private String region;
    private String chargeItem;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime startTime;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime endTime;
    private UsageMeta usageMeta;

    @Data
    public static class UsageMeta {
        String minuteReducer;
        String dayReducer;
        String monthReducer;
        boolean removeZero;
    }
}
