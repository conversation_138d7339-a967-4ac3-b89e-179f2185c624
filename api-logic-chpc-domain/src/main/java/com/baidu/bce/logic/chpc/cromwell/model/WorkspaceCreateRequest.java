package com.baidu.bce.logic.chpc.cromwell.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import jakarta.validation.constraints.Pattern;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkspaceCreateRequest {

    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){1,13}[a-zA-Z0-9]$",
            message = "parameter is invalid.")
    private String name;
    @Pattern(regexp = "^.{0,1000}$", message = "is invalid")
    private String description;
    private String clusterId;
    private String bosBucket;

}
