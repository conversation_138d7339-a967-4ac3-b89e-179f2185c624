package com.baidu.bce.logic.chpc.cos.service;

import com.baidu.bce.logic.chpc.cluster.model.IClusterCreateRequest;

import java.util.TreeMap;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
public class CosStackTemplateGeneratorFactory {

    private static TreeMap<String, ICosStackTemplateGenerator> generatorMap = new TreeMap<>();

    public static void register(Class<? extends IClusterCreateRequest> cls, ICosStackTemplateGenerator generator) {
        generatorMap.put(cls.getName(), generator);
    }

    public static ICosStackTemplateGenerator getCosStackTemplateGenerator(IClusterCreateRequest request) {
        return generatorMap.get(request.getClass().getName());
    }

}
