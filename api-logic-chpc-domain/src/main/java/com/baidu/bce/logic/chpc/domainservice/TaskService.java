package com.baidu.bce.logic.chpc.domainservice;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.gateway.TaskDAOGateway;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.core.util.UuidUtil;
import com.google.common.collect.Lists;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Service
public class TaskService {

    @Resource
    private TaskDAOGateway taskDAOGateway;


    public List<Task> getByStatus(String... status) {

        List<String> statusList = Lists.newArrayList(status);

        if (CollectionUtils.isNotEmpty(statusList)) {
            return taskDAOGateway.listByStatus(statusList);
        }

        return Collections.emptyList();
    }

    public List<Task> getByTypeAndStatus(String type, String... status) {

        List<String> statusList = Lists.newArrayList(status);

        if (CollectionUtils.isNotEmpty(statusList)) {
            return taskDAOGateway.listByTypeAndStatus(type, statusList);
        }

        return Collections.emptyList();
    }

    public List<Task> getByTaskUuid(String clusterId, String taskUuid) {

        if (StringUtils.isEmpty(taskUuid)) {
            return Collections.emptyList();
        }

        return taskDAOGateway.listByTaskUuid(clusterId, taskUuid);
    }


    public Boolean insert(TaskType taskType, String clusterId,
                          String queueId, String source, String taskUuid) {

        return this.insert(taskType, clusterId, queueId, source, taskUuid, Collections.emptyMap());
    }

    public Boolean insert(TaskType taskType, String clusterId,
                          String queueId, String taskUuid, Map<String, Object> extra) {

        return this.insert(taskType, clusterId, queueId, "", taskUuid, extra);
    }

    public Boolean insert(TaskType taskType, String clusterId,
                          String queueId, String source, String taskUuid,
                          Map<String, Object> extra) {
        return insert(taskType, clusterId, queueId, source, taskUuid, extra, null);
    }

    public Boolean insert(TaskType taskType, String clusterId,
                          String queueId, String source, String taskUuid,
                          Map<String, Object> extra, String cosStackId) {

        Task task = new Task();
        task.setStatus(TaskStatus.PROCESSING.getValue());
        task.setTaskId(String.format("t-%s", UuidUtil.generateShortUuid()));
        task.setTaskType(taskType.getTaskType());
        task.setClusterId(clusterId);
        task.setQueueId(queueId);
        task.setSource(source);
        task.setTaskUuid(taskUuid);
        task.setExtra(JacksonUtil.toJson(extra));
        if (StringUtils.isNotEmpty(cosStackId)) {
            task.setCosStackId(cosStackId);
        }

        return taskDAOGateway.insert(task);
    }

    public Boolean updateStatus(String taskId, String status) {
        return taskDAOGateway.update(taskId, status, null);
    }

    public Boolean updateStatusByTaskUuid(String taskUuid, String status) {
        return taskDAOGateway.updateByTaskUuid(taskUuid, status, null);
    }

    public Boolean updateExtra(String taskId, Map<String, Object> extra) {
        return taskDAOGateway.update(taskId, null, JacksonUtil.toJson(extra));
    }

    /**
     * @Description 根据任务UUID更新COS堆栈ID，返回一个Boolean值表示是否成功更新。
     * @param taskUuid 任务的UUID字符串，不能为空。
     * @param stackId COS堆栈的ID字符串，可以为空。
     * @return Boolean类型，如果成功更新则返回true，否则返回false。
     */
    public Boolean updateCosStackIdByTaskUuid(String taskUuid, String stackId) {
        return taskDAOGateway.updateStackIdByTaskUuid(taskUuid, stackId);
    }

    /**
     * 更新任务的COS堆栈ID。
     *
     * @param taskId 任务ID，不能为空
     * @param stackId COS堆栈ID，可以为空
     * @return 如果成功更新则返回true，否则返回false
     */
    public Boolean updateCosStackId(String taskId, String stackId) {
        return taskDAOGateway.updateStackId(taskId, stackId);
    }

}


