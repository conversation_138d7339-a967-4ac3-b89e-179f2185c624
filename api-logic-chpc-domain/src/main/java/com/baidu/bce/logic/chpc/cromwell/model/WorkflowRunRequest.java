package com.baidu.bce.logic.chpc.cromwell.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import jakarta.validation.constraints.Pattern;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowRunRequest {
    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){1,48}[a-zA-Z0-9]$",
            message = "parameter is invalid.")
    private String name;
    private Boolean callCaching = true;
    private String failureMode = "ContinueWhilePossible";
    private String inputs;
    private Long version;
    private String workflowId;
}
