package com.baidu.bce.logic.chpc.model;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SoftwareRecord {
    /**
     * 集群名称
     */
    private String clusterId;
    /**
     * 软件名称
     */
    private String name;
    /**
     * 软件版本
     */
    private String version;

    private String oosExecutionId;
    /**
     * 软件状态
     */
    private String status;
    /**
     * 失败原因
     */
    private String msg;
    /**
     * 安装节点ID
     */
    private String installedInstanceId;

    private boolean deleted;

    private LocalDateTime updatedTime;
    
    private LocalDateTime createdTime;

}