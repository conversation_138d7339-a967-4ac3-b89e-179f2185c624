package com.baidu.bce.logic.chpc.model;

import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.HostDetail;
import com.baidu.bce.logic.chpc.tag.Tag;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@NoArgsConstructor
public class Job {
    /**
     * 作业id
     */
    private String clusterId;
    /**
     * 作业id
     */
    private String jobId;
    /**
     * 作业名
     */
    private String jobName;
    /**
     * 作业状态
     */
    private String jobState;
    /**
     * 是否为数组作业
     */
    private Boolean jobArray;
    /**
     * 作业优先级
     */
    private Long priority;
    /**
     * 作业队列名
     */
    private String queue;
    /**
     * 作业提交用户
     */
    private String jobOwner;
    /**
     * 作业输出路径
     */
    private String outputPath;
    /**
     * 作业错误路径
     */
    private String errorPath;
    /**
     * 作业信息
     */
    private String comment;
    /**
     * 作业执行节点
     */
    private String execHost;
    /**
     * 作业文件输入路径
     */
    private String chpcInputPath;
    /**
     * 作业文件输出路径
     */
    private String chpcOutputPath;
    /**
     * 作业创建时间
     */
    private Long ctime;
    /**
     * 作业修改时间
     */
    private Long mtime;
    /**
     * 作业开始时间
     */
    private Long stime;
    /**
     * 作业节点列表
     */
    private List<HostDetail> hostList;
    /**
     * 作业标签
     */
    private List<Tag> tags;
}
