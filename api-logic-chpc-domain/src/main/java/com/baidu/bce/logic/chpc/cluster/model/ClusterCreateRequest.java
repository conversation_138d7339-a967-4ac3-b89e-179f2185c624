package com.baidu.bce.logic.chpc.cluster.model;

import java.util.List;

import com.baidu.bce.logic.chpc.tag.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;

import com.baidu.bce.logic.chpc.software.model.SoftwareVersion;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ClusterCreateRequest {

        // 集群参数
        @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){1,13}[a-zA-Z0-9]$", message = "clusterName is invalid.")
        String clusterName; // 集群名称

        // @Pattern(regexp = "^.{0,250}$", message = "is invalid")
        String description; // 集群描述

        String zoneName; // 例如：cn-bj-d

        String vpcId;

        String securityGroupId;

        String securityGroupType = "normal"; // 默认为普通安全组

        // 管理节点信息
        NodeInfo masterSpec;

        // 登录节点信息
        NodeInfo loginSpec;

        boolean enableHa; // 管控节点是否开启高可用

        String password;

        /**
         * 密钥对登录
         */
        String keypairId;

        // 网络
        boolean useEip = false;

        int networkCapacityInMbps = 1;

        String internetChargeType;

        @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9-_/.]){0,64}", message = "eipName is invalid.")
        String eipName;

        // 存储 & 共享存储
        List<MountInfo> mountList;

        List<QueueInfo> queueList;

        @Pattern(regexp = "^(cloud|hybrid)$", message = "clusterType is invalid.")
        String clusterType = "cloud"; // 默认为公有云集群

        String schedulerVersion;

        int schedulePlugin = 1;

        /**
         * 作业调度器类型
         * - slurm
         * - sge
         */
        @Pattern(regexp = "^(slurm|sge|pbs|openpbs)$", message = "schedulerType is invalid.")
        String schedulerType = "slurm";

        String schedulePluginVersion = "0.1.0";

        String softwareDir = "/home/<USER>";

        String schedulerIp; // 调度器节点 ip（混合云使用）

        String schedulerHost; // 调度器节点 host（混合云使用）

        Integer maxNodes = 100;

        Integer maxCpus = 10000;

        Boolean forbidDelete = false; // 开启删除保护

        List<SoftwareVersion> softwareList; // 自定义安装的软件列表

        Long sshPort = 22L;

        Long portalPort = 12011L;

        // 集群使用的域账号
        String domainAccount;

        // 集群绑定的Tags
        @Valid
        List<Tag> tags;

}
