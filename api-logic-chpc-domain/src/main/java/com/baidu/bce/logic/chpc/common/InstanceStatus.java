package com.baidu.bce.logic.chpc.common;

public enum InstanceStatus {

    CREATING,   // 虚机创建中

    WAITING_TO_START,   // 虚机创建成功

    WAITING_TO_MANUAL_START,   // 等待手动启动

    STARTING,   // oos下发启动调度器命令后

    STARTED,    // 调度器启动成功

    RUNNING,    // 运行中

    OPERATING,    // 操作中（slurm移动队列）

    INSTALLING, // 安装cromwell

    RESETING, // 重置中

    // 已经下发oos重置命令，这种状态对于客户来说，还是RESETING状态，只是内部区分有没有下发oos命令

    RESETED,

    EXPIRED,    // 过期或欠费

    SNAPSHOTPROCESSING, // 快照操作中

    IMAGEPROCESSING, // 镜像操作中

    RECHARGING, // 续费中

    DELETED,     // 虚机删除

    DELETING;   // 虚机删除中

    public String nameLowerCase() {
        return name().toLowerCase();
    }

}
