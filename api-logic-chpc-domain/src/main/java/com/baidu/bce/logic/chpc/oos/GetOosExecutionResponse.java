package com.baidu.bce.logic.chpc.oos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetOosExecutionResponse {
    private boolean success;

    /**
     * 若失败，返回失败原因
     */
    private String msg;

    private Result result;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result {
        String id; // d-xxx

        Timestamp createdTimestamp;

        Timestamp updatedTimestamp;

        Timestamp finishedTimestamp;

        /**
         * 执行状态，可选值：
         * - PENDING          - 等待中
         * - RUNNING          - 运行中
         * - SUCCESS          - 运行成功
         * - FAILED           - 运行失败
         * - PAUSED           - 暂停
         * - CANCELED         - 已取消
         * - ROLLBACK         - 回滚中
         * - ROLLBACK_SUCCESS - 回滚成功
         * - ROLLBACK_FAILED  - 回滚失败
         */
        String state;

        /**
         * 执行处于当前状态的原因
         */
        String reason;

        List<Task> tasks;


    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Task {
        String id;

        /**
         * 任务所属执行
         */
        Dag dag;

        Timestamp createdTimestamp;

        Timestamp updatedTimestamp;

        Timestamp finishedTimestamp;

        /**
         * 任务状态，可选值：
         * - PENDING         - 等待中
         * - RUNNING         - 运行中
         * - SUCCESS         - 运行成功
         * - FAILED          - 运行失败
         * - UP_FOR_RETRY    - 等待重试
         * - UPSTREAM_FAILED - 上游节点失败
         */
        String state;

        CreateOosExecutionRequest.Operator operator;

    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Dag {
        String id; // d-xxx
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OutputContext {
        int exitCode;

        String stderr;

        String stdout;
    }


}
