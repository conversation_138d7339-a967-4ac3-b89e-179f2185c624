package com.baidu.bce.logic.chpc.cluster.domainservice;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.chpc.cfs.CfsAddRequest;
import com.baidu.bce.logic.chpc.cfs.MountTarget;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.chpc.common.NumberUtils;
import com.baidu.bce.logic.chpc.common.ZoneUtil;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.WhitelistGateway;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.chpc.user.Const;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
@Slf4j
public abstract class CommonClusterCreateRequestValidator implements IClusterCreateRequestValidator {

    @Autowired
    protected CfsGateway cfsGateway;

    @Autowired
    protected SubnetGateway subnetGateway;

    @Autowired
    protected ClusterDAOGateway clusterDAOGateway;

    @Autowired
    protected WhitelistGateway whitelistGateway;


    public void validateCfsParameters(CfsAddRequest cfsAddRequest, String vpcId) {
        // cfsMountPoint 不为空，cfsId必须不为空
        if (StringUtils.isNotEmpty(cfsAddRequest.getCfsMountPoint()) && StringUtils.isEmpty(cfsAddRequest.getCfsId())) {
            throw new CommonExceptions.RequestInvalidException("cfsId is empty.");
        }

        if (StringUtils.isEmpty(cfsAddRequest.getCfsId())) {
            log.debug("cfsId is empty.");
            return;
        }

        List<MountTarget> cfsMountPoints;
        try {
            cfsMountPoints = cfsGateway.getCfsMountPoint(cfsAddRequest.getCfsId());
        } catch (WebClientResponseException e) {
            throw new CommonException.CfsServiceException(e.getResponseBodyAsString());
        } catch (Exception e) {
            throw new CommonException.CfsServiceException(e.getMessage());
        }

        if (CollectionUtils.isEmpty(cfsMountPoints)) {
            throw new CommonExceptions.RequestInvalidException("The cfsId " + cfsAddRequest.getCfsId() +
                    " does not have mount point.");
        }
        MountTarget mountTarget = null;
        for (MountTarget target : cfsMountPoints) {
            if (target.getDomain().equals(cfsAddRequest.getCfsMountPoint())) {
                mountTarget = target;
                break;
            }
        }
        if (mountTarget == null) {
            throw new CommonException.ResourceNotExistException(cfsAddRequest.getCfsMountPoint() + " does not exist.");
        }

        SubnetVo subnet = subnetGateway.getSubnet(mountTarget.getSubnetId());
        if (!subnet.getVpcShortId().equalsIgnoreCase(vpcId)) {
            throw new CommonExceptions.RequestInvalidException("The cfsMountPoint " + cfsAddRequest.getCfsMountPoint() +
                    "'s vpcId " + subnet.getVpcShortId() + " does not match " + vpcId);
        }
    }

    protected void validInstanceCount(int managerCount) {
        if (managerCount < 1 || managerCount > 2) {
            throw new CommonExceptions.RequestInvalidException("managerCount range from 1 to 2");
        }
    }

    public static void validateZoneName(String zoneName) {
        if (StringUtils.isEmpty(zoneName)) {
            throw new CommonExceptions.RequestInvalidException("zoneName should not empty.");
        }
        // 暂时只有api，后续有console时，需要判断来源
        if (ZoneUtil.isNotMatchApiZone(zoneName)) {
            throw new CommonExceptions.RequestInvalidException("zoneName illegal.");
        }
    }

    protected void validateQuota(int computeCount) {

        Map<String, String> quotaType2Quota = whitelistGateway.getQuota(LogicUserService.getAccountId(),
                Const.Quota.CHPC_CLUSTER_QUOTA,
                Const.Quota.CHPC_COMPUTE_NODE_QUOTA);

        int clusterQuota = NumberUtils.parseIntWithDefault(quotaType2Quota.get(Const.Quota.CHPC_CLUSTER_QUOTA),
                Const.DefaultQuota.CHPC_CLUSTER_DEFAULT_QUOTA);

        int computeNodeQuota = NumberUtils.parseIntWithDefault(quotaType2Quota.get(Const.Quota.CHPC_COMPUTE_NODE_QUOTA),
                Const.DefaultQuota.CHPC_COMPUTE_NODE_DEFAULT_QUOTA);

        Long clusterCount = clusterDAOGateway.count(null, LogicUserService.getAccountId(), null);

        if (clusterCount >= clusterQuota) {
            log.debug("chpc_cluster_quota is: {}, cluster created: {}", clusterQuota, clusterCount);
            throw new CommonException.QuotaException("Exceed cluster quota: " + clusterQuota);
        }

        if (computeCount > computeNodeQuota) {
            log.debug("chpc_compute_quota is: {}, compute create: {}", computeNodeQuota, computeCount);
            throw new CommonException.QuotaException("Exceed compute node quota: " + computeNodeQuota);
        }

    }
}
