package com.baidu.bce.logic.chpc.cromwell;

import com.baidu.bce.logic.chpc.common.CromwellFileType;
import com.baidu.bce.logic.chpc.common.GlobalUuidUtil;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowCreateRequest;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowUpdateRequest;
import com.baidu.bce.logic.chpc.cromwell.model.WorkspaceCreateRequest;
import com.baidu.bce.logic.chpc.model.Workflow;
import com.baidu.bce.logic.chpc.model.Workflowfile;
import com.baidu.bce.logic.chpc.model.Workspace;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class Generator {
    @Autowired
    GlobalUuidUtil globalUuidUtil;

    public Workspace genWorkspace(WorkspaceCreateRequest request) {
        Workspace workspace = new Workspace();
        workspace.setWorkspaceId(globalUuidUtil.genWorkspaceShortId());
        workspace.setClusterId(request.getClusterId());
        workspace.setName(request.getName());
        workspace.setDescription(request.getDescription());
        workspace.setBosBucket(request.getBosBucket());
        return workspace;
    }

    public Workflow genWorkflow(WorkflowCreateRequest request) {
        Workflow workflow = new Workflow();
        workflow.setWorkflowId(globalUuidUtil.genWorkflowShortId());
        workflow.setWorkspaceId(request.getWorkspaceId());
        workflow.setName(request.getName());
        workflow.setDescription(request.getDescription());
        workflow.setVersion(Long.valueOf(1));
        workflow.setLanguage(request.getLanguage());
        workflow.setLanguageVersion(request.getLanguageVersion());
        workflow.setIntroduction(request.getIntroduction());
        workflow.setDocument(request.getDocument());
        if (request.getInputs() != null && !"".equals(request.getInputs())){
            workflow.setInputs(request.getInputs());
        }

        return workflow;
    }

    public List<Workflowfile> genWorkflowfileList(String workflowId, WorkflowCreateRequest request) {

        List<Workflowfile> workflowfileList = new ArrayList<>();

        Workflowfile mainflowfile = new Workflowfile();
        mainflowfile.setWorkflowId(workflowId);
        mainflowfile.setType(CromwellFileType.MAIN_FILE.nameLowerCase());
        mainflowfile.setPath(request.getMainFilePath());
        mainflowfile.setVersion(Long.valueOf(1));
        mainflowfile.setContent(request.getMainFileContent());

        workflowfileList.add(mainflowfile);

        if (request.getConfigFilePath() != null) {
            Workflowfile configflowfile = new Workflowfile();
            configflowfile.setWorkflowId(workflowId);
            configflowfile.setType(CromwellFileType.CONFIG_FILE.nameLowerCase());
            configflowfile.setPath(request.getConfigFilePath());
            configflowfile.setVersion(Long.valueOf(1));
            configflowfile.setContent(request.getConfigFileContent());

            workflowfileList.add(configflowfile);
        }
        if (request.getDepFiles() != null) {
            for (DepFile depFile : request.getDepFiles()) {
                Workflowfile depflowfile = new Workflowfile();
                depflowfile.setWorkflowId(workflowId);
                depflowfile.setType(CromwellFileType.DEP_FILE.nameLowerCase());
                depflowfile.setPath(depFile.getDepFilePath());
                depflowfile.setVersion(Long.valueOf(1));
                depflowfile.setContent(depFile.getDepFileContent());
                workflowfileList.add(depflowfile);
            }
        }

        return workflowfileList;
    }

    public List<Workflowfile> genUpdateWorkflowfileList(String workflowId, WorkflowUpdateRequest request, Long version) {

        List<Workflowfile> workflowfileList = new ArrayList<>();

        if (null != request.getMainFileContent() && !"".equals(request.getMainFileContent())) {
            Workflowfile mainflowfile = new Workflowfile();
            mainflowfile.setWorkflowId(workflowId);
            mainflowfile.setType(CromwellFileType.MAIN_FILE.nameLowerCase());
            mainflowfile.setPath(request.getMainFilePath());
            mainflowfile.setVersion(version);
            mainflowfile.setContent(request.getMainFileContent());
            workflowfileList.add(mainflowfile);
        }


        if (request.getConfigFilePath() != null) {
            Workflowfile configflowfile = new Workflowfile();
            configflowfile.setWorkflowId(workflowId);
            configflowfile.setType(CromwellFileType.CONFIG_FILE.nameLowerCase());
            configflowfile.setPath(request.getConfigFilePath());
            configflowfile.setVersion(version);
            configflowfile.setContent(request.getConfigFileContent());

            workflowfileList.add(configflowfile);
        }

        if (request.getDepFiles() != null) {
            for (DepFile depFile : request.getDepFiles()) {
                Workflowfile depflowfile = new Workflowfile();
                depflowfile.setWorkflowId(workflowId);
                depflowfile.setType(CromwellFileType.DEP_FILE.nameLowerCase());
                depflowfile.setPath(depFile.getDepFilePath());
                depflowfile.setVersion(version);
                depflowfile.setContent(depFile.getDepFileContent());
                workflowfileList.add(depflowfile);
            }
        }

        return workflowfileList;
    }
}
