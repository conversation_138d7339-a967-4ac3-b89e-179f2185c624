package com.baidu.bce.logic.chpc.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class Workflow {

    private String name;
    private String description;
    private String language;
    private String languageVersion;
    private String workspaceId;
    private Long version;
    private String workflowId;
    private String accountId;
    private String mainFileContent;
    private String mainFilePath;
    private String configFileContent;
    private String configFilePath;
    private String introduction;
    private String document;
    private String inputs;
    private LocalDateTime updatedTime;
    private LocalDateTime createdTime;
}
