package com.baidu.bce.logic.chpc.common;

/**
 * @Author: lilu24
 * @Date: 2023-01-05
 */
public class ChpcConstant {

    public static final String INSTANCE_NODE_TYPE = "nodeType";

    public static final String BACKEND_TASK_ID = "backendTaskId";

    public static final String BACKEND_INSTANCE_ID = "backendInstanceId";

    public static final String BACKEND_INSTANCE_UUID = "backendInstanceUuId";

    public static final String BACKEND_HOSTNAME = "backendHostname";

    public static final String BACKEND_COS_STACK_ID = "backendCosStackId";

    public static final String AUTO_SCALING_FLAG = "autoScalingFlag";

    public static final String SHRINK_CYCLE = "shrinkCycle";

    public static final String MANUAL_SCALING_FLAG = "manualScalingFlag";

    public static final String RELEASE_RESOURCE_FLAG = "releaseResourceFlag";

    public static final String CLUSTER_DELETED_TASK = "clusterDeletedTask";

    public static final String TYPE_EXISTED = "typeExisted";

    public static final String INSTANCE_ID = "instanceId";

    public static final String SOURCE_TASK_ID = "sourceTaskId";

    public static final String TASK_STATUS = "taskStatusId";

    public static final String TASK_FAILED_REASON = "taskFailedReason";

    public static final String CLUSTER_TYPE = "clusterType";

    public static final String NEXT_TASK = "nextTask";

    public static final String TASK_WAIT_TIMES = "taskWaitTimes";

    public static final String WORKSPACE_ID = "workspaceId";

    public static final String PFS_ID = "pfsId";

    public static final String FILESET_ID = "filesetId";

    public static final String ACCOUNT_ID = "accountId";

    public static final String WORKFLOW_RUN_UUID = "runUuid";

    public static final String WORKFLOW_RUNID = "runId";

    public static final String SOFTWARE_NAME = "softwareName";

    public static final String SOFTWARE_VERSION = "softwareVersion";

    public static final String TASK_RETRY_TIMES = "taskRetryTimes";

    public static final String RESOURCE_LEAK = "ResourceLeak";

    public static final Integer HTTP_STATUS_200 = 200;

    public static final Integer HTTP_STATUS_500 = 500;

    public static final Integer BCC_MAX_COUNT = 1000;

    public static final String SERVICE_TYPE_CHPC = "CHPC";

    public static final String SERVICE_TYPE_TAG = "Tag";

    public static final Integer NUCLEAR_STORAGE_RATIO = 8;

    public static final String CHPC_ACCOUNTID = "1545b9ab1a0f46b2bf5ebf32461987fe";

    public static final String CHPC_SANDBOX_ACCOUNTID = "********************************";

    public static final String CHPC_DEFAULT_SECURITY_GROUP_NAME = "CHPC-默认安全组";

    public static final String CHPC_NFS_PORT = "2049";

    public static final String CHPC_PBS_PORT = "15001";

    public static final String CHPC_CLUSTER_API_PORT = "8100";

    public static final String CHPC_APP_SERVER_PORT = "12011";

    public static final String CHPC_APP_SERVER_HTTPS_PORT = "12012";

    public static final String CHPC_WEBSOCKIFY_PORT = "8780";

    public static final String CHPC_CUDA_VERSION = "cudaVersion";

    public static final String CHPC_GPU_DRIVER_VERSION = "gpuDriverVersion";

    public static final String CHPC_CUDNN_VERSION = "cudnnVersion";

    public static final String SECURITY_GROUP_RULE_DUPLICATED_ERR_MSG = "Security group rule is duplicated";

    public static final String ESG_RULE_DUPLICATED_ERR_MSG = "Enterprise security group rule exist already";

    public static final String SECURITY_GROUP_TYPE_NORMAL = "normal";

    public static final String SECURITY_GROUP_TYPE_ENTERPRISE = "enterprise";

    public static final String QUEUE_IDS = "queueIds";

    public static final String QUEUE_NAMES = "queueNames";

    public static final String SOFTWARE_LIST = "softwareList";

    public static final String CHPC_APP_SERVER = "chpc-app-server";

    public static final String CHPC_VNC_SERVER = "vncserver";

    public static final String INSTANCE_IDS = "instanceIds";

    public static final String QUEUE_NAME = "queueName";

    public static final String INSTANCE_HOSTNAME_LIST = "instanceHostnameList";

    public static final String INSTANCE_SPEC_LIST = "instanceSpecList";

    public static final String COS_STACK_NAME = "stackName";

    public static final String CLUSTER_TAGS = "clusterTags";

    public static final String NODES_TAGS = "nodesTags";

    public static final String SHRINK_HOSTNAMES = "shrinkHostnames";

    public static final String SHRINK_NUM = "shrinkNum";

    public static final String SUCC_SHRINK_HOSTNAMES = "succShrinkHostnames";

    public static final String SHRINK_MESSAGE = "shrinkMessage";

    public static final String OPEN_LDAP = "OpenLDAP";

    public static final String PASSWORD = "password";

    public static final String AESPASSWORD = "aespassword";

    public static final String BCT_ACTION_SHRINK = "AutoShrink";

    public static final String BCT_ACTION_EXPAND = "AutoExpand";

    public static final String ADD_INSTANCE_COUNT = "AddInstanceCount";

    public static final String OOS_EXECUTION_ID = "oosExecutionId";

    public static final String START_FAILED_INSTANCES = "startFailedInstances";

    public static final String GPU_DRIVER_VERSION = "gpuDriverVersion";

    public static final String CUDA_VERSION = "cudaVersion";

    public static final String CUDNN_VERSION = "cudnnVersion";

}
