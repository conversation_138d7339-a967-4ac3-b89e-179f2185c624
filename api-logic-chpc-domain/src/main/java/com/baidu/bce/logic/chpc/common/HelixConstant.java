package com.baidu.bce.logic.chpc.common;

public class HelixConstant {
    public static final String MINIPROTEIN_DESIGN = "miniprotein_design";

    public static final String ANTIBODY_DESIGN = "antibody_design";

    public static final String HF3AGAB = "hf3agab";

    public static final String HELIXFOLD3 = "helixfold3";

    public static final String HELIXVS = "helixvs";

    public static final String HELIXVS_SYN = "helixvs_syn";

    public static final String LINEAR_DESIGN = "linear_design";

    public static final String LINEAR_FOLD = "linear_fold";

    public static final String LINEAR_PARTITION = "linear_partition";

    public static final String HELIXFOLD3_ORDER_KEY = "helixfold3-service";

    public static final String HELIXVS_ORDER_KEY = "helixvs-service";

    public static final String HELIX_CPU_ORDER_KEY = "helix-cpu";

    public static final String HELIXVS_CHEMDIV = "ChemDiv";

    public static final String HELIXVS_CHEMDIV_NORMAL = "ChemDivNormal";

    public static final String HELIXVS_LIFECHEMICALS = "Lifechemicals";

    public static final String HELIXVS_LIFECHEMICALS_NORMAL = "LifechemicalsNormal";

    public static final String HELIXVS_TARGETMOL = "Targetmol_CherryPick";

    public static final String HELIXVS_TARGETMOL_NORMAL = "TargetmolNormal";

    public static final String HELIXVS_TOPSCIENCE_DATABASE = "topscience database";

    public static final String HELIXVS_TOPSCIENCE_DATABASE_NORMAL = "TopscienceDatabaseNormal";

    public static final String HELIXVS_MOCK_TASK = "mock_task_id";


    public static final int HELIXFOLD3_SUCCEED_TASK_STATUS = 0;

    public static final int HELIXFOLD3_FAILED_TASK_STATUS = 1;

    public static final int HELIXFOLD3_CANCEL_TASK_STATUS = 2;


}
