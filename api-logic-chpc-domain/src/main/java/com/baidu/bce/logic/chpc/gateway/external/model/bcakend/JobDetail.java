package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class JobDetail {
    /**
     * 作业id
     */
    @JsonProperty("job_id")
    private String jobId;
    /**
     * 作业名
     */
    @JsonProperty("job_name")
    private String jobName;
    /**
     * 作业状态
     */
    @JsonProperty("job_state")
    private String jobState;
    /**
     * 作业优先级
     */
    @JsonProperty("priority")
    private Long priority;
    /**
     * 作业队列名
     */
    @JsonProperty("queue")
    private String queue;
    /**
     * 作业提交用户
     */
    @JsonProperty("job_owner")
    private String jobOwner;
    /**
     * 作业输出路径
     */
    @JsonProperty("output_path")
    private String outputPath;
    /**
     * 作业错误路径
     */
    @JsonProperty("error_path")
    private String errorPath;
    /**
     * 作业文件输入路径
     */
    @JsonProperty("chpc_input_path")
    private String chpcInputPath;
    /**
     * 作业文件输出路径
     */
    @JsonProperty("chpc_output_path")
    private String chpcOutputPath;
    /**
     * 是否是数组任务
     */
    @JsonProperty("job_array")
    private Boolean jobArray;
    /**
     * 作业信息
     */
    @JsonProperty("comment")
    private String comment;
    /**
     * 作业执行节点
     */
    @JsonProperty("exec_host")
    private String execHost;
    /**
     * 作业创建时间
     */
    @JsonProperty("ctime")
    private Long ctime;
    /**
     * 作业修改时间
     */
    @JsonProperty("mtime")
    private Long mtime;
    /**
     * 作业开始时间
     */
    @JsonProperty("stime")
    private Long stime;

    /**
     * 作业执行时间
     */
    @JsonProperty("run_time")
    private Long runTime;
    /**
     * 作业所在节点列表
     */
    @JsonProperty("host_list")
    List<HostDetail> hostList;
    /**
     * 作业标签
     */
    @JsonProperty("tags")
    List<Tag> tags;
}
