package com.baidu.bce.logic.chpc.cromwell.model;

import com.baidu.bce.logic.chpc.cromwell.DepFile;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import jakarta.validation.constraints.Size;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowUpdateRequest {

    @Size(max = 1000, message = "is invalid")
    private String description;
    private String mainFileContent;
    private String mainFilePath;
    private List<DepFile> depFiles;
    private String configFileContent;
    private String configFilePath;
    private String introduction;
    private String document;
    private String inputs;
}
