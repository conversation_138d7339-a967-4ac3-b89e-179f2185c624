package com.baidu.bce.logic.chpc.gateway;

import java.util.List;

import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.model.Instance;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
public interface InstanceDAOGateway {

    Boolean batchInsert(List<Instance> instances);

    List<Instance> findBy(String clusterId, String queueId,
                          String instanceNodeType, String cosStackId,
                          List<String> statusList);

    List<Instance> findByClusterIdAndQueueId(String clusterId, String queueId, int offset, int limit);

    List<Instance> findAllByClusterIdAndQueueId(String clusterId, String queueId);

    List<Instance> findAllByClusterIdAndQueueIdAndChargeType(String clusterId, String queueId, ChargeType chargeType);

    List<Instance> findByClusterId(String clusterId);

    List<Instance> findByClusterIdIn(List<String> clusterIds);


    Instance findBy(String instanceId);

    Instance findByHostName(String hostName);

    Instance findByHostNameIgnoreDeleted(String hostName);

    List<Instance> findBy(List<String> instanceIds);

    List<Instance> findAllByClusterIdAndInstanceIds(String clusterId, List<String> instanceIds);

    List<Instance> findAllByHostNames(List<String> instanceIds);

    List<Instance> findAllByClusterIdAndHostNames(String clusterId, List<String> hostnames);

    Instance findMasterInstance(String clusterId);

    Boolean updateStatus(String instanceId, String oosExecutionId, String status);

    Boolean updateSchedulerConfig(String clusterId, String instanceType, String schedulerIp, String schedulerHost);

    Boolean update(Instance instance);

    Boolean updateByStatus(Instance instance, List<String> status);

    Boolean delete(String instanceId);

    Long count(String clusterId, String queueId,
               String instanceId, String hostname, String status, String nodeType);

    Long countComputeNode(String queueId, String nodeType);
}
