package com.baidu.bce.logic.chpc.skyform;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetTenantResponse {
    private Integer code;
    private String msg;
    private RetObj retObj;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RetObj {
        private String uuid;
        private String tenantName;
        private String tenantCode;
        private String companyName;
        private String companyCode;
        private String companyId;
        private String contactName;
        private String contactTel;
        private String contactAddress;
        private String status;
        private String description;
        private String createBy;
        private String createTime;
        private String updateBy;
        private String updateTime;
        private String quota;
        private String isGenUsergrp;
    }
}