package com.baidu.bce.logic.chpc.cos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateStackResponse {
    boolean success;

    /**
     * 若失败，返回失败原因
     */
    String msg;


    StackResult result;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class StackResult {

        /**
         * 栈ID
         */
        String id;

    }



}
