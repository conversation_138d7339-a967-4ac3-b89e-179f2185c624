package com.baidu.bce.logic.chpc.common;

import java.util.HashMap;
import java.util.Map;

public class SchedulerPriorityRange {
    public static Map<String, Long[]> schedulerPriorityRangeMap = new HashMap<String, Long[]>(){
        {
            put(ClusterSchedulerType.SLURM.getName(), new Long[]{0L, 4294967293L});
            put(ClusterSchedulerType.SGE.getName(), new Long[]{-1023L, 1024L});
            put(ClusterSchedulerType.PBS.getName(), new Long[]{-1024L, 1023L});
            put(ClusterSchedulerType.OPENPBS.getName(), new Long[]{-1024L, 1023L});
        }
    };
}
