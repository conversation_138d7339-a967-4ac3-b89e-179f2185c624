package com.baidu.bce.logic.chpc.permission;

public class PermissionConstant {
    /**
     * 资源类型
     */
    public static final String RESOURCE_TYPE_NORMAL = "NORMAL";
    public static final String RESOURCE_TYPE_CLUSTER_LIST = "CLUSTER_LIST";
    public static final String RESOURCE_TYPE_QUEUE_LIST = "QUEUE_LIST";
    public static final String RESOURCE_TYPE_SERVICE_LIST = "SERVICE_LIST";
    public static final String RESOURCE_TYPE_JOB_LIST = "JOB_LIST";

    /**
     * 资源前缀
     */
    public static final String RESOURCE_CLUSTER_PREFIX = "cluster/";
    public static final String RESOURCE_QUEUE_PREFIX = "queue/";
    public static final String RESOURCE_SERVICE_PREFIX = "service/";
    public static final String RESOURCE_JOB_PREFIX = "job/";

    /**
     * 服务号
     */
    public static final String SERVICE_CHPC = "bce:chpc";

    public static final String DEFAULT_CREATE_RESOURCE = "*";

    public static final String RESOURCE_OWNER_FOR_CREATING = "USE_REQUESTER_ACCOUNTID";

    public static final String ALLOW_PERMISSION = "ALLOW";
    public static final String DENY_PERMISSION  = "DENY";

    /**
     * 系统权限
     */
    public static final String CHPC_READ    = "READ";
    public static final String CHPC_OPERATE = "OPERATE";
    public static final String CHPC_CONTROL = "FULL_CONTROL";

    /**
     * 自定义权限
     */
    public static final String CLUSTER_READONLY = "CLUSTER_READONLY";
    public static final String CLUSTER_MODIFY   = "CLUSTER_MODIFY";

    public static final String QUEUE_READONLY = "QUEUE_READONLY";
    public static final String QUEUE_MODIFY   = "QUEUE_MODIFY";

    public static final String SERVICE_READONLY = "SERVICE_READONLY";
    public static final String SERVICE_MODIFY   = "SERVICE_MODIFY";

    public static final String OTHER_READONLY = "OTHER_READONLY";
    public static final String OTHER_MODIFY   = "OTHER_MODIFY";

    public static final String JOB_SPEC   = "JOB_SPEC";
    public static final String JOB_ALL   = "JOB_ALL";
}
