package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.model.UserCallbackTask;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2024-03-24
 */
public interface UserCallbackTaskDAOGateway {
    Boolean insert(UserCallbackTask task);

    Boolean update(String taskId, String status);

    List<UserCallbackTask> listByTaskId(String clusterId, String taskId);

    List<UserCallbackTask> listByJobIdAndStatus(String clusterId, List<String> jobId, String status);
}
