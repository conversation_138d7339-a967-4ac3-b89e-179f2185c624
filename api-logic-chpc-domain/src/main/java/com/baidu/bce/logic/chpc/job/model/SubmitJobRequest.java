package com.baidu.bce.logic.chpc.job.model;

import java.util.List;
import java.util.Map;

import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.Valid;
import lombok.Data;
import jakarta.validation.constraints.Pattern;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode
@NoArgsConstructor
public class SubmitJobRequest {

    @Pattern(regexp = "^[a-zA-Z0-9_\\-.]{0,30}$", message = "jobName is invalid.")
    private String jobName = "";

    @Pattern(regexp = "^[A-Za-z0-9\\-\\s\\_\\.\\/\\$]{0,512}$", message = "jobCmd is invalid.")
    private String jobCmd;

    private String queue = "";

    @Pattern(regexp = "^[A-Za-z0-9\\-\\_\\.\\/\\$]{0,512}$", message = "postCmd is invalid.")
    private String postCmd = "";

    private Integer nhosts = 0;

    private Integer ncpus = 0;

    private Integer limitTimeInMinutes = 0;

    @Pattern(regexp = "^[A-Za-z0-9\\-\\_\\.\\/\\$]{0,512}$", message = "stdoutPath is invalid.")
    private String stdoutPath = "";

    @Pattern(regexp = "^[A-Za-z0-9\\-\\_\\.\\/\\$]{0,512}$", message = "stderrPath is invalid.")
    private String stderrPath = "";

    private Map<String,String> envVars;

    private String bosFilePath = "";

    private String jobProduct = "";

    @Pattern(regexp = "^[A-Za-z\\- ]{0,64}$", message = "decompressCmd is invalid.")
    private String decompressCmd = "";
    // 提交作业的用户
    private String username;

    private String password;

    @Valid
    List<Tag> tags;


}
