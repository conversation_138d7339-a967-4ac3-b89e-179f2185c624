package com.baidu.bce.logic.chpc.cluster.domainservice;

import com.baidu.bce.logic.chpc.cluster.model.IClusterCreateRequest;

import java.util.TreeMap;

/**
 * <AUTHOR>
 * @Date 2023/9/14
 */
public class ClusterCreateRequestValidatorFactory {

    private static TreeMap<String, IClusterCreateRequestValidator> validatorMap = new TreeMap<>();

    public static IClusterCreateRequestValidator getValidator(IClusterCreateRequest clusterCreateRequest) {
        return validatorMap.getOrDefault(clusterCreateRequest.getClass().getName(), null);
    }

    public static void register(Class<? extends IClusterCreateRequest> cls, IClusterCreateRequestValidator validator) {
        validatorMap.put(cls.getName(), validator);
    }
}
