package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;


@Data
public class BackendSchedulerResponse {

    public String maxQueuedJobs;
    public List<AclInfo> aclInfo;
    public String jobHistory;
    public String schedIteration;
    public List<QueueConfig> queueConfigList;
    public String maxJobs;

    @Data
    public static class AclInfo {
        public List<String> userList;
        public String queueName;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class UserMaxRunLimit {
        private String userName;
        private String mem;
        private String cpus;
        private String nodes;
        private String maxJobs;
    }
    @Data
    public static class QueueConfig {
        public List<UserMaxRunLimit> userMaxRunLimitList;
        public String queueName;
    }
}
