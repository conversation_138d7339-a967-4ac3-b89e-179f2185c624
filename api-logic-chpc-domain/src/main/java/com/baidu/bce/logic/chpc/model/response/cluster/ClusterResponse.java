package com.baidu.bce.logic.chpc.model.response.cluster;


import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: lilu24
 * @Date: 2022-11-18
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClusterResponse extends BaseResponse {

    private String clusterId;

    private String stackId;

    private String orderId;

    private String status;

    @JsonIgnore
    private String taskId;
}
