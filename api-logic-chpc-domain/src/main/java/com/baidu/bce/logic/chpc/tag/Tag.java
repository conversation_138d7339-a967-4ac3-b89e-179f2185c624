package com.baidu.bce.logic.chpc.tag;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class Tag {
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5-_/.]{1,65}$", message = "tagKey is invalid")
    private String tagKey;
    @Pattern(regexp = "^[a-zA-Z0-9\\u4e00-\\u9fa5-_/.]{0,65}$", message = "tagValue is invalid")
    private String tagValue;


    public void setTagKey(String tagKey) {
        this.tagKey = tagKey;
    }

    public void setTagValue(String tagValue) {
        this.tagValue = tagValue;
    }

    @Override
    public String toString() {
        return "Tag{" + "tagKey='" + tagKey + '\'' + ", tagValue='" + tagValue + '\'' + '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Tag tag = (Tag) o;
        return Objects.equals(tagKey, tag.tagKey) && Objects.equals(tagValue, tag.tagValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tagKey, tagValue);
    }

}
