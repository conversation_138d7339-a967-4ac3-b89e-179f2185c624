package com.baidu.bce.logic.chpc.autoscaling.gateway;

import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;

import java.util.List;

public interface IAutoScalingDAOGateway {


    Long count(String asId);

    Boolean insert(AutoScaling autoScaling);

    Boolean update(AutoScaling autoScaling);

    Boolean delete(String asId);

    Boolean updateByStatus(AutoScaling autoScaling, AutoScalingStatus oldStatus);

    List<AutoScaling> getAll(String accountId, String orderByType);

    List<AutoScaling> getByQueueIds(List<String> queueIds, String status);

    List<AutoScaling> getByClusterId(String clusterId, String accountId);

    AutoScaling getByAsId(String asId);

    AutoScaling getByQueueId(String queueId, String accountId);
    
    AutoScaling getByClusterIdAndQueueId(String clusterId, String queueId, String accountId);


}
