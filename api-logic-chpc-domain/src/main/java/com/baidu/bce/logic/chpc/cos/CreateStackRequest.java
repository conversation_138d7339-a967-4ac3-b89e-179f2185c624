package com.baidu.bce.logic.chpc.cos;

import java.util.HashMap;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateStackRequest {

    /**
     * 用户ID，必填
     */
    String userId;

    /**
     * 资源栈名称，同user下唯一，必填
     */
    String name;

    /**
     * 描述，选填
     */
    String description;

    /**
     * 标签列表，选填
     */
    Map<String, String> tags = new HashMap<>();

    /**
     * 模版，必填
     */
    Template template;

    /**
     * 超时时间。单位：秒，cos服务默认60*60=3600秒
     */
    Integer timeout;

    /**
     * 是否禁用回滚，默认false，即会回滚
     */
    Boolean disableRollback;

    /**
     * 模版参数，选填
     */
    Map<String, Object> params;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Template {
        /**
         * 可以引用一个已注册的模板
         */
        Ref ref;

        /**
         * 可以动态创建一个模板，支持以下格式
         * json编码的模板内容
         */
        String json;

        /**
         * yaml编码的模板内容
         */
        String yaml;

        /**
         * terraform模板内容
         */
        String terraform;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Ref {
        // 模板类型，必填
        // GLOBAL - 公共模板
        // INDIVIDUAL - 个人模板
        String type;

        // 模板id，必填
        String id;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Resources {

        Map<String, Object> resources;

    }

}
