package com.baidu.bce.logic.chpc.cos;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetStackListRequest {
    /**
     * 按名称进行过滤，部分匹配，选填
     */
    String name = "";

    // 排序字段, 选填
    // 若未设置，默认为createTime
    // 可选项：
    // createTime - 创建时间
    String sort = "createTime";

    // 是否升序，选填，默认false
    Boolean ascending = false;

    // 第几页，从1开始计数，必填
    Integer pageNo = 1;

    // 每页展示数量，必填，最大值：100
    Integer pageSize = 10;
}
