package com.baidu.bce.logic.chpc.common;

public class HelixJob<PERSON>tatus {
    public static final String TO_BE_SUBMITTED = "TO_BE_SUBMITTED";

    public static final String SUBMIT_FAILED = "SUBMIT_FAILED";

    public static final String SUBMITTED = "SUBMITTED";

    public static final String USER_SUBMIT = "SUBMIT";

    public static final String USER_CANCEL = "CANCEL";

    public static final String CANCELLED = "CANCELLED";

    public static final String RUNNING = "RUNNING";

    public static final String PENDING = "PENDING";

    public static final String SUSPENDED = "SUSPENDED";

    public static final String COMPLETED = "COMPLETED";

    public static final String CONFIGURING = "CONFIGURING";

    public static final String RESIZING = "RESIZING";

    public static final String REQUEUED = "REQUEUED";

    public static final String REVOKED = "REVOKED";

    public static final String COMPLETING = "COMPLETING";

    public static final String FAILED = "FAILED";

}
