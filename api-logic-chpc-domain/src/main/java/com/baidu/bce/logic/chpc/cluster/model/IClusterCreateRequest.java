package com.baidu.bce.logic.chpc.cluster.model;

import com.baidu.bce.logic.chpc.cfs.CfsAddRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.queue.QueueAddRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
public interface IClusterCreateRequest {

    public String getClusterName();

    public String getDescription();

    public String getDefaultQueueName();

    public String getSchedulerType();

    public String getChargeType();

    public int getPeriod();

    public String getPeriodUnit();

    public boolean isAutoRenew();

    public Integer getAutoRenewPeriod();

    public String getAutoRenewPeriodUnit();

    public String getZoneName();

    public boolean isEnableHa();

    public InstanceAddRequest getManagerNode();

    public List<CfsAddRequest> getCfsVolumes();

    public List<QueueAddRequest> getQueues();

    public String getVpcId();

    public String getSecurityGroupId();
}
