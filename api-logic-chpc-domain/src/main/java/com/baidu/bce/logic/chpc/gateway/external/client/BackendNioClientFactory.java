package com.baidu.bce.logic.chpc.gateway.external.client;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.logic.core.util.WebClientBuilderUtil;
import com.baidu.bce.logic.core.webclient.SignByStsExchangeFilter;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.google.common.collect.Maps;

import io.netty.channel.ChannelOption;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.netty.resources.ConnectionProvider;
import reactor.netty.http.client.HttpClient;
import reactor.util.retry.Retry;

@Service
@Slf4j
public class BackendNioClientFactory {

    @Resource
    public RegionConfiguration regionConfiguration;

    private final WebClient.Builder webClientBuilder;

    private final Map<String, SignByStsExchangeFilter> signByStsExchangeFilterMap;

    private final Map<String, BackendNioClient> clientCache = new ConcurrentHashMap<>();

    public BackendNioClientFactory(Map<String, SignByStsExchangeFilter> signByStsExchangeFilterMap) {
        // 配置连接池的参数
        ConnectionProvider connectionProvider = ConnectionProvider.builder("customConnectionPool")
                .maxConnections(400)          // 设置最大连接数 maxConnTotal
                .maxIdleTime(Duration.ofSeconds(30)) // 设置空闲时间
                .maxLifeTime(Duration.ofMinutes(60))  // 设置连接的最大生命周期
                .build();
        // 配置 HttpClient 并指定超时时间和连接池
        HttpClient httpClient = HttpClient.create(connectionProvider)
                .responseTimeout(Duration.ofMillis(10000))         // 设置 readTimeout
                .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, 3000); // 设置连接超时时间

        BackendRetryPolicy retryPolicy = new BackendRetryPolicy();
        
        this.webClientBuilder = WebClient.builder()
            .filter((request, next) -> {if (request.headers().containsKey("No-Retry")) { 
                return next.exchange(request).retryWhen(Retry.backoff(0, Duration.ofMillis(5))); 
            }
                return next.exchange(request)
                    .retryWhen(Retry.backoff(2, Duration.ofMillis(5))
                    .filter(throwable -> shouldRetry(retryPolicy, throwable))  // 加入 shouldRetry 条件
                    .doAfterRetry(retrySignal -> {
                        long retryInterval = getRetryInterval(retryPolicy, retrySignal.failure());
                        logRetry(retrySignal.failure(), retryInterval);
                }));})
            .clientConnector(new ReactorClientHttpConnector(httpClient));

        this.signByStsExchangeFilterMap = signByStsExchangeFilterMap;
    }

    private boolean shouldRetry(BackendRetryPolicy retryPolicy, Throwable throwable) {
        return retryPolicy.retryException(throwable).isRetry();
    }

    private long getRetryInterval(BackendRetryPolicy retryPolicy, Throwable throwable) {
        return retryPolicy.retryException(throwable).getRetryIntervalInMs();
    }

    private void logRetry(Throwable throwable, long retryInterval) {
        log.warn("Retrying due to: " + throwable.getMessage() + " after " + retryInterval + "ms");
    }

    public BackendNioClient createClient(String endpoint, String accountId) {
        return clientCache.computeIfAbsent(endpoint, key -> {
            BackendNioClient client = WebClientBuilderUtil.nioClient(
                endpoint, webClientBuilder,
                signByStsExchangeFilterMap.get(regionConfiguration.getCurrentRegion()),
                webClient -> new BackendNioClient(webClient, Maps.newHashMap())
            );
            client.accountId = accountId;
            return client;
        });
    }
}
