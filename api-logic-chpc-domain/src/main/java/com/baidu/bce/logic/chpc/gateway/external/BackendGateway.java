package com.baidu.bce.logic.chpc.gateway.external;

import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AutoScalingProbeRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AutoScalingProbeResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionScriptResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendTaskResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ComputeNode;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.CreateWorkflowRunRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.GetClusterResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.GetComputeNodeResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListAvailableTagsResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListComputeNodesResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListQueuesResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.Queue;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ShrinkNodesResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunResResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunStatusResponse;
import com.baidu.bce.logic.chpc.tag.Tag;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
public interface BackendGateway {

    BackendCommonResponse startCluster(String clusterId);

    BackendCommonResponse stopCluster(String clusterId, Boolean force);

    BackendCommonResponse modifyCluster(String clusterId, int maxCpus, int maxNodes);

    GetClusterResponse getClusterDetail(String clusterId);

    BackendCommonResponse addQueue(String clusterId, Queue queue);

    BackendCommonResponse removeQueue(String clusterId, String queueName, Boolean force, Boolean check);

    BackendCommonResponse updateQueueAutoScaling(AutoScaling autoScaling, List<String> excludeNodeHostnames);

    ListQueuesResponse listQueue(String clusterId);

    ListComputeNodesResponse listComputeNodes(String clusterId, String queueName);

    GetComputeNodeResponse getComputeNodeDetail(String clusterId, String queueName,
                                                String hostName, String instanceId);

    BackendCommonResponse removeNode(String clusterId, String queueName, String hostName,
                                     String instanceId, Boolean autoScaling, Boolean force);

    ShrinkNodesResponse shrinkNodes(String clusterId, String queueName, List<String> hostNames,
                                    int shrinkNum);

    BackendCommonResponse addNode(String clusterId, String queueName, ComputeNode node);

    BackendTaskResponse getTaskDetail(String clusterId, String taskId);

    AutoScalingProbeResponse autoScalingProbe(String clusterId, AutoScalingProbeRequest request);

    WorkflowRunStatusResponse getWorkflowRunStatus(String clusterId, String runUuid);

    WorkflowRunStatusResponse abortWorkflow(String clusterId, String runUuid);

    WorkflowRunStatusResponse createWorkflowRun(String clusterId, CreateWorkflowRunRequest requestd);

    WorkflowRunResResponse getWorkflowRunRes(String clusterId, String runUuid);

    ListAvailableTagsResponse listAvailableTags(String clusterId);

    BackendCommonResponse modifyTags(String clusterId, String jobId, List<Tag> tags);

    BackendActionProxyResponse actionProxy(String clusterId, String action, String arguments, Boolean noRetry);

    BackendActionProxyResponse actionProxy(String clusterId, String action, String arguments);

    BackendActionScriptResponse actionScript(String clusterId, String action, String arguments);

    BackendCommonResponse authDomainUser(String clusterId, String userName, String password);
}
