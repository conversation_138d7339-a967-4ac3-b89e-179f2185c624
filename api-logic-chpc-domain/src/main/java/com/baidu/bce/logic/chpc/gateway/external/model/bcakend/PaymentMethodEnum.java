package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import lombok.Getter;

import java.util.Arrays;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Getter
public enum PaymentMethodEnum {
    PREPAID("PREPAID"),
    POSTPAID("POSTPAID"),
    SPOT("SPOT"),
    TIDE("TIDE");

    private final String value;

    PaymentMethodEnum(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    public static PaymentMethodEnum fromValue(String input) {
        return Arrays.stream(PaymentMethodEnum.values())
                .filter(b -> b.value.equals(input))
                .findFirst()
                .orElse(null);
    }

}