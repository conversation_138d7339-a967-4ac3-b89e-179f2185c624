package com.baidu.bce.logic.chpc.bcm.gateway;

import com.baidu.bce.logic.chpc.bcm.BcmBatchQueryRequest;
import com.baidu.bce.logic.chpc.bcm.BcmBatchQueryResponse;
import com.baidu.bce.logic.chpc.bcm.BcmPartialQueryRequest;
import com.baidu.bce.logic.chpc.bcm.BcmPartialQueryResponse;

public interface BcmGateway {

    BcmBatchQueryResponse batchQuery(BcmBatchQueryRequest request);

    BcmPartialQueryResponse partialQuery(BcmPartialQueryRequest request);
}
