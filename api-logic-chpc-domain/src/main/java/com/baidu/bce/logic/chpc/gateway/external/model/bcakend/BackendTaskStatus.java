package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import lombok.Getter;

import java.util.Arrays;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Getter
public enum BackendTaskStatus {

    INIT("INIT"),
    DOING("DOING"),
    FAIL("FAIL"),
    <PERSON>UCCE<PERSON>("SUCCESS"),
    FREEZ<PERSON>("FREEZE"),

    RUNNING("RUNNING"),
    STOPPING("STOPPING"),
    STOPPED("STOPPED");

    private final String value;

    BackendTaskStatus(String value) {
        this.value = value;
    }

    public static BackendTaskStatus fromValue(String input) {
        return Arrays.stream(BackendTaskStatus.values())
                .filter(b -> b.value.equals(input))
                .findFirst()
                .orElse(null);
    }
}