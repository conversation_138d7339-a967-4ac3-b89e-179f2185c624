package com.baidu.bce.logic.chpc.common;

import com.baidu.bce.logic.chpc.autoscaling.gateway.IAutoScalingDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.core.util.UuidUtil;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
public class GlobalUuidUtil {


    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    IAutoScalingDAOGateway autoScalingDAOGateway;

    @Resource
    QueueDAOGateway queueDAOGateway;


    public String genClusterShortId() {

        int retryCount = 0;
        while (retryCount < 5) {
            try {
                String clusterId = String.format("c-%s", UuidUtil.generateShortUuid());
                Boolean isExisted = isExistClusterId(clusterId, null, null);
                if (!isExisted) {
                    return clusterId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate stack_id in database.");
    }

    public String genWorkspaceShortId() {

        int retryCount = 0;
        while (retryCount < 5) {
            try {
                String clusterId = String.format("w-%s", UuidUtil.generateShortUuid());
                Boolean isExisted = isExistClusterId(clusterId, null, null);
                if (!isExisted) {
                    return clusterId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate stack_id in database.");
    }


    public String genWorkflowShortId() {

        int retryCount = 0;
        while (retryCount < 5) {
            try {
                String clusterId = String.format("f-%s", UuidUtil.generateShortUuid());
                Boolean isExisted = isExistClusterId(clusterId, null, null);
                if (!isExisted) {
                    return clusterId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate stack_id in database.");
    }


    public String genWorkflowRunId() {

        int retryCount = 0;
        while (retryCount < 5) {
            try {
                String clusterId = String.format("r-%s", UuidUtil.generateShortUuid());
                Boolean isExisted = isExistClusterId(clusterId, null, null);
                if (!isExisted) {
                    return clusterId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate stack_id in database.");
    }


    public String genAutoScalingShortId() {

        int retryCount = 0;
        while (retryCount < 5) {
            try {
                String asId = String.format("as-%s", UuidUtil.generateShortUuid());
                Boolean isExisted = isExistAutoScalingId(asId);
                if (!isExisted) {
                    return asId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate asId in database.");
    }

    public String generateQueueId() {
        int retryCount = 0;

        while (retryCount < 5) {
            try {
                String queueId = String.format("q-%s", UuidUtil.generateShortUuid());
                Boolean isExisted = this.isExistQueueId(null, queueId, null);
                if (!isExisted) {
                    return queueId;
                }
            } catch (DuplicateKeyException e) {
                retryCount++;
            }
        }
        throw new DuplicateKeyException("Duplicate queue_id in database.");
    }

    public Boolean isExistQueueId(String clusterId, String queueId, String status) {
        return queueDAOGateway.count(clusterId, queueId, status) != 0;
    }

    public Boolean isExistAutoScalingId(String clusterId) {
        return autoScalingDAOGateway.count(clusterId) != 0;
    }


    public Boolean isExistClusterId(String clusterId, String accountId, String status) {
        return clusterDAOGateway.count(clusterId, accountId, status) != 0;
    }
}
