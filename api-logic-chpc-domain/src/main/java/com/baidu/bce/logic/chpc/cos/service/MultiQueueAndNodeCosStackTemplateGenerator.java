package com.baidu.bce.logic.chpc.cos.service;

import com.baidu.bce.internalsdk.cos.model.CdsDiskForCreate;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.cluster.model.IClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.MultiQueueAndNodeClusterCreateRequest;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.common.charge.EipChargeType;
import com.baidu.bce.logic.chpc.common.charge.PeriodUnit;
import com.baidu.bce.logic.chpc.common.holder.AccessKeyThreadHolder;
import com.baidu.bce.logic.chpc.cos.CreateStackRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.queue.QueueAddRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
@Component
public class MultiQueueAndNodeCosStackTemplateGenerator implements ICosStackTemplateGenerator {

    @Override
    public CreateStackRequest.Template generate(IClusterCreateRequest clusterCreateRequest) {
        if (!(clusterCreateRequest instanceof MultiQueueAndNodeClusterCreateRequest)) {
            return null;
        }
        MultiQueueAndNodeClusterCreateRequest request = (MultiQueueAndNodeClusterCreateRequest) clusterCreateRequest;
        Map<String, Object> resourceTemplates = new HashMap<>();
        int idx = 0;
        Map<String, Object> master = generateOneResourceTemplate(
                request,
                InstanceNodeType.MASTER,
                request.getManagerNode()
        );
        resourceTemplates.put("bcc_" + InstanceNodeType.MASTER.getType() + "_" + (idx++), master);

        for (QueueAddRequest queueAddRequest : request.getQueues()) {
            if (CollectionUtils.isEmpty(queueAddRequest.getComputeNodes())) {
                continue;
            }
            for (InstanceAddRequest instanceAddRequest : queueAddRequest.getComputeNodes()) {
                for (int i = 0; i < instanceAddRequest.getCount(); i++) {
                    Map<String, Object> compute = generateOneResourceTemplate(
                            request,
                            InstanceNodeType.COMPUTE,
                            instanceAddRequest
                    );
                    resourceTemplates.put("bcc_" + InstanceNodeType.COMPUTE.getType() + "_" + (idx++), compute);
                }

            }
        }

        CreateStackRequest.Resources resources = new CreateStackRequest.Resources();
        resources.setResources(resourceTemplates);

        CreateStackRequest.Template template = new CreateStackRequest.Template();
        template.setJson(JacksonUtil.encode(resources));

        return template;
    }

    private Map<String, Object> generateOneResourceTemplate(
            MultiQueueAndNodeClusterCreateRequest request,
            InstanceNodeType nodeType,
            InstanceAddRequest instanceAddRequest
    ) {
        Map<String, Object> resource = new HashMap<>();
        resource.put("type", "BCE::BCC::CreateInstanceBySpec");
        Map<String, Object> properties = new HashMap<>();

        properties.put("spec", instanceAddRequest.getSpec());
        properties.put("zoneName", instanceAddRequest.getZoneName());

        // 默认使用管控节点的子网
        properties.put("subnetId", request.getManagerNode().getSubnetId());

        // 计算节点可以配置其他的子网
        if (StringUtils.isNotEmpty(instanceAddRequest.getSubnetId())) {
            properties.put("subnetId", instanceAddRequest.getSubnetId());
        }

        properties.put("securityGroupId", request.getSecurityGroupId());


        if (StringUtils.isNotEmpty(request.getSecurityGroupId())) {
            properties.put("securityGroupId", request.getSecurityGroupId());
        }

        properties.put("rootDiskSizeInGb", instanceAddRequest.getSystemDiskSize());
        properties.put("rootDiskStorageType", getDiskType(instanceAddRequest.getSystemDiskType()));
        properties.put("purchaseCount", 1);
        properties.put("imageId", instanceAddRequest.getImageId());

        if (StringUtils.isNotEmpty(instanceAddRequest.getPassword())) {
            properties.put("adminPass", instanceAddRequest.getPassword());
            properties.put("encrypted-key", AccessKeyThreadHolder.getAccessKey());
        }
        if (StringUtils.isNotEmpty(instanceAddRequest.getKeypairId())) {
            properties.put("keypairId", instanceAddRequest.getKeypairId());
        }

        properties.put("paymentTiming", request.getChargeType());
        if (ChargeType.Prepaid.name().equalsIgnoreCase(request.getChargeType())) {
            // console bcc 目前不支持Year单位，故用 Month*12
            if (PeriodUnit.Year.name().equalsIgnoreCase(request.getPeriodUnit())) {
                properties.put("reservationLength", request.getPeriod() * 12);
            } else {
                properties.put("reservationLength", request.getPeriod());
            }
            properties.put("reservationTimeUnit", PeriodUnit.Month.name());
            if (request.isAutoRenew()) {
                properties.put("autoRenewTimeUnit", request.getAutoRenewPeriodUnit());
                properties.put("autoRenewTime", request.getAutoRenewPeriod());
                properties.put("cdsAutoRenew", true);
            }
        }

        Tag tag = new Tag();
        tag.setTagKey("nodeType");
        tag.setTagValue(nodeType.getType());

        // 标记chpc来源tag，用于虚机通网后，返回虚机创建成功
        Tag fromTag = new Tag();
        fromTag.setTagKey("from");
        fromTag.setTagValue("chpc");

        properties.put("tags", Arrays.asList(tag, fromTag));

        if (nodeType == InstanceNodeType.MASTER && instanceAddRequest.getNetworkCapacityInMbps() > 0) {
            properties.put("networkCapacityInMbps", instanceAddRequest.getNetworkCapacityInMbps());
            if (StringUtils.isNotEmpty(instanceAddRequest.getInternetChargeType())) {
                properties.put("internetChargeType", instanceAddRequest.getInternetChargeType());
                if (instanceAddRequest.getInternetChargeType().equalsIgnoreCase(
                        EipChargeType.BANDWIDTH_PREPAID.name())) {
                    properties.put("reservationLength", 1);
                }
            }
            if (StringUtils.isNotEmpty(instanceAddRequest.getEipName())) {
                properties.put("eipName", instanceAddRequest.getEipName());
            }
        }

        if ( CollectionUtils.isNotEmpty(instanceAddRequest.getDataDiskList()) ) {
            List<CdsDiskForCreate> cdsList = new ArrayList<>();
            for ( DiskInfo disk : instanceAddRequest.getDataDiskList() ) {
                for ( int j = 0; j < disk.getCdsNum(); j++ ) {
                    CdsDiskForCreate cdsDiskForCreate = new CdsDiskForCreate();
                    cdsDiskForCreate.setCdsSizeInGB(disk.getSize());
                    cdsDiskForCreate.setStorageType(getDiskType(disk.getStorageType()));
                    cdsList.add(cdsDiskForCreate);
                }
            }
            properties.put("createCdsList", cdsList);
        }

        resource.put("properties", properties);
        return resource;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        CosStackTemplateGeneratorFactory.register(MultiQueueAndNodeClusterCreateRequest.class, this);
    }

    public String getDiskType(String diskType) {
        if ("premium_ssd".equals(diskType)) {
            return "cloud_hp1";
        } else if ("ssd".equals(diskType)) {
            return "hp1";
        } else if ("ENHANCED_SSD_PL1".equals(diskType)) {
            return "enhanced_ssd_pl1";
        }
        return diskType;
    }
}
