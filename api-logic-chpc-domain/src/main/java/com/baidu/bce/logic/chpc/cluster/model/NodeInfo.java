package com.baidu.bce.logic.chpc.cluster.model;

import java.util.List;

import com.baidu.bce.logic.chpc.tag.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Pattern;

import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class NodeInfo {
    /**
     * 集群中所有节点的付费类型，取值范围：
     * * Postpaid：按量付费
     * * Prepaid：包年包月
     * 默认 Postpaid
     */
    @Pattern(regexp = "^(Postpaid|Prepaid)$", message = "chargeType is invalid.")
    String chargeType = ChargeType.Postpaid.name();

    /**
     * 购买集群节点的时长。单位由periodUnit指定，取值范围：
     * * 当参数priceUnit取值为Year时：1~3。
     * * 当参数priceUnit取值为Month时：1~9。
     * 默认值：1。
     */
    int period = 1;

    /**
     * 购买集群节点的时长单位。取值范围：
     * * Year
     * * Month
     * 默认值：Month。
     */
    @Pattern(regexp = "^(Month|Year)$", message = "periodUnit is invalid.")
    String periodUnit = "Month";

    boolean autoRenew = false; // 预付费时，是否自动续费

    Integer autoRenewPeriod = 1;

    String autoRenewPeriodUnit = "Month";

    String spec; // 如bcc.g5.c32m64

    int count = 0; // 节点数量默认0个

    String imageId; // 节点镜像ID

    String cudaVersion; // cuda版本

    String gpuDriverVersion; // gpu驱动版本

    String cudnnVersion; // cudnn版本

    DiskInfo systemDisk;

    List<DiskInfo> dataDiskList;

    String subnetId; // 计算节点所在子网ID

    String userData;

    // 节点绑定的Tags
    @Valid
    List<Tag> tags;

    // ebc ht&numa配置
    String cpuThreadConfig;

    String numaConfig;
}
