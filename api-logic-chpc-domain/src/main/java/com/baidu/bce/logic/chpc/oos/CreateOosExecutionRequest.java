package com.baidu.bce.logic.chpc.oos;

import com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateOosExecutionRequest {

    /**
     * https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/KzHUM_sAtc/MdtWHAe55g/
     * l64vwNJWFinQMv#anchor-3437c120-c2e2-11ed-b626-3b824be0a92f
     *
     * namespace, 选填，默认值：default
     * 该字段可用于为内部服务创建用户不可见的私有空间，例如chpc服务使用oos时，使用namespace=BCE_CHPC
     */
    private String namespace;

    /**
     * 选填
     */
    private String description;

    /**
     * dag 模版，必填
     */
    private Template template;
    private List<Tag> tags;

    @Data
    public static class Template {

        String name;

        boolean linear;

        List<Operator> operators;

    }


    @Data
    public static class Operator {

        // 任务名称，必填，你可以指定任意的名称
        String name;

        /**
         * 描述，选填
         */
        String description;

        /**
         * 执行的任务，必填，"BCE::Agent::ExecuteShell"表示在虚机内执行脚本
         */
        String operator;

        /**
         * 重试次数，选填，默认值：0，表示不重试
         */
        int retries;

        /**
         * 重试间隔，单位：毫秒，选填，默认值：5min
         */
        int retryInterval;

        /**
         * 超时时长，单位：毫秒，选填，默认值：6小时
         * 若Task执行时长超过该限制，将会认为任务失败
         */
        int timeout;

        /**
         * 等待agent进入online状态的超时时间，启动后单位：毫秒，选填
         * 适用于虚机启动后立即执行oos任务的场景，给予虚机和agent启动的时间。若超时，则设置task运行失败
         */
        int waitOnAgentMilli = 180000;

        /**
         * 任务参数，必填
         */
        Map<String, Object> properties;

    }


}
