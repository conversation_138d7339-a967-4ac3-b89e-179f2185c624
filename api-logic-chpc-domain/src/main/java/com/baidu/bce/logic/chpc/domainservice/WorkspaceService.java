package com.baidu.bce.logic.chpc.domainservice;

import com.baidu.bce.logic.chpc.gateway.WorkspaceDAOGateway;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

@Service
public class WorkspaceService {
    @Resource
    private WorkspaceDAOGateway workspaceDAOGateway;

    public void updateWorkspaceStatus(String workspaceId, String status, String accountId) {
        workspaceDAOGateway.updateWorkspace(workspaceId, status, accountId);
    }
}
