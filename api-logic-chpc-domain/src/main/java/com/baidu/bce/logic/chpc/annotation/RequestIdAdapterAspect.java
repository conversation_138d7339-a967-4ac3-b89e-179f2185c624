package com.baidu.bce.logic.chpc.annotation;

import com.baidu.bce.internalsdk.core.BceInternalRequest;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Slf4j
@Aspect
@Component
public class RequestIdAdapterAspect {

    private static final String REQUEST_ID = "requestId";

    @Pointcut("@annotation(com.baidu.bce.logic.chpc.annotation.RequestIdAdapter)")
    public void pointCut() {
    }


    @AfterReturning(value = "pointCut()", returning = "response")
    public void afterReturning(Object response) {

        try {
            if (!(response instanceof BaseResponse)) {
                return;
            }

            String requestId = BceInternalRequest.getThreadRequestId();
            if (StringUtils.isEmpty(requestId)) {
                return;
            }
            log.debug("enhanced return result with request id:{}", requestId);

            ((BaseResponse) response).setRequestId(requestId);
        } catch (Exception e) {
            log.error("RequestIdAdapterAspect:", e);
        }
    }


}
