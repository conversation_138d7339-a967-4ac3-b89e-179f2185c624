package com.baidu.bce.logic.chpc.autoscaling.model;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ExecuteAutoScalingInfo {

    @JsonProperty("queue_name")
    String queueName;

    @JsonProperty("scale_count")
    Integer scaleCount;

    String message;

    List<Node> nodes;

    Integer shrinkCycle;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Node {
        String hostIP;

        String status;

        String nodeID;

        String nodeName;

        String spec;

        String queue;

    }
}
