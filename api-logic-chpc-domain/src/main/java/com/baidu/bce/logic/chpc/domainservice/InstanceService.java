package com.baidu.bce.logic.chpc.domainservice;


import java.util.List;

import jakarta.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.model.Instance;
import com.google.common.collect.Lists;

/**
 * <AUTHOR>
 * @since 2022-12-07
 */
@Service
public class InstanceService {

    @Resource
    private InstanceDAOGateway instanceDAOGateway;

    public Boolean batchInsert(List<Instance> instances) {

        if (CollectionUtils.isEmpty(instances)) {
            return false;
        }

        return instanceDAOGateway.batchInsert(instances);
    }

    public List<Instance> findByStatus(String clusterId, String queueId, String... status) {

        List<String> statusList = Lists.newArrayList(status);

        return instanceDAOGateway.findBy(clusterId, queueId, null, null, statusList);
    }

    public List<Instance> findByCosAndStatus(String clusterId, String cosStackId, String... status) {

        List<String> statusList = Lists.newArrayList(status);

        return instanceDAOGateway.findBy(clusterId, null, null, cosStackId, statusList);
    }


    public List<Instance> findBy(String clusterId, String queueId) {
        return instanceDAOGateway.findBy(clusterId, queueId, null, null, null);
    }

    public List<Instance> findByCosStackId(String clusterId, String queueId, String cosStackId) {
        return instanceDAOGateway.findBy(clusterId, queueId, null, cosStackId, null);
    }

    public Instance findBy(String instanceId) {
        return instanceDAOGateway.findBy(instanceId);
    }

    public List<Instance> findByInstanceNodeType(String clusterId, String queueId, String instanceNodeType) {
        return instanceDAOGateway.findBy(clusterId, queueId, instanceNodeType, null, null);
    }


    public Boolean updateStatusAndExecutionId(String instanceId, String oosExecutionId, String status) {
        return instanceDAOGateway.updateStatus(instanceId, oosExecutionId, status);
    }

    public Boolean updateSchedulerConfig(String clusterId, String instanceType, String schedulerIp, String schedulerHost) {
        return instanceDAOGateway.updateSchedulerConfig(clusterId, instanceType, schedulerIp, schedulerHost);
    }

    public Boolean delete(String instanceId) {
        return instanceDAOGateway.delete(instanceId);
    }

    public Boolean isExisted(String clusterId, String queueId,
                             String instanceId, String hostname, String status) {
        return instanceDAOGateway.count(clusterId, queueId, instanceId, hostname, status, null) != 0;
    }

    public int countByClusterIdAndQueueIdAndNodeType(String clusterId, String queueId, InstanceNodeType nodeType) {
        return instanceDAOGateway.count(
                clusterId,
                queueId,
                null,
                null,
                null,
                nodeType.getType()
        ).intValue();
    }
}
