package com.baidu.bce.logic.chpc.annotation;

import com.baidu.bce.logic.chpc.domainservice.ClusterService;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.HandlerMapping;

import jakarta.annotation.Resource;
import java.util.Map;

/**
 * @Author: lilu24
 * @Date: 2023-01-18
 */
@Slf4j
@Aspect
@Component
public class ValidateAuthenticationAspect {


    @Resource
    private ClusterService clusterService;

    @Resource
    private InstanceService instanceService;

    @Resource
    private QueueDAOGateway queueDAOGateway;


    public ValidateAuthenticationAspect() {
    }

    @Pointcut("@annotation(com.baidu.bce.logic.chpc.annotation.ValidateAuthentication)")
    public void pointCut() {
    }


    @Before("pointCut()")
    public void beforePointCut() {

        ServletRequestAttributes servletRequestAttributes =
                (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();

        Map<String, Object> pathVariables = (Map<String, Object>) servletRequestAttributes.getRequest()
                .getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);


        String accountId = LogicUserService.getAccountId();
        String clusterId = String.valueOf(pathVariables.getOrDefault("clusterId", ""));
        Cluster cluster = new Cluster();
        log.debug("validate authentication, account: {}, clusterId: {}", accountId, clusterId);

        if (StringUtils.isNotEmpty(clusterId)) {
            cluster = clusterService.findByAll(clusterId);
            // Boolean isClusterExisted = clusterService.isExisted(clusterId, accountId, null);
            if (cluster == null) {
                throw new CommonExceptions.ResourceNotExistException();
            }
        }

        String queueId = String.valueOf(pathVariables.getOrDefault("queueId", ""));
        if (StringUtils.isNotEmpty(clusterId) && StringUtils.isNotEmpty(queueId) && cluster.getSchedulePlugin() == 0) {
            Boolean isQueueExisted = queueDAOGateway.isExisted(clusterId, queueId, null);
            if (!isQueueExisted) {
                throw new CommonExceptions.ResourceNotExistException();
            }
        }

        String queueName = String.valueOf(pathVariables.getOrDefault("queueName", ""));
        if (StringUtils.isNotEmpty(clusterId) && StringUtils.isNotEmpty(queueName) && cluster.getSchedulePlugin() == 0) {

            Queue queue = queueDAOGateway.getByName(clusterId, queueName);
            if (queue == null) {
                throw new CommonExceptions.ResourceNotExistException();
            }
        }

        String instanceId = String.valueOf(pathVariables.getOrDefault("instanceId", ""));
        if (StringUtils.isNotEmpty(clusterId) && StringUtils.isNotEmpty(instanceId) && cluster.getSchedulePlugin() == 0) {

            Boolean isInstanceExisted = instanceService.isExisted(clusterId, null,
                    instanceId, null, null);
            if (!isInstanceExisted) {
                throw new CommonExceptions.ResourceNotExistException();
            }
        }

    }


}
