package com.baidu.bce.logic.chpc.common.charge;

import com.baidu.bce.logic.core.exception.CommonExceptions;
import org.apache.commons.lang3.StringUtils;

public enum PeriodUnit {
    Month(1, 9),

    Year(1, 3);

    private final int minPeriod;

    private final int maxPeriod;

    PeriodUnit(int minPeriod, int maxPeriod) {
        this.minPeriod = minPeriod;
        this.maxPeriod = maxPeriod;
    }

    /**
     * 校验预付费方式的【付费时长】和【付费时长单位】
     *
     * @param period     时间
     * @param periodUnit 时间单位
     */
    public static void verifyPeriodAndPeriodUnit(int period, String periodUnit) {
        for (PeriodUnit value : values()) {
            if (value.name().equals(periodUnit)) {
                if (period < value.minPeriod || period > value.maxPeriod) {
                    throw new CommonExceptions.RequestInvalidException("The period should range from " +
                            value.minPeriod + " to " + value.maxPeriod);
                }
                return;
            }
        }
        throw new CommonExceptions.RequestInvalidException("The periodUnit only support: " +
                StringUtils.join(values(), ","));
    }
}
