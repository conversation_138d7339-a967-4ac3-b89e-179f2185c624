package com.baidu.bce.logic.chpc.bcc.gateway;

import com.baidu.bce.logic.chpc.bcc.BccInstance;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.BccInstanceDetail;
import com.baidubce.services.bcc.model.InstanceModel;

import java.util.List;

public interface BccGateway {

    List<BccInstance> getBccInstances(List<String> instanceIds);

    List<InstanceModel> getBccInstancesByZoneName(String zoneName, String instanceIds, String instanceNames);

    BccInstanceDetail getBccInstanceDetail(String instanceId);

    void rebuildBatchBccInstance(List<String> instanceIds, String imageId, String password, String keypairId);

    public void rebootBatchBccInstance(List<String> instanceIds, Boolean forceStop);

}
