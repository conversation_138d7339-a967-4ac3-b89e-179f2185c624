package com.baidu.bce.logic.chpc.cromwell.model;

import com.baidu.bce.logic.chpc.cromwell.DepFile;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowCreateRequest {
    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){1,13}[a-zA-Z0-9]$",
            message = "parameter is invalid.")
    private String name;
    @Size(max = 1000, message = "is invalid")
    private String description;
    private String language;
    private String languageVersion;
    private String workspaceId;
    private String mainFileContent;
    private String mainFilePath;
    private List<DepFile> depFiles;
    private String configFileContent;
    private String configFilePath;
    private String introduction;
    private String document;
    private String inputs;
}
