package com.baidu.bce.logic.chpc.common.charge;

public class ChargeUtil {


    /**
     * 校验付费类型和付费时长及付费时间单位
     *
     * @param chargeType 付费类型
     * @param period     付费时长
     * @param periodUnit 时间单位
     */
    public static void validateChargingTypeAndPeriod(String chargeType, int period, String periodUnit) {

        ChargeType.verifyChargeType(chargeType);

        if (ChargeType.Prepaid.name().equalsIgnoreCase(chargeType)) {
            PeriodUnit.verifyPeriodAndPeriodUnit(period, periodUnit);
        }
    }
}
