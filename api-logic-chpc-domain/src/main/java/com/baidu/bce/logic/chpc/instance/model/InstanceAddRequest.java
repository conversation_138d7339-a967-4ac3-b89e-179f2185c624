package com.baidu.bce.logic.chpc.instance.model;

import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.Valid;
import lombok.Data;

import java.util.List;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class InstanceAddRequest {

    // 基础配置
    private String nodeSource;
    // 客户 Local 节点 Hostnames
    private String nodeHosts;
    // 客户云上已有节点 ID 列表
    private List<String> instanceIds;

    @Min(value = 1, message = "count must be at least 1.")
    @Max(value = 99, message = "count cannot exceed 99.")
    private int count = 1;

    @NotBlank(message = "spec is required.")
    @NotEmpty(message = "spec cannot be empty.")
    private String spec;

    // 系统盘配置
    @Min(value = 40, message = "systemDiskSize must be at least 40GB.")
    @Max(value = 2048, message = "systemDiskSize cannot exceed 2048GB.")
    private int systemDiskSize = 40;

    private String systemDiskType = "cloud_hp1";

    // 数据盘配置
    List<DiskInfo> dataDiskList;
    private int dataDiskSize;
    private String dataDiskType = "cloud_hp1";
    private int dataDiskCount;

    // 计费与续费配置
    private String chargeType = "Postpaid";
    private Integer period = 1;
    private String periodUnit = "Month";
    private boolean autoRenew;
    private Integer autoRenewPeriod = 1;
    private String autoRenewPeriodUnit = "Month";

    // 网络与安全配置
    private String zoneName;
    private String subnetId;
    private String securityGroupId;

    private String imageId;

    // 认证配置
    private String password;
    private String keypairId;

    // 网络配置
    private int networkCapacityInMbps;
    private String internetChargeType;

    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9-_/.]){0,64}",
            message = "parameter is invalid.")
    String eipName;

    String cudaVersion;

    String gpuDriverVersion;

    String cudnnVersion;

    @Valid
    private List<Tag> tags;

    // ebc ht&numa配置
    private String cpuThreadConfig;

    private String numaConfig;
}
