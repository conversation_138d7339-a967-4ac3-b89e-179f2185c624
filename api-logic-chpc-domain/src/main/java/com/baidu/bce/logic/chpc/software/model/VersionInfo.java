package com.baidu.bce.logic.chpc.software.model;

import java.time.LocalDateTime;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class VersionInfo {
        String value;

        String status;

        @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
        private LocalDateTime createdTime;

        private String msg;
}
