package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Data
public class Queue {

    @JsonProperty("name")
    private String queueName;

    @JsonProperty("default")
    private Boolean isDefault;

    @JsonProperty("computeResources")
    private List<ComputeResource> computeResources;

    /**
     * 是否开启自动扩容
     */
    @JsonProperty("enableAutoGrow")
    private Boolean enableAutoGrow;

    /**
     * 是否开启自动缩容
     */
    @JsonProperty("enableAutoShrink")
    private Boolean enableAutoShrink;

    /**
     * 不参与缩容的节点列表
     */
    @JsonProperty("excludeNodeHostnames")
    private List<String> excludeNodeHostnames;

    /**
     * 队列最大节点数
     */
    @JsonProperty("maxNodes")
    Integer maxNodes;

    /**
     * 队列最小节点数
     */
    @JsonProperty("minNodes")
    Integer minNodes;

    /**
     * 单次最大伸缩节点数
     */
    @JsonProperty("maxScalePerCycle")
    Integer maxScalePerCycle;


}
