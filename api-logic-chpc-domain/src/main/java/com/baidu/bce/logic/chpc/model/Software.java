package com.baidu.bce.logic.chpc.model;

import java.time.LocalDateTime;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class Software {
    /**
     * 软件名称
     */
    private String name;
    /**
     * 软件版本
     */
    private String version;
    /**
     * 软件说明
     */
    private String description;
    /**
     * 所属行业
     */
    private String category;
    /**
     * 支持的系统
     */
    private String supportOs;
    /**
     * 支持的架构
     */
    private String supportArch;
    /**
     * 依赖软件
     */
    private String dependency;

    /**
     * 安装节点类型
     */
    private String nodeType;

    private LocalDateTime updatedTime;

    private LocalDateTime createdTime;

}
