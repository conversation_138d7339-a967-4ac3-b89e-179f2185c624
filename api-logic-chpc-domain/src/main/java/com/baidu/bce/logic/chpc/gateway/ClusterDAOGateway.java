package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.model.Cluster;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
public interface ClusterDAOGateway {

    Long count(String clusterName, String accountID);

    Long count(String clusterId, String accountId, String status);

    Cluster findByClusterId(String clusterId, String accountId);

    Cluster findByClusterIdAll(String clusterId, String accountId);

    List<Cluster> findByAccountId(String accountId);

    List<Cluster> findByAccountIdAll(String accountId);

    Boolean insert(Cluster cluster);

    List<Cluster> listByStatus(List<String> status);

    void updateClusterStatus(String clusterId, String status);

    void updateClusterPassword(String clusterId, String password);

    void updateClusterWithErrorMessage(String clusterId, String status, String message);

    void updatesoftwareDirByClusterId(String clusterId, String softwareDir);

    void updateClusterHeartbeat(String clusterId, Long heartbeat);

    Boolean update(Cluster cluster);

    Boolean delete(String clusterId);

    void deleteWithStatus(String clusterId, ClusterStatus status);
}
