package com.baidu.bce.logic.chpc.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-12-07
 */
@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Instance {
    @Data
    public class InstanceResource {
        private long cpu;
        private String memory;
    }
    /**
     * 集群id
     */
    private String clusterId;
    /**
     * 实例id
     */
    private String instanceId;

    /**
     * 实例长id
     */
    private String instanceUuid;
    /**
     * 队列id, 不对外展示
     */
    @JsonIgnore
    private String queueId;

    private String queueName;

    /**
     * 节点类型master/backup/compute/login
     */
    private String nodeType;

    private String subnetId;

    private List<Tag> tags;

    /**
     * 实例对应的eip带宽，为0代表不开启
     */
    private Integer eipNetworkCapacity;
    /**
     * cos对应的资源栈
     */
    private String cosStackId;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime expireTime;

    @JsonIgnore
    private LocalDateTime updatedTime;

    private String hostName;

    private String status;

    private String oosExecutionId;

    private String publicIp;

    private String privateIp;

    private String vpcId;

    private String vpcName;

    private String subnetName;

    private String zoneName;

    private String spec;

    /**
     * floatingIp 不对外暴露
     */
    @JsonIgnore
    private String floatingIp;

    private String chargeType;

    /**
     * for example m-xxx
     */
    private String imageId;

    /**
     * for example Ubuntu or Centos
     */
    private String osName;

    /**
     * for example 20.04 LTS
     */
    private String osVersion;

    /**
     * for example ENG
     */
    private String osLang;

    /**
     * for example linux or Windows
     */
    private String osType;

    /**
     * cpu and other resource of this node
     */
    private InstanceResource totalResource;

    /**
     * cpu and other resource used of this node
     */
    private InstanceResource allocatedResource;

    private String scheduleStatus;

    private List<Map<String, String>> labels;

    /**
     * 节点归属 cloud：云上节点 local：本地节点
     */
    private String attribution;
    /**
     * 调度器节点 ip
     */
    private String schedulerIp;
    /**
     * 调度器节点 host
     */
    private String schedulerHost;

    /**
     * ssh port
     */
    private Long sshPort;
    /**
     * portal port
     */
    private Long portalPort;
}

