package com.baidu.bce.logic.chpc.common;

import lombok.Getter;

import java.util.Arrays;

/**
 * @Author: lilu24
 * @Date: 2022-12-28
 */
@Getter
public enum TaskStatus {


    PROCESSING("processing"),
    TIMEOUT("timeout"),

    SUCCEED("succeed"),
    FAILED("failed");

    private final String value;

    TaskStatus(String value) {
        this.value = value;
    }


    public static TaskStatus fromValue(String input) {
        return Arrays.stream(TaskStatus.values())
                .filter(b -> b.value.equals(input))
                .findFirst()
                .orElse(null);
    }
}
