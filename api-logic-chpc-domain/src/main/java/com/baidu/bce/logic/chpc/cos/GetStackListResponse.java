package com.baidu.bce.logic.chpc.cos;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetStackListResponse {

    Boolean success;

    String msg;

    Result result;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result {
        List<StackResult> stacks;
        String orderBy;
        String order;
        Integer pageNo;
        Integer pageSize;
        Integer totalCount;
    }

}
