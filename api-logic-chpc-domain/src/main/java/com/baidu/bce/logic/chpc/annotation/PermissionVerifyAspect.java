package com.baidu.bce.logic.chpc.annotation;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;
// import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

import java.io.UnsupportedEncodingException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.baidu.bce.internalsdk.iam.IamNioClient;
// import com.baidu.bce.internalsdk.iam.IamNioClient;
import com.baidu.bce.internalsdk.iam.model.BatchPermissionRequest;
import com.baidu.bce.internalsdk.iam.model.BatchVerifyResults;
import com.baidu.bce.internalsdk.iam.model.VerifyResult;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionException;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.permission.QueueVOForPermission;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: huxiguo
 * @Date: 2024-03-22
 */
@Slf4j
@Aspect
@Component
public class PermissionVerifyAspect {
    @Resource
    private ClusterDAOGateway clusterDAOGateway;

    @Resource
    private QueueDAOGateway queueDAOGateway;

    @Resource
    private BackendGateway backendGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    private static final Logger LOGGER = LoggerFactory.getLogger(PermissionVerifyAspect.class);

    @Autowired
    private IamLogicService iamLogicService;

    @Resource
    private IamNioClient iamNioClient;

    // @Resource
    // private IamNioClient iamNioClient;

    @Pointcut("@annotation(com.baidu.bce.logic.chpc.annotation.PermissionVerify)")
    public void pointCut() {
    }

    /**
     * 对于参数，如果要指定资源，必须在参数上添加IdPermission注解，无论是字符串还是对象；
     * 对象中的属性，要在资源id的字段上加上IdPermission注解，并指定服务号和权限列表
     *
     * @param proceedingJoinPoint
     * @return
     * @throws Throwable
     */
    @Around("pointCut()")
    public Object permissionVerifyInterceptor(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        boolean permissionVerifySuccess = false;
        boolean skipPermissionVerify = false;
        List<String> resourceIDs = new ArrayList<>();
        String[] permissions = {};
        try {
            // STEP1. 获取鉴权所需的上下文
            // 获取临时权限Token（一般用在临时授权带有ACL限制的场景）
            HttpServletRequest request =
                    ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
            // header 不存在，返回 null
            String securityToken = request.getHeader("x-bce-security-token");
            LOGGER.debug("got securityToken {}", securityToken);

            MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
            Method method = methodSignature.getMethod();

            // 从注解中获取『类型』
            PermissionVerify permissionVerifyAnnotation = method.getAnnotation(PermissionVerify.class);
            if (permissionVerifyAnnotation == null) {
                throw new Exception(String.format(
                        "method %s is invalid, there is no PermissionVerify annotation decorating it", method.getName()));
            }

            String serviceID = permissionVerifyAnnotation.service();
            permissions = permissionVerifyAnnotation.permissions();

            resourceIDs = new ArrayList<>();
            String resourceOwner = null;
            String resourceType = null;
            PermissionVerify.ResourceLocation realResourceLocation = permissionVerifyAnnotation.resourceLocation();
            if (PermissionVerify.ResourceLocation.NO_RESOURCE_ID == realResourceLocation) {
                resourceIDs.add(PermissionConstant.DEFAULT_CREATE_RESOURCE);
                resourceOwner = PermissionConstant.RESOURCE_OWNER_FOR_CREATING;
                resourceType = PermissionConstant.RESOURCE_TYPE_NORMAL;

            } else if (PermissionVerify.ResourceLocation.NO_RESOURCE_ID_CLUSTER_LIST == realResourceLocation) {
                // 针对集群列表，需要获取全部的集群列表去IAM批量鉴权
                List<Cluster> clusters = clusterDAOGateway.findByAccountIdAll(LogicUserService.getAccountId());
                String userAccountID = LogicUserService.getAccountId();
                LOGGER.debug("in permissionVerifyInterceptor cluster list, resourceIDs {}, userAccountID {}", resourceIDs,
                        userAccountID);
                if (clusters.isEmpty()) {
                    skipPermissionVerify = true;
                } else {
                    for (Cluster c : clusters) {
                        resourceIDs.add(c.getClusterId());
                    }
                }
                resourceOwner = userAccountID;
                resourceType = PermissionConstant.RESOURCE_TYPE_CLUSTER_LIST;

            } else if (PermissionVerify.ResourceLocation.NO_RESOURCE_ID_QUEUE_LIST == realResourceLocation) {
                // 针对队列列表，需要获取该集群全部的队列列表去IAM批量鉴权
                resourceIDs.addAll(getResourceIDs(proceedingJoinPoint, permissionVerifyAnnotation));
                String userAccountID = LogicUserService.getAccountId();
                LOGGER.debug("in permissionVerifyInterceptor queue list, resourceIDs {}, userAccountID {}", resourceIDs,
                        userAccountID);
                // 对于queueList场景，cluster id是必需的，如果不指定，直接鉴权失败
                if (CollectionUtils.isEmpty(resourceIDs)) {
                    throw new PermissionException.PermissionDenyExceptionWithEmptyResource();
                }
                // 获取当前集群的队列列表
                String clusterId = resourceIDs.get(0);
                Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
                if (cluster == null) {
                    skipPermissionVerify = true;
                } else {
                    List<QueueVOForPermission> queues = new ArrayList<>();
                    if (cluster.getSchedulePlugin() == 0) {
                        queues = queueDAOGateway.listByClusterId(clusterId)
                                .stream()
                                .map(QueueVOForPermission::convertToGroupVOTest)
                                .collect(Collectors.toList());
                    } else {
                        try {
                            BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "queue_list", "--console");
                            LOGGER.debug("BackendActionProxyResponse {}", resp.getData());
                            ObjectMapper mapper = new ObjectMapper();
                            try {
                                queues = mapper.readValue(resp.getData(), mapper.getTypeFactory().constructCollectionType(List.class,
                                        QueueVOForPermission.class));
                            } catch (Exception e) {
                                LOGGER.error("listQueues error", e);
                                queues = new ArrayList<>();
                            }
                        } catch (WebClientResponseException e) {
                            LOGGER.error("BackendActionProxyResponse error", e);
                            if (!e.getResponseBodyAsString().contains("No master instance")) {
                                throw new PermissionException.ResourceNotExistException(e.getResponseBodyAsString());
                            }
                        } catch (Exception e) {
                            LOGGER.error("BackendActionProxyResponse error", e);
                            if (!e.getMessage().contains("No master instance")) {
                                throw new PermissionException.ResourceNotExistException(e.getMessage());
                            }
                        }
                    }

                    LOGGER.debug("in permissionVerifyInterceptor queue list, clusterId {}, queues {}", clusterId, queues);
                    if (CollectionUtils.isEmpty(queues)) {
                        skipPermissionVerify = true;
                    } else {
                        for (QueueVOForPermission q : queues) {
                            resourceIDs.add(q.getQueueName());
                        }
                    }
                }

                resourceOwner = userAccountID;
                resourceType = PermissionConstant.RESOURCE_TYPE_QUEUE_LIST;

            } else if (PermissionVerify.ResourceLocation.NO_RESOURCE_ID_SERVICE_LIST == realResourceLocation) {
                resourceIDs.add(PermissionConstant.DEFAULT_CREATE_RESOURCE);
                resourceOwner = PermissionConstant.RESOURCE_OWNER_FOR_CREATING;
                resourceType = PermissionConstant.RESOURCE_TYPE_SERVICE_LIST;

            } else if (PermissionVerify.ResourceLocation.NO_RESOURCE_ID_JOB_LIST == realResourceLocation) {
                resourceIDs.add(PermissionConstant.DEFAULT_CREATE_RESOURCE);
                resourceOwner = PermissionConstant.RESOURCE_OWNER_FOR_CREATING;
                resourceType = PermissionConstant.RESOURCE_TYPE_JOB_LIST;

            } else {
                resourceIDs.addAll(getResourceIDs(proceedingJoinPoint, permissionVerifyAnnotation));
                String userAccountID = LogicUserService.getAccountId();
                LOGGER.debug("in permissionVerifyInterceptor in string, resourceIDs {}, userAccountID {}", resourceIDs,
                        userAccountID);
                // 对于非create/list场景，resource id是必需的，如果不指定，直接鉴权失败
                if (CollectionUtils.isEmpty(resourceIDs)) {
                    throw new PermissionException.PermissionDenyExceptionWithEmptyResource();
                }
                // TODO: 2024-03-22
                // if (!resourcesBelongToThisAccount(resourceIDs, userAccountID)) {
                //     throw new AccessNonOwnedResource();
                // }
                resourceOwner = userAccountID;
                resourceType = PermissionConstant.RESOURCE_TYPE_NORMAL;
            }

            LOGGER.debug("got skipPermissionVerify {} service {}, permissions {}, resourceIDs {}, resourceType {}, resourceOwner {}",
                    skipPermissionVerify, serviceID, permissions, resourceIDs, resourceType, resourceOwner);
            if (!skipPermissionVerify) {
                permissionVerifySuccess = permissionVerifySuccess(resourceIDs, resourceType, resourceOwner, serviceID, permissions, securityToken);
            }

            if (PermissionVerify.ResourceLocation.NO_RESOURCE_ID_CLUSTER_LIST == realResourceLocation ||
                PermissionVerify.ResourceLocation.NO_RESOURCE_ID_QUEUE_LIST == realResourceLocation ||
                PermissionVerify.ResourceLocation.NO_RESOURCE_ID_JOB_LIST == realResourceLocation) {
                permissionVerifySuccess = true;
            }

        } catch (Throwable throwable) {
            LOGGER.debug("permission verify error, {}", throwable.toString());
            throw throwable;
        }

        if (!permissionVerifySuccess) {
            LOGGER.debug("permission verify failed");
            throw new PermissionException.PermissionDenyException(generatePermissionDenyMessage(resourceIDs, permissions));
        }

        LOGGER.debug("permission verify success");

        // 正常处理方法
        Object result = null;
        try {
            result = proceedingJoinPoint.proceed(proceedingJoinPoint.getArgs());
        } catch (Exception e) {
            LOGGER.debug("permission verify execute failed, error_msg={}", e.getMessage());
            throw e;
        }

        return result;
    }

    /**
     * 去IAM进行权限验证
     * @param resourceIDs
     * @param resourceOwner
     * @param serviceID
     * @param permissions
     * @param securityToken
     * @return 通过返回true， 异常或拒绝返回false
     */
    public boolean permissionVerifySuccess(List<String> resourceIDs, String resourceType, String resourceOwner, String serviceID,
                                                    String[] permissions, String securityToken) throws UnsupportedEncodingException {
        LOGGER.debug("STEP1. 准备IAM鉴权所需的上下文信息");
        String region = regionConfiguration.getCurrentRegion();
        if (StringUtils.isBlank(region)) {
            region = "bj";
        }
        String userId = LogicUserService.getUserId();
        List<String> resourcesWithPrefix = new ArrayList<String>();
        /*
        for (String resourceID : resourceIDs) {
            if (StringUtils.equals(resourceID, PermissionConstant.DEFAULT_CREATE_RESOURCE)) {
                resourcesWithPrefix.add("cluster/" + resourceID);
            } else {
                resourcesWithPrefix.add("cluster/" + resourceID);
            }
        }
        */
        if (PermissionConstant.RESOURCE_TYPE_NORMAL.equals(resourceType)) {
            if (resourceIDs.size() == 1) { // {*} or {c-xxx}
                String element = resourceIDs.iterator().next();
                resourcesWithPrefix.add(PermissionConstant.RESOURCE_CLUSTER_PREFIX + element);
            } else if (resourceIDs.size() == 2) { // {c-xxx, xxx-q}
                String clusterElement = "";
                String queueElement = "";
                for (String element : resourceIDs) {
                    if (element.startsWith("c-")) {
                        clusterElement = PermissionConstant.RESOURCE_CLUSTER_PREFIX + element;
                    } else {
                        // 需要保证队列名字不以c-开头命名
                        queueElement = PermissionConstant.RESOURCE_QUEUE_PREFIX + element;
                    }
                }
                resourcesWithPrefix.add(clusterElement + "/" + queueElement);
            }
        } else if (PermissionConstant.RESOURCE_TYPE_CLUSTER_LIST.equals(resourceType)){
            for (String element : resourceIDs) {
                resourcesWithPrefix.add(PermissionConstant.RESOURCE_CLUSTER_PREFIX + element);
            }
        } else if (PermissionConstant.RESOURCE_TYPE_QUEUE_LIST.equals(resourceType)){
            String clusterElement = PermissionConstant.RESOURCE_CLUSTER_PREFIX + resourceIDs.get(0);
            for (int i = 1; i < resourceIDs.size(); i++) {
                String element = resourceIDs.get(i);
                String queueElement = PermissionConstant.RESOURCE_QUEUE_PREFIX + element;
                resourcesWithPrefix.add(clusterElement + "/" + queueElement);
            }
        } else if (PermissionConstant.RESOURCE_TYPE_SERVICE_LIST.equals(resourceType)){
            for (String element : resourceIDs) {
                resourcesWithPrefix.add(PermissionConstant.RESOURCE_SERVICE_PREFIX + element);
            }
        } else if (PermissionConstant.RESOURCE_TYPE_JOB_LIST.equals(resourceType)) {
            for (String element : resourceIDs) {
                resourcesWithPrefix.add(PermissionConstant.RESOURCE_JOB_PREFIX + element);
            }
        }

        List<String> permissionList = Arrays.asList(permissions);

        LOGGER.debug("STEP2. 准备 IAM client {}", region);
        String authToken = iamLogicService.getConsoleToken(region).join().getId();
        LOGGER.debug("STEP3. 生成批量鉴权请求 {} {}", permissionList, authToken);
        BatchPermissionRequest batchPermissionRequest = new BatchPermissionRequest();
        List<BatchPermissionRequest.Request> verifyList = new ArrayList<>();
        BatchPermissionRequest.Request verifyItem = new BatchPermissionRequest.Request();
        verifyItem.setPermission(permissionList);
        verifyItem.setRegion(region);
        verifyItem.setResourceOwner(resourceOwner);
        verifyItem.setResource(resourcesWithPrefix);
        verifyItem.setService(serviceID);
        verifyList.add(verifyItem);
        batchPermissionRequest.setVerifyList(verifyList);
        if (securityToken != null) {
            LOGGER.debug("STEP3.5. 获取securityToken {}", securityToken);
            batchPermissionRequest.setSecurityToken(securityToken);
        }

        LOGGER.debug("STEP4. 发起鉴权请求");
        BatchVerifyResults verifyResults = iamNioClient.batchVerify(userId, batchPermissionRequest, false).block();
        if (verifyResults == null) {
            LOGGER.debug("verify results is null");
            return false;
        }
        List<BatchVerifyResults.BatchVerifyResult> results = verifyResults.getVerifyResults();
        if (CollectionUtils.isEmpty(results) || results.size() != 1) {
            LOGGER.debug("size of verify results must be 1");
            return false;
        }

        // 保存批量鉴权结果，回传到业务代码中做筛选
        AspectResultHolder.setAspectResult(results);

        BatchVerifyResults.BatchVerifyResult result = results.get(0);
        if (result == null || CollectionUtils.isEmpty(result.getResult())) {
            LOGGER.debug("size of sub verify results is 0");
            return false;
        }

        for (VerifyResult subResult : result.getResult()) {
            if (subResult == null) {
                return false;
            }
            if (!PermissionConstant.ALLOW_PERMISSION.equals(subResult.getEffect())) {
                LOGGER.debug("permission not allow. result = {}", subResult);
                return false;
            }
        }
        LOGGER.debug("verify success!");
        return true;
    }

    /**
     * 获取本次请求的资源ID
     *
     * @param proceedingJoinPoint
     * @param permissionVerifyAnn
     * @return 本次请求的资源ID列表
     */
    private Set<String> getResourceIDs(ProceedingJoinPoint proceedingJoinPoint, PermissionVerify permissionVerifyAnn) {
        Set<String> resourceIds = new HashSet<>();
        // STEP1. 拿到所有用 PermissionResourceID 修饰的参数
        MethodSignature methodSignature = (MethodSignature) proceedingJoinPoint.getSignature();
        Method method = methodSignature.getMethod();
        Object[] args = proceedingJoinPoint.getArgs();                           // 获取所有参数
        Annotation[][] parameterAnnotations = method.getParameterAnnotations();  // 获取所有参数的注解，与args顺序对应
        for (int argIndex = 0; argIndex < args.length; argIndex++) {
            for (Annotation annotation : parameterAnnotations[argIndex]) {
                if (annotation instanceof PermissionResourceID) {
                    resourceIds.add(args[argIndex].toString());
                }
            }
        }

        return resourceIds;
    }

    // private IamClient getIamClient(String region) {
    //     if (iamClient != null) {
    //         return iamClient;
    //     }
    //     String endpoint = EndpointManager.getInstance().getRegion(region).getEndpoint(IamClient.SERVICE_NAME);
    //     iamClient = new IamClient(endpoint);
    //     iamClient.setSubuserEnabled(true);
    //     return iamClient;
    // }

    private String generatePermissionDenyMessage(List<String> resourceIDs, String[] permissions) {
        List<String> defaultSysPermissions = Arrays.asList(
                PermissionConstant.CHPC_CONTROL,
                PermissionConstant.CHPC_OPERATE,
                PermissionConstant.CHPC_READ
        );
        List<String> sysPermissions = new ArrayList<>();
        List<String> cusPermissions = new ArrayList<>();
        for (String p : permissions) {
            if (defaultSysPermissions.contains(p)) {
                sysPermissions.add(p);
            } else {
                cusPermissions.add(p);
            }
        }
        String msg = String.format("针对如下资源：%s，当前用户缺少如下系统权限：%s",
                String.join(", ", resourceIDs),
                String.join(", ", sysPermissions)
        );
        if (!cusPermissions.isEmpty()) {
            msg += String.format(" 或 自定义权限：%s", String.join(", ", cusPermissions));
        }
        msg += "，请联系该账户管理员进行添加";
        return msg;
    }
}