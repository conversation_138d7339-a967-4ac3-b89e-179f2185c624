package com.baidu.bce.logic.chpc.cfs;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FileSystem {
    private String fsId;
    private String fsName;
    private String vpcId;
    private String type;
    private String protocol;
    private String fsUsage;
    private String status;
    private Integer capacityQuota;
    @JsonProperty("mountTargetList")
    private List<MountTarget> mountTargets;


}
