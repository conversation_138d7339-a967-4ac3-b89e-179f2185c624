package com.baidu.bce.logic.chpc.gateway.external.client;

import java.util.Map;

import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListAvailableTagsResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ModifyTagsRequest;
import org.springframework.web.reactive.function.client.WebClient;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceNioClient;
import com.baidu.bce.internalsdk.core.WebclientConfigProperties;
import com.baidu.bce.internalsdk.core.WebclientConfigUtil;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AutoScalingProbeRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AutoScalingProbeResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionScriptResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendTaskResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ComputeNode;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.CreateComputeNodeRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.CreateQueueRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.CreateWorkflowRunRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.GetClusterResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.GetComputeNodeResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListComputeNodesResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListQueuesResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ModifyClusterRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ShrinkNodesRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ShrinkNodesResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.StopClusterRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunResResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunStatusResponse;

import lombok.extern.slf4j.Slf4j;

import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AuthDomainUserRequest;


/**
 * @Author: lilu24
 * @Date: 2023-01-06
 */
@Slf4j
public class BackendNioClient extends BceNioClient {

    String accountId;

    public BackendNioClient(WebClient webClient, Map<String, WebclientConfigProperties> configs) {
        super(webClient, configs);
    }

    public BackendCommonResponse startCluster() {
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path("/v1/cluster/start").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(BackendCommonResponse.class)
            .block();
    }

    public BackendCommonResponse stopCluster(Boolean force) {
        StopClusterRequest request = new StopClusterRequest();
        request.setForce(force);
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path("/v1/cluster/stop").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(BackendCommonResponse.class)
            .block();
    }

    public BackendCommonResponse modifyCluster(ModifyClusterRequest request) {
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path("/v1/cluster/modify").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(BackendCommonResponse.class)
            .block();
    }
    public ListAvailableTagsResponse listAvailableTags() {
        return webClient
                .get()
                .uri(uriBuilder -> uriBuilder.path("/v1/tags").build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .retrieve()
                .bodyToMono(ListAvailableTagsResponse.class)
                .block();
    }

    public BackendCommonResponse modifyTags(ModifyTagsRequest request) {
        return webClient
                .put()
                .uri(uriBuilder -> uriBuilder.path("/v1/tags").build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(request)
                .retrieve()
                .bodyToMono(BackendCommonResponse.class)
                .block();
    }

    public GetClusterResponse getClusterDetail() {
        return webClient
            .get()
            .uri(uriBuilder -> uriBuilder.path("/v1/cluster").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(GetClusterResponse.class)
            .block();
    }

    public BackendCommonResponse addQueue(CreateQueueRequest createQueueRequest) {
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path("/v1/queue").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .bodyValue(createQueueRequest)
            .retrieve()
            .bodyToMono(BackendCommonResponse.class)
            .block();
    }

    public BackendCommonResponse removeQueue(String queueName, Boolean force, Boolean check) {
        return webClient
            .delete()
            .uri(
                uriBuilder ->
                    uriBuilder
                        .path("/v1/queue/" + queueName)
                        .queryParam("force", force == null ? false : force)
                        .queryParam("check", check == null ? false : check)
                        .build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(BackendCommonResponse.class)
            .block();
    }

    public BackendCommonResponse updateQueueAutoScaling(CreateQueueRequest updateQueueSpecRequest, String queueName) {
        return webClient
            .patch()
            .uri(uriBuilder -> uriBuilder.path("/v1/queue/" + queueName).build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .bodyValue(updateQueueSpecRequest)
            .retrieve()
            .bodyToMono(BackendCommonResponse.class)
            .block();
    }

    public ListQueuesResponse listQueue() {
        return webClient
            .get()
            .uri(uriBuilder -> uriBuilder.path("/v1/queues").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(ListQueuesResponse.class)
            .block();
    }

    public ListComputeNodesResponse listComputeNodes(String queueName) {
        return webClient
            .get()
            .uri(uriBuilder -> uriBuilder.path(String.format("/v1/queue/%s/compute_nodes", queueName)).build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(ListComputeNodesResponse.class)
            .block();
    }

    public GetComputeNodeResponse getComputeNodeDetail(String queueName, String hostName, String instanceId) {
        return webClient
            .get()
            .uri(
                uriBuilder ->
                    uriBuilder
                        .path(String.format("/v1/queue/%s/compute_node/%s", queueName, instanceId))
                        .queryParam("nodeName", hostName)
                        .build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(GetComputeNodeResponse.class)
            .block();
    }

    public BackendCommonResponse removeNode(
        String queueName, String hostName, String instanceId, Boolean autoScaling, Boolean force) {
        return webClient
            .delete()
            .uri(
                uriBuilder ->
                    uriBuilder
                        .path(String.format("/v1/queue/%s/compute_node/%s", queueName, instanceId))
                        .queryParam("force", String.valueOf(force))
                        .queryParam("autoScaling", String.valueOf(autoScaling))
                        .queryParam("nodeName", hostName)
                        .build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(BackendCommonResponse.class)
            .block();
    }

    public BackendCommonResponse addNode(String queueName, ComputeNode node) {
        CreateComputeNodeRequest createComputeNodeRequest = new CreateComputeNodeRequest();
        createComputeNodeRequest.setNode(node);
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path(String.format("/v1/queue/%s/compute_node", queueName)).build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .bodyValue(createComputeNodeRequest)
            .retrieve()
            .bodyToMono(BackendCommonResponse.class)
            .block();
    }

    public BackendTaskResponse getTaskDetail(String taskId) {
        return webClient
            .get()
            .uri(uriBuilder -> uriBuilder.path(String.format("/v1/task/%s", taskId)).build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(BackendTaskResponse.class)
            .block();
    }

    public AutoScalingProbeResponse autoScalingProbe(AutoScalingProbeRequest request) {
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path("/v1/autoscaling/probe").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(AutoScalingProbeResponse.class)
            .block();
    }

    public WorkflowRunStatusResponse getWorkflowRunStatus(String runUuid) {
        return webClient
            .get()
            .uri(uriBuilder -> uriBuilder.path(String.format("/api/workflows/v1/%s/status", runUuid)).build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(WorkflowRunStatusResponse.class)
            .block();
    }

    public WorkflowRunStatusResponse abortWorkflow(String runUuid) {
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path(String.format("/api/workflows/v1/%s/abort", runUuid)).build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(WorkflowRunStatusResponse.class)
            .block();
    }

    public WorkflowRunStatusResponse createWorkflowRun(CreateWorkflowRunRequest request) {
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path("/v1/workflow/run").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .bodyValue(request)
            .retrieve()
            .bodyToMono(WorkflowRunStatusResponse.class)
            .block();
    }

    public WorkflowRunResResponse getWorkflowRunRes(String runUuid) {
        return webClient
            .get()
            .uri(uriBuilder -> uriBuilder.path(String.format("/api/workflows/v1/%s/outputs", runUuid)).build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .retrieve()
            .bodyToMono(WorkflowRunResResponse.class)
            .block();
    }

    public BackendActionProxyResponse actionProxy(BackendActionProxyRequest req) {
        if (req.getNoRetry() != null && req.getNoRetry()) {
            return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path("/v1/action/proxy").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .header("No-Retry", "true")
            .bodyValue(req)
            .retrieve()
            .bodyToMono(BackendActionProxyResponse.class)
            .block();
        } else {
            return webClient.post().uri(uriBuilder -> uriBuilder.path("/v1/action/proxy").build()).httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs)).
            attribute(BceConstant.X_BCE_ACCOUNT, accountId).bodyValue(req).retrieve().bodyToMono(BackendActionProxyResponse.class).block();
        }
    }

    public BackendActionScriptResponse actionScript(BackendActionProxyRequest req) {
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path("/v1/action/script").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .bodyValue(req)
            .retrieve()
            .bodyToMono(BackendActionScriptResponse.class)
            .block();
    }

    public ShrinkNodesResponse shrinkNodes(ShrinkNodesRequest req) {
        return webClient
            .post()
            .uri(uriBuilder -> uriBuilder.path("/v1/autoscaling/shrinknodes").build())
            .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
            .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
            .bodyValue(req)
            .retrieve()
            .bodyToMono(ShrinkNodesResponse.class)
            .block();
    }

    public BackendCommonResponse authDomainUser(AuthDomainUserRequest req){
        return webClient
                .post()
                .uri(uriBuilder -> uriBuilder.path("/v1/user/auth").build())
                .httpRequest(WebclientConfigUtil.defautlRequestConsumer(configs))
                .attribute(BceConstant.X_BCE_ACCOUNT, accountId)
                .bodyValue(req)
                .retrieve()
                .bodyToMono(BackendCommonResponse.class)
                .block();

    }
}
