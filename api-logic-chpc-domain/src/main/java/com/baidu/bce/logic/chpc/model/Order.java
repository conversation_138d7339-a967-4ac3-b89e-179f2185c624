package com.baidu.bce.logic.chpc.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class Order {
    /**
     * 订单 id
     */
    private String orderId;
    /**
     * 主账户 id
     */
    private String accountId;
    /**
     * 服务类型
     */
    private String serviceType;
    /**
     * 产品类型
     */
    private String productType;
    /**
     * 产品类型
     */
    private String itemKey;
    /**
     * 资源状态
     */
    private String status;
    /**
     * 启停原因
     */
    private String reason;
    /**
     * 是否删除
     */
    private Boolean deleted;

    private LocalDateTime createdTime;

    private LocalDateTime updatedTime;

}
