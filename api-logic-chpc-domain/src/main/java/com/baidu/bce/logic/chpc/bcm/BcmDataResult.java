package com.baidu.bce.logic.chpc.bcm;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BcmDataResult {
    String region;

    String scope;

    String userId;

    String resourceId;

    String metricName;

    List<BcmDimension> dimensions;

    List<BcmDataPoint> dataPoints;
}
