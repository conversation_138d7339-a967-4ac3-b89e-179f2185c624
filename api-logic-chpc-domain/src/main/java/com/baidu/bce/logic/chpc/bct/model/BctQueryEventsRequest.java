package com.baidu.bce.logic.chpc.bct.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BctQueryEventsRequest {
    private String domainId;
    private List<FieldFilter> filters = new ArrayList<>();
    @JsonFormat(
        shape = Shape.STRING,
        pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'",
        timezone = "UTC"
    )
    @NotNull(message = "初始时间不能为空")
    private Date startTime;
    @JsonFormat(
        shape = Shape.STRING,
        pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'",
        timezone = "UTC"
    )
    @NotNull(message = "结束时间不能为空")
    private Date endTime;
    private int pageNo;
    @Max(1000)
    private int pageSize;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FieldFilter {

        public FieldFilter(){
        }

        public FieldFilter(String field, String value) {
            this.field = field;
            this.value = value;
        }

        String field;

        String value;

    }

}
