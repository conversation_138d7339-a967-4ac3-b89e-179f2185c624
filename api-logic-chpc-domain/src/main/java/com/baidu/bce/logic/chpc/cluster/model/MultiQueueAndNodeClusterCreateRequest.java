package com.baidu.bce.logic.chpc.cluster.model;

import com.baidu.bce.logic.chpc.cfs.CfsAddRequest;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.queue.QueueAddRequest;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
@Validated
public class MultiQueueAndNodeClusterCreateRequest implements IClusterCreateRequest{
    private String clientToken;

    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){1,13}[a-zA-Z0-9]$",
            message = "parameter is invalid.")
    protected String clusterName; // 集群名称

    @Pattern(regexp = "^.{0,250}$", message = "is invalid")
    protected String description; // 集群描述

    /**
     * 集群中所有节点的付费类型，取值范围：
     * * Postpaid：按量付费
     * * Prepaid：包年包月
     * 默认 Postpaid
     */
    protected String chargeType = ChargeType.Postpaid.name();

    /**
     * 作业调度器类型
     * - slurm
     * - sge
     */
    protected String schedulerType = "slurm";

    /**
     * 购买集群节点的时长。单位由periodUnit指定，取值范围：
     * * 当参数priceUnit取值为Year时：1~3。
     * * 当参数priceUnit取值为Month时：1~9。
     * 默认值：1。
     */
    protected int period = 1;

    /**
     * 购买集群节点的时长单位。取值范围：
     * * Year
     * * Month
     * 默认值：Month。
     */
    protected String periodUnit = "Month";

    protected boolean autoRenew = false; // 预付费时，是否自动续费

    protected Integer autoRenewPeriod = 1;

    protected String autoRenewPeriodUnit = "Month";


    protected boolean enableHa; // 管控节点是否开启高可用

    @NotNull(message = "must not be null.")
    @Valid
    protected InstanceAddRequest managerNode;

    protected List<CfsAddRequest> cfsVolumes;

    @NotEmpty(message = "must not be empty, because default queue was required.")
    @Valid
    protected List<QueueAddRequest> queues;

    @NotEmpty
    protected String vpcId;

    @NotEmpty
    protected String securityGroupId;

    public String getClientToken() {
        return clientToken;
    }

    public void setClientToken(String clientToken) {
        this.clientToken = clientToken;
    }

    @Override
    public String getClusterName() {
        return clusterName;
    }

    public void setClusterName(String clusterName) {
        this.clusterName = clusterName;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public String getDefaultQueueName() {
        return this.queues.stream().filter(QueueAddRequest::isDefault).findFirst().get().getQueueName();
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String getChargeType() {
        return chargeType;
    }

    public void setChargeType(String chargeType) {
        this.chargeType = chargeType;
    }

    @Override
    public String getSchedulerType() {
        return schedulerType;
    }

    public void setSchedulerType(String schedulerType) {
        this.schedulerType = schedulerType;
    }

    @Override
    public int getPeriod() {
        return period;
    }

    public void setPeriod(int period) {
        this.period = period;
    }

    @Override
    public String getPeriodUnit() {
        return periodUnit;
    }

    public void setPeriodUnit(String periodUnit) {
        this.periodUnit = periodUnit;
    }

    @Override
    public boolean isAutoRenew() {
        return autoRenew;
    }

    public void setAutoRenew(boolean autoRenew) {
        this.autoRenew = autoRenew;
    }

    @Override
    public Integer getAutoRenewPeriod() {
        return autoRenewPeriod;
    }

    public void setAutoRenewPeriod(Integer autoRenewPeriod) {
        this.autoRenewPeriod = autoRenewPeriod;
    }

    @Override
    public String getAutoRenewPeriodUnit() {
        return autoRenewPeriodUnit;
    }

    @Override
    public String getZoneName() {
        return this.managerNode.getZoneName();
    }

    public void setAutoRenewPeriodUnit(String autoRenewPeriodUnit) {
        this.autoRenewPeriodUnit = autoRenewPeriodUnit;
    }

    @Override
    public boolean isEnableHa() {
        return enableHa;
    }

    public void setEnableHa(boolean enableHa) {
        this.enableHa = enableHa;
    }

    @Override
    public InstanceAddRequest getManagerNode() {
        return managerNode;
    }

    public void setManagerNode(InstanceAddRequest managerNode) {
        this.managerNode = managerNode;
    }

    @Override
    public List<CfsAddRequest> getCfsVolumes() {
        return cfsVolumes;
    }

    public void setCfsVolumes(List<CfsAddRequest> cfsVolumes) {
        this.cfsVolumes = cfsVolumes;
    }

    @Override
    public List<QueueAddRequest> getQueues() {
        return queues;
    }

    public void setQueues(List<QueueAddRequest> queues) {
        this.queues = queues;
    }

    @Override
    public String getVpcId() {
        return vpcId;
    }

    public void setVpcId(String vpcId) {
        this.vpcId = vpcId;
    }

    @Override
    public String getSecurityGroupId() {
        return securityGroupId;
    }

    public void setSecurityGroupId(String securityGroupId) {
        this.securityGroupId = securityGroupId;
    }
}
