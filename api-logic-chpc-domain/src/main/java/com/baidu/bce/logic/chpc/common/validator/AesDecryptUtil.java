package com.baidu.bce.logic.chpc.common.validator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

@Slf4j
@Component
public class AesDecryptUtil {
    private static final String ALGORITHM = "AES";
    private static final String ALGORITHM_FULL = "AES/ECB/PKCS5Padding";
    private static final String CHARSET_NAME = "UTF-8";
    private static final char[] HEX_CHARS = {'0', '1', '2', '3', '4', '5', '6', '7',
            '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
    private static final String AES_KEY = "IdsGHHX2yYShLzT1";



    /**
     * 对传入的 content 进行 AES 解密，返回字符串
     *
     */
    public static String decrypt(String content) {
        try {
            // AES decrypt operation
            byte[] bytes = doAes(hexStr2Bytes(content), AES_KEY, Cipher.DECRYPT_MODE);
            // AES 解密后的字节数组转换为字符串
            return new String(bytes, CHARSET_NAME);
        } catch (Exception e) {
            return null;
        }
    }
    /**
     * AES 对称加密解密 Operation
     *
     */
    public static byte[] doAes(byte[] contentBytes, String hexKey, int mode) {
        try {
            // 生成 AES 密钥
            SecretKeySpec secretKeySpec = new SecretKeySpec(hexKey.getBytes(CHARSET_NAME), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM_FULL);
            // init 密码器，加密（ENCRYPT_MODE）or 解密（DECRYPT_MODE）
            cipher.init(mode, secretKeySpec);
            return cipher.doFinal(contentBytes);
        } catch (Exception e) {
            return null;
        }
    }
    /**
     * hex string to byte[]
     * 2 hex = 1 byte
     *
     */
    public static byte[] hexStr2Bytes(String hexStr) {
        if (hexStr == null || hexStr.trim().length() == 0 ) {
            return null;
        }
        byte[] bytes = new byte[hexStr.length() >> 1];
        for (int i = 0; i < bytes.length; i++) {
            String subStr = hexStr.substring(i * 2, i * 2 + 2);
            bytes[i] = (byte) Integer.parseInt(subStr, 16);
        }
        return bytes;
    }
}