package com.baidu.bce.logic.chpc.tag.gateway;

import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;

import java.util.List;

public interface TagsGateway {

    // 创建标签
    void createTags(List<Tag> tags);

    // 查询标签
    TagAssociationFulls listTags(FullTagListRequest request);

    List<Tag> listTagsV2(FullTagListRequest request);

    // 更新/删除标签
    void createAndAssignTag(CreateAndAssignTagRequest request);

}
