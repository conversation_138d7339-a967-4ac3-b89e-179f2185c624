package com.baidu.bce.logic.chpc.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Author: lilu24
 * @Date: 2023-01-18
 * <p>
 * 此注解用于校验controller中，请求路径参数中的资源是否存在，请求参数包括：
 * - clusterId
 * - groupId
 * - instanceId
 * 路径参数命名规范必须按照上述参数配置
 */
@Target(ElementType.METHOD)
@Retention(value = RetentionPolicy.RUNTIME)
public @interface ValidateAuthentication {


}
