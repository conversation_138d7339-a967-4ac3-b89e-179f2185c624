package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WorkflowRunResResponse {
    @JsonProperty("id")
    private String id;

    @JsonProperty("outputs")
    private Map<String, String> outputs;
}
