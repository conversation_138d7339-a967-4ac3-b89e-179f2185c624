package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class HostDetail {

    @JsonProperty("hostName")
    private String hostName;

    @JsonProperty("instanceId")
    private String instanceId;

    @JsonProperty("instanceUuid")
    private String instanceUuid;

    @JsonProperty("privateIp")
    private String privateIp;
}
