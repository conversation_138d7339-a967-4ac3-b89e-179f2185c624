package com.baidu.bce.logic.chpc.tag;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@EqualsAndHashCode
@NoArgsConstructor
public class TagListResourcesRequest {
    private String tagKey;   // 标签key
    private String tagValue; // 标签value
    private String orderBy;  // 排序字段，支持：createTime, "name", "region", 默认值：createTime
    private String order;    // 排序类型，支持：asc,desc, 默认值：desc
    private String region;   // 地域, 不填写代表没有
    private Integer pageNo;  // 页码，默认值：1
    private Integer pageSize; // 每页条数，默认值：10,最大值：100
    private Boolean bindable; // 是否绑定标签，默认值：false
}

