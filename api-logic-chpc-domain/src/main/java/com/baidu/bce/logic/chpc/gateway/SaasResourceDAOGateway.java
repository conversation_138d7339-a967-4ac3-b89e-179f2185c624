package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.model.SaasResource;

public interface SaasResourceDAOGateway {

    Boolean insert(SaasResource saasResource);

    Boolean update(SaasResource saasResource);

    Boolean delete(String resourceUuid);

    // 查询当前用户的资源
    SaasResource findByUserId(String userId, String resourceType);

    // 查询所属租户的资源
    SaasResource findByAccountId(String accountId, String resourceType);

    SaasResource findByOrderId(String orderId);

    SaasResource findByResourceUuid(String resourceUuid, String accountId);

}
