package com.baidu.bce.logic.chpc.tag;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ResourcesForTag implements Cloneable {
    private String name = ""; // 资源标识名称
    private String resourceUuid = ""; // Tag绑定的资源场id
    private String resourceId = ""; // Tag绑定的资源短id
    private String region = "";
    private String status = ""; // 同billing状态
    private String productType = ""; // prepay or postpay
    private String createTime = "";
    private Boolean bind = true;
}
