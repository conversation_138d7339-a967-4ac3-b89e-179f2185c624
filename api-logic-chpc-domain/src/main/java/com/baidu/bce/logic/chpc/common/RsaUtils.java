package com.baidu.bce.logic.chpc.common;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.spec.PKCS8EncodedKeySpec;

public class RsaUtils {
    public static final String PUBLIC_KEY = "-----BEGIN PUBLIC KEY-----\n"
            + "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQChKcFfV3FEDaOv2gTOOlRYm+Yk\0"
            + "fcKUtI81/B/klSidVRBtaL/d8s0nerJFv39gi42U5DFewKEMTJlWboAdvQfLshsH\0"
            + "D++x62x4XCFeFz7Uc4H30AXNVHLhB6S1oFlYWldjK+EQ/jpOFh+ct86cLf4pnMrp\0"
            + "tqG35j6d4hDDOtqY9wIDAQAB\n"
            + "-----<PERSON><PERSON> PUBLIC KEY-----";

    public static final String PRIVATE_KEY = "-----B<PERSON>IN PRIVATE KEY-----\n"
            + "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAKEpwV9XcUQNo6/a\0"
            + "BM46VFib5iR9wpS0jzX8H+SVKJ1VEG1ov93yzSd6skW/f2CLjZTkMV7AoQxMmVZu\0"
            + "gB29B8uyGwcP77HrbHhcIV4XPtRzgffQBc1UcuEHpLWgWVhaV2Mr4RD+Ok4WH5y3\0"
            + "zpwt/imcyum2obfmPp3iEMM62pj3AgMBAAECgYBJF6Hu9r6VCcaSK8QAMC4u3c3c\0"
            + "APlt7hIBfu6MAiYgbQL9TAOwyrzGyfvubDJ9++zjeTb27VBdPqRQsd8DHcdZn5Tl\0"
            + "whGhbfi6JlvqZ+YTRO+xq5gEfxr/GmKYkoCeqQ1XMM+XP9vnKMu6LNb58Um71JH0\0"
            + "o1wZhMZOFOZJMslqwQJBAM1Zvr2ZUTh1a9k6QACKbWfFaWh+vLRuWgyGMzfL//s4\0"
            + "tDFM8w1ggNB3K/YD1dwLawrg/rSQpCZNyNlDWQoX0mECQQDI6ex1CQer4BPMDR6W\0"
            + "/2kUEMR/0YxUm5y71aC1JQZc3P/wOlGbAU8SCsYSM0+ShYhHz4AlR0DJmOecTZT4\0"
            + "TFpXAkEAtlhx4oSJNdw21189Q/2n5tTKpAqeYI35ElP8mRhiB1+zym9tCDzgRbz8\0"
            + "4WlIdywAclmWcxpZWNYLermuECNVIQJAdz3eiNeDJg8nsMNYlGWiB7ar6PCFSPHS\0"
            + "Y+i2KMyAqjy/6eOy6zwroZmjVMn/QAbgiz4r3/QaD4wgajPhZjxqoQJABCpqky+v\0"
            + "ArjsP6vFSouX0JUtUVIfD1LRctBrLCIxOZ82zv1r5QFHIq5NHpmQ3du0S8ZzoCfa\0"
            + "HqwiQJfMk1LdMg==\n"
            + "-----END PRIVATE KEY-----";

    private static final String ALGORITHM = "RSA";

    private RsaUtils() {
    }

    public static String decrypt(String content, String privateKey) throws Exception {
        Cipher cipher = Cipher.getInstance(ALGORITHM);
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(
                Base64.decodeBase64(privateKey.split("\n")[1]));
        KeyFactory keyFactory = KeyFactory.getInstance(ALGORITHM);
        PrivateKey priKey = keyFactory.generatePrivate(pkcs8KeySpec);
        cipher.init(Cipher.DECRYPT_MODE, priKey);
        return new String(cipher.doFinal(Base64.decodeBase64(content)));
    }

}
