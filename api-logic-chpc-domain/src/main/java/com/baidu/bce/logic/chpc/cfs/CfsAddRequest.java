package com.baidu.bce.logic.chpc.cfs;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
public class CfsAddRequest {
    /**
     * 共享存储cfsId
     * 例如：cfs-6B76rTO4vS
     */
    protected String cfsId;

    /**
     * cfs挂载点
     * 例如：cfs-6B76rTO4vS.lb-c659e6c1.cfs.bj.baidubce.com
     */
    protected String cfsMountPoint;

    /**
     * 挂载共享存储的目录
     * 默认：/mnt/cfs
     */
    protected String mountDirectory = "/mnt/cfs";

    public String getCfsId() {
        return cfsId;
    }

    public void setCfsId(String cfsId) {
        this.cfsId = cfsId;
    }

    public String getCfsMountPoint() {
        return cfsMountPoint;
    }

    public void setCfsMountPoint(String cfsMountPoint) {
        this.cfsMountPoint = cfsMountPoint;
    }

    public String getMountDirectory() {
        return mountDirectory;
    }

    public void setMountDirectory(String mountDirectory) {
        this.mountDirectory = mountDirectory;
    }
}
