package com.baidu.bce.logic.chpc.autoscaling.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class AutoScaling {

    String asId;

    String asName;

    /**
     * accountId 对外不展示
     */
    @JsonIgnore
    String accountId;

    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    LocalDateTime createdTime;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    LocalDateTime updatedTime;

    /**
     * deleted 对外不展示
     */
    @JsonIgnore
    Boolean deleted;

    String clusterId;

    String clusterName;

    /**
     * queueId 对外不展示
     */
    @JsonIgnore
    String queueId;

    String queueName;

    String zoneName;

    String subnetId;

    String securityGroupId;

    Integer maxNodesInQueue;

    Integer minNodesInQueue;

    Integer maxScalePerCycle;

    Boolean enableAutoGrow;

    Boolean enableAutoShrink;

    /**
     * 不使用自动伸缩策略的节点id
     */
    List<String> excludeNodes;

    String spec;

    Integer systemDiskSize;

    String systemDiskType;

    List<DiskInfo> dataDiskList;

    AutoScalingStatus status;

    String imageId;

    String hostnamePrefix;

    String cudaVersion;

    String gpuDriverVersion;

    String cudnnVersion;

    private List<Tag> tags;

    private String cpuThreadConfig;

    private String numaConfig;
    
}
