package com.baidu.bce.logic.chpc.gateway.external;


import com.baidu.bce.logic.chpc.gateway.external.model.bcc.BccInstanceDetail;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2022-12-09
 */
public interface BccExternalGateway {

    BccInstanceDetail getBccServerDetail(String serverId);

    void rebootServer(String serverId, Boolean forceStop);

    void stopServer(String serverId, Boolean forceStop, Boolean stopWithNoCharge);

    void startServer(String serverId);

    void deleteServer(String serverId);

    void deleteServers(List<String> serverIds);
}
