package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AutoScalingProbeResponse {

    Info info;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Info {
        String queueName;

        Integer scaleCount;

        String message;

        List<Node> shrinkNodes;

        Integer shrinkCycle = 0;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Node {
        String hostIP;

        String status;

        String nodeID;

        String nodeName;

        String spec;

        String queue;

    }
}
