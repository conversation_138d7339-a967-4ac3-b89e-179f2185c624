package com.baidu.bce.logic.chpc.annotation;

public class AspectResultHolder {
    private static final ThreadLocal<Object> ASPECT_RESULT = new ThreadLocal<>();

    public static void setAspectResult(Object result) {
        ASPECT_RESULT.set(result);
    }

    public static Object getAspectResult() {
        return ASPECT_RESULT.get();
    }

    public static void clear() {
        ASPECT_RESULT.remove();
    }
}
