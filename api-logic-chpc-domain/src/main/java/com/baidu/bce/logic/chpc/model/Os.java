package com.baidu.bce.logic.chpc.model;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
public class Os {

    /**
     * 调度器类型，slurm、sge、pbs
     */
    private String schedulerType;
    /**
     * 调度器版本
     */
    private String schedulerVersion;
    /**
     * 支持的架构，如amd64 (64bit)、x86_64 (64bit)
     */
    private String osArch;
    /**
     * 支持的操作系统。如Ubuntu、CentOS
     */
    private String osName;
    /**
     * 支持的操作系统版本,如20.04 LTS、7.9
     */
    private String osVersion;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime createdTime;
    @JsonFormat(pattern = BceConstant.DATETIME_FORMAT)
    private LocalDateTime updatedTime;
}
