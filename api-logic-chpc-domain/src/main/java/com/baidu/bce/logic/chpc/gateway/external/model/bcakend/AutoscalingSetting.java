package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2023-01-04
 */
@Data
public class AutoscalingSetting {

    @JsonProperty("checkIntervalInSec")
    private Integer checkIntervalInSec;

    @JsonProperty("checkCountToScaleout")
    private Integer checkCountToScaleOut;

    @JsonProperty("checkCountToScalein")
    private Integer checkCountToScaleIn;

    @JsonProperty("minCount")
    private Integer minCount;

    @JsonProperty("maxCount")
    private Integer maxCount;

    @JsonProperty("tideTable")
    private List<TideTable> tideTable;


}
