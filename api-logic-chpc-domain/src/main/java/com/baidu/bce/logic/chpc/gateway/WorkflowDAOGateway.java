package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.model.Workflow;

import java.util.List;

public interface WorkflowDAOGateway {

    Boolean createWorkflow(Workflow workflow);

    List<Workflow> listWorkflow(String workspaceId, String name, String accountId);

    Workflow getWorkflow(String workflowId, String accountId);

    Workflow getWorkflowByVersion(String workflowId, String accountId, Long version);

    List<Workflow> getWorkflowByName(String workspaceId, String workflowName, String accountId);

    Boolean updateWorkflow(Workflow workflow, String accountId);


    Boolean deleteWorkflow(String workflowId, String accountId);
}
