package com.baidu.bce.logic.chpc.billing.gateway;

import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;

public interface ServiceCatalogGateway {

    OrderUuidResult createOrder(String accountId, CreateOrderRequest<CreateNewTypeOrderItem> request);

}
