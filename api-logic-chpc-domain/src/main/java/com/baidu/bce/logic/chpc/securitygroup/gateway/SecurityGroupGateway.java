package com.baidu.bce.logic.chpc.securitygroup.gateway;

import com.baidubce.services.bcc.model.securitygroup.ListSecurityGroupsResponse;
import com.baidubce.services.bcc.model.securitygroup.CreateSecurityGroupRequest;
import com.baidubce.services.bcc.model.securitygroup.SecurityGroupRuleOperateRequest;
import com.baidubce.services.bcc.model.securitygroup.ListSecurityGroupsRequest;
import com.baidubce.services.esg.model.CreateEsgRequest;
import com.baidubce.services.esg.model.ListEsgRequest;
import com.baidubce.services.esg.model.ListEsgResponse;
import com.baidubce.services.esg.model.EsgRuleOperateRequest;

public interface SecurityGroupGateway {

    String createSecurityGroup(CreateSecurityGroupRequest request);
    ListSecurityGroupsResponse listSecurityGroups(ListSecurityGroupsRequest request) ;
    void addSecurityGroupRule(SecurityGroupRuleOperateRequest request);

    String createEnterpriseSecurityGroup(CreateEsgRequest request);
    ListEsgResponse listEnterpriseSecurityGroups(ListEsgRequest request);
    void addEnterpriseSecurityGroupRule(EsgRuleOperateRequest request);
}
