package com.baidu.bce.logic.chpc.common.exceptions;

import com.baidu.bce.internalsdk.core.HttpStatus;
import com.baidu.bce.internalsdk.exception.BceException;

public class CommonException {

    /**
     * 请求参数无效 400
     */
    public static class RequestInvalidException extends BceException {
        public RequestInvalidException() {
            super("Bad request parameters or illegal request.", HttpStatus.ERROR_INPUT_INVALID, "BadRequest");
        }

        public RequestInvalidException(String message) {
            super(message, HttpStatus.ERROR_INPUT_INVALID, "BadRequest");
        }
    }

    /**
     * 请求参数无效 400
     */
    public static class InvalidateZoneException extends BceException {
        public InvalidateZoneException() {
            super("Invalidate zone name.", HttpStatus.ERROR_INPUT_INVALID, "Instance.InvalidateZoneException");
        }
    }

    /**
     * 请求参数无效 403
     */
    public static class QuotaException extends BceException {
        public QuotaException() {
            super("Exceed quota exceed.", HttpStatus.ERROR_OPERATION_DENY, "QuotaExceeded");
        }

        public QuotaException(String message) {
            super(message, HttpStatus.ERROR_OPERATION_DENY, "QuotaExceeded");
        }
    }


    /**
     * 404 资源不存在
     */
    public static class ResourceNotExistException extends BceException {
        public ResourceNotExistException() {
            super("The specified object is not found or resource do not exist.",
                    HttpStatus.ERROR_RESOURCE_NOT_EXIST, "NoSuchObject");
        }

        public ResourceNotExistException(String msg) {
            super(msg, HttpStatus.ERROR_RESOURCE_NOT_EXIST, "NoSuchObject");
        }
    }


    /**
     * 500 依赖服务异常
     */
    public static class CosServiceException extends BceException {

        public CosServiceException(String msg) {
            super(msg, HttpStatus.ERROR_COMPUTER, "CosServiceException");
        }
    }

    /**
     * 500 依赖服务异常
     */
    public static class CfsServiceException extends BceException {

        public CfsServiceException(String msg) {
            super(msg, HttpStatus.ERROR_COMPUTER, "CfsServiceException");
        }
    }

    /**
     * 依赖服务异常
     */
    public static class RelatedServiceException extends BceException {

        public RelatedServiceException(String msg, String code) {
            super(msg, HttpStatus.ERROR_COMPUTER, code);
        }

        public RelatedServiceException(String msg, int httpCode, String code) {
            super(msg, httpCode, code);
        }

    }

}
