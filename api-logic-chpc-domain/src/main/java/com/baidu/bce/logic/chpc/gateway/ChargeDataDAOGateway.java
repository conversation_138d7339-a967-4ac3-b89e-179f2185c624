package com.baidu.bce.logic.chpc.gateway;

import java.time.LocalDateTime;

import com.baidu.bce.logic.chpc.model.ChargeData;

public interface ChargeDataDAOGateway {

    Boolean insert(ChargeData chargeData);

    Boolean update(ChargeData chargeData);

    Boolean delete(String instanceId, String chargeItem, LocalDateTime chargeTime);

    ChargeData findByInstanceId(String instanceId, String chargeItem, LocalDateTime chargeTime);

}
