package com.baidu.bce.logic.chpc.domainservice;

import java.util.List;

import jakarta.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.cfs.CfsAddRequest;
import com.baidu.bce.logic.chpc.cfs.FileSystem;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.chpc.cluster.model.MountInfo;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.model.Cfs;

/**
 * <AUTHOR>
 * @since 2022-12-07
 */
@Service
public class CfsService {

    @Resource
    private CfsDAOGateway cfsDAOGateway;

    @Resource
    private CfsGateway cfsGateway;

    public Boolean insert(Cfs cfs) {
        return cfsDAOGateway.insert(cfs);
    }

    public Boolean isExisted(String clusterId, String cfsId) {
        return cfsDAOGateway.countByClusterId(clusterId, cfsId) != 0;
    }

    public List<Cfs> findByClusterId(String clusterId) {
        return cfsDAOGateway.findBy(clusterId, null);
    }

    public Boolean delete(String clusterId, String cfsId) {
        return cfsDAOGateway.delete(clusterId, cfsId);
    }

    /**
     * {@inheritDoc}
     * 根据挂载信息删除Cfs实例。
     *
     * @param clusterId Cfs实例所在集群的ID，不能为空
     * @param cfsId Cfs实例的ID，不能为空
     * @param mountTarget Cfs实例的挂载目标，不能为空
     * @param mountDir Cfs实例的挂载路径，不能为空
     *
     * @return Boolean true表示成功删除，false表示未找到对应的Cfs实例
     */
    public Boolean deleteByMountInfo(String clusterId, String cfsId, String mountTarget, String mountDir) {
        return cfsDAOGateway.deleteByMountInfo(clusterId, cfsId, mountTarget, mountDir);
    }

    /**
     * {@summary}
     * 生成一个Cfs对象，包含所需的信息。
     * 如果cfsId不存在，则抛出IllegalArgumentException异常。
     *
     * @param request   ClusterCreateRequest类型，包含创建集群所需的参数，包括cfsId、mountDirectory等。
     * @param clusterId String类型，表示集群ID。
     *
     * @return Cfs类型，返回一个包含所需信息的Cfs对象。
     * @throws IllegalArgumentException 当cfsId不存在时抛出此异常。
     */
    public Cfs genCfs(MountInfo mountInfo, String clusterId) {
        Cfs cfs = new Cfs();
        cfs.setCfsId(mountInfo.getCfsId());
        cfs.setClusterId(clusterId);
        cfs.setMountDir(mountInfo.getMountDirectory());
        cfs.setMountTarget(mountInfo.getMountPoint());
        cfs.setMountOption(mountInfo.getMountOption());

        if (mountInfo.getCfsId() != null && !mountInfo.getCfsId().isEmpty()) {
            List<FileSystem> fileSystems = cfsGateway.getCfsFileSystem(mountInfo.getCfsId());
            if (CollectionUtils.isEmpty(fileSystems)) {
                throw new IllegalArgumentException("The cfsId " + mountInfo.getCfsId() + " does not exist.");
            }

            for (FileSystem fileSystem : fileSystems) {
                if (fileSystem.getFsId().equalsIgnoreCase(mountInfo.getCfsId())) {
                    cfs.setCfsType(fileSystem.getType());
                    cfs.setStorageProtocol(fileSystem.getProtocol());
                }
            }
        } else {
            cfs.setCfsType("hdd");
            cfs.setStorageProtocol("nfs");
        }
        return cfs;
    }

    public Cfs genCfs(CfsAddRequest cfsAddRequest, String clusterId) {
        Cfs cfs = new Cfs();
        cfs.setCfsId(cfsAddRequest.getCfsId());
        cfs.setClusterId(clusterId);
        cfs.setMountDir(cfsAddRequest.getMountDirectory());
        cfs.setMountTarget(cfsAddRequest.getCfsMountPoint());

        List<FileSystem> fileSystems = cfsGateway.getCfsFileSystem(cfsAddRequest.getCfsId());
        if (CollectionUtils.isEmpty(fileSystems)) {
            throw new IllegalArgumentException("The cfsId " + cfsAddRequest.getCfsId() + " does not exist.");
        }

        for (FileSystem fileSystem : fileSystems) {
            if (fileSystem.getFsId().equalsIgnoreCase(cfsAddRequest.getCfsId())) {
                cfs.setCfsType(fileSystem.getType());
                cfs.setStorageProtocol(fileSystem.getProtocol());
            }
        }
        return cfs;
    }

}
