package com.baidu.bce.logic.chpc.common.interceptors;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.logic.chpc.common.holder.AccessKeyThreadHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.servlet.HandlerInterceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Slf4j
public class AccessKeyThreadLocalInterceptor implements HandlerInterceptor {


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String ak = request.getHeader("x-bce-accesskey");
        if (StringUtils.isEmpty(ak) || StringUtils.isBlank(ak)) {
            String auth = request.getHeader(BceConstant.AUTHORIZATION);
            if (StringUtils.isNotEmpty(auth) && auth.startsWith("bce-auth")) {
                ak = auth.split("/")[1];
            }

        }
        AccessKeyThreadHolder.setAccessKey(ak);
        log.info("set access key:" + ak);
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request,
                                HttpServletResponse response,
                                Object handler,
                                Exception ex) {
        AccessKeyThreadHolder.clear();
        log.info("clean threadLocal");
    }
}
