package com.baidu.bce.logic.chpc.gateway.external;

import com.baidu.bce.internalsdk.cos.model.ClusterCreateRequest;
import com.baidu.bce.internalsdk.cos.model.ClusterResourceDetail;
import com.baidu.bce.internalsdk.cos.model.ClusterResponse;

import java.util.List;


/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
public interface CosExternalGateway {

    ClusterResponse createClusterResource(ClusterCreateRequest clusterCreateRequest);


    Boolean deleteCosStackResource(String stackId);

    List<ClusterResourceDetail> getClusterResourceDetail(String stackId);

    ClusterResponse getClusterStatus(String stackId);
}
