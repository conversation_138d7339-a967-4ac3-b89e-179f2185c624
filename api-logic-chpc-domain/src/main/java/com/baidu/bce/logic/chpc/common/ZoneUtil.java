package com.baidu.bce.logic.chpc.common;

import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
public class ZoneUtil {


    @Resource
    RegionConfiguration regionConfiguration;

    private static final String API_ZONE_PATTERN_REGEX = "^([a-zA-Z]+)-([0-9a-zA-Z\\-]+)-([0-9a-zA-Z]+)$";
    private static final Pattern API_ZONE_PATTERN = Pattern.compile(API_ZONE_PATTERN_REGEX);
    private static final String CONSOLE_ZONE_PATTERN_REGEX = "^zone[0-9a-zA-Z]+$";
    private static final Pattern CONSOLE_ZONE_PATTERN = Pattern.compile(CONSOLE_ZONE_PATTERN_REGEX);


    public static boolean isNotMatchApiZone(String apiZone) {
        return !API_ZONE_PATTERN.matcher(apiZone).matches();
    }

    public static boolean isMatchConsoleZone(String consoleZone) {
        return CONSOLE_ZONE_PATTERN.matcher(consoleZone).matches();
    }

    public static String getCountryFromApiZone(String apiZone) {
        if (isNotMatchApiZone(apiZone)) {
            throw new CommonException.InvalidateZoneException();
        }
        return apiZone.split("-")[0];
    }

    public static String getRegionFromApiZone(String apiZone) {
        Matcher matcher = API_ZONE_PATTERN.matcher(apiZone);
        if (!matcher.matches()) {
            throw new CommonException.InvalidateZoneException();
        }
        return matcher.group(2).toUpperCase();
    }

    /**
     * cn-hb-fsg-a
     */
    public static String getZoneNumberFromApiZone(String apiZone) {
        Matcher matcher = API_ZONE_PATTERN.matcher(apiZone);
        if (!matcher.matches()) {
            throw new CommonException.InvalidateZoneException();
        }
        return "zone" + matcher.group(3).toUpperCase();
    }

    public static String getZoneNumberFromConsoleZone(String consoleZone) {
        if (!isMatchConsoleZone(consoleZone)) {
            throw new CommonException.InvalidateZoneException();
        }
        return consoleZone.substring(4).toLowerCase();
    }

    /**
     * 获取API形式的zoneName，如cn-bj-d
     *
     * @param logicalZone zoneD
     * @return 如 cn-bj-d
     */
    public String getApiZoneName(String logicalZone) {
        return "cn-" + regionConfiguration.getCurrentRegion() + "-" + logicalZone.substring(4).toLowerCase();
    }


}
