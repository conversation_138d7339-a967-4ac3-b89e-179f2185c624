package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.baidu.bce.logic.chpc.cromwell.DepFile;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateWorkflowRunRequest {
    @JsonProperty("workflowId")
    private String workflowId;
    @JsonProperty("mainFileContent")
    private String mainFileContent;
    @JsonProperty("mainFilePath")
    private String mainFilePath;
    @JsonProperty("callCaching")
    private Boolean callCaching;
    @JsonProperty("failureMode")
    private String failureMode;
    @JsonProperty("depFiles")
    private List<DepFile> depFiles;
    @JsonProperty("configFileContent")
    private String configFileContent;
    @JsonProperty("configFilePath")
    private String configFilePath;
    @JsonProperty("inputs")
    private String inputs;


}
