package com.baidu.bce.logic.chpc.skyform.gateway;

import com.baidu.bce.logic.chpc.skyform.GetUserResponse;
import com.baidu.bce.logic.chpc.skyform.GetTenantResponse;
import com.baidu.bce.logic.chpc.skyform.AddTenantOrUserResponse;
import com.baidu.bce.logic.chpc.skyform.ActiveUserOrRoleResponse;
import com.baidu.bce.logic.chpc.skyform.BaseResponse;
import com.baidu.bce.logic.chpc.skyform.ListJobsResponse;

import java.util.List;

public interface SkyFormGateway {

    String getAdminToken();

    GetUserResponse getUserByloginName(String loginName);

    GetUserResponse getUsersByTenantId(String tenantId);

    GetTenantResponse getTenantByTenantName(String tenantName);

    AddTenantOrUserResponse addTenant(String accountId);

    // userType，创建的时候可以设置，0：portal用户， 1：管理用户，2：不限权限用户
    // 密码采用默认密码:Passw0rd   AES:8WUXd7K3U8YQNr2nA+iaFA==
    AddTenantOrUserResponse addUser(String tenantId, String loginName, String password);

    // 激活用户
    ActiveUserOrRoleResponse activeUser(String uuid);

    // 新增用户角色。1:系统管理员 4:组织管理员 5:普通用户
    ActiveUserOrRoleResponse addUserRole(String uuid, List<String> roleIds);

    // 终端权限管理，给组织设置，下属用户会默认继承
    BaseResponse batchSaveAspTerminalPermission(String tenantId);

    // 获取登录token
    String getUserToken(String userId);


    // 设置组织Quota
    BaseResponse configTenantQuota(String tenantId, String cpuHourQuota, String gpuHourQuota, String storageQuota);


    // 删除用户
    BaseResponse removeUsers(List<String> uuids);

    // 停止作业
    BaseResponse stopJobs(List<String> uuids);


    // 删除组织
    BaseResponse removeTenants(List<String> uuids);


    // 根据时间查询作业列表
    ListJobsResponse listJobs(String tenantId, String beginSubmitTime, String endSubmitTime);


    // 查询作业
    ListJobsResponse listJobById(String jobId);



}
