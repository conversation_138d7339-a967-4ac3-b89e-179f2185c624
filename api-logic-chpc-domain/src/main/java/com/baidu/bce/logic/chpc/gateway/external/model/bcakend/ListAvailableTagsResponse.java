package com.baidu.bce.logic.chpc.gateway.external.model.bcakend;

import com.baidu.bce.logic.chpc.tag.Tag;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ListAvailableTagsResponse {
    @JsonProperty("request_id")
    private String requestId;

    private Integer code;

    private String message;
    /**
     * 作业标签
     */
    @JsonProperty("tags")
    List<Tag> tags;
}
