package com.baidu.bce.logic.chpc.bcm;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BcmPartialQueryResponse {
    String requestId;
    String code;
    String message;
    PageResponse result;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class PageResponse {
        Integer pageNo;

        Integer pageSize;

        Integer totalCount;

        List<BcmDataResult> result;
    }
}
