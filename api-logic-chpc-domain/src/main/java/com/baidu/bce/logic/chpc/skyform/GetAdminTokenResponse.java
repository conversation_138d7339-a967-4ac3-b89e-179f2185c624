package com.baidu.bce.logic.chpc.skyform;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetAdminTokenResponse {
    Integer code;

    String msg;

    private RetObj retObj;


    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RetObj {
        String user;

        String token;

        String authorization;

        String isAuthority;

        String loginMes;

        String meuns;

        String modules;

        String permissions;

        String rolePlatforms;

    }
}
