package com.baidu.bce.logic.chpc.common.charge;

import com.baidu.bce.logic.core.exception.CommonExceptions;
import org.apache.commons.lang3.StringUtils;

public enum ChargeType {

    Postpaid,

    Prepaid,
    
    Unknown;

    /**
     * 校验付费类型
     *
     * @param chargeType 待校验付费类型
     */
    public static void verifyChargeType(String chargeType) {
        for (ChargeType value : values()) {
            if (value.name().equalsIgnoreCase(chargeType)) {
                return;
            }
        }
        throw new CommonExceptions.RequestInvalidException("The chargeType only support: " +
                StringUtils.join(values(), ","));
    }

}
