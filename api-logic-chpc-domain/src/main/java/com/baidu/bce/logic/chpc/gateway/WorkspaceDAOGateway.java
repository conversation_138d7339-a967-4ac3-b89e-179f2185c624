package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.model.Workspace;

import java.util.List;

public interface WorkspaceDAOGateway {

    Boolean createWorkspace(Workspace workspace);

    List<Workspace> listWorkspace(String name, String accountId);

    Workspace getWorkspace(String workspaceId, String accountId);

    Workspace getWorkspaceByName(String workspaceName, String accountId);

    List<Workspace> getWorkspaceByClusterId(String clusterId);

    Boolean updateWorkspace(String workspaceId, String status, String accountId);


    Boolean deleteWorkspace(String workspaceId, String accountId);

}
