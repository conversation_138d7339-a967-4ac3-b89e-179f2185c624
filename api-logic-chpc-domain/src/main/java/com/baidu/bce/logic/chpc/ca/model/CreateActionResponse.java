package com.baidu.bce.logic.chpc.ca.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateActionResponse {

    private Result result;

    private String requestId;

    private String code;

    private boolean success;

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class Result {
        // 命令id
        String actionId;
        // 动作类型。枚举值：COMMAND（脚本命令），FILE_UPLOAD（文件上传）
        String actionType;
        // 命令名称
        String actionName;
        // 脚本类型。枚举值： SHELL、POWERSHELL
        String commandType;
        // 命令版本号。从0开始，每次修改+1
        Integer version;
        // 执行id，仅在命令执行时返回
        String runId;
    }
   
}
