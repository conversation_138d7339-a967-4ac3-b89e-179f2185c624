package com.baidu.bce.logic.chpc.gateway;

import com.baidu.bce.logic.chpc.model.Workflowfile;

import java.util.List;

public interface WorkflowfileDAOGateway {

    Boolean createWorkflowfile(List<Workflowfile> workflowfileList);


    List<Workflowfile> getWorkflowfile(String workflowId);

    List<Workflowfile> getWorkflowfileByVersion(String workflowId, Long version);


    Boolean deleteWorkflowfile(String workflowId);

    Boolean deleteWorkflowByfileType(String workflowId, String fileType);
}
