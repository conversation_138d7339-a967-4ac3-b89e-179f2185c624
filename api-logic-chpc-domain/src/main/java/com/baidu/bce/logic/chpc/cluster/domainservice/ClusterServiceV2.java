package com.baidu.bce.logic.chpc.cluster.domainservice;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.internalsdk.cos.model.CdsDiskForCreate;
import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.internalsdk.iam.model.IamEncryptResponse;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.bcc.BccInstance;
import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.cfs.MountTarget;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.chpc.cluster.model.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.cluster.model.MountInfo;
import com.baidu.bce.logic.chpc.cluster.model.NodeInfo;
import com.baidu.bce.logic.chpc.cluster.model.QueueInfo;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.CommonValidateUtil;
import com.baidu.bce.logic.chpc.common.EventType;
import com.baidu.bce.logic.chpc.common.GlobalUuidUtil;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.NumberUtils;
import com.baidu.bce.logic.chpc.common.ZoneUtil;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.common.charge.ChargeUtil;
import com.baidu.bce.logic.chpc.common.charge.EipChargeType;
import com.baidu.bce.logic.chpc.common.charge.PeriodUnit;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.common.validator.AesDecryptUtil;
import com.baidu.bce.logic.chpc.cos.CreateStackRequest;
import com.baidu.bce.logic.chpc.cos.CreateStackResponse;
import com.baidu.bce.logic.chpc.cos.gateway.CosGateway;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterEventDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareDAOGateway;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.WhitelistGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag;
import com.baidu.bce.logic.chpc.iam.IamGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Event;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.securitygroup.gateway.SecurityGroupGateway;
import com.baidu.bce.logic.chpc.software.model.SoftwareVersion;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.chpc.user.Const;
import com.baidu.bce.logic.chpc.vpc.gateway.VpcGateway;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidubce.BceClientException;
import com.baidubce.services.bcc.model.SecurityGroupModel;
import com.baidubce.services.bcc.model.SecurityGroupRuleModel;
import com.baidubce.services.bcc.model.securitygroup.CreateSecurityGroupRequest;
import com.baidubce.services.bcc.model.securitygroup.ListSecurityGroupsRequest;
import com.baidubce.services.bcc.model.securitygroup.ListSecurityGroupsResponse;
import com.baidubce.services.bcc.model.securitygroup.SecurityGroupRuleOperateRequest;
import com.baidubce.services.esg.model.CreateEsgRequest;
import com.baidubce.services.esg.model.EnterpriseSecurityGroup;
import com.baidubce.services.esg.model.EnterpriseSecurityGroupRule;
import com.baidubce.services.esg.model.EsgRuleOperateRequest;
import com.baidubce.services.esg.model.ListEsgRequest;
import com.baidubce.services.esg.model.ListEsgResponse;
import com.baidubce.services.vpc.model.GetVpcResponse;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

// import com.baidu.bce.logic.chpc.iam.IamEncryptResponse;

@Slf4j
@Service
public class ClusterServiceV2 {

    @Autowired
    CosGateway cosGateway;

    @Autowired
    CfsGateway cfsGateway;

    @Autowired
    CfsDAOGateway cfsDAOGateway;

    @Autowired
    ClusterDAOGateway clusterDAOGateway;

    @Autowired
    TagsDAOGateway tagsDAOGateway;

    @Autowired
    QueueDAOGateway queueDAOGateway;

    @Autowired
    WhitelistGateway whitelistGateway;

    @Autowired
    GlobalUuidUtil globalUuidUtil;

    @Autowired
    TaskService taskService;

    @Autowired
    BackendGateway backendGateway;

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Resource
    ClusterEventDAOGateway clusterEventDAOGateway;

    @Resource
    SubnetGateway subnetGateway;

    @Autowired
    ClusterGenerator clusterGenerator;

    @Resource
    BccGateway bccGateway;

    @Resource
    SecurityGroupGateway securityGroupGateway;

    @Resource
    SoftwareDAOGateway softwareDAOGateway;

    @Resource
    VpcGateway vpcGateway;

    @Resource
    IamGateway iamGateway;

    /**
     * 创建集群
     *
     * @param clusterCreateRequest 集群创建请求参数，包含集群名称、集群类型等信息
     * @return ClusterResponse 返回创建的集群信息，包含集群Id和云堆栈Id等信息
     * @throws BceException 如果创建安全组失败，则抛出BceException异常
     */
    public ClusterResponse createCluster(ClusterCreateRequest clusterCreateRequest, String stackName) {
        // *1.校验参数
        validateCreateClusterParameters(clusterCreateRequest);
        // *2.生成集群信息
        Cluster cluster = genCluster(clusterCreateRequest, stackName);
        try {
            clusterDAOGateway.insert(cluster);
        } catch (Exception e) {
            // 如果是因为唯一key冲突(重名的clusterName)导致的异常则转换为bce异常，否则按原异常抛出
            if (clusterDAOGateway.count(cluster.getName(), cluster.getAccountId()) > 0) {
                throw new CommonExceptions.RequestInvalidException("can't create cluster with same name");
            }
            throw new BceException(e.getMessage());
        }

        // *3.插入事件
        insertEvents(
                cluster.getClusterId(),
                CollectionUtils.isNotEmpty(clusterCreateRequest.getMountList()),
                clusterCreateRequest.getLoginSpec() != null,
                clusterCreateRequest.getQueueList(),
                clusterCreateRequest.getClusterType(),
                clusterCreateRequest.getMountList());
        // 更新创建安全组事件
        clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CREATE_SECURITY_GRUOP, EventType.SUCCEED, "");

        ClusterResponse response = new ClusterResponse();
        response.setClusterId(cluster.getClusterId());
        return response;
    }

    /**
     * @throws void 无异常抛出
     * @Description 插入事件
     * @Param clusterId 集群ID
     * @Param useNfs 是否使用NFS，true表示使用，false表示不使用
     * @Param loginImageId 登录节点镜像ID，为空表示没有登录节点
     * @Param queueInfoList 队列信息列表，包含计算节点数量，null或空列表表示没有计算节点
     * @Param clusterType 调度器类型，Hybrid表示混合模式，其他值表示单一模式
     * @Return void 无返回值
     */
    private void insertEvents(
            String clusterId,
            Boolean useNfs,
            Boolean hasLoginNode,
            List<QueueInfo> queueInfoList,
            String clusterType,
            List<MountInfo> mountList) {
        // 插入事件
        Event event = new Event();
        event.setClusterId(clusterId);
        event.setStatus(EventType.READY_TO_START);
        event.setErrMsg("");
        // 创建安全组
        event.setName(EventType.CREATE_SECURITY_GRUOP);
        if (!clusterEventDAOGateway.insert(event)) {
            log.debug("event has already been inserted");
        }
        // 创建资源
        event.setName(EventType.CREATE_RESOURCE);
        if (!clusterEventDAOGateway.insert(event)) {
            log.debug("event has already been inserted");
        }

        if (ClusterType.HYBRID.nameLowerCase().equals(clusterType)) {
            // 启动代理节点
            event.setName(EventType.START_PROXY_NODE);
            if (!clusterEventDAOGateway.insert(event)) {
                log.debug("event has already been inserted");
            }
        } else {
            // 启动管理节点
            event.setName(EventType.START_MANAGER_NODE);
            if (!clusterEventDAOGateway.insert(event)) {
                log.debug("event has already been inserted");
            }

            // 启动登录节点，如果有
            if (hasLoginNode) {
                event.setName(EventType.START_LOGIN_NODE);
                if (!clusterEventDAOGateway.insert(event)) {
                    log.debug("event has already been inserted");
                }
            }

            // 启动计算节点,如果有
            if (queueInfoList != null && queueInfoList.size() > 0) {
                Boolean isHasComputeNode = false;
                for (int i = 0; i < queueInfoList.size(); i++) {
                    if (queueInfoList.get(i).getComputeSpec() != null
                            && queueInfoList.get(i).getComputeSpec().getCount() >= 1) {
                        isHasComputeNode = true;
                    }
                }
                if (isHasComputeNode) {
                    event.setName(EventType.START_COMPUTE_NODE);
                    if (!clusterEventDAOGateway.insert(event)) {
                        log.debug("event has already been inserted");
                    }
                }
            }
        }
        // 检查网络
        event.setName(EventType.CHECK_NETWORK);
        if (!clusterEventDAOGateway.insert(event)) {
            log.debug("event has already been inserted");
        }
        // 检查共享存储（可选）
        if (useNfs
                || (ClusterType.CLOUD.nameLowerCase().equals(clusterType) && mountList != null
                && mountList.size() > 0)) {
            event.setName(EventType.CHECK_SHARED_STORAGE);
            if (!clusterEventDAOGateway.insert(event)) {
                log.debug("event has already been inserted");
            }
        }
        // 检查调度器服务
        event.setName(EventType.CHECK_SCHEDULER);
        if (!clusterEventDAOGateway.insert(event)) {
            log.debug("event has already been inserted");
        }
    }

    /**
     * @Description: 根据 VPC ID 和安全组类型（normal、enterprise）获取或创建对应的安全组。
     * 如果已存在可用安全组，则直接返回其 ID；如果不存在可用安全组，则创建一个新的安全组并返回其 ID。
     * 如果安全组类型为 normal，则创建的是普通安全组；如果安全组类型为 enterprise，则创建的是企业安全组。
     * @Param vpcId string - VPC ID，不能为空
     * @Param sgType string - 安全组类型，只能为 normal、enterprise 中的一种，不能为空
     * @Returns string - 返回安全组 ID，如果创建失败则抛出异常
     * @Throws BceException - 如果安全组类型非法，则抛出此异常
     */
    public String getOrCreateSecurityGroup(String vpcId, String sgType, Long sshPort, Long appSeverPort) {
        String securityGroupId = "";
        switch (sgType) {
            case "normal":
                // 普通安全组
                // 检查是否已有可用安全组
                ListSecurityGroupsRequest request = new ListSecurityGroupsRequest();
                request.setVpcId(vpcId);
                while (true) {
                    ListSecurityGroupsResponse resp = securityGroupGateway.listSecurityGroups(request);
                    for (SecurityGroupModel sg : resp.getSecurityGroups()) {
                        if (sg.getName().equals(ChpcConstant.CHPC_DEFAULT_SECURITY_GROUP_NAME)) {
                            // 有默认安全组，使用已有安全组
                            securityGroupId = sg.getId();
                            break;
                        }
                    }
                    if (!StringUtils.isEmpty(securityGroupId)) {
                        break;
                    }
                    request.setMarker(resp.getNextMarker());
                    if (!resp.getIsTruncated()) {
                        break;
                    }
                }
                if (StringUtils.isEmpty(securityGroupId)) {
                    // 无可用安全组，创建
                    CreateSecurityGroupRequest createReq = new CreateSecurityGroupRequest();
                    createReq.setName(ChpcConstant.CHPC_DEFAULT_SECURITY_GROUP_NAME);
                    createReq.setVpcId(vpcId);
                    createReq.setDesc("CHPC 默认安全组，请勿删除");
                    List<SecurityGroupRuleModel> rules = new ArrayList<>();
                    SecurityGroupRuleModel securityGroupRuleModel = new SecurityGroupRuleModel();
                    // 出站规则 all
                    securityGroupRuleModel = new SecurityGroupRuleModel();
                    securityGroupRuleModel.setDirection("egress");
                    rules.add(securityGroupRuleModel);
                    createReq.setRules(rules);
                    securityGroupId = securityGroupGateway.createSecurityGroup(createReq);
                }
                // 增加入站规则
                // 组内互通
                SecurityGroupRuleOperateRequest securityGroupRuleOperateRequest = new SecurityGroupRuleOperateRequest();
                securityGroupRuleOperateRequest.setSecurityGroupId(securityGroupId);
                SecurityGroupRuleModel securityGroupRuleModel = new SecurityGroupRuleModel();
                securityGroupRuleModel.setDirection("ingress");
                securityGroupRuleModel.setProtocol("all");
                securityGroupRuleModel.setPortRange("1-65535");
                securityGroupRuleModel.setSourceGroupId(securityGroupId);
                securityGroupRuleOperateRequest.setRule(securityGroupRuleModel);
                try {
                    securityGroupGateway.addSecurityGroupRule(securityGroupRuleOperateRequest);
                } catch (BceClientException e) {
                    log.debug("add security group rule err:{}", e.getMessage());
                    if (!e.getMessage().contains(ChpcConstant.SECURITY_GROUP_RULE_DUPLICATED_ERR_MSG)) {
                        // 添加失败且不是由于重复规则，则抛出异常
                        log.error("add security group rule failed, securityGroupId: {}", securityGroupId);
                        throw e;
                    }
                }
                // 其他对外端口 cluster-api 8100, app-server（12011或用户自定义）, ssh（用户自定义）
                List<String> securityGroupPorts = new ArrayList<>();
                securityGroupPorts.add(ChpcConstant.CHPC_CLUSTER_API_PORT);
                if (sshPort != null) {
                    securityGroupPorts.add(String.valueOf(sshPort));
                }
                if (appSeverPort != null) {
                    securityGroupPorts.add(String.valueOf(appSeverPort));
                    // app-server https 端口
                    securityGroupPorts.add(String.valueOf(appSeverPort + 1));
                    // websockify 端口
                    securityGroupPorts.add(ChpcConstant.CHPC_WEBSOCKIFY_PORT);
                } else {
                    securityGroupPorts.add(ChpcConstant.CHPC_APP_SERVER_PORT);
                    securityGroupPorts.add(ChpcConstant.CHPC_APP_SERVER_HTTPS_PORT);
                    securityGroupPorts.add(ChpcConstant.CHPC_WEBSOCKIFY_PORT);
                }
                for (String port : securityGroupPorts) {
                    securityGroupRuleOperateRequest = new SecurityGroupRuleOperateRequest();
                    securityGroupRuleOperateRequest.setSecurityGroupId(securityGroupId);
                    securityGroupRuleModel = new SecurityGroupRuleModel();
                    securityGroupRuleModel.setDirection("ingress");
                    securityGroupRuleModel.setProtocol("tcp");
                    securityGroupRuleModel.setPortRange(port);
                    securityGroupRuleOperateRequest.setRule(securityGroupRuleModel);
                    try {
                        securityGroupGateway.addSecurityGroupRule(securityGroupRuleOperateRequest);
                    } catch (BceClientException e) {
                        log.debug("add security group rule err:{}", e.getMessage());
                        if (!e.getMessage().contains(ChpcConstant.SECURITY_GROUP_RULE_DUPLICATED_ERR_MSG)) {
                            // 添加失败且不是由于重复规则，则抛出异常
                            log.error("add security group rule failed, securityGroupId: {}, port: {}",
                                    securityGroupId, port);
                            throw e;
                        }
                    }
                }
                return securityGroupId;
            case "enterprise":
                // 企业安全组
                // 检查是否已有可用安全组
                ListEsgRequest esgRequest = new ListEsgRequest();
                while (true) {
                    ListEsgResponse resp =
                            securityGroupGateway.listEnterpriseSecurityGroups(esgRequest);
                    for (EnterpriseSecurityGroup esg : resp.getEnterpriseSecurityGroups()) {
                        if (esg.getName().equals(ChpcConstant.CHPC_DEFAULT_SECURITY_GROUP_NAME)) {
                            // 有可用安全组，直接返回
                            securityGroupId = esg.getId();
                            break;
                        }
                    }
                    if (!StringUtils.isEmpty(securityGroupId)) {
                        break;
                    }
                    esgRequest.setMarker(resp.getNextMarker());
                    if (!resp.getIsTruncated()) {
                        break;
                    }
                }
                // 无可用安全组，创建
                if (StringUtils.isEmpty(securityGroupId)) {
                    CreateEsgRequest esgCreateReq = new CreateEsgRequest();
                    esgCreateReq.setName(ChpcConstant.CHPC_DEFAULT_SECURITY_GROUP_NAME);
                    esgCreateReq.setDesc("CHPC 默认安全组，请勿删除");
                    List<EnterpriseSecurityGroupRule> esgRules = new ArrayList<>();
                    EnterpriseSecurityGroupRule enterpriseSecurityGroupRule = new
                            EnterpriseSecurityGroupRule();
                    // 出站规则 all
                    enterpriseSecurityGroupRule = new EnterpriseSecurityGroupRule();
                    enterpriseSecurityGroupRule.setAction("allow");
                    enterpriseSecurityGroupRule.setProtocol("all");
                    enterpriseSecurityGroupRule.setDirection("egress");
                    enterpriseSecurityGroupRule.setPriority(100);
                    enterpriseSecurityGroupRule.setEthertype("IPv4");
                    enterpriseSecurityGroupRule.setSourceIp("all"); // 规则源IP
                    enterpriseSecurityGroupRule.setDestIp("all"); // 规则目的IP
                    enterpriseSecurityGroupRule.setPortRange("1-65535");
                    esgRules.add(enterpriseSecurityGroupRule);
                    esgCreateReq.setRules(esgRules);
                    log.debug("rules of esg:{}", esgRules.toString());
                    securityGroupId =
                            securityGroupGateway.createEnterpriseSecurityGroup(esgCreateReq);
                }
                // 增加入站规则
                // vpc内互通
                // 查询vpc网段
                GetVpcResponse getVpcResponse = vpcGateway.getVpc(vpcId);
                String vpcCidr = getVpcResponse.getVpc().getCidr();
                // 增加规则
                EsgRuleOperateRequest esgRuleOperateRequest = new EsgRuleOperateRequest();
                esgRuleOperateRequest.setEnterpriseSecurityGroupId(securityGroupId);
                EnterpriseSecurityGroupRule enterpriseSecurityGroupRule = new
                        EnterpriseSecurityGroupRule();
                enterpriseSecurityGroupRule.setAction("allow");
                enterpriseSecurityGroupRule.setDirection("ingress");
                enterpriseSecurityGroupRule.setProtocol("all");
                enterpriseSecurityGroupRule.setPriority(100);
                enterpriseSecurityGroupRule.setEthertype("IPv4");
                enterpriseSecurityGroupRule.setSourceIp(vpcCidr); // 规则源IP
                enterpriseSecurityGroupRule.setDestIp("all"); // 规则目的IP
                enterpriseSecurityGroupRule.setPortRange("1-65535");
                esgRuleOperateRequest.setRules(Arrays.asList(enterpriseSecurityGroupRule));
                try {
                    securityGroupGateway.addEnterpriseSecurityGroupRule(esgRuleOperateRequest);
                } catch (BceClientException e) {
                    log.debug("add security group rule err:{}", e.getMessage());
                    if (!e.getMessage().contains(ChpcConstant.ESG_RULE_DUPLICATED_ERR_MSG)) {
                        // 添加失败且不是由于重复规则，则抛出异常
                        log.error("add security group rule failed, securityGroupId: {}",
                                securityGroupId);
                        throw e;
                    }
                }
                // 其他对外端口 cluster-api 8100, app-server（12011或用户自定义）, ssh（用户自定义）
                securityGroupPorts = new ArrayList<>();
                securityGroupPorts.add(ChpcConstant.CHPC_CLUSTER_API_PORT);
                if (sshPort != null) {
                    securityGroupPorts.add(String.valueOf(sshPort));
                }
                if (appSeverPort != null) {
                    securityGroupPorts.add(String.valueOf(appSeverPort));
                    // app-server https 端口
                    securityGroupPorts.add(String.valueOf(appSeverPort + 1));
                    // websockify 端口
                    securityGroupPorts.add(ChpcConstant.CHPC_WEBSOCKIFY_PORT);
                } else {
                    securityGroupPorts.add(ChpcConstant.CHPC_APP_SERVER_PORT);
                    securityGroupPorts.add(ChpcConstant.CHPC_APP_SERVER_HTTPS_PORT);
                    securityGroupPorts.add(ChpcConstant.CHPC_WEBSOCKIFY_PORT);
                }
                for (String port : securityGroupPorts) {
                    esgRuleOperateRequest = new EsgRuleOperateRequest();
                    esgRuleOperateRequest.setEnterpriseSecurityGroupId(securityGroupId);
                    enterpriseSecurityGroupRule = new EnterpriseSecurityGroupRule();
                    enterpriseSecurityGroupRule.setAction("allow");
                    enterpriseSecurityGroupRule.setDirection("ingress");
                    enterpriseSecurityGroupRule.setProtocol("tcp");
                    enterpriseSecurityGroupRule.setPriority(100);
                    enterpriseSecurityGroupRule.setEthertype("IPv4");
                    enterpriseSecurityGroupRule.setSourceIp("all"); // 规则源IP
                    enterpriseSecurityGroupRule.setDestIp("all"); // 规则目的IP
                    enterpriseSecurityGroupRule.setPortRange(port);
                    esgRuleOperateRequest.setRules(Arrays.asList(enterpriseSecurityGroupRule));
                    try {
                        securityGroupGateway.addEnterpriseSecurityGroupRule(esgRuleOperateRequest);
                    } catch (BceClientException e) {
                        log.debug("add security group rule err:{}", e.getMessage());
                        if (!e.getMessage().contains(ChpcConstant.ESG_RULE_DUPLICATED_ERR_MSG)) {
                            // 添加失败且不是由于重复规则，则抛出异常
                            log.error("add security group rule failed, securityGroupId: {}",
                                    securityGroupId);
                            throw e;
                        }
                    }
                }
                return securityGroupId;
            default:
                throw new BceException("invalid security group type:" + sgType);
        }
    }

    /**
     * call manager instance server(cluster-api) to stop cluster
     *
     * @param clusterId
     * @param force
     * @return
     */
    public ClusterResponse stopCluster(String clusterId, boolean force) {
        BackendCommonResponse commonResponse = backendGateway.stopCluster(clusterId, force);
        CommonValidateUtil.validBackendCommonResponse(commonResponse);
        log.info("stop cluster %s task id:{}", clusterId, commonResponse.getTaskId());
        ClusterResponse response = new ClusterResponse();
        response.setClusterId(clusterId);
        response.setStatus(ClusterStatus.DELETING.nameLowerCase());
        response.setTaskId(commonResponse.getTaskId());
        return response;
    }

    /**
     * 删除集群前的校验
     * - 集群状态
     * - 队列状态
     * - 实例状态
     *
     * @param clusterId 待删除集群ID
     */
    public String validateDeleteCluster(String clusterId) {
        // 集群状态，active、创建失败、异常状态才能删除
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, LogicUserService.getAccountId());
        if (cluster == null) {
            throw new CommonException.ResourceNotExistException();
        }
        if (!ClusterStatus.ACTIVE.nameLowerCase().equals(cluster.getStatus())
                && !ClusterStatus.CREATE_FAILED.nameLowerCase().equals(cluster.getStatus())
                && !ClusterStatus.ERROR.nameLowerCase().equals(cluster.getStatus())
                && !ClusterStatus.START_FAILED.nameLowerCase().equals(cluster.getStatus())
                && !ClusterStatus.TO_BE_AUTHORIZED.nameLowerCase().equals(cluster.getStatus())) {
            throw new CommonExceptions.RequestInvalidException(
                    "cluster " + clusterId + " cannot delete with " + cluster.getStatus() + " status.");
        }

        // 实例状态检查，过滤预付费
        List<Instance> instances = instanceDAOGateway.findByClusterId(clusterId);

        // 预付费实例检查
        validateContainsPrepayInstance(instances);

        if (cluster.getForbidDelete()) {
            throw new CommonExceptions.RequestInvalidException(
                    "cluster " + clusterId + " cannot delete with forbidDelete flag.");
        }
        return cluster.getName();
    }

    /**
     * @param instances {@code List<Instance>} 需要验证的实例列表
     * @throws CommonExceptions.RequestInvalidException 如果存在预付费实例，则抛出该异常
     * @Description: 验证是否包含预付费实例，如果存在则抛出异常
     */
    public void validateContainsPrepayInstance(List<Instance> instances) {
        List<String> instanceIds = instances.stream().map(Instance::getInstanceId).collect(Collectors.toList());

        List<BccInstance> bccInstances = bccGateway.getBccInstances(instanceIds);

        List<String> prepayInstances = bccInstances
                .stream()
                .filter(instance -> instance.getProductType().equalsIgnoreCase(ChargeType.Prepaid.name()))
                .map(BccInstance::getInstanceId)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(prepayInstances)) {
            log.warn("delete cluster failed, cannot delete prepay instance, instanceId is {}", prepayInstances);
            throw new CommonExceptions.RequestInvalidException(
                    "cannot delete prepay instance " + StringUtils.join(prepayInstances, ","));
        }
    }

    /**
     * {@summary}
     * 生成一个Cluster对象，并设置其属性。
     *
     * @param request ClusterCreateRequest - 包含创建集群所需信息的请求对象
     * @return Cluster - 返回一个包含创建集群所需信息的Cluster对象
     * @throws Exception 如果任何参数为空或者请求对象中的字段不符合要求，则抛出异常
     */
    public Cluster genCluster(ClusterCreateRequest request, String stackName) {
        Cluster cluster = new Cluster();
        cluster.setClusterId(globalUuidUtil.genClusterShortId());
        cluster.setName(request.getClusterName());
        cluster.setDescription(request.getDescription());
        cluster.setVpcId(request.getVpcId());
        for (QueueInfo queueInfo : request.getQueueList()) {
            if (queueInfo.getIsDefault()) {
                if (queueInfo.getComputeSpec() != null) {
                    cluster.setSubnetId(queueInfo.getComputeSpec().getSubnetId());
                    break;
                }
            }
        }
        if (StringUtils.isEmpty(cluster.getSubnetId())) {
            cluster.setSubnetId(request.getMasterSpec().getSubnetId());
        }
        cluster.setSecurityGroupId(request.getSecurityGroupId());
        cluster.setSecurityGroupType(request.getSecurityGroupType());
        cluster.setSchedulerType(request.getSchedulerType());
        cluster.setStatus(ClusterStatus.CREATING.nameLowerCase());
        cluster.setAccountId(LogicUserService.getAccountId());
        cluster.setChargeType(request.getMasterSpec().getChargeType());
        cluster.setLogicalZone(ZoneUtil.getZoneNumberFromApiZone(request.getZoneName()));
        cluster.setImageId(request.getMasterSpec().getImageId());
        cluster.setClusterType(request.getClusterType());
        // cluster 表只记录 managerSpec
        cluster.setSpec(request.getMasterSpec().getSpec());
        Map<String, String> extra = new HashMap<>();

        extra.put("stackName", stackName);
        if (StringUtils.isNotEmpty(request.getSchedulerIp())) {
            extra.put("schedulerIp", request.getSchedulerIp());
            extra.put("schedulerHost", request.getSchedulerHost());
        }
        if (request.getSshPort() != null) {
            extra.put("sshPort", String.valueOf(request.getSshPort()));
        }
        if (request.getPortalPort() != null) {
            extra.put("portalPort", String.valueOf(request.getPortalPort()));
        }
        cluster.setSchedulerVersion(request.getSchedulerVersion());
        cluster.setSchedulePlugin(request.getSchedulePlugin());
        cluster.setSchedulePluginVersion(request.getSchedulePluginVersion());
        cluster.setSoftwareDir(request.getSoftwareDir());
        cluster.setMaxNodes(request.getMaxNodes());
        cluster.setMaxCpus(request.getMaxCpus());

        cluster.setExtra(JacksonUtil.toJson(extra));
        cluster.setForbidDelete(request.getForbidDelete());

        cluster.setPassword(request.getPassword());
        cluster.setKeypairId(request.getKeypairId());
        cluster.setDomainAccount(request.getDomainAccount());
        return cluster;
    }

    /**
     * @Description: 在COS中创建一个集群，并返回创建的堆栈信息。
     * 该方法会生成一个模板，用于创建集群，并将其发送到COS服务器进行处理。
     * 如果COS服务器返回内部错误，则抛出CommonException.RelatedServiceException异常。
     * @Param queueInfo QueueInfo - 队列信息，包含队列名称和队列ID等信息
     * @Param request ClusterCreateRequest - 集群创建请求，包含集群相关参数，例如集群名称、集群类型等信息
     * @Return CreateStackResponse - 创建的堆栈信息，包含堆栈ID、状态等信息
     * @Throws CommonException.RelatedServiceException 当COS服务器返回内部错误时抛出此异常
     */
    public CreateStackResponse createClusterInCos(ClusterCreateRequest request, String stackName) {
        CreateStackRequest createStackRequest = new CreateStackRequest();
        createStackRequest.setUserId(LogicUserService.getAccountId());
        // createStackRequest.setName("stack-" + UUIDUtil.generateShortUuid());
        createStackRequest.setName(stackName);

        createStackRequest.getTags().put("from", "chpc");
        createStackRequest.setTemplate(generateTemplates(request));
        createStackRequest.setTimeout(3600 * 6);

        CreateStackResponse response;
        try {
            response = cosGateway.createStack(createStackRequest);
        } catch (WebClientResponseException e) {
            log.error("create stack failed, {}", e);
            if (e.getResponseBodyAsString().contains("Connection timed out")) {
                throw new CommonException.CosServiceException("COS 服务访问失败");
            }
            throw new CommonException.CosServiceException(
                    "CosService " + e.getResponseBodyAsString());
        } catch (Exception e) {
            log.error("create stack failed, {}", e);
            if (e.getMessage().contains("Connection timed out")) {
                throw new CommonException.CosServiceException("COS 服务访问失败");
            }
            throw new CommonException.CosServiceException(
                    "CosService " + e.getMessage());
        }

        return response;
    }

    /**
     * 生成模板，包含管理节点、登录节点和计算节点的资源。如果是云环境，则会根据请求参数生成相应的资源模板。
     * 如果请求中没有指定CFS ID，则会自动为其创建一个CFS。
     *
     * @param request ClusterCreateRequest，包含集群创建请求信息，包括管理节点、登录节点和计算节点的配置等信息
     * @return CreateStackRequest.Template，包含生成的模板信息，包括JSON格式的资源模板和资源列表
     */
    public CreateStackRequest.Template generateTemplates(ClusterCreateRequest request) {

        Map<String, Object> templates = new HashMap<>();
        int offset = 0;
        for (QueueInfo queueInfo : request.getQueueList()) {
            if (queueInfo.getIsDefault()) {
                Map<String, Object> masters = generateTemplate(
                        request.getMasterSpec(),
                        "master",
                        request,
                        0,
                        queueInfo.getQueueName(),
                        queueInfo.getQueueId());
                templates.putAll(masters);
                if (ClusterType.CLOUD.nameLowerCase().equals(request.getClusterType())) {
                    if (request.getLoginSpec() != null) {
                        Map<String, Object> login = generateTemplate(
                                request.getLoginSpec(),
                                "login",
                                request,
                                0,
                                queueInfo.getQueueName(),
                                queueInfo.getQueueId());
                        templates.putAll(login);
                    }
                }
            }
            if (ClusterType.CLOUD.nameLowerCase().equals(request.getClusterType())) {
                if (queueInfo.getComputeSpec() != null) {
                    Map<String, Object> computes = generateTemplate(
                            queueInfo.getComputeSpec(),
                            "compute",
                            request,
                            offset,
                            queueInfo.getQueueName(),
                            queueInfo.getQueueId());
                    templates.putAll(computes);
                    offset += queueInfo.getComputeSpec().getCount();
                }
            }
        }

        CreateStackRequest.Resources resources = new CreateStackRequest.Resources();
        resources.setResources(templates);

        CreateStackRequest.Template template = new CreateStackRequest.Template();
        template.setJson(JacksonUtil.encode(resources));

        return template;
    }

    /**
     * 生成模板，包含节点类型、数量、队列信息和请求参数等信息。
     * 只有master节点会申请外部IP，其他节点使用同一个公网IP。
     *
     * @param spec      BCC实例规格，如"g.n4.large"
     * @param count     需要创建的节点数量，默认为1
     * @param nodeType  节点类型，可选值为"master"或"compute"
     * @param queueInfo 队列信息，包含队列名称和队列所在区域的信息
     * @param request   创建集群请求参数，包含密码、镜像ID、系统盘大小、系统盘类型、网络容量、付费方式等信息
     * @return {@code Map<String, Object>} 包含节点类型和对应的资源信息的Map，每个节点对应一个资源
     */
    public Map<String, Object> generateTemplate(
            NodeInfo nodeSpec,
            String nodeType,
            ClusterCreateRequest request,
            int offsetIdx,
            String queueName,
            String queueId) {
        Map<String, Object> resources = new HashMap<>();
        // 只有login有 eip
        boolean isLogin = "login".equalsIgnoreCase(nodeType);

        int idx = offsetIdx;
        for (int i = 0; i < nodeSpec.getCount(); i++) {
            idx++;
            Map<String, Object> resource = new HashMap<>();
            resource.put("type", "BCE::BCC::Instance");
            Map<String, Object> properties = new HashMap<>();
            resource.put("count", 1);

            properties.put("spec", nodeSpec.getSpec());
            properties.put("zoneName", request.getZoneName());

            if (StringUtils.isNotEmpty(nodeSpec.getSubnetId())) {
                properties.put("subnetId", nodeSpec.getSubnetId());
            }

            if (StringUtils.isNotEmpty(request.getSecurityGroupId())) {
                if (ChpcConstant.SECURITY_GROUP_TYPE_NORMAL.equalsIgnoreCase(request.getSecurityGroupType())) {
                    List<String> securityGroups = new ArrayList<>();
                    securityGroups.add(request.getSecurityGroupId());
                    properties.put("securityGroupIds", securityGroups);
                } else if (ChpcConstant.SECURITY_GROUP_TYPE_ENTERPRISE.equalsIgnoreCase(request.getSecurityGroupType())) {
                    List<String> esgs = new ArrayList<>();
                    esgs.add(request.getSecurityGroupId());
                    properties.put("enterpriseSecurityGroupIds", esgs);
                }
            }

            properties.put("rootDiskSizeInGb", nodeSpec.getSystemDisk().getSize());
            properties.put("rootDiskStorageType", getDiskType(nodeSpec.getSystemDisk().getStorageType()));
            // properties.put("purchaseCount", 1);

            if (InstanceNodeType.MASTER.getType().equalsIgnoreCase(nodeType)) {
                properties.put("imageId", nodeSpec.getImageId());
            } else if (InstanceNodeType.COMPUTE.getType().equalsIgnoreCase(nodeType)) {
                if (StringUtils.isNotEmpty(nodeSpec.getImageId())) {
                    properties.put("imageId", nodeSpec.getImageId());
                }
            } else if (InstanceNodeType.LOGIN.getType().equalsIgnoreCase(nodeType)) {
                if (StringUtils.isNotEmpty(request.getLoginSpec().getImageId())) {
                    properties.put("imageId", request.getLoginSpec().getImageId());
                }
            }
            if (StringUtils.isNotEmpty(request.getPassword())) {
                // 数据库AES密码解析为原文
                String password = AesDecryptUtil.decrypt(request.getPassword());
                // 兼容用户没有ak时候，给用户创建随机密码的bcc
                try {
                    IamEncryptResponse encryptResponse = iamGateway.encrypt(password, LogicUserService.getAccountId());
                    String ak = encryptResponse.getAccesskeyId();
                    String skEncryptedPwd = encryptResponse.getCipherHex();
                    properties.put("adminPass", skEncryptedPwd);
                    properties.put("encryptedKey", ak);
                } catch (Exception e) {
                    log.error("iamGateway encrypt password failed", e);
                }
            }
            if (StringUtils.isNotEmpty(request.getKeypairId())) {
                properties.put("keypairId", request.getKeypairId());
            }

            Map<String, Object> billing = new HashMap<>();
            billing.put("paymentTiming", nodeSpec.getChargeType());
            if (ChargeType.Prepaid.name().equalsIgnoreCase(nodeSpec.getChargeType())) {
                Map<String, Object> reservation = new HashMap<>();
                // console bcc 目前不支持Year单位，故用 Month*12
                if (PeriodUnit.Year.name().equalsIgnoreCase(nodeSpec.getPeriodUnit())) {
                    reservation.put("reservationLength", nodeSpec.getPeriod() * 12);
                } else {
                    reservation.put("reservationLength", nodeSpec.getPeriod());
                }
                reservation.put("reservationTimeUnit", PeriodUnit.Month.name());
                billing.put("reservation", reservation);
                if (nodeSpec.isAutoRenew()) {
                    properties.put("autoRenewTimeUnit", nodeSpec.getAutoRenewPeriodUnit());
                    properties.put("autoRenewTime", nodeSpec.getAutoRenewPeriod());
                    properties.put("cdsAutoRenew", true);
                }
            }
            properties.put("billing", billing);
            List<Tag> tags = new ArrayList<>();

            Tag tag = new Tag();
            tag.setTagKey("nodeType");
            tag.setTagValue(nodeType);

            tags.add(tag);
            // 标记chpc来源tag，用于虚机通网后，返回虚机创建成功
            Tag fromTag = new Tag();
            fromTag.setTagKey("from");
            fromTag.setTagValue("chpc");

            tags.add(fromTag);

            // 为gpu相关软件版本打标签
            if (StringUtils.isNotEmpty(nodeSpec.getGpuDriverVersion())){
                Tag gpuDriverTag = new Tag();
                gpuDriverTag.setTagKey(ChpcConstant.GPU_DRIVER_VERSION);
                gpuDriverTag.setTagValue(nodeSpec.getGpuDriverVersion());
                tags.add(gpuDriverTag);
            }

            if (StringUtils.isNotEmpty(nodeSpec.getCudaVersion())){
                Tag cudaTag = new Tag();
                cudaTag.setTagKey(ChpcConstant.CUDA_VERSION);
                cudaTag.setTagValue(nodeSpec.getCudaVersion());
                tags.add(cudaTag);
            }

            if (StringUtils.isNotEmpty(nodeSpec.getCudnnVersion())){
                Tag cudnnTag = new Tag();
                cudnnTag.setTagKey(ChpcConstant.CUDNN_VERSION);
                cudnnTag.setTagValue(nodeSpec.getCudnnVersion());
                tags.add(cudnnTag);
            }

            // 管理节点和登录节点不需要加队列标签
            if (!isLogin && !InstanceNodeType.MASTER.getType().equalsIgnoreCase(nodeType)) {
                Tag queueNameTag = new Tag();
                queueNameTag.setTagKey("queueName");
                queueNameTag.setTagValue(queueName);
                tags.add(queueNameTag);

                Tag queueIdTag = new Tag();
                queueIdTag.setTagKey("queueId");
                queueIdTag.setTagValue(queueId);
                tags.add(queueIdTag);
            }

            // 用户设置的标签
            if (nodeSpec.getTags() != null) {
                for (int j = 0; j < nodeSpec.getTags().size(); j++) {
                    Tag userTag = new Tag();
                    userTag.setTagKey(nodeSpec.getTags().get(j).getTagKey());
                    userTag.setTagValue(nodeSpec.getTags().get(j).getTagValue());
                    tags.add(userTag);
                }
            }

            properties.put("tags", tags);

            if (isLogin && request.isUseEip()) {
                properties.put("networkCapacityInMbps", request.getNetworkCapacityInMbps());
                if (StringUtils.isNotEmpty(request.getInternetChargeType())) {
                    properties.put("internetChargeType", request.getInternetChargeType());
                    if (request.getInternetChargeType().equalsIgnoreCase(EipChargeType.BANDWIDTH_PREPAID.name())) {
                        properties.put("reservationLength", 1);
                    }
                }
                if (StringUtils.isNotEmpty(request.getEipName())) {
                    properties.put("eipName", request.getEipName());
                }
            }
            if (nodeSpec.getDataDiskList() != null) {
                for (DiskInfo dataDisk : nodeSpec.getDataDiskList()) {
                    if (dataDisk.getCdsNum() > 0 && dataDisk.getSize() > 0) {
                        List<CdsDiskForCreate> cdsList = new ArrayList<>();
                        for (int j = 0; j < dataDisk.getCdsNum(); j++) {
                            CdsDiskForCreate cdsDiskForCreate = new CdsDiskForCreate();
                            cdsDiskForCreate.setCdsSizeInGB(dataDisk.getSize());
                            cdsDiskForCreate.setStorageType(getDiskType(dataDisk.getStorageType()));
                            cdsList.add(cdsDiskForCreate);
                        }
                        properties.put("createCdsList", cdsList);
                    }
                }
            }
            // ebc 如果有ht&numa配置需要在创建时传给cos
            if (StringUtils.isNotEmpty(nodeSpec.getCpuThreadConfig())) {
                properties.put("cpuThreadConfig", nodeSpec.getCpuThreadConfig());
            }

            if (StringUtils.isNotEmpty(nodeSpec.getNumaConfig())){
                properties.put("numaConfig", nodeSpec.getNumaConfig());
            }

            resource.put("properties", properties);
            resources.put("bcc_" + nodeType + "_" + idx, resource);
        }
        return resources;
    }

    public String getDiskType(String diskType) {
        if ("premium_ssd".equals(diskType)) {
            return "cloud_hp1";
        } else if ("ssd".equals(diskType)) {
            return "hp1";
        } else if ("ENHANCED_SSD_PL1".equals(diskType)) {
            return "enhanced_ssd_pl1";
        }
        return diskType;
    }

    /**
     * 根据磁盘类型返回最小大小，如果不是指定的三种类型则默认为50GB。
     *
     * @param diskType 磁盘类型，可选值包括："cloud_hp1", "hp1", "enhanced_ssd_pl1"
     * @return 返回一个整数，表示磁盘的最小大小，单位为GB，如果不是指定的三种类型则默认为50GB
     */
    public int diskTypeMinSize(String diskType) {
        if ("cloud_hp1".equals(diskType)) {
            return 50;
        } else if ("hp1".equals(diskType)) {
            return 5;
        } else if ("enhanced_ssd_pl1".equals(diskType)) {
            return 50;
        }
        return 50;
    }

    /**
     * {@summary}
     * 校验创建集群参数。包括每个队列的计算节点数量和计算规格、是否设置默认队列、收费类型和周期等。
     *
     * @param request ClusterCreateRequest 集群创建请求对象，包含集群信息和队列信息。
     * @throws CommonExceptions.RequestInvalidException 如果请求中存在无效或缺失的参数，则抛出此异常。
     */
    public void validateCreateClusterParameters(ClusterCreateRequest request) {
        // 检查clusterName是否已经存在
        if (clusterDAOGateway.count(request.getClusterName(), LogicUserService.getAccountId()) != 0) {
            throw new CommonExceptions.RequestInvalidException("can't create cluster with same name");
        }
        // 检查 masterSpec
        if (request.getMasterSpec() == null) {
            throw new CommonExceptions.RequestInvalidException("masterSpec is empty.");
        }
        if (request.getMasterSpec().getCount() != 1) {
            throw new CommonExceptions.RequestInvalidException("masterSpec.count is invalid.");
        }
        if (StringUtils.isEmpty(request.getMasterSpec().getSpec())) {
            throw new CommonExceptions.RequestInvalidException("masterSpec.spec is empty.");
        }
        if (StringUtils.isEmpty(request.getMasterSpec().getImageId())) {
            throw new CommonExceptions.RequestInvalidException("masterSpec.imageId is empty.");
        }
        if (StringUtils.isEmpty(request.getMasterSpec().getSubnetId())) {
            throw new CommonExceptions.RequestInvalidException("masterSpec subnetId is empty.");
        }
        if (request.getMasterSpec().getSystemDisk() == null) {
            throw new CommonExceptions.RequestInvalidException("masterSpec systemDisk is empty.");
        }
        if (request.getMasterSpec().getSystemDisk().getCdsNum() != 1) {
            throw new CommonExceptions.RequestInvalidException("masterSpec systemDisk num is invalid, must be 1.");
        }
        if (request.getMasterSpec().getSystemDisk().getSize() < 20) {
            throw new CommonExceptions.RequestInvalidException("masterSpec systemDisk size is invalid.");
        }
        // 公有云集群，必须是OpenLDAP
        if (ClusterType.CLOUD.nameLowerCase().equals(request.getClusterType())) {
            // 兼容前端未传递参数
            if (StringUtils.isEmpty(request.getDomainAccount())) {
                request.setDomainAccount(ChpcConstant.OPEN_LDAP);
            }
            if (!ChpcConstant.OPEN_LDAP.equals(request.getDomainAccount())) {
                throw new CommonException.RequestInvalidException("Invalidate domain account");
            }
        }
        // 混合云集群，暂时不支持域账号接入
        if (ClusterType.HYBRID.nameLowerCase().equals(request.getClusterType())) {
            if (!StringUtils.isEmpty(request.getDomainAccount())) {
                throw new CommonException.RequestInvalidException("Invalidate domain account");
            }
        }
        List<DiskInfo> dataDisks = request.getMasterSpec().getDataDiskList();
        if (dataDisks != null) {
            int count = 0;
            for (DiskInfo dataDisk : dataDisks) {
                if (dataDisk.getCdsNum() <= 0) {
                    continue;
                }
                if (dataDisk.getSize() >= diskTypeMinSize(dataDisk.getStorageType())) {
                    count += dataDisk.getCdsNum();
                } else {
                    throw new CommonExceptions.RequestInvalidException(
                            "dataDisk "
                                    + dataDisk.getStorageType()
                                    + " size "
                                    + dataDisk.getSize()
                                    + " is invalid. less than "
                                    + diskTypeMinSize(dataDisk.getStorageType()));
                }
            }
            if (count > 5) {
                throw new CommonExceptions.RequestInvalidException("dataDisk count is invalid. larger than 5.");
            }
        }
        ChargeUtil.validateChargingTypeAndPeriod(
                request.getMasterSpec().getChargeType(),
                request.getMasterSpec().getPeriod(),
                request.getMasterSpec().getPeriodUnit());

        // 检查 loginSpec
        if (request.getLoginSpec() != null) {
            if (request.getLoginSpec().getCount() != 1) {
                throw new CommonExceptions.RequestInvalidException("loginSpec.count is invalid.");
            }
            if (StringUtils.isEmpty(request.getLoginSpec().getSpec())) {
                throw new CommonExceptions.RequestInvalidException("loginSpec.spec is empty.");
            }
            if (StringUtils.isEmpty(request.getLoginSpec().getImageId())) {
                throw new CommonExceptions.RequestInvalidException("loginSpec.imageId is empty.");
            }
            if (StringUtils.isEmpty(request.getLoginSpec().getSubnetId())) {
                throw new CommonExceptions.RequestInvalidException("loginSpec subnetId is empty.");
            }
            if (request.getLoginSpec().getSystemDisk() == null) {
                throw new CommonExceptions.RequestInvalidException("loginSpec systemDisk is empty.");
            }
            if (request.getLoginSpec().getSystemDisk().getCdsNum() != 1) {
                throw new CommonExceptions.RequestInvalidException("loginSpec systemDisk num is invalid, must be 1.");
            }
            if (request.getLoginSpec().getSystemDisk().getSize() < 20) {
                throw new CommonExceptions.RequestInvalidException("loginSpec systemDisk size is invalid.");
            }
            List<DiskInfo> loginDataDisks = request.getLoginSpec().getDataDiskList();
            if (loginDataDisks != null) {
                int count = 0;
                for (DiskInfo dataDisk : loginDataDisks) {
                    if (dataDisk.getCdsNum() <= 0) {
                        continue;
                    }
                    if (dataDisk.getSize() >= diskTypeMinSize(dataDisk.getStorageType())) {
                        count += dataDisk.getCdsNum();
                    } else {
                        throw new CommonExceptions.RequestInvalidException(
                                "dataDisk "
                                        + dataDisk.getStorageType()
                                        + " size "
                                        + dataDisk.getSize()
                                        + " is invalid. less than "
                                        + diskTypeMinSize(dataDisk.getStorageType()));
                    }
                }
                if (count > 5) {
                    throw new CommonExceptions.RequestInvalidException("dataDisk count is invalid. larger than 5.");
                }
            }
            ChargeUtil.validateChargingTypeAndPeriod(
                    request.getLoginSpec().getChargeType(),
                    request.getLoginSpec().getPeriod(),
                    request.getLoginSpec().getPeriodUnit());
        } else {
            if (request.isUseEip()) {
                throw new CommonExceptions.RequestInvalidException("loginSpec is empty. can not use eip");
            }
        }
        if (ClusterType.HYBRID.nameLowerCase().equals(request.getClusterType())) {
            if (request.getLoginSpec() != null) {
                throw new CommonExceptions.RequestInvalidException("loginSpec is not supported in hybrid cluster.");
            }
            if (request.isUseEip()) {
                throw new CommonExceptions.RequestInvalidException("eip is not supported in hybrid cluster.");
            }
            // 如果 schedulerIp 或者 schedulerHost 为空
            if (StringUtils.isEmpty(request.getSchedulerIp()) || StringUtils.isEmpty(request.getSchedulerHost())) {
                throw new CommonExceptions.RequestInvalidException("schedulerIp or schedulerHost is empty.");
            }
        }
        // 校验每个队列的 computeCount 和 computeSpec
        int defaultQueue = 0;
        int computeNodes = 0;
        int computeCpus = 0;
        if ((ClusterType.HYBRID.nameLowerCase().equals(request.getClusterType()))
                || request.getQueueList() == null
                || request.getQueueList().isEmpty()) {
            // *如果队列为空，或者是混合云集群（即使填了队列也忽略），则自动创建一个队列
            List<QueueInfo> queueList = new ArrayList<>();
            QueueInfo queueInfo = new QueueInfo();
            queueInfo.setQueueName("defaultQueue");
            NodeInfo computeSpec = new NodeInfo();
            computeSpec.setSpec(request.getMasterSpec().getSpec());
            computeSpec.setCount(1);
            computeSpec.setImageId(request.getMasterSpec().getImageId());
            computeSpec.setSubnetId(request.getMasterSpec().getSubnetId());
            DiskInfo systemDisk = new DiskInfo();
            systemDisk.setSize(40);
            computeSpec.setSystemDisk(systemDisk);
            queueInfo.setComputeSpec(computeSpec);
            queueInfo.setIsDefault(true);
            queueList.add(queueInfo);
            request.setQueueList(queueList);
            defaultQueue = 1;
            computeNodes += 1;
            computeCpus += getCpusFromSpec(request.getMasterSpec().getSpec());
        } else if (ClusterType.CLOUD.nameLowerCase().equals(request.getClusterType())) {
            // *如果是公有云，则校验每个队列的配置
            for (QueueInfo queueInfo : request.getQueueList()) {
                isValidQueueName(queueInfo.getQueueName(), request.getSchedulerType());

                if (queueInfo.getIsDefault()) {
                    defaultQueue++;
                }

                if (queueInfo.getIsAutoScale() && queueInfo.getComputeSpec() == null) {
                    throw new CommonExceptions.RequestInvalidException(
                            "queue " + queueInfo.getQueueName() + " isAutoScale is true, but computeSpec is empty.");
                }
                // *开启了自动扩缩容或者节点数大于0，则必须指定队列的规格；
                if (queueInfo.getComputeSpec() != null && queueInfo.getComputeSpec().getCount() > 0
                        || queueInfo.getIsAutoScale()) {
                    if (StringUtils.isEmpty(queueInfo.getComputeSpec().getSpec())) {
                        throw new CommonExceptions.RequestInvalidException("computeSpec.spec is empty.");
                    }
                    if (StringUtils.isEmpty(queueInfo.getComputeSpec().getImageId())) {
                        throw new CommonExceptions.RequestInvalidException("computeSpec.imageId is empty.");
                    }
                    if (StringUtils.isEmpty(queueInfo.getComputeSpec().getSubnetId())) {
                        throw new CommonExceptions.RequestInvalidException("computeSpec.subnetId is empty.");
                    }

                    if (queueInfo.getComputeSpec().getSystemDisk().getCdsNum() != 1) {
                        throw new CommonExceptions.RequestInvalidException(
                                "computeSpec systemDisk num is invalid, must be 1.");
                    }
                    if (queueInfo.getComputeSpec().getSystemDisk().getSize() < 20) {
                        throw new CommonExceptions.RequestInvalidException("computeSpec systemDisk size is invalid.");
                    }
                    List<DiskInfo> loginDataDisks = queueInfo.getComputeSpec().getDataDiskList();
                    if (loginDataDisks != null) {
                        int count = 0;
                        for (DiskInfo dataDisk : loginDataDisks) {
                            if (dataDisk.getCdsNum() <= 0) {
                                continue;
                            }
                            if (dataDisk.getSize() >= diskTypeMinSize(dataDisk.getStorageType())) {
                                count += dataDisk.getCdsNum();
                            } else {
                                throw new CommonExceptions.RequestInvalidException(
                                        "dataDisk "
                                                + dataDisk.getStorageType()
                                                + " size "
                                                + dataDisk.getSize()
                                                + " is invalid. less than "
                                                + diskTypeMinSize(dataDisk.getStorageType()));
                            }
                        }
                        if (count > 5) {
                            throw new CommonExceptions.RequestInvalidException(
                                    "dataDisk count is invalid. larger than 5.");
                        }
                    }

                    ChargeUtil.validateChargingTypeAndPeriod(
                            queueInfo.getComputeSpec().getChargeType(),
                            queueInfo.getComputeSpec().getPeriod(),
                            queueInfo.getComputeSpec().getPeriodUnit());
                    computeNodes += queueInfo.getComputeSpec().getCount();
                    computeCpus += getCpusFromSpec(queueInfo.getComputeSpec().getSpec())
                            * queueInfo.getComputeSpec().getCount();
                }
            }
        }
        log.debug("defaultQueue: " + defaultQueue);

        // 检查maxNodes
        if (request.getMaxNodes() > 0 && computeNodes > request.getMaxNodes()) {
            throw new CommonExceptions.RequestInvalidException("添加的计算节点数超过设置的集群最大节点数.");
        }
        // 检查maxCpus
        if (request.getMaxCpus() > 0 && computeCpus > request.getMaxCpus()) {
            throw new CommonExceptions.RequestInvalidException("添加的计算节点CPU核数超过设置的集群最大CPU核数.");
        }

        // 校验是否设置了默认队列，且队列就一个
        if (defaultQueue != 1) {
            throw new CommonExceptions.RequestInvalidException("Only one default queue is allowed.");
        }

        validateZoneParameter(request);

        validateCfsParameters(request);

        validateQuota(request);

        validateSoftwareParameters(request);
    }

    /**
     * @param queueName     String 队列名
     * @param schedulerType String 调度器类型（"openpbs"或"slurm"）
     * @throws CommonExceptions.RequestInvalidException 如果队列名不合法，抛出此异常
     * @Description: 检查队列名是否合法，符合规则并抛出异常
     */
    public static void isValidQueueName(String queueName, String schedulerType) {
        // 正则表达式
        String regex = "^[a-zA-Z][a-zA-Z0-9_]{0,13}[a-zA-Z0-9]$";

        // 校验
        if (!queueName.matches(regex)) {
            throw new CommonExceptions.RequestInvalidException("queue name " + queueName + " is invalid.");
        }

        if (schedulerType.equalsIgnoreCase("openpbs") && "default_queue".equals(queueName)) {
            throw new CommonExceptions.RequestInvalidException("pbs 队列名不能用 default_queue.");
        }
        if (schedulerType.equalsIgnoreCase("slurm") && "default".equals(queueName)) {
            throw new CommonExceptions.RequestInvalidException("slurm 队列名不能用 default.");
        }
    }

    private int getCpusFromSpec(String spec) {
        String[] specParts = spec.split("\\.");
        if (specParts.length < 3) {
            throw new CommonExceptions.RequestInvalidException("invalid spec " + spec);
        }
        String[] resParts = specParts[2].split("m");
        if (resParts.length != 2) {
            throw new CommonExceptions.RequestInvalidException("invalid spec " + spec);
        }
        return Integer.parseInt(resParts[0].substring(1));
    }

    public void validateZoneParameter(ClusterCreateRequest request) {
        if (StringUtils.isEmpty(request.getZoneName())) {
            throw new CommonExceptions.RequestInvalidException("zoneName should not empty.");
        }
        // 暂时只有api，后续有console时，需要判断来源
        if (ZoneUtil.isNotMatchApiZone(request.getZoneName())) {
            throw new CommonExceptions.RequestInvalidException("zoneName illegal.");
        }
    }

    /**
     * 校验共享存储参数合法性
     *
     * @param request 请求参数
     */
    public void validateCfsParameters(ClusterCreateRequest request) {
        Boolean validSoftwarePath = false;
        if (ClusterType.HYBRID.nameLowerCase().equals(request.getClusterType())) {
            // request.getMountList() 数量不能超过1
            if (request.getMountList().size() > 1) {
                throw new CommonExceptions.RequestInvalidException(
                        "The mountList size is invalid. Must be no more than 1.");
            }
            if (request.getMountList().size() == 0) {
                validSoftwarePath = true;
            }
        } else {
            if (request.getMountList().size() < 1) {
                throw new CommonExceptions.RequestInvalidException(
                        "The mountList size is invalid. Must be no less than 1.");
            }
        }
        String softwareDir = request.getSoftwareDir();
        // 判断 softwareDir 是否以/software 结尾
        if (StringUtils.isNotEmpty(softwareDir) && !softwareDir.endsWith("/software")) {
            throw new CommonExceptions.RequestInvalidException("The softwareDir should end with /software.");
        }
        for (MountInfo mountInfo : request.getMountList()) {
            if (StringUtils.isEmpty(mountInfo.getMountPoint())) {
                throw new CommonExceptions.RequestInvalidException("The mount point is empty.");
            }
            // 判断 mountInfo.getMountDirectory() 是否为有效的挂载地址
            isValidMountDirectory(mountInfo.getMountDirectory());

            // 判断 request.getSoftwareDir() 是否包含 mountInfo.getMountPoint()
            if (softwareDir.contains(mountInfo.getMountDirectory())) {
                validSoftwarePath = true;
            }
            // 校验 mountOption
            if (mountInfo.getMountOption() != null && StringUtils.isNotEmpty(mountInfo.getMountOption())) {
                if (!isValidMountOption(mountInfo.getMountOption())) {
                    throw new CommonExceptions.RequestInvalidException(
                            String.format("The mount option is invalid. {}", mountInfo.getMountOption()));
                }
            }

            if (mountInfo.getCfsId() == null || StringUtils.isEmpty(mountInfo.getCfsId())) {
                log.debug("cfsId is empty.");
                // 判断 mountInfo.getMountPoint() 是否以 baidubce.com 结尾
                if (mountInfo.getMountPoint().endsWith("baidubce.com")) {
                    throw new CommonExceptions.RequestInvalidException(
                            "mountPoint " + mountInfo.getMountPoint() + ": cfsId is empty.");
                }
                mountInfo.setCfsId("");
                continue;
            }

            List<MountTarget> cfsMountPoints;
            try {
                cfsMountPoints = cfsGateway.getCfsMountPoint(mountInfo.getCfsId());
            } catch (WebClientResponseException e) {
                throw new CommonException.CfsServiceException(e.getResponseBodyAsString());
            } catch (Exception e) {
                throw new CommonException.CfsServiceException(e.getMessage());
            }

            if (CollectionUtils.isEmpty(cfsMountPoints)) {
                throw new CommonExceptions.RequestInvalidException(
                        "The cfsId " + mountInfo.getCfsId() + " does not have mount point.");
            }
            MountTarget mountTarget = null;
            for (MountTarget target : cfsMountPoints) {
                if (mountInfo.getMountPoint().contains(target.getDomain())) {
                    mountTarget = target;
                    break;
                }
            }
            if (mountTarget == null) {
                throw new CommonException.ResourceNotExistException(mountInfo.getMountPoint() + " does not exist.");
            }

            SubnetVo subnet = subnetGateway.getSubnet(mountTarget.getSubnetId());
            if (!subnet.getVpcShortId().equalsIgnoreCase(request.getVpcId())) {
                throw new CommonExceptions.RequestInvalidException(
                        "The cfsMountPoint "
                                + mountInfo.getMountPoint()
                                + "'s vpcId "
                                + subnet.getVpcShortId()
                                + " does not match "
                                + request.getVpcId());
            }
        }
        if (!validSoftwarePath) {
            throw new CommonExceptions.RequestInvalidException("The softwareDir is invalid. not in any mountPoint.");
        }
    }

    /**
     * @Description 判断给定的 mount 目录是否有效，不能是以下的系统目录之一：
     * /、/bin、/boot、/chpcdata、/dev、/etc、/lib、/lib64、/mnt、/opt、/plugin、/proc、/root、/run、/sbin、/sys、/tmp、/usr、/var
     * @Param mountDir 需要判断的 mount 目录字符串
     * @Return boolean 返回布尔值，true 表示有效，false 表示无效
     * <p>
     * 无效的目录包括：
     * </p>
     * /、/bin、/boot、/chpcdata、/dev、/etc、/lib、/lib64、/mnt、/opt、/plugin、/proc、/root、/run、/sbin、/sys、/tmp、/usr、/var
     */
    public static void isValidMountDirectory(String mountDir) {
        // mountDir 不能是以下的系统目录之一
        // /、/bin、/boot、/chpcdata、/dev、/etc、/lib、/lib64、/mnt、/opt、/plugin、/proc、/root、/run、/sbin、/sys、/tmp、/usr、/var
        String[] systemDirs = {
                "/",
                "/bin",
                "/boot",
                "/chpcdata",
                "/dev",
                "/etc",
                "/lib",
                "/lib64",
                "/mnt",
                "/opt",
                "/plugin",
                "/proc",
                "/root",
                "/run",
                "/sbin",
                "/sys",
                "/tmp",
                "/usr",
                "/var"
        };
        for (String dir : systemDirs) {
            if (mountDir.equals(dir)) {
                throw new CommonExceptions.RequestInvalidException("无效挂载路径：" + mountDir + "，不能覆盖系统目录");
            }
        }

        Pattern unixPattern = Pattern.compile("^/(?:[^/]+/)*[^/]*$");
        Pattern windowsPattern = Pattern.compile("^[A-Za-z]:(?:[\\/][^\\/]*)*$");

        if (!unixPattern.matcher(mountDir).matches() && !windowsPattern.matcher(mountDir).matches()) {
            throw new CommonExceptions.RequestInvalidException("无效挂载路径：" + mountDir + "，不是有效目录");
        }
        // 如果 mountDir 中包含空格，报错
        if (mountDir.contains(" ")) {
            throw new CommonExceptions.RequestInvalidException("无效挂载路径：" + mountDir + "，不能包含空格");
        }
    }

    /**
     * 判断是否为有效的挂载选项。
     * 有效的挂载选项应该以 "-t nfs" 或 "-t nfs4" 开头，后面跟着空格、-o 和一系列字符。
     *
     * @param mountOption 要判断的挂载选项，类型为字符串
     * @return true 如果挂载选项有效；false 如果挂载选项无效
     */
    public static boolean isValidMountOption(String mountOption) {
        // 正则表达式，匹配以-t nfs 或 -t nfs4 开头，后面跟着空格、-o 和一系列字符的字符串
        String regex = "^-t\\s+(nfs|nfs4)\\s+-o\\s+.*$";
        return mountOption.matches(regex);
    }

    /**
     * 校验用户默认quota
     * 单个region集群总数为3个
     * 集群计算节点总数为500个
     *
     * @param request 请求参数
     */
    public void validateQuota(ClusterCreateRequest request) {
        int computeCount = 0;
        for (QueueInfo queueInfo : request.getQueueList()) {
            if (queueInfo.getComputeSpec() != null) {
                computeCount += queueInfo.getComputeSpec().getCount();
            }
        }

        if (request.getMasterSpec().getCount() < 1 || request.getMasterSpec().getCount() > 2) {
            throw new CommonExceptions.RequestInvalidException("managerCount range from 1 to 2");
        }

        if (request.getLoginSpec() != null && request.getLoginSpec().getCount() > 1) {
            throw new CommonExceptions.RequestInvalidException("loginCount should equals to 0 or 1");
        }

        if (computeCount < 0) {
            throw new CommonExceptions.RequestInvalidException("computeCount should greater than or equal 0");
        }

        Map<String, String> quotaType2Quota = whitelistGateway.getQuota(
                LogicUserService.getAccountId(), Const.Quota.CHPC_CLUSTER_QUOTA, Const.Quota.CHPC_COMPUTE_NODE_QUOTA);

        int clusterQuota = NumberUtils.parseIntWithDefault(
                quotaType2Quota.get(Const.Quota.CHPC_CLUSTER_QUOTA), Const.DefaultQuota.CHPC_CLUSTER_DEFAULT_QUOTA);

        int computeNodeQuota = NumberUtils.parseIntWithDefault(
                quotaType2Quota.get(Const.Quota.CHPC_COMPUTE_NODE_QUOTA),
                Const.DefaultQuota.CHPC_COMPUTE_NODE_DEFAULT_QUOTA);

        Long clusterCount = clusterDAOGateway.count(null, LogicUserService.getAccountId(), null);

        if (clusterCount >= clusterQuota) {
            log.debug("chpc_cluster_quota is: {}, cluster created: {}", clusterQuota, clusterCount);
            throw new CommonException.QuotaException("Exceed cluster quota: " + clusterQuota);
        }

        if (computeCount > computeNodeQuota) {
            log.debug("chpc_compute_quota is: {}, compute create: {}", computeNodeQuota, computeCount);
            throw new CommonException.QuotaException("Exceed compute node quota: " + computeNodeQuota);
        }
    }

    /**
     * @param request ClusterCreateRequest类型，包含软件列表信息
     * @throws CommonExceptions.RequestInvalidException 当软件名称和版本号不存在时抛出该异常
     * @Description: 校验软件参数，包括名称和版本号。如果不存在则抛出异常。
     */
    public void validateSoftwareParameters(ClusterCreateRequest request) {
        List<SoftwareVersion> softwares = request.getSoftwareList();

        List<com.baidu.bce.logic.chpc.model.Software> softwareList = softwareDAOGateway.findAll();

        // 将 softwareList 转换为map，key 为 name-version，并遍历softwares判断是否在 map中
        Map<String, com.baidu.bce.logic.chpc.model.Software> softwareMap = new HashMap<>();
        for (com.baidu.bce.logic.chpc.model.Software software : softwareList) {
            softwareMap.put(software.getName() + "-" + software.getVersion(), software);
        }
        if (softwares == null || softwares.isEmpty()) {
            return;
        }
        for (SoftwareVersion software : softwares) {
            if (!softwareMap.containsKey(software.getName() + "-" + software.getVersion())) {
                throw new CommonExceptions.RequestInvalidException(
                        "Software " + software.getName() + "-" + software.getVersion() + " does not exist");
            }
        }
    }
}
