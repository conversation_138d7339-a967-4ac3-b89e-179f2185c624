package com.baidu.bce.logic.chpc.bct.model;

import lombok.Data;

import java.util.Arrays;
import java.util.List;

@Data
public class BctConstants {
    private List<String> eventName = Arrays.asList("开启集群保护状态",
            "关闭集群保护状态", "创建集群", "删除集群", "安装软件", "卸载软件", "设置调度器信息", "新建节点扩容",
            "扩容已有节点", "扩容本地节点", "创建队列", "删除队列", "手动缩容并释放节点", "手动缩容", "添加用户",
            "删除用户", "修改用户权限", "修改用户密码", "调整作业优先级", "自动伸缩", "批量删除自动伸缩策略", "重置集群密码", "修改集群描述", "停止调度", "移动队列");

//    private List<String> resourceType = Arrays.asList("集群ID", "集群名称",
//            "软件名称", "最大作业数量", "最大排队作业数量", "队列名称", "节点ID", "用户名称", "权限", "集群描述",
//            "集群删除保护状态", "集群类型", "调度器类型", "地域", "节点配置", "队列最少节点数量", "队列最多节点数量");
}
