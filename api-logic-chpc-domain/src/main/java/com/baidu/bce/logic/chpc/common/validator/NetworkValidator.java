package com.baidu.bce.logic.chpc.common.validator;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.chpc.common.ZoneUtil;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

@Slf4j
@Component
public class NetworkValidator {

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    SubnetGateway subnetGateway;

    @Resource
    ZoneUtil zoneUtil;


    /**
     * 扩容节点的子网必须和集群属于同一个VPC
     * <p>
     * 1. subnetId 为空   && zoneName 为空
     * 直接返回，不校验
     * 2. subnetId 为空   && zoneName 不为空
     * 如果当前集群的子网az属性不等于zoneName, 则抛出异常
     * 3. subnetId 不为空  && zoneName 为空
     * 校验subnetId和VPC的关系，使用subnetId的zoneName
     * 4. subnetId 不为空  && zoneName 不为空
     * 校验subnetId和VPC的关系，校验subnetId和zoneName
     *
     * @param clusterId   集群ID
     * @param subnetId    子网ID
     * @param apiZoneName 例如cn-bj-a
     */
    public Map<String, String> validateSubnetAndVpc(String clusterId, String subnetId, String apiZoneName) {

        Map<String, String> map = new HashMap<>();

        if (StringUtils.isEmpty(subnetId) && StringUtils.isEmpty(apiZoneName)) {
            return map;
        }

        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());

        if (StringUtils.isEmpty(subnetId) && StringUtils.isNotEmpty(apiZoneName)) {
            String zoneName = ZoneUtil.getZoneNumberFromApiZone(apiZoneName);
            SubnetVo subnetVo = subnetGateway.getSubnet(cluster.getSubnetId());

            if (!zoneName.equalsIgnoreCase(subnetVo.getAz())) {
                throw new CommonExceptions.RequestInvalidException(apiZoneName + " does not equals to subnetId " +
                        subnetVo.getShortId() + "'s zoneName " + zoneUtil.getApiZoneName(subnetVo.getAz()));
            }

            map.put("subnetId", cluster.getSubnetId());
            return map;
        }

        SubnetVo subnet;
        if (StringUtils.isNotEmpty(subnetId) && StringUtils.isEmpty(apiZoneName)) {
            subnet = validateSubnetMatchVpc(subnetId, cluster);
            map.put("zoneName", subnet.getAz());
            return map;
        }

        if (StringUtils.isNotEmpty(subnetId) && StringUtils.isNotEmpty(apiZoneName)) {
            subnet = validateSubnetMatchVpc(subnetId, cluster);
            String zoneName = ZoneUtil.getZoneNumberFromApiZone(apiZoneName);
            if (!zoneName.equalsIgnoreCase(subnet.getAz())) {
                throw new CommonExceptions.RequestInvalidException("The request subnetId belongs to " +
                        zoneUtil.getApiZoneName(subnet.getAz()) + " and does not match " + apiZoneName);
            }
        }
        return map;
    }

    private SubnetVo validateSubnetMatchVpc(String subnetId, Cluster cluster) {
        SubnetVo subnet;
        try {
            subnet = subnetGateway.getSubnet(subnetId);
        } catch (WebClientResponseException e) {
            throw new CommonException.RelatedServiceException("SubnetService " + e.getResponseBodyAsString(),
                   e.getStatusCode().toString());
        } catch (BceInternalResponseException e) {
            throw new CommonException.RelatedServiceException("SubnetService " + e.getMessage(),
                   e.getCode().toString());
        }
        if (!cluster.getVpcId().equals(subnet.getVpcShortId())) {
            log.debug("Cluster: {}, SubnetVo: {}", cluster, subnet);
            throw new CommonExceptions.RequestInvalidException("The request subnetId belongs to vpcId: " +
                    subnet.getVpcShortId() + " and does not equals to cluster vpcId: " + cluster.getVpcId());
        }
        return subnet;
    }

}
