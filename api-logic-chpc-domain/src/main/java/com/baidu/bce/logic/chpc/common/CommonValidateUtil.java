package com.baidu.bce.logic.chpc.common;


import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import lombok.Data;

/**
 * @Author: lilu24
 * @Date: 2023-01-09
 */
@Data
public class CommonValidateUtil {
// todo 重新设计校验机制

    /*
    public static void validateCluster(Cluster cluster) {

        String accountId = LogicUserService.getAccountId();
        if (cluster == null ||
                StringUtils.isEmpty(cluster.getAccountId()) ||
                !cluster.getAccountId().equalsIgnoreCase(accountId)) {

            throw new CommonExceptions.RequestInvalidException(
                    "The cluster id is incorrect or is not a cluster created by the current account");
        }
    }
     */

    public static void validateGroup(Queue queue) {

        if (queue == null || queue.getIsDefault()) {

            throw new CommonExceptions.RequestInvalidException("The default group or queue does not allow deletion");
        }
    }

    public static void validateInstance(Instance instance) {

        if (instance == null) {

            throw new CommonExceptions.RequestInvalidException("The instance to be operation does not exist");
        }
    }

    public static void validBackendCommonResponse(BackendCommonResponse backendResponse) {

        if (!ChpcConstant.HTTP_STATUS_200.equals(backendResponse.getCode())) {

            throw new CommonExceptions.RequestInvalidException(backendResponse.getMessage());
        }
    }
}
