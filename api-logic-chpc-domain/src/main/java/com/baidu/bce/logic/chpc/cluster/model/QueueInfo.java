package com.baidu.bce.logic.chpc.cluster.model;

import com.baidu.bce.logic.chpc.tag.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueueInfo {
    /**
     * 创建集群时的队列名称
     */
    String queueName = "defaultQueue";

    String queueId; // 队列ID

    String description; // 队列描述

    Boolean isDefault = false; // 是否是默认队列

    NodeInfo computeSpec; // 队列中节点列表

    /**
     * 队列中最大节点数
     */
    // @Max(500)
    @Min(0)
    Integer maxNodesInQueue = 0;

    /**
     * 队列中最小节点数，避免在缩容过程中将队列中所有节点都释放
     */
    @Min(value = 0)
    Integer minNodesInQueue = 0;

    /**
     * 单次最大伸缩节点数
     */
    // @Max(50)
    @Min(0)
    Integer maxScalePerCycle = 0;

    Boolean isAutoScale = false; // 是否开启自动扩缩容

    /**
     * 是否开启自动扩容
     */
    Boolean enableAutoGrow;

    /**
     * 是否开启自动缩容
     */
    Boolean enableAutoShrink;

    String hostnamePrefix;

    // 队列绑定的Tags
    @Valid
    List<Tag> tags;
}
