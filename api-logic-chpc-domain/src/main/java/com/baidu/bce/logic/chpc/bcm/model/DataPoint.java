package com.baidu.bce.logic.chpc.bcm.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataPoint {
        Double average;

        Double sum;

        Double minimum;

        Double maximum;

        Integer sampleCount;

        String timestamp;

        public DataPoint(String timestamp) {
                this.timestamp = timestamp;
                this.average = null;
                this.sum = null;
                this.minimum = null;
                this.maximum = null;
                this.sampleCount = null;
        }
}
