package com.baidu.bce.logic.chpc.cluster.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class MountInfo {
        /**
         * 共享存储cfsId
         * 例如：cfs-6B76rTO4vS
         */
        String cfsId;

        /**
         * cfs挂载点
         * 例如：cfs-6B76rTO4vS.lb-c659e6c1.cfs.bj.baidubce.com
         */
        String mountPoint;

        /**
         * 挂载共享存储的目录
         * 默认：/mnt/cfs
         */
        String mountDirectory = "/mnt/cfs";

        /**
         * 自定义挂载参数
         * 
         * 默认为空
         */
        String mountOption;
}
