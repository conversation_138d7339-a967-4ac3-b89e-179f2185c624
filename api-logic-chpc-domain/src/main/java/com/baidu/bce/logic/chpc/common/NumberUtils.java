package com.baidu.bce.logic.chpc.common;

import org.apache.commons.lang3.StringUtils;

public class NumberUtils extends org.springframework.util.NumberUtils {

    /**
     * 如果 x 在 [min,max] 中则返回 x，否则返回 dft
     *
     * @param x   原始值
     * @param min 区间左边界，为 null 表示负无穷
     * @param max 区间有边界，为 null 表示正无穷
     * @return 调整后的结果
     */
    public static int inRangeOrDefault(Integer x, Integer min, Integer max, int dft) {
        if (x == null || (min != null && x < min) || (max != null && x > max)) {
            return dft;
        }
        return x;
    }

    /**
     * 字符串转数字，有问题不抛异常，而是直接返回默认值
     *
     * @param orig 原始字符串
     * @param dft  默认值
     * @return 转换后的结果
     */
    public static int parseIntWithDefault(String orig, int dft) {
        if (StringUtils.isEmpty(orig)) {
            return dft;
        }
        try {
            return Integer.parseInt(orig);
        } catch (Throwable ignored) {
            return dft;
        }
    }
}
