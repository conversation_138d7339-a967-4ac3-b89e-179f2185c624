package com.baidu.bce.logic.chpc.queue;

import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/13
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class QueueAddRequest {
    private String clientToken;

    @Pattern(regexp = "^[a-zA-Z]([a-zA-Z0-9_]){0,13}[a-zA-Z0-9]$",
            message = "The specified parameter queueName is invalid.")
    @NotEmpty
    private String queueName;

    @Pattern(regexp = "^.{0,250}$", message = "is invalid")
    private String description;

    @JsonProperty("isDefault")
    private boolean isDefault;

    @Valid
    private List<InstanceAddRequest> computeNodes;


}
