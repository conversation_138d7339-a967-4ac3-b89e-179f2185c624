package com.baidu.bce.logic.chpc.bcm;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class BcmBatchQueryRequest {
    String scope;

    String userId;

    String region;

    List<String> metricNames;

    String startTime;

    String endTime;

    Integer cycle;

    List<List<BcmDimension>> dimensions;

    List<String> statistics;

    String type;
}
