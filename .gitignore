# Compiled source #
###################
*.class
*.dll
*.exe
*.o
*.so

# Packages #
############
# it's better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip
*.war
*.tar.gz

# Logs and databases #
######################
*.log
logs/
logging-undefine*/


# OS generated files #
######################
.DS_Store*
ehthumbs.db
Icon?
Thumbs.db

# Editor Files #
################
*~
*.swp

# Gradle Files #
################
.gradle/
.m2
gradlew
gradlew.bat

# Build output directies
target/
output/
classes/

# IntelliJ specific files/directories
out
.idea/
*.ipr
*.iws
*.iml
atlassian-ide-plugin.xml

# Eclipse specific files/directories
.classpath
.project
.settings/
.metadata

# NetBeans specific files/directories
.nbattrs

# temp ignore
*.cache
*.diff
*.patch
*.tmp
tree.txt

application.pid
application.port

application.properties
endpoint.json
.vscode
build_test*
