<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.baidu.bce</groupId>
    <artifactId>api-logic-chpc-parent</artifactId>
    <version>${api-logic-chpc-version}</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>com.baidu.bce.boot</groupId>
        <artifactId>bce-web-framework-parent</artifactId>
        <version>1.0.178.1</version>
    </parent>

    <modules>
        <module>api-logic-chpc-adapter</module>
        <module>api-logic-chpc-app</module>
        <module>api-logic-chpc-client</module>
        <module>api-logic-chpc-domain</module>
        <module>api-logic-chpc-infrastructure</module>
        <module>api-logic-chpc-start</module>
        <module>cov.baidu.com</module>
    </modules>

    <properties>
        <api-logic-chpc-version>version</api-logic-chpc-version>
        <java.version>17</java.version>
        <source.encoding>UTF-8</source.encoding>
        <maven.build.timestamp.format>yyyy-MM-dd HH:mm:ssZ</maven.build.timestamp.format>
        <apollo.version>2.1.0</apollo.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.baidu.bce</groupId>
                <artifactId>api-logic-chpc-adapter</artifactId>
                <version>${api-logic-chpc-version}</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.bce</groupId>
                <artifactId>api-logic-chpc-app</artifactId>
                <version>${api-logic-chpc-version}</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.bce</groupId>
                <artifactId>api-logic-chpc-client</artifactId>
                <version>${api-logic-chpc-version}</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.bce</groupId>
                <artifactId>api-logic-chpc-domain</artifactId>
                <version>${api-logic-chpc-version}</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.bce</groupId>
                <artifactId>api-logic-chpc-infrastructure</artifactId>
                <version>${api-logic-chpc-version}</version>
            </dependency>
            <dependency>
                <groupId>com.baidu.bce</groupId>
                <artifactId>api-logic-chpc-start</artifactId>
                <version>${api-logic-chpc-version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client</artifactId>
                <version>${apollo.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-logical-vpc-external-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <artifactId>bce-springdoc-spring-boot-starter</artifactId>
            <groupId>com.baidu.bce.boot</groupId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-web-framework-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baidu.bce.boot</groupId>
            <artifactId>bce-sdk-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.7.1</version>
        </dependency>
    </dependencies>


    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>Baidu_Local</id>
            <url>https://maven.baidu-int.com/nexus/content/repositories/Baidu_Local</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>Baidu_Local_Snapshots</id>
            <url>https://maven.baidu-int.com/nexus/content/repositories/Baidu_Local_Snapshots</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>always</updatePolicy>
            </snapshots>
        </repository>
    </repositories>

    <!-- The remote central repository -->
    <distributionManagement>
        <repository>
            <id>Baidu_Local</id>
            <url>https://maven.baidu-int.com/nexus/content/repositories/Baidu_Local</url>
        </repository>
        <snapshotRepository>
            <id>Baidu_Local_Snapshots</id>
            <url>https://maven.baidu-int.com/nexus/content/repositories/Baidu_Local_Snapshots</url>
        </snapshotRepository>
    </distributionManagement>


</project>
