package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.bcc.BccInstance;
import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.chpc.gateway.ChpcGateway;
import com.baidu.bce.logic.chpc.chpc.model.PushAutoScalingBctEventRequest;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterOosTaskStatus;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.EventType;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.common.SoftwareStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterEventDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag;
import com.baidu.bce.logic.chpc.model.Cfs;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Event;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.SoftwareRecord;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.oos.CreateOosExecutionRequest;
import com.baidu.bce.logic.chpc.oos.GetOosExecutionResponse;
import com.baidu.bce.logic.chpc.scheduler.model.CommandContent;
import com.baidu.bce.logic.chpc.scheduler.model.GpuDriverConfig;
import com.baidu.bce.logic.chpc.scheduler.model.Node;
import com.baidu.bce.logic.chpc.scheduler.model.SharedStorage;
import com.baidu.bce.logic.chpc.service.IAutoScalingService;
import com.baidu.bce.logic.chpc.service.software.SoftwareServiceImpl;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.chpc.software.model.InstallSoftwareRequest;
import com.baidu.bce.logic.chpc.software.model.InstallSoftwareResponse;
import com.baidu.bce.logic.chpc.software.model.SoftwareVersion;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.fasterxml.jackson.core.io.JsonStringEncoder;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Slf4j
@Component(value = "group_started_task")
public class GroupStartedTask extends AbstractSchedulerTask {

    @Resource
    ClusterEventDAOGateway clusterEventDAOGateway;

    @Value("${chpc.task.wait-times}")
    private Integer taskWaitTimes;

    @Value("${software.checkArch:true}")
    private boolean checkSoftwareArch;

    @Value("${bce.web.commons.gray.enabled:false}")
    private boolean grayEnabled;

    @Value("${bce.web.commons.bos.bucket.endpoint}")
    private String bosEndpoint;

    @Autowired
    SoftwareRecordDAOGateway softwareRecordDAOGateway;

    @Resource
    BccGateway bccGateway;

    @Resource
    CfsDAOGateway cfsDAOGateway;

    @Resource
    IAutoScalingService iAutoScalingService;

    @Resource
    SoftwareServiceImpl softwareServiceImpl;

    @Resource
    ChpcGateway chpcGateway;

    /**
     * {@inheritDoc}
     * 根据任务的等待时间，判断是否可以开始启动集群。如果不能，则返回处理中状态；如果可以，则尝试启动实例，并返回结果。
     * 如果启动过程中出现问题，则返回失败状态；如果启动成功，则更新队列和集群状态，返回成功状态。
     *
     * @param task    任务对象
     * @param cluster 集群对象
     * @param queue   队列对象
     * @return {@code Map<String, Object>} 包含任务状态的Map，包含两个键值对：{@code TASK_STATUS} -
     * 任务状态，{@code SUCCEED} - 成功，{@code FAILED} - 失败，{@code PROCESSING} -
     * 处理中
     * @throws Exception 当任务等待时间校验失败或启动实例过程中发生异常时抛出异常
     */
    @Override
    public Map<String, Object> executeTask(final Task task, final Cluster cluster, final Queue queue) {
        // if (!validateWaitTimes(task)) {
        // return Collections.singletonMap(ChpcConstant.TASK_STATUS,
        // TaskStatus.PROCESSING.getValue());
        // }
        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);

        log.debug(
                "[AddNodeDebug-stackId:{}] begin to start clusterId: {}, queueId: {}",
                task.getCosStackId(),
                cluster.getClusterId(),
                queue.getQueueId());

        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            Event event =
                    clusterEventDAOGateway.findByClusterIdAndEvent(task.getClusterId(), EventType.START_PROXY_NODE);
            if (EventType.READY_TO_START.equals(event.getStatus())) {
                clusterEventDAOGateway.update(
                        cluster.getClusterId(), EventType.START_PROXY_NODE, EventType.PROGRESSING, event.getErrMsg());
            }
        }

        // 查询gpu驱动配置
        List<String> queueIds = new ArrayList<>();
        GpuDriverConfig gpuDriverConfig = new GpuDriverConfig();
        Object queueIdsObj = extraMap.get(ChpcConstant.QUEUE_IDS);
        Object cudaVersionObj = extraMap.get(ChpcConstant.CHPC_CUDA_VERSION);
        Object gpuDriverVersionObject = extraMap.get(ChpcConstant.CHPC_GPU_DRIVER_VERSION);
        Object cudnnVersionObj = extraMap.get(ChpcConstant.CHPC_CUDNN_VERSION);
        if (queueIdsObj instanceof List<?>
                && cudaVersionObj instanceof List<?>
                && gpuDriverVersionObject instanceof List<?>
                && cudaVersionObj instanceof List<?>) {
            @SuppressWarnings("unchecked")
            List<String> tmpQueueIds = (List<String>) queueIdsObj;
            queueIds = tmpQueueIds;
            @SuppressWarnings("unchecked")
            List<String> cudaVersions = (List<String>) cudaVersionObj;
            @SuppressWarnings("unchecked")
            List<String> gpuDriverVersions = (List<String>) gpuDriverVersionObject;
            @SuppressWarnings("unchecked")
            List<String> cudnnVersions = (List<String>) cudnnVersionObj;
            // 找到queueId对应的gpu驱动版本
            int index = queueIds.indexOf(queue.getQueueId());
            if (index == -1) {
                log.error(
                        "[AddNodeDebug] clusterId: {} queueId: {} not found in task queueIds: {}",
                        cluster.getClusterId(),
                        queue.getQueueId(),
                        queueIds);
                return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            }
            gpuDriverConfig.setCudaVersion(cudaVersions.get(index));
            gpuDriverConfig.setGpuDriverVersion(gpuDriverVersions.get(index));
            gpuDriverConfig.setCudnnVersion(cudnnVersions.get(index));
        }

        // *从 costackId 查询所有节点
        List<Instance> instances =
                instanceService.findByCosAndStatus(
                        cluster.getClusterId(),
                        task.getCosStackId(),
                        InstanceStatus.WAITING_TO_START.nameLowerCase(),
                        InstanceStatus.STARTING.nameLowerCase());

        List<String> failedInstanceIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(instances)) {

            Map<String, List<Instance>> groupByInstanceType =
                    instances.stream().collect(Collectors.groupingBy(Instance::getNodeType));

            log.debug(
                    "[AddNodeDebug-stackId:{}] start to start instance clusterId: {}",
                    task.getCosStackId(),
                    cluster.getClusterId());

            // 启动顺序：主节点 -> 备节点（若有）-> 登录节点（若有）-> 计算节点(若有）
            if (groupByInstanceType.containsKey(InstanceNodeType.MASTER.getType())) {
                if (ClusterType.CLOUD.nameLowerCase().equals(cluster.getClusterType())) {
                    Event event =
                            clusterEventDAOGateway.findByClusterIdAndEvent(
                                    task.getClusterId(), EventType.START_MANAGER_NODE);
                    if (event != null && EventType.READY_TO_START.equals(event.getStatus())) {
                        clusterEventDAOGateway.update(
                                cluster.getClusterId(), EventType.START_MANAGER_NODE, EventType.PROGRESSING, "");
                    }
                }
                failedInstanceIds =
                    checkOrStartInstance(task, groupByInstanceType.get(InstanceNodeType.MASTER.getType()));

            } else if (groupByInstanceType.containsKey(InstanceNodeType.LOGIN.getType())) {
                if (ClusterType.CLOUD.nameLowerCase().equals(cluster.getClusterType())) {
                    Event event =
                            clusterEventDAOGateway.findByClusterIdAndEvent(
                                    task.getClusterId(), EventType.START_MANAGER_NODE);
                    if (event != null && EventType.PROGRESSING.equals(event.getStatus())) {
                        clusterEventDAOGateway.update(
                                cluster.getClusterId(), EventType.START_MANAGER_NODE, EventType.SUCCEED, "");
                    }
                    event =
                            clusterEventDAOGateway.findByClusterIdAndEvent(task.getClusterId(), EventType.START_LOGIN_NODE);
                    if (event != null && EventType.READY_TO_START.equals(event.getStatus())) {
                        clusterEventDAOGateway.update(
                                cluster.getClusterId(), EventType.START_LOGIN_NODE, EventType.PROGRESSING, "");
                    }
                }
                failedInstanceIds = checkOrStartInstance(task, groupByInstanceType.get(InstanceNodeType.LOGIN.getType()));

            } else if (groupByInstanceType.containsKey(InstanceNodeType.BACKUP.getType())) {
                failedInstanceIds =
                    checkOrStartInstance(
                        task, groupByInstanceType.get(InstanceNodeType.BACKUP.getType()));

            } else if (groupByInstanceType.containsKey(InstanceNodeType.COMPUTE.getType())) {
                if (ClusterType.CLOUD.nameLowerCase().equals(cluster.getClusterType())) {
                    Event event =
                            clusterEventDAOGateway.findByClusterIdAndEvent(
                                    task.getClusterId(), EventType.START_MANAGER_NODE);
                    if (event != null && EventType.PROGRESSING.equals(event.getStatus())) {
                        clusterEventDAOGateway.update(
                                cluster.getClusterId(), EventType.START_MANAGER_NODE, EventType.SUCCEED, "");
                    }
                    event =
                            clusterEventDAOGateway.findByClusterIdAndEvent(task.getClusterId(), EventType.START_LOGIN_NODE);
                    if (event != null && EventType.PROGRESSING.equals(event.getStatus())) {
                        clusterEventDAOGateway.update(
                                cluster.getClusterId(), EventType.START_LOGIN_NODE, EventType.SUCCEED, "");
                    }

                    event =
                            clusterEventDAOGateway.findByClusterIdAndEvent(
                                    task.getClusterId(), EventType.START_COMPUTE_NODE);
                    if (event != null && EventType.READY_TO_START.equals(event.getStatus())) {
                        clusterEventDAOGateway.update(
                                cluster.getClusterId(), EventType.START_COMPUTE_NODE, EventType.PROGRESSING, "");
                    }
                }
                failedInstanceIds =
                    checkOrStartInstance(
                        task, groupByInstanceType.get(InstanceNodeType.COMPUTE.getType()));
            }

            log.debug(
                    "[AddNodeDebug-stackId:{}] finish starting instance clusterId: {}",
                    task.getCosStackId(),
                    cluster.getClusterId());
        }
        if (failedInstanceIds.size() > 0) {
            // 对于自动扩容任务，如果部分节点启动失败，不影响其他节点
            if (extraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)) {
                List<Instance> allInstances =
                    instanceService.findByCosStackId(
                        cluster.getClusterId(),
                        null,
                        task.getCosStackId());
                if (failedInstanceIds.size() != allInstances.size()) {
                    // 检查是否还有未启动的实例
                    List<Instance> checkInstanceStatus =
                        instanceService.findByCosAndStatus(
                            cluster.getClusterId(), task.getCosStackId(),
                            InstanceStatus.WAITING_TO_START.nameLowerCase(), InstanceStatus.STARTING.nameLowerCase());
                    // 如果有，则继续等待下次任务检查
                    if (CollectionUtils.isNotEmpty(checkInstanceStatus) && checkInstanceStatus.size() != failedInstanceIds.size()) {
                        return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
                    }
                    // 如果没有，则将失败的节点删除，并标记任务成功
                    for (String instanceId : failedInstanceIds) {
                        bccExternalGateway.deleteServer(instanceId);
                        instanceService.delete(instanceId);
                    }
                    extraMap.put(ChpcConstant.START_FAILED_INSTANCES, failedInstanceIds);
                    extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
                    log.debug("自动扩容部分节点启动失败，失败节点：{}", failedInstanceIds);
                    return extraMap;
                }
            }
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            extraMap.put(ChpcConstant.CLUSTER_TYPE, cluster.getClusterType());
            return extraMap;
        }

        // 再次检查所有实例状态
        // *根据 cosstackId 查询所有实例
        List<Instance> checkInstanceStatus =
                instanceService.findByCosAndStatus(
                        cluster.getClusterId(), task.getCosStackId(),
                        InstanceStatus.WAITING_TO_START.nameLowerCase(), InstanceStatus.STARTING.nameLowerCase());

        if (CollectionUtils.isNotEmpty(checkInstanceStatus)) {
            return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
        }

        // 将自动伸缩信息同步到集群
        List<AutoScaling> autoScalingList = autoScalingDAOGateway.getByQueueIds(queueIds, null);
        Map<String, Queue> queuesById =
                queueDAOGateway.getByQueueIds(queueIds).stream().collect(Collectors.toMap(Queue::getQueueId, q -> q));
        for (AutoScaling autoScaling : autoScalingList) {
            if (autoScaling != null) {
                autoScaling.setQueueName(queuesById.get(autoScaling.getQueueId()).getName());
                iAutoScalingService.updateAutoScalerToClusterManager(autoScaling);
            }
        }

        // 更新队列为已经就绪状态
        // *更新所以队列状态为成功（队列列表放在 task 的extra 中）
        for (String queueId : queueIds) {
            queueDAOGateway.updateStatus(queueId, QueueStatus.ACTIVE.nameLowerCase());
        }

        // queueDAOGateway.updateStatus(queue.getQueueId(),
        // QueueStatus.ACTIVE.nameLowerCase());

        extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());

        // // * 最后创建节点
        // if
        // (TaskSourceType.ADD_INSTANCE_TO_GROUP.getTaskSourceType().equals(task.getSource())
        // &&
        // ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
        // if
        // (ClusterSchedulerType.PBS.getName().equalsIgnoreCase(cluster.getSchedulerType())
        // ||
        // ClusterSchedulerType.OPENPBS.getName().equalsIgnoreCase(cluster.getSchedulerType()))
        // {
        // Map<String, Object> curExtraMap = JacksonUtil.decodeToMap(task.getExtra(),
        // Object.class);
        // List<String> instanceHosts = ServiceUtil
        // .castToList(curExtraMap.get(ChpcConstant.INSTANCE_HOSTNAME_LIST));
        // List<String> instanceSpecs =
        // ServiceUtil.castToList(curExtraMap.get(ChpcConstant.INSTANCE_SPEC_LIST));
        // String queueName = (String) curExtraMap.get(ChpcConstant.QUEUE_NAME);
        // log.debug(
        // "[AddNodeDebug] start to create cloud node, clusterId:{} queueName:{}
        // instanceHosts:{} instanceSpecs:{}",
        // task.getClusterId(), queueName, instanceHosts, instanceSpecs);
        // for (int i = 0; i < instanceHosts.size(); i++) {
        // log.debug("[AddNodeDebug] create cloud node, clusterId:{} host:{} spec:{}
        // queue:{}",
        // task.getClusterId(), instanceHosts.get(i), instanceSpecs.get(i), queueName);
        // BackendActionProxyResponse resp =
        // backendGateway.actionProxy(task.getClusterId(), "node_create",
        // String.format("--host=%s --queue=%s --spec=%s", instanceHosts.get(i),
        // queueName,
        // instanceSpecs.get(i)));
        // if (resp.getCode() != 200) {
        // log.debug("[AddNodeDebug] create cloud node failed, clusterId:{} errmsg:{}",
        // task.getClusterId(), resp.getMessage());
        // throw new CommonException.RelatedServiceException(
        // "create cloud node failed, " + resp.getMessage(), "scheduler plugin");
        // }
        // }
        // }
        // }

        log.debug(
                "[AddNodeDebug-stackId:{}] finish group started task clusterId: {}",
                task.getCosStackId(),
                cluster.getClusterId());

        extraMap.put(ChpcConstant.CLUSTER_TYPE, cluster.getClusterType());
        return extraMap;
    }

    /**
     * @param task      Task 任务信息
     * @param instances List<Instance> 实例列表，包含等待启动和启动中状态的实例
     * @return Boolean 返回true表示所有实例都处理完成，false表示存在启动失败的实例
     * @description 检查或者开始实例，如果实例是等待启动状态，则向OOS服务发送命令，并将实例状态更新为启动中。
     * 如果实例是启动中状态，则获取OOS任务执行结果，根据结果更新实例状态和执行ID。
     * 如果实例是启动中状态，且OOS任务执行成功，则更新实例状态为已启动；
     * 如果实例是启动中状态，且OOS任务执行失败，则更新实例状态为启动失败，并更新集群状态为启动失败。
     */
    private List<String> checkOrStartInstance(Task task, List<Instance> instances) {

        List<String> failedInstanceIds = new ArrayList<>();

        if (CollectionUtils.isEmpty(instances)) {
            return failedInstanceIds;
        }

        // 批量获取bcc的信息(主要是获取bcc的tag)
        List<String> instanceIds = instances.stream().map(Instance::getInstanceId).toList();
        List<BccInstance> bccInstances = bccGateway.getBccInstances(instanceIds);
        Map<String, BccInstance> idToBccInstance = bccInstances.stream().
                collect(Collectors.toMap(BccInstance::getInstanceId, bccInstance -> bccInstance,
                        (existing, replacement) -> replacement));
        // todo 对于计算节点，可以并行下发命令来加速
        for (Instance instance : instances) {

            if (InstanceStatus.WAITING_TO_START.nameLowerCase().equalsIgnoreCase(instance.getStatus())) {

                // 获取bcc的tag信息拿到gpu相关软件的tag
                BccInstance bccInstance = idToBccInstance.get(instance.getInstanceId());
                List<Tag> tags = bccInstance.getTags();
                log.debug("bcc tags: {}", tags);
                GpuDriverConfig gpuDriverConfig = new GpuDriverConfig();
                for (Tag tag : tags){
                    if (tag.getTagKey().equalsIgnoreCase(ChpcConstant.GPU_DRIVER_VERSION)){
                        gpuDriverConfig.setGpuDriverVersion(tag.getTagValue());
                    }
                    if (tag.getTagKey().equalsIgnoreCase(ChpcConstant.CUDA_VERSION)){
                        gpuDriverConfig.setCudaVersion(tag.getTagValue());
                    }
                    if (tag.getTagKey().equalsIgnoreCase(ChpcConstant.CUDNN_VERSION)){
                        gpuDriverConfig.setCudnnVersion(tag.getTagValue());
                    }
                }
                CreateOosExecutionRequest request = generateOosExecutionRequest(instance, gpuDriverConfig);

                log.debug(
                        "begin to send oos command, instance id: {}, content: {}",
                        instance.getInstanceId(),
                        JacksonUtil.toJson(request));

                String executionId;
                try {

                    log.debug(
                            "[AddNodeDebug-stackId:{}] begin to send oos command, instance id: {}",
                            instance.getCosStackId(),
                            instance.getInstanceId());

                    executionId = oosGateway.createExecution(request);
                } catch (Exception e) {
                    log.error(
                            "call oos service failed in waiting_to_start to starting, instanceId:{}, exception:{}",
                            instance.getInstanceId(),
                            e);
                    continue;
                }

                instanceService.updateStatusAndExecutionId(
                        instance.getInstanceId(), executionId, InstanceStatus.STARTING.nameLowerCase());
                // 存在登录节点，添加chpc-app-server软件安装记录
                if (InstanceNodeType.LOGIN.getType().equalsIgnoreCase(instance.getNodeType())) {
                    SoftwareRecord record = new SoftwareRecord();
                    record.setClusterId(instance.getClusterId());
                    record.setName(ChpcConstant.CHPC_APP_SERVER);
                    record.setVersion("1.0.1");
                    record.setOosExecutionId(executionId);
                    record.setStatus(SoftwareStatus.INSTALLING.nameLowerCase());
                    softwareRecordDAOGateway.insert(record);

                    SoftwareRecord vncRecord = new SoftwareRecord();
                    vncRecord.setClusterId(instance.getClusterId());
                    vncRecord.setName(ChpcConstant.CHPC_VNC_SERVER);
                    vncRecord.setVersion("1.8.0");
                    vncRecord.setOosExecutionId(executionId);
                    vncRecord.setStatus(SoftwareStatus.INSTALLING.nameLowerCase());
                    softwareRecordDAOGateway.insert(vncRecord);
                }

                log.debug("success to send oos, instance id: {}", instance.getInstanceId());
            } else if (InstanceStatus.STARTING.nameLowerCase().equalsIgnoreCase(instance.getStatus())) {

                GetOosExecutionResponse.Result response = oosGateway.getExecutionById(instance.getOosExecutionId());
                log.debug(
                        "oos response, id :{}, status:{}, output:{}",
                        instance.getOosExecutionId(),
                        response.getState(),
                        response.getReason());
                if (ClusterOosTaskStatus.SUCCESS.name().equalsIgnoreCase(response.getState())) {
                    instance.setStatus(InstanceStatus.STARTED.nameLowerCase());
                    instanceService.updateStatusAndExecutionId(
                            instance.getInstanceId(), response.getId(), InstanceStatus.STARTED.nameLowerCase());
                }

                if (ClusterOosTaskStatus.FAILED.name().equalsIgnoreCase(response.getState())) {
                    Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
                    failedInstanceIds.add(instance.getInstanceId());
                    // 手动或自动扩容启动失败，继续执行其他实例启动任务
                    if (extraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG) || 
                    extraMap.containsKey(ChpcConstant.MANUAL_SCALING_FLAG)) {
                        continue;
                    }
                    // 如果是创建集群失败，则立即返回失败节点
                    String errorMessage =
                            "the oos of "
                                    + instance.getOosExecutionId()
                                    + " is "
                                    + response.getState()
                                    + ",Reason: "
                                    + response.getReason();
                    // 创建集群时，启动slurm失败，更新集群为启动失败状态
                    clusterService.updateClusterWithErrorMessage(
                        instance.getClusterId(), ClusterStatus.START_FAILED.nameLowerCase(), errorMessage);
                    return failedInstanceIds;
                }
            }
        }

        return failedInstanceIds;
    }

    /**
     * {@inheritDoc}
     * 在执行任务后，如果任务失败则创建一个组删除任务，并将源任务的taskId传递给新任务。
     * 如果包含自动伸缩标识，则更新自动伸缩状态为正常。
     *
     * @param task    任务对象
     * @param paraMap 参数map，包含任务状态、自动伸缩标识和手动伸缩标识（可选）
     * @throws Exception 如果发生异常，抛出该异常
     */
    @Override
    public void afterExecuteTask(final Task task, final Map<String, Object> paraMap) {

        String executeStatus = String.valueOf(paraMap.get(ChpcConstant.TASK_STATUS));
        String clusterType = String.valueOf(paraMap.get(ChpcConstant.CLUSTER_TYPE));

        if (paraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)) {
            AutoScaling autoScaling = new AutoScaling();
            autoScaling.setAsId((String) paraMap.get(ChpcConstant.AUTO_SCALING_FLAG));
            autoScaling.setStatus(AutoScalingStatus.NORMAL);
            autoScalingDAOGateway.update(autoScaling);
        }

        if (TaskStatus.FAILED.getValue().equalsIgnoreCase(executeStatus)) {
            Map<String, Object> groupDeletedParaMap = new HashMap<>();
            if (paraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)) {
                groupDeletedParaMap.put(ChpcConstant.AUTO_SCALING_FLAG, paraMap.get(ChpcConstant.AUTO_SCALING_FLAG));
                // 推送BCT事件
                try {
                    PushAutoScalingBctEventRequest request = new PushAutoScalingBctEventRequest();
                    request.setUserId(LogicUserService.getAccountId());
                    Cluster cluster = clusterService.findByAll(task.getClusterId());
                    request.setClusterId(task.getClusterId());
                    request.setClusterName(cluster.getName());
                    Queue queue = queueDAOGateway.getByQueueId(task.getQueueId());
                    request.setQueueName(queue.getName());
                    Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
                    String msg = String.format("当前队列存在作业排队，需要 %d 节点，因此自动扩容，新建节点",
                            (Integer) (extraMap.getOrDefault(ChpcConstant.ADD_INSTANCE_COUNT, 1)));
                    request.setMessage(msg);
                    request.setSuccess(false);
                    request.setAsId((String) paraMap.get(ChpcConstant.AUTO_SCALING_FLAG));
                    chpcGateway.pushAutoExpandBctEvent(request);
                } catch (WebClientResponseException e) {
                    log.warn("push auto expand bct event error, err:{}", e.getResponseBodyAsString());
                } catch (Exception e) {
                    log.warn("push auto expand bct event error, err:{}", e.getMessage());
                }
            }
            if (paraMap.containsKey(ChpcConstant.MANUAL_SCALING_FLAG)) {
                groupDeletedParaMap.put(
                        ChpcConstant.MANUAL_SCALING_FLAG, paraMap.get(ChpcConstant.MANUAL_SCALING_FLAG));
            }

            groupDeletedParaMap.put(ChpcConstant.SOURCE_TASK_ID, task.getTaskId());
            taskService.insert(
                    TaskType.GROUP_DELETED_TASK,
                    task.getClusterId(),
                    task.getQueueId(),
                    TaskType.GROUP_STARTED_TASK.getTaskType(),
                    task.getTaskUuid(),
                    groupDeletedParaMap,
                    task.getCosStackId());
            // 更新事件状态
            if (TaskSourceType.ADD_RESOURCE_TO_CLUSTER.getTaskSourceType().equals(task.getSource())) {
                // *更新事件状态，只有创建集群才会更新事件
                String taskFailedReason = String.valueOf(paraMap.get(ChpcConstant.TASK_FAILED_REASON));
                if (ClusterType.HYBRID.nameLowerCase().equals(clusterType)) {
                    clusterEventDAOGateway.update(
                            task.getClusterId(), EventType.CREATE_RESOURCE, EventType.FAILED, taskFailedReason);
                } else if (ClusterType.CLOUD.nameLowerCase().equals(clusterType)) {
                    String nodeType = String.valueOf(paraMap.get(ChpcConstant.INSTANCE_NODE_TYPE));
                    String eventStr = EventType.START_MANAGER_NODE;
                    if (InstanceNodeType.LOGIN.getType().equals(nodeType)) {
                        eventStr = EventType.START_LOGIN_NODE;
                    }
                    if (InstanceNodeType.COMPUTE.getType().equals(nodeType)) {
                        eventStr = EventType.START_COMPUTE_NODE;
                    }
                    Event event = clusterEventDAOGateway.findByClusterIdAndEvent(task.getClusterId(), eventStr);
                    if (event != null && EventType.PROGRESSING.equals(event.getStatus())) {
                        clusterEventDAOGateway.update(
                                task.getClusterId(), eventStr, EventType.FAILED, taskFailedReason);
                    }
                }
            }

        } else if (TaskStatus.SUCCEED.getValue().equalsIgnoreCase(executeStatus)) {
            log.debug("group started task succeed. paraMap:{}", paraMap);
            if (paraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)) {
                // 推送BCT事件
                try {
                    List<Instance> instances =
                            instanceService.findByCosAndStatus(
                                    task.getClusterId(), task.getCosStackId(), InstanceStatus.STARTED.nameLowerCase());
                    PushAutoScalingBctEventRequest request = new PushAutoScalingBctEventRequest();
                    request.setUserId(LogicUserService.getAccountId());
                    Cluster cluster = clusterService.findByAll(task.getClusterId());
                    request.setClusterId(task.getClusterId());
                    request.setClusterName(cluster.getName());
                    Queue queue = queueDAOGateway.getByQueueId(task.getQueueId());
                    request.setQueueName(queue.getName());
                    request.setSuccess(true);
                    List<String> expandNodes = new ArrayList<>();
                    @SuppressWarnings("unchecked")
                    List<String> failedInstances = (List<String>) (paraMap.get(ChpcConstant.START_FAILED_INSTANCES));
                    if (failedInstances == null) {
                        failedInstances = new ArrayList<>();
                    }
                    for (Instance instance : instances) {
                        if (failedInstances.contains(instance.getInstanceId())) {
                            continue;
                        }
                        expandNodes.add(String.format("%s（ID：%s）", instance.getHostName(), instance.getInstanceId()));
                    }
                    String msg = String.format("当前队列存在作业排队，需要 %d 节点，因此自动扩容，新建节点 %s",
                            instances.size(), String.join("，", expandNodes));
                    request.setMessage(msg);
                    request.setAsId((String) paraMap.get(ChpcConstant.AUTO_SCALING_FLAG));
                    log.debug("push auto expand bct event request:{}", JacksonUtil.encode(request));
                    chpcGateway.pushAutoExpandBctEvent(request);
                } catch (WebClientResponseException e) {
                    log.warn("push auto expand bct event error, err:{}", e.getResponseBodyAsString());
                } catch (Exception e) {
                    log.warn("push auto expand bct event error, err:{}", e.getMessage());
                }
            }
            // 开启后置检查
            Map<String, Object> extraMap = new HashMap<>();
            extraMap.put(ChpcConstant.SOURCE_TASK_ID, task.getTaskId());
            // * 只有创建集群，才会启动 GROUP_CHECK_TASK
            if (TaskSourceType.ADD_RESOURCE_TO_CLUSTER.getTaskSourceType().equals(task.getSource())) {
                Map<String, Object> extraMapSource = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
                extraMap.put(ChpcConstant.SOURCE_TASK_ID, task.getTaskId());
                List<com.baidu.bce.logic.chpc.tag.Tag> clusterTags = ServiceUtil.castToTags(extraMapSource.get(ChpcConstant.CLUSTER_TAGS));
                extraMap.put(ChpcConstant.CLUSTER_TAGS, clusterTags);
                taskService.insert(
                        TaskType.GROUP_CHECK_TASK,
                        task.getClusterId(),
                        task.getQueueId(),
                        TaskType.GROUP_STARTED_TASK.getTaskType(),
                        task.getTaskUuid(),
                        extraMap,
                        task.getCosStackId());

                // 更新事件状态
                if (ClusterType.HYBRID.nameLowerCase().equals(clusterType)) {
                    clusterEventDAOGateway.update(
                            task.getClusterId(), EventType.START_PROXY_NODE, EventType.SUCCEED, "");
                } else if (ClusterType.CLOUD.nameLowerCase().equals(clusterType)) {
                    updateEventStatusIfProgressing(
                            task.getClusterId(), EventType.START_MANAGER_NODE, clusterEventDAOGateway);
                    updateEventStatusIfProgressing(
                            task.getClusterId(), EventType.START_LOGIN_NODE, clusterEventDAOGateway);
                    updateEventStatusIfProgressing(
                            task.getClusterId(), EventType.START_COMPUTE_NODE, clusterEventDAOGateway);
                }

                // * 安装自定义软件
                log.info("=====> {} start install software in cluster {}", task.getTaskId(), task.getClusterId());
                Object softwareListObj = extraMapSource.get(ChpcConstant.SOFTWARE_LIST);
                log.info("=====> {} software list: {}", task.getTaskId(), softwareListObj);

                if (softwareListObj instanceof List<?>) {
                    // @SuppressWarnings("unchecked")
                    // 将softwareListObj 序列化成json 字符串，然后再重新解析成 List<SoftwareVersion>
                    List<SoftwareVersion> softwareList =
                            JacksonUtil.decodeToList(JacksonUtil.encode(softwareListObj), SoftwareVersion.class);
                    log.info("=====> {} software list: {}", task.getTaskId(), softwareList);
                    InstallSoftwareRequest req = softwareServiceImpl.softwareInfo2InstallSoftwareRequest(softwareList);
                    log.info("=====> {} software req: {}", task.getTaskId(), softwareList);

                    InstallSoftwareResponse res = softwareServiceImpl.installSoftware(task.getClusterId(), req);
                    log.info("=====> {} software res: {}", task.getTaskId(), res);
                }
            }
        }
    }

    private CreateOosExecutionRequest generateOosExecutionRequest(Instance instance, GpuDriverConfig gpuDrvierConfig) {
        Map<String, String> idMaps = new HashMap<>();
        idMaps.put("instanceId", instance.getInstanceId());

        List<Map<String, String>> idLists = new ArrayList<>();
        idLists.add(idMaps);

        Map<String, Object> properties = new HashMap<>();
        properties.put("content", generateCommandContent(instance, gpuDrvierConfig));
        properties.put("user", "root");
        properties.put("workDir", "/");
        // 待执行命令的虚机列表
        properties.put("__workerSelectors__", idLists);

        CreateOosExecutionRequest.Operator operator = new CreateOosExecutionRequest.Operator();
        operator.setName("cloud_assist_shell");
        operator.setOperator("BCE::Agent::ExecuteShell");
        operator.setDescription("exec shell command");
        operator.setProperties(properties);

        CreateOosExecutionRequest.Template template = new CreateOosExecutionRequest.Template();
        template.setName("BCE-BCC-BulkyRunCommand");
        template.setLinear(true);
        template.setOperators(Collections.singletonList(operator));

        CreateOosExecutionRequest request = new CreateOosExecutionRequest();
        request.setTemplate(template);
        return request;
    }

    /**
     * @Description: 生成命令内容，包括节点类型、区域、集群信息、主节点、登录节点、备份节点、队列和共享存储等。
     * @Param commandInstance Instance - 命令实例对象，包含集群ID和节点类型。
     * @Return String - 返回格式化后的命令内容字符串。
     * @Throws Exception 无异常抛出。
     */
    private String generateCommandContent(Instance commandInstance, GpuDriverConfig gpuDrvierConfig) {
        CommandContent commandContent = new CommandContent();
        commandContent.setNodeType(commandInstance.getNodeType());
        String region = regionConfiguration.getCurrentRegion();
        if (bosEndpoint.contains("sandbox")) {
            region = "sandbox";
        }
        if (grayEnabled) {
            region += "test";
        }
        commandContent.setRegion(region);

        Cluster cluster = clusterService.findBy(commandInstance.getClusterId());
        commandContent.setEnableHa(cluster.isEnableHa());
        commandContent.setSchedulerType(cluster.getSchedulerType());
        commandContent.setClusterName(cluster.getName());
        commandContent.setClusterId(cluster.getClusterId());
        commandContent.setEnableMonitor(false);

        List<Instance> instanceList = instanceService.findBy(cluster.getClusterId(), null);

        Map<String, List<Instance>> instancesByInstanceType =
                instanceList.stream().collect(Collectors.groupingBy(Instance::getNodeType));

        Instance masterInstance = instancesByInstanceType.get(InstanceNodeType.MASTER.getType()).get(0);
        commandContent.setMasterHostname(masterInstance.getHostName());

        List<Instance> loginNodes = instancesByInstanceType.get(InstanceNodeType.LOGIN.getType());
        if (CollectionUtils.isNotEmpty(loginNodes)) {
            // 登录节点只会有一个
            commandContent.setLoginHostname(loginNodes.get(0).getHostName());
        }

        List<String> backupHostnames =
                this.generateBackupHostnames(instancesByInstanceType.get(InstanceNodeType.BACKUP.getType()));
        commandContent.setBackupHostnames(backupHostnames);

        List<com.baidu.bce.logic.chpc.scheduler.model.Queue> queueList =
                this.generateQueueList(
                        commandInstance.getClusterId(), instancesByInstanceType.get(InstanceNodeType.COMPUTE.getType()));
        commandContent.setQueueList(queueList);
        List<SharedStorage> sharedStorage = this.generateSharedStorageList(commandInstance.getClusterId());
        commandContent.setSharedStorageList(sharedStorage);

        String createClusterCmd = " --action installNode";
        createClusterCmd += String.format(" --clusterId %s", cluster.getClusterId());
        createClusterCmd += String.format(" --clusterName %s", cluster.getName());
        createClusterCmd += String.format(" --nodeType %s", commandInstance.getNodeType());
        // 目前登录节点，只能有一个
        if (CollectionUtils.isNotEmpty(loginNodes) && loginNodes.size() == 1) {
            createClusterCmd += String.format(" --loginHost=%s", loginNodes.get(0).getHostName());
        }
        createClusterCmd += String.format(" --clusterType %s", cluster.getClusterType());
        createClusterCmd += String.format(" --enableHa %s", cluster.isEnableHa());
        createClusterCmd += String.format(" --enableMonitor %s", false);
        createClusterCmd +=
                String.format(
                        " --backupHostnames %s",
                        new String(new JsonStringEncoder().quoteAsString(JacksonUtil.toJson(backupHostnames))));
        createClusterCmd += String.format(" --scheduler %s", cluster.getSchedulerType());
        createClusterCmd += String.format(" --schedulerVersion %s", cluster.getSchedulerVersion());
        createClusterCmd += String.format(" --pluginVersion %s", cluster.getSchedulePluginVersion());
        createClusterCmd += String.format(" --userId %s", cluster.getAccountId());
        createClusterCmd += String.format(" --region %s", region);
        createClusterCmd +=
                String.format(
                        " --queueList '%s'", new String(new JsonStringEncoder().quoteAsString(JacksonUtil.toJson(queueList))));
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            createClusterCmd += String.format(" --masterIp %s", masterInstance.getSchedulerIp());
            createClusterCmd += String.format(" --masterHost %s", masterInstance.getSchedulerHost());
        } else {
            createClusterCmd += String.format(" --masterIp %s", masterInstance.getPrivateIp());
            createClusterCmd += String.format(" --masterHost %s", masterInstance.getHostName());
        }
        createClusterCmd += String.format(" --softwareDir %s", cluster.getSoftwareDir());
        if (CollectionUtils.isNotEmpty(sharedStorage)) {
            String mountNfsCmd =
                    String.format(
                            " --sharedStorageList '%s'",
                            new String(new JsonStringEncoder().quoteAsString(JacksonUtil.toJson(sharedStorage))));
            createClusterCmd += mountNfsCmd;
        }
        if (commandInstance.getSshPort() != null) {
            createClusterCmd += String.format(" --sshPort %s", String.valueOf(commandInstance.getSshPort()));
        }
        createClusterCmd += String.format(" --maxNodes %d", cluster.getMaxNodes());
        createClusterCmd += String.format(" --maxCpus %d", cluster.getMaxCpus());

        // *增加 cluster 信息和 nfs 信息
        return this.generateFinalContent(
                commandContent, commandInstance, masterInstance, cluster, createClusterCmd, gpuDrvierConfig);
    }

    /**
     * @Description: 获取实例的架构信息，如果不是检查软件架构，则返回"amd64"。
     * @Param instance Instance - 实例对象，包含实例ID等信息
     * @Return String - 返回实例的架构信息，可能为"amd64"或者实际的架构信息
     * @Throws Exception 无异常抛出异常
     */
    public String getInstanceArch(Instance instance) {
        if (!checkSoftwareArch) {
            return "amd64";
        }

        List<String> instanceIds = new ArrayList<>();
        instanceIds.add(instance.getInstanceId());
        List<BccInstance> bccInstances = bccGateway.getBccInstances(instanceIds);

        String instanceArch = "amd64";

        if (bccInstances.size() == 1) {
            BccInstance masterInstance = bccInstances.get(0);
            instanceArch = masterInstance.getOsArch();
        }
        if (instanceArch.contains("amd64") || instanceArch.contains("x86")) {
            instanceArch = "amd64";
        }

        return instanceArch;
    }

    /**
     * @Description: 生成最终命令内容，包括创建集群、安装 environment module等操作
     * @Param commandContent CommandContent类型，命令参数对象
     * @Param commandInstance Instance类型，命令实例对象
     * @Return String 返回最终命令字符串
     * @Throws BceException 如果获取实例架构时出现异常，则抛出BceException异常
     */
    private String generateFinalContent(
            CommandContent commandContent,
            Instance commandInstance,
            Instance master,
            Cluster cluster,
            String createClusterCmd,
            GpuDriverConfig gpuDriverConfig) {

        String script = "install.sh";
        String openldap = "openldap_2.5.13_amd64";
        String chpcApp = "chpc-app-server_1.0.1_amd64";
        String region = regionConfiguration.getCurrentRegion();
        if (bosEndpoint.contains("sandbox")) {
            region = "sandbox";
        }
        if (grayEnabled) {
            region += "test";
        }
        String instanceArch;
        try {
            instanceArch = getInstanceArch(commandInstance);
            log.info("=====>instanceArch: {}", instanceArch);
        } catch (Exception e) {
            log.error("getInstanceArch error: {}", e);
            throw new BceException(e.getMessage());
        }

        StringBuilder result = new StringBuilder();
        // 安装gpu驱动
        String softwareFile = "";
        if (gpuDriverConfig != null
                && !StringUtils.isEmpty(gpuDriverConfig.getCudaVersion())
                && !StringUtils.isEmpty(gpuDriverConfig.getGpuDriverVersion())
                && !StringUtils.isEmpty(gpuDriverConfig.getCudnnVersion())) {
            softwareFile = String.format(String.format("gpu-driver_1.0_%s", instanceArch));
            String command =
                    String.format(
                            "wget %s/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh %s %s %s %s %s %s",
                            bosEndpoint,
                            softwareFile,
                            bosEndpoint,
                            instanceArch,
                            script,
                            cluster.getSchedulerType(),
                            region);
            command +=
                    String.format(
                            " --gpuDriverVersion %s --cudaVersion %s --cudnnVersion %s",
                            gpuDriverConfig.getGpuDriverVersion(),
                            gpuDriverConfig.getCudaVersion(),
                            gpuDriverConfig.getCudnnVersion());
            result.append(command).append(" && ");
        }
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            // * 混合集群使用该逻辑
            softwareFile =
                    String.format(
                            String.format("%s_%s_%s", cluster.getSchedulerType(), cluster.getSchedulerVersion(), instanceArch));
            String baseCmd =
                    String.format(
                            "wget %s/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh %s %s %s %s %s %s",
                            bosEndpoint,
                            softwareFile,
                            bosEndpoint,
                            instanceArch,
                            script,
                            cluster.getSchedulerType(),
                            region);

            result.append(baseCmd).append(createClusterCmd);

        } else {
            // * 公有云集群使用该逻辑

            // 1、 安装ldap
            String installOpenldap =
                    String.format(
                            "wget %s/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh %s %s %s %s %s %s",
                            bosEndpoint,
                            openldap,
                            bosEndpoint,
                            instanceArch,
                            script,
                            cluster.getSchedulerType(),
                            region);

            String installOpenldapCmd = String.format(" --region %s", region);
            installOpenldapCmd += String.format(" --node_type %s", commandInstance.getNodeType());
            installOpenldapCmd += String.format(" --master_host %s", master.getHostName());
            result.append(installOpenldap).append(installOpenldapCmd);

            // 2、安装环境

            softwareFile =
                    String.format(
                            String.format("%s_%s_%s", cluster.getSchedulerType(), cluster.getSchedulerVersion(), instanceArch));
            String baseCmd =
                    String.format(
                            "wget %s/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh %s %s %s %s %s %s",
                            bosEndpoint,
                            softwareFile,
                            bosEndpoint,
                            instanceArch,
                            script,
                            cluster.getSchedulerType(),
                            region);
            result.append(" && ").append(baseCmd).append(createClusterCmd);

            // 4、登录节点需要安装chpc-app-server
            if (InstanceNodeType.LOGIN.getType().equalsIgnoreCase(commandInstance.getNodeType())) {
                String chpcAppServer =
                        String.format(
                                "wget %s/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh %s %s %s %s %s %s",
                                bosEndpoint,
                                chpcApp,
                                bosEndpoint,
                                instanceArch,
                                script,
                                cluster.getSchedulerType(),
                                region);

                String installchpcAppServerCmd = String.format(" --region %s", region);
                installchpcAppServerCmd += String.format(" --scheduler %s", cluster.getSchedulerType());
                installchpcAppServerCmd += String.format(" --port %s", commandInstance.getPortalPort());
                if (commandInstance.getPublicIp() != null && !commandInstance.getPublicIp().isEmpty()) {
                    installchpcAppServerCmd += String.format(" --eip %s", commandInstance.getPublicIp());
                }
                installchpcAppServerCmd += String.format(" --cluster_id %s", cluster.getClusterId());
                installchpcAppServerCmd += String.format(" --cluster_name %s", cluster.getName());
                installchpcAppServerCmd += String.format(" --version %s", "1.0.1");
                result.append(" && ").append(chpcAppServer).append(installchpcAppServerCmd);
            }
        }

        // 增加安装 environment module的步骤
        softwareFile = String.format(String.format("environment-modules_4.1.1-1_%s", instanceArch));
        String command =
                String.format(
                        "wget %s/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh %s %s %s %s %s %s %s",
                        bosEndpoint,
                        softwareFile,
                        bosEndpoint,
                        instanceArch,
                        script,
                        "xxx",
                        region,
                        cluster.getSoftwareDir());
        result.append(" && ").append(command);

        if (InstanceNodeType.LOGIN.getType().equalsIgnoreCase(commandInstance.getNodeType())) {
            // 安装 vncserver，依赖于 modulefile
            softwareFile = String.format(String.format("vncserver_1.8.0_%s", instanceArch));
            command = String.format(
                    "wget %s/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh %s %s %s %s %s %s %s",
                    bosEndpoint,
                    softwareFile,
                    bosEndpoint,
                    instanceArch,
                    script,
                    "xxx",
                    region,
                    cluster.getSoftwareDir());
            result.append(" && ").append(command);
        }
        log.info("=======> command:{}", result.toString());

        return result.toString();
    }

    /**
     * 生成备份节点的主机名列表
     *
     * @param backupNodes 备份节点列表，包括每个节点的实例信息
     * @return 返回一个包含所有备份节点主机名的List，如果没有备份节点则返回空List
     */
    private List<String> generateBackupHostnames(List<Instance> backupNodes) {

        if (CollectionUtils.isEmpty(backupNodes)) {
            return Collections.emptyList();
        }

        return backupNodes.stream().map(Instance::getHostName).collect(Collectors.toList());
    }

    /**
     * 生成共享存储列表，包括 CFS 类型的存储。
     *
     * @param clusterId 集群 ID，用于查询 CFS 存储信息
     * @return 返回一个包含 SharedStorage 对象的列表，如果没有任何 CFS 存储则返回空列表
     */
    private List<SharedStorage> generateSharedStorageList(String clusterId) {

        List<Cfs> cfsList = cfsService.findByClusterId(clusterId);
        if (CollectionUtils.isEmpty(cfsList)) {
            return Collections.emptyList();
        }
        List<SharedStorage> sharedStorageList = new ArrayList<>();
        for (Cfs cfs : cfsList) {
            SharedStorage storage = new SharedStorage();
            storage.setStorageProtocol(cfs.getStorageProtocol());
            storage.setMountTarget(cfs.getMountTarget());
            storage.setMountDir(cfs.getMountDir());
            String mountOption = cfs.getMountOption();
            // 将 mountOption 中的空格转为^
            if (mountOption != null) {
                mountOption = mountOption.replaceAll(" ", "^");
            }
            storage.setMountOption(mountOption);
            sharedStorageList.add(storage);
        }

        return sharedStorageList;
    }

    /**
     * 生成队列列表。
     *
     * @param clusterId    集群ID，不能为空
     * @param computeNodes 计算节点列表，可以为空
     * @return 返回一个包含所有队列的List<com.baidu.bce.logic.chpc.scheduler.model.Queue>类型的列表，如果计算节点列表为空则返回默认队列
     * 如果没有找到对应的队列则跳过该队列
     */
    private List<com.baidu.bce.logic.chpc.scheduler.model.Queue> generateQueueList(
            String clusterId, List<Instance> computeNodes) {
        List<com.baidu.bce.logic.chpc.scheduler.model.Queue> queueList = new ArrayList<>();

        if (CollectionUtils.isEmpty(computeNodes)) {
            Queue queue = queueDAOGateway.getClusterDefaultQueue(clusterId);
            com.baidu.bce.logic.chpc.scheduler.model.Queue defaultQueue =
                    new com.baidu.bce.logic.chpc.scheduler.model.Queue();
            defaultQueue.setDefault(true);
            defaultQueue.setName(queue.getName());
            // 查询并增加自动伸缩信息
            AutoScaling autoScaling = autoScalingDAOGateway.getByQueueId(queue.getQueueId(), null);
            if (autoScaling != null && autoScaling.getStatus() == AutoScalingStatus.NORMAL) {
                defaultQueue.setMaxNodes(autoScaling.getMaxNodesInQueue());
                defaultQueue.setMinNodes(autoScaling.getMinNodesInQueue());
                defaultQueue.setMaxScalePerCycle(autoScaling.getMaxScalePerCycle());
                defaultQueue.setEnableAutoGrow(autoScaling.getEnableAutoGrow());
                defaultQueue.setEnableAutoShrink(autoScaling.getEnableAutoShrink());
                defaultQueue.setSpec(autoScaling.getSpec());
            }
            queueList.add(defaultQueue);
            return queueList;
        }

        Map<String, Queue> groupsById =
                queueDAOGateway.listByClusterId(clusterId).stream().collect(Collectors.toMap(Queue::getQueueId, g -> g));

        Map<String, List<Instance>> computeNodesByGroupId =
                computeNodes.stream().collect(Collectors.groupingBy(Instance::getQueueId));

        for (Map.Entry<String, Queue> groupInfo : groupsById.entrySet()) {

            String groupId = groupInfo.getKey();
            Queue queueInfo = groupInfo.getValue();

            com.baidu.bce.logic.chpc.scheduler.model.Queue queue = new com.baidu.bce.logic.chpc.scheduler.model.Queue();
            queue.setDefault(queueInfo.getIsDefault());
            queue.setName(queueInfo.getName());

            // 查询并增加自动伸缩信息
            AutoScaling autoScaling = autoScalingDAOGateway.getByQueueId(queueInfo.getQueueId(), null);
            if (autoScaling != null && autoScaling.getStatus() == AutoScalingStatus.NORMAL) {
                queue.setMaxNodes(autoScaling.getMaxNodesInQueue());
                queue.setMinNodes(autoScaling.getMinNodesInQueue());
                queue.setMaxScalePerCycle(autoScaling.getMaxScalePerCycle());
                queue.setEnableAutoGrow(autoScaling.getEnableAutoGrow());
                queue.setEnableAutoShrink(autoScaling.getEnableAutoShrink());
                queue.setSpec(autoScaling.getSpec());
            }

            List<Instance> nodes = computeNodesByGroupId.get(groupId);
            if (CollectionUtils.isNotEmpty(nodes)) {
                queue.setNode(nodes.stream().map(this::convertToNode).collect(Collectors.toList()));
            }

            queueList.add(queue);
        }

        return queueList;
    }

    private Node convertToNode(Instance instance) {

        Node node = new Node();
        node.setIp(instance.getPrivateIp());
        node.setSpec(instance.getSpec());
        node.setId(instance.getInstanceId());
        node.setHostname(instance.getHostName());
        return node;
    }

    private void updateEventStatusIfProgressing(
            String clusterId, String eventType, ClusterEventDAOGateway clusterEventDAOGateway) {
        Event event = clusterEventDAOGateway.findByClusterIdAndEvent(clusterId, eventType);
        if (event != null && EventType.PROGRESSING.equals(event.getStatus())) {
            clusterEventDAOGateway.update(clusterId, eventType, EventType.SUCCEED, "");
        }
    }
}
