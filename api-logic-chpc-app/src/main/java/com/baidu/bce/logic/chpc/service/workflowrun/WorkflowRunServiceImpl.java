package com.baidu.bce.logic.chpc.service.workflowrun;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.WorkflowRunStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.CromwellFileType;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.common.WorkspaceStatus;
import com.baidu.bce.logic.chpc.common.GlobalUuidUtil;
import com.baidu.bce.logic.chpc.cromwell.DepFile;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowRunRequest;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.WorkflowDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkflowRunDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkflowfileDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkspaceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.CreateWorkflowRunRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunResResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunStatusResponse;
import com.baidu.bce.logic.chpc.model.Workflow;
import com.baidu.bce.logic.chpc.model.WorkflowRun;
import com.baidu.bce.logic.chpc.model.Workflowfile;
import com.baidu.bce.logic.chpc.model.Workspace;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunDeleteResponse;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunGetResponse;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunResponse;
import com.baidu.bce.logic.chpc.model.response.workflowrun.WorkflowRunVO;
import com.baidu.bce.logic.chpc.model.response.workspace.NameisExistResponse;
import com.baidu.bce.logic.chpc.service.IWorkflowRunService;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.UUID;
import java.util.HashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class WorkflowRunServiceImpl implements IWorkflowRunService {

    @Resource
    WorkflowDAOGateway workflowDAOGateway;

    @Autowired
    GlobalUuidUtil globalUuidUtil;

    @Resource
    WorkflowfileDAOGateway workflowfileDAOGateway;

    @Resource
    TaskService taskService;

    @Resource
    WorkflowRunDAOGateway workflowRunDAOGateway;

    @Resource
    BackendGateway backendGateway;

    @Resource
    private WorkspaceDAOGateway workspaceDAOGateway;

    @Override
    public WorkflowRunResponse workflowRun(WorkflowRunRequest workflowRunRequest) {

        WorkflowRunResponse workflowRunResponse = new WorkflowRunResponse();

        Pattern pattern = Pattern.compile("[^\\u0000-\\uFFFF\\u4E00-\\u9FFF]+");
        Matcher matcher = pattern.matcher(workflowRunRequest.getInputs());

        if (matcher.find()) {
            StringBuilder invalidCharacters = new StringBuilder();
            do {
                invalidCharacters.append(matcher.group());
            } while (matcher.find());
            workflowRunResponse.setMessage("输入存在非法字符: " + invalidCharacters.toString());
            return workflowRunResponse;
        }

        String accountId = LogicUserService.getAccountId();
        Workflow workflow;
        if (workflowRunRequest.getVersion() == null) {
            workflow = workflowDAOGateway.getWorkflow(workflowRunRequest.getWorkflowId(), accountId);
        } else {
            workflow = workflowDAOGateway.getWorkflowByVersion(workflowRunRequest.getWorkflowId(), accountId, workflowRunRequest.getVersion());
        }
        if (null == workflow) {
            throw new CommonExceptions.RequestInvalidException("workflow not exist!");
        }
        // 调用cluster-api的执行接口
        CreateWorkflowRunRequest request = new CreateWorkflowRunRequest();
        if (workflowRunRequest.getInputs() != null) {
            request.setInputs(workflowRunRequest.getInputs());
        }

        List<Workflowfile> workflowfileList = workflowfileDAOGateway.getWorkflowfileByVersion(workflowRunRequest.getWorkflowId(), workflow.getVersion());

        List<DepFile> depFiles = new ArrayList<>();
        for (Workflowfile workflowfile : workflowfileList) {
            if (CromwellFileType.MAIN_FILE.nameLowerCase().equals(workflowfile.getType())) {
                request.setMainFilePath(workflowfile.getPath());
                request.setMainFileContent(workflowfile.getContent());
            } else if (CromwellFileType.CONFIG_FILE.nameLowerCase().equals(workflowfile.getType())) {
                request.setConfigFilePath(workflowfile.getPath());
                request.setConfigFileContent(workflowfile.getContent());
            } else {
                DepFile depFile = new DepFile();
                depFile.setDepFilePath(workflowfile.getPath());
                depFile.setDepFileContent(workflowfile.getContent());
                depFiles.add(depFile);
            }
        }
        if (depFiles.size() > 0) {
            request.setDepFiles(depFiles);
        }
        if (workflowRunRequest.getCallCaching() != null) {
            request.setCallCaching(workflowRunRequest.getCallCaching());
        }
        if (workflowRunRequest.getFailureMode() != null) {
            request.setFailureMode(workflowRunRequest.getFailureMode());
        }
        request.setWorkflowId(workflowRunRequest.getWorkflowId());
        Workspace workspace = workspaceDAOGateway.getWorkspace(workflow.getWorkspaceId(), accountId);

        if (null == workspace) {
            throw new CommonExceptions.RequestInvalidException("workspace not exist!");
        }

        if (!WorkspaceStatus.INSTALLED.nameLowerCase().equals(workspace.getStatus())) {
            throw new CommonExceptions.RequestInvalidException("the workspace: " + workspace.getWorkspaceId() + " is still being installed");
        }
        WorkflowRunStatusResponse workflowRunStatusResponse = null;

        try {
            workflowRunStatusResponse = backendGateway.createWorkflowRun(workspace.getClusterId(), request);
        }catch (Exception e) {
            workflowRunResponse.setMessage("工作流执行失败，请检查集群: " + workspace.getClusterName() + " 的状态");
            return workflowRunResponse;
        }
        if (workflowRunStatusResponse == null) {
            throw new CommonExceptions.RequestInvalidException("run workflow failed!");
        }
        String runId = globalUuidUtil.genWorkflowRunId();
        // 将返回执行信息写入db
        WorkflowRun workflowRun = new WorkflowRun();
        workflowRun.setRunUuid(workflowRunStatusResponse.getId());
        workflowRun.setRunId(runId);
        workflowRun.setStatus(workflowRunStatusResponse.getStatus());
        workflowRun.setName(workflowRunRequest.getName());
        workflowRun.setCallCaching(workflowRunRequest.getCallCaching());
        workflowRun.setFailureMode(workflowRunRequest.getFailureMode());
        workflowRun.setInputs(workflowRunRequest.getInputs());
        workflowRun.setWorkflowId(workflowRunRequest.getWorkflowId());
        workflowRun.setWorkspaceId(workspace.getWorkspaceId());
        workflowRun.setVersion(workflow.getVersion());
        workflowRun.setWorkflowName(workflow.getName());
        workflowRun.setAccountId(accountId);

        workflowRunDAOGateway.createWorkflowRun(workflowRun);

        // 新建task，轮训查询工作流执行结果

        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put(ChpcConstant.WORKFLOW_RUN_UUID, workflowRunStatusResponse.getId());
        extraMap.put(ChpcConstant.WORKFLOW_RUNID, runId);

        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.RUN_WORKFLOW_TASK,
                workspace.getClusterId(), "",
                TaskSourceType.RUN_WORKFLOW_TASK.getTaskSourceType(),
                taskId, extraMap, null);

        workflowRunResponse.setRunId(runId);
        workflowRunResponse.setStatus(workflowRunStatusResponse.getStatus());
        return workflowRunResponse;
    }

    @Override
    public List<WorkflowRunVO> listWorkflowRun(String workspaceId, String workflowId, String name, String status) {
        String accountId = LogicUserService.getAccountId();
        // 直接查询db
        List<WorkflowRun> workflowRunList = workflowRunDAOGateway.getWorkflowRunById(workspaceId, workflowId, name, status, accountId);
        List<WorkflowRunVO> workflowRunVOList = new ArrayList<>();
        for (WorkflowRun workflowRun : workflowRunList) {
            WorkflowRunVO workflowRunVO = new WorkflowRunVO();
            workflowRunVO.setName(workflowRun.getName());
            workflowRunVO.setInputs(workflowRun.getInputs());
            workflowRunVO.setOutputs(workflowRun.getOutputs());
            workflowRunVO.setStatus(workflowRun.getStatus());
            workflowRunVO.setWorkflowId(workflowRun.getWorkflowId());
            workflowRunVO.setWorkflowName(workflowRun.getWorkflowName());
            workflowRunVO.setRunId(workflowRun.getRunId());
            workflowRunVO.setVersion(workflowRun.getVersion());
            workflowRunVO.setUpdatedTime(workflowRun.getUpdatedTime());
            workflowRunVO.setCreatedTime(workflowRun.getCreatedTime());
            workflowRunVOList.add(workflowRunVO);
        }
        return workflowRunVOList;
    }

    @Override
    public WorkflowRunGetResponse getWorkflowRun(String runId) {

        String accountId = LogicUserService.getAccountId();
        WorkflowRunGetResponse workflowRunGetResponse = new WorkflowRunGetResponse();
        // 先查询db，如果工作流状态已完成，直接返回
        WorkflowRun workflowRun = workflowRunDAOGateway.getWorkflowRun(runId, accountId);
        if (workflowRun == null) {
            throw new CommonExceptions.RequestInvalidException("workflow runId not found!");
        }
        if (WorkflowRunStatus.Succeeded.name().equals(workflowRun.getStatus()) || WorkflowRunStatus.Failed.name().equals(workflowRun.getStatus())
                || WorkflowRunStatus.Aborted.name().equals(workflowRun.getStatus())) {
            workflowRunGetResponse.setName(workflowRun.getName());
            workflowRunGetResponse.setInputs(workflowRun.getInputs());
            workflowRunGetResponse.setOutputs(workflowRun.getOutputs());
            workflowRunGetResponse.setStatus(workflowRun.getStatus());
            workflowRunGetResponse.setUpdatedTime(workflowRun.getUpdatedTime());
            workflowRunGetResponse.setCreatedTime(workflowRun.getCreatedTime());
            workflowRunGetResponse.setRunId(workflowRun.getRunId());
            workflowRunGetResponse.setVersion(workflowRun.getVersion());
            workflowRunGetResponse.setWorkflowId(workflowRun.getWorkflowId());
            workflowRunGetResponse.setWorkflowName(workflowRun.getWorkflowName());
            workflowRunGetResponse.setCallCaching(workflowRun.getCallCaching());
            workflowRunGetResponse.setFailureMode(workflowRun.getFailureMode());
            return workflowRunGetResponse;
        }

        Workflow workflow = workflowDAOGateway.getWorkflow(workflowRun.getWorkflowId(), accountId);

        Workspace workspace = workspaceDAOGateway.getWorkspace(workflow.getWorkspaceId(), accountId);


        // 如果工作流状态未完成，再调用一次查询接口
        WorkflowRunStatusResponse workflowRunStatus = backendGateway.getWorkflowRunStatus(workspace.getClusterId(), workflowRun.getRunUuid());


        WorkflowRunResResponse workflowRunResResponse = backendGateway.getWorkflowRunRes(workspace.getClusterId(), workflowRun.getRunUuid());

        if (workflowRunStatus != null && workflowRunResResponse != null && !workflowRunStatus.getStatus().equals(workflowRun.getStatus())) {
            // 更新db
            String regex = "[^\\x00-\\x7F]+";
            String outputs = JacksonUtil.toJson(workflowRunResResponse.getOutputs()).replaceAll(regex, "");
            workflowRunDAOGateway.updateWorkflowRunStatus(runId, workflowRunStatus.getStatus(), outputs);
        }

        workflowRunGetResponse.setName(workflowRun.getName());
        workflowRunGetResponse.setInputs(workflowRun.getInputs());
        if (workflowRunResResponse != null) {
            workflowRunGetResponse.setOutputs(JacksonUtil.toJson(workflowRunResResponse.getOutputs()));
        }
        if (workflowRunStatus != null) {
            workflowRunGetResponse.setStatus(workflowRunStatus.getStatus());
        }else {
            workflowRunGetResponse.setStatus(workflowRun.getStatus());
        }
        workflowRunGetResponse.setVersion(workflowRun.getVersion());
        workflowRunGetResponse.setUpdatedTime(workflowRun.getUpdatedTime());
        workflowRunGetResponse.setCreatedTime(workflowRun.getCreatedTime());
        workflowRunGetResponse.setRunId(workflowRun.getRunId());
        workflowRunGetResponse.setWorkflowName(workflowRun.getWorkflowName());
        workflowRunGetResponse.setCallCaching(workflowRun.getCallCaching());
        workflowRunGetResponse.setFailureMode(workflowRun.getFailureMode());

        return workflowRunGetResponse;
    }

    @Override
    public WorkflowRunDeleteResponse deleteWorkflowRun(String runId) {
        String accountId = LogicUserService.getAccountId();
        WorkflowRun workflowRun = workflowRunDAOGateway.getWorkflowRun(runId, accountId);
        if (workflowRun == null) {
            throw new CommonExceptions.RequestInvalidException("workflow runId not found!");
        }
        // 工作流run非终态
        if (!(WorkflowRunStatus.Succeeded.name().equals(workflowRun.getStatus()) ||
                WorkflowRunStatus.Failed.name().equals(workflowRun.getStatus()) ||
                WorkflowRunStatus.Aborted.name().equals(workflowRun.getStatus()))) {
            throw new CommonExceptions.RequestInvalidException("The workflow run status: " + workflowRun.getStatus() + " , is not final so cannot be deleted");
        }
        workflowRunDAOGateway.deleteWorkflowRun(runId, accountId);
        WorkflowRunDeleteResponse workflowRunDeleteResponse = new WorkflowRunDeleteResponse();
        workflowRunDeleteResponse.setRunId(runId);
        workflowRunDeleteResponse.setStatus(workflowRun.getStatus());
        workflowRunDeleteResponse.setWorkflowId(workflowRun.getWorkflowId());
        return workflowRunDeleteResponse;
    }

    @Override
    public NameisExistResponse workflowRunNameisExist(String workflowId, String workflowRunName) {
        String accountId = LogicUserService.getAccountId();
        NameisExistResponse nameisExistResponse = new NameisExistResponse();
        List<WorkflowRun> getWorkflowRunList = workflowRunDAOGateway.getWorkflowRunByName(workflowId, workflowRunName, accountId);
        if (getWorkflowRunList != null && getWorkflowRunList.size() > 0 ) {
            nameisExistResponse.setIsExist(true);
        } else {
            nameisExistResponse.setIsExist(false);
        }
        return nameisExistResponse;
    }

    @Override
    public WorkflowRunResponse abortWorkflow(String runId) {
        String accountId = LogicUserService.getAccountId();
        WorkflowRun workflowRun = workflowRunDAOGateway.getWorkflowRun(runId, accountId);
        if (workflowRun == null) {
            throw new CommonExceptions.RequestInvalidException("workflow runId not found!");
        }

        if (!WorkflowRunStatus.Running.name().equals(workflowRun.getStatus())) {
            throw new CommonExceptions.RequestInvalidException("workflow run status is not Running, so can not abort!");
        }
        Workflow workflow = workflowDAOGateway.getWorkflow(workflowRun.getWorkflowId(), accountId);
        if (workflow == null) {
            throw new CommonExceptions.RequestInvalidException("workflow not found!");
        }

        Workspace workspace = workspaceDAOGateway.getWorkspace(workflow.getWorkspaceId(), accountId);
        if (workspace == null) {
            throw new CommonExceptions.RequestInvalidException("workspace not found!");
        }
        WorkflowRunStatusResponse workflowRunStatusResponse = backendGateway.abortWorkflow(workspace.getClusterId(), workflowRun.getRunUuid());

        workflowRunDAOGateway.updateWorkflowRunStatus(runId, workflowRunStatusResponse.getStatus(), "");
        // 新建task，轮训查询工作流执行结果

        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put(ChpcConstant.WORKFLOW_RUN_UUID, workflowRunStatusResponse.getId());
        extraMap.put(ChpcConstant.WORKFLOW_RUNID, runId);

        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.RUN_WORKFLOW_TASK,
                workspace.getClusterId(), "",
                TaskSourceType.RUN_WORKFLOW_TASK.getTaskSourceType(),
                taskId, extraMap, null);
        WorkflowRunResponse workflowRunResponse = new WorkflowRunResponse();
        workflowRunResponse.setRunId(runId);
        workflowRunResponse.setStatus(workflowRunStatusResponse.getStatus());

        return workflowRunResponse;
    }
}
