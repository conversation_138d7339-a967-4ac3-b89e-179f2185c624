package com.baidu.bce.logic.chpc.service.storage;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.baidu.bce.externalsdk.logical.network.subnet.model.SubnetVo;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.logic.chpc.cfs.FileSystem;
import com.baidu.bce.logic.chpc.cfs.MountTarget;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.chpc.cluster.domainservice.ClusterServiceV2;
import com.baidu.bce.logic.chpc.cluster.model.MountInfo;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException.CfsServiceException;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException.RequestInvalidException;
import com.baidu.bce.logic.chpc.domainservice.CfsService;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.model.Cfs;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.response.cfs.CfsVO;
import com.baidu.bce.logic.chpc.permission.PermissionException.ResourceNotExistException;
import com.baidu.bce.logic.chpc.service.IStorageService;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.chpc.storage.model.BatchUpdateStorageRequest;
import com.baidu.bce.logic.chpc.storage.model.BatchUpdateStorageResponse;
import com.baidu.bce.logic.chpc.storage.model.StorageInfo;
import com.baidu.bce.logic.chpc.storage.model.StorageQueryResponse;
import com.baidu.bce.logic.chpc.storage.model.StorageResult;
import com.baidu.bce.logic.chpc.storage.model.UpdateStorageRequest;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.core.exception.CommonExceptions;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class StorageServiceImpl implements IStorageService {

    @Resource
    CfsService cfsService;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    SubnetGateway subnetGateway;

    @Autowired
    CfsGateway cfsGateway;

    /**
     * {@inheritDoc}
     * 根据集群ID获取存储列表，并返回一个包含所有存储信息的StorageQueryResponse对象。
     * 如果集群不存在或者集群类型为HYBRID，则不能修改存储配置。
     * 如果集群类型为HYBRID，则不能修改存储配置。
     * 如果集群类型为其他类型，则可以修改存储配置。
     *
     * @param clusterId 集群ID，不能为空
     * @return StorageQueryResponse 包含所有存储信息的StorageQueryResponse对象
     * @throws RequestInvalidException 当集群不存在时抛出此异常
     */
    @Override
    public StorageQueryResponse getStorageList(String clusterId) {
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        if (cluster == null) {
            log.error("cluster is not found, id: {}", clusterId);
            throw new RequestInvalidException("cluster is not found, id: " + clusterId);
        }

        List<Cfs> cfsList = cfsService.findByClusterId(clusterId);
        List<StorageInfo> storages = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cfsList)) {
            List<CfsVO> cfsVOList = cfsList.stream()
                    .map(ServiceUtil::convertToCfsVO)
                    .collect(Collectors.toList());
            for (CfsVO cfs : cfsVOList) {
                String cfsId = cfs.getCfsId();
                String fileSystemName = "";
                if (Strings.isNotEmpty(cfsId)) {
                    List<FileSystem> fileSystem;
                    try {
                        fileSystem = cfsGateway.getCfsFileSystem(cfs.getCfsId());
                        // 没有挂载点，报错
                        if (CollectionUtils.isEmpty(fileSystem)) {
                            log.error("The cfsId " + cfsId + " does not have file system");
                        } else if (fileSystem.size() > 0) {
                            fileSystemName = fileSystem.get(0).getFsName();
                        }
                    } catch (WebClientResponseException e) {
                        log.error("get cfs failed, cfsId={}, errorMsg={}", cfsId, e.getResponseBodyAsString());
                    } catch (Exception e) {
                        log.error("get cfs failed, cfsId={}, errorMsg={}", cfsId, e.getMessage());
                    }
                }
                // 如果 cfs.getMountTarget() 以 cfs.bj.baidubce.com 结尾，增加三个固定的挂载目录
                if (cfs.getMountTarget().endsWith("baidubce.com")
                        || cfs.getMountTarget().endsWith("bce.sandbox.baidu.com")) {
                    StorageInfo cfsData = new StorageInfo();
                    cfsData.setCfsId(cfs.getCfsId());
                    cfsData.setCfsName(fileSystemName);
                    cfsData.setRemoteAddr(cfs.getMountTarget());
                    cfsData.setRemoteDirectory("/");
                    cfsData.setLocalDirectory("/chpcdata");
                    cfsData.setStorageProtocal(cfs.getStorageProtocol());
                    cfsData.setCanBeModified(false);
                    storages.add(cfsData);

                    StorageInfo cfsHome = new StorageInfo();
                    cfsHome.setCfsId(cfs.getCfsId());
                    cfsHome.setCfsName(fileSystemName);
                    cfsHome.setRemoteAddr(cfs.getMountTarget());
                    cfsHome.setRemoteDirectory("/chpc/" + clusterId + "/home");
                    cfsHome.setLocalDirectory(cfs.getMountDir());
                    cfsHome.setStorageProtocal(cfs.getStorageProtocol());
                    cfsHome.setCanBeModified(false);
                    storages.add(cfsHome);

                    StorageInfo cfsSoftware = new StorageInfo();
                    cfsSoftware.setCfsId(cfs.getCfsId());
                    cfsSoftware.setCfsName(fileSystemName);
                    cfsSoftware.setRemoteAddr(cfs.getMountTarget());
                    cfsSoftware.setRemoteDirectory("/chpc/" + clusterId + "/home/<USER>");
                    cfsSoftware.setLocalDirectory(cluster.getSoftwareDir());
                    cfsSoftware.setStorageProtocal(cfs.getStorageProtocol());
                    cfsSoftware.setCanBeModified(false);
                    storages.add(cfsSoftware);
                } else {
                    // 将 mountTarget 以：进行拆分
                    String[] mountTarget = cfs.getMountTarget().split(":");
                    StorageInfo cfsData = new StorageInfo();
                    cfsData.setCfsId(cfs.getCfsId());
                    cfsData.setCfsName(fileSystemName);
                    cfsData.setRemoteAddr(mountTarget[0]);
                    cfsData.setRemoteDirectory(mountTarget[1]);
                    cfsData.setLocalDirectory(cfs.getMountDir());
                    cfsData.setStorageProtocal(cfs.getStorageProtocol());
                    if (ClusterType.HYBRID.nameLowerCase().equalsIgnoreCase(cluster.getClusterType())) {
                        cfsData.setCanBeModified(false);
                    } else {
                        cfsData.setCanBeModified(true);
                    }
                    storages.add(cfsData);
                }
            }
        }
        StorageQueryResponse response = new StorageQueryResponse();
        response.setStorages(storages);
        return response;
    }

    /**
     * {@inheritDoc}
     * 更新存储信息，包括挂载和卸载操作。如果集群类型为混合云，则不支持挂载和卸载操作。
     * 挂载操作：将指定的 cfs 挂载到本地目录下；卸载操作：将指定的 cfs 从本地目录中卸载。
     * 如果存储信息为空，则抛出 RequestInvalidException 异常。
     * 如果集群不存在或者不属于当前账户，则抛出 RequestInvalidException 异常。
     * 如果挂载点不存在，则抛出 RequestInvalidException 异常。
     * 如果挂载点已经存在，但是操作为卸载，则抛出 RequestInvalidException 异常。
     * 如果操作为卸载，但是挂载点不存在，则抛出 RequestInvalidException 异常。
     * 如果操作为卸载，但是挂载点不属于当前集群，则抛出 RequestInvalidException 异常。
     *
     * @param clusterId 集群 ID
     * @param request   更新存储请求，包含存储信息
     * @param action    操作类型，可选值为 "create" 表示挂载，"delete" 表示卸载
     * @return UpdateStorageResponse 更新存储响应，包含存储结果
     * @throws RequestInvalidException 请求无效异常，包含错误消息
     */
    @Override
    public void updateStorage(String clusterId, UpdateStorageRequest request, String action) {
        // 判断集群是否为空
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        if (cluster == null) {
            log.error("cluster is not found, id: {}", clusterId);
            throw new RequestInvalidException("cluster is not found, id: " + clusterId);
        }
        // 混合云集群不支持挂载、卸载操作
        if (ClusterType.HYBRID.nameLowerCase().equalsIgnoreCase(cluster.getClusterType())) {
            throw new RequestInvalidException("Hybrid cluster does not support storage operation");
        }
        List<Cfs> cfsList = cfsService.findByClusterId(clusterId);
        String defaultCfsMountPoint = "";
        String defaultCfsMountDir = "";
        List<CfsVO> cfsVOList = cfsList.stream()
                .map(ServiceUtil::convertToCfsVO)
                .collect(Collectors.toList());
        for (CfsVO cfsVO : cfsVOList) {
            if (cfsVO.getMountTarget().endsWith("baidubce.com")) {
                defaultCfsMountPoint = cfsVO.getMountTarget();
                defaultCfsMountDir = cfsVO.getMountDir();
            }
        }

        StorageInfo storageInfo = request.getStorageInfo();
        if (storageInfo == null) {
            log.error("The storage info is empty");
            throw new RequestInvalidException("The storage info is empty");
        }

        // 1. 检查 cfs 挂载点是否存在（参数校验）
        checkMountInfo(storageInfo, cluster, defaultCfsMountPoint, defaultCfsMountDir);

        String cfsId = storageInfo.getCfsId();
        String mountPoint = storageInfo.getRemoteAddr();
        String mountDir = storageInfo.getRemoteDirectory();
        String localDir = storageInfo.getLocalDirectory();

        // 2. 检查是否已经挂载
        String targetMountTarget = mountPoint + ":" + mountDir;
        String targetMountDir = localDir;

        Boolean isMounted = false;
        for (CfsVO cfs : cfsVOList) {
            if (targetMountTarget.equals(cfs.getMountTarget()) && targetMountDir.equals(cfs.getMountDir())) {
                isMounted = true;
            }
        }

        // 3. 挂载or卸载
        if ("create".equalsIgnoreCase(action)) {
            if (isMounted) {
                // 已经存在，保持幂等性
                log.info("The cfs is already mounted, clusterId: {}, cfsId: {}, {}, {}",
                        clusterId, cfsId, targetMountTarget, targetMountDir);
                throw new CommonExceptions.RequestInvalidException("The cfs mount dir is already mounted");
            }
            // 创建挂载点
            MountInfo mountInfo = new MountInfo();
            mountInfo.setCfsId(cfsId);
            mountInfo.setMountPoint(targetMountTarget);
            mountInfo.setMountDirectory(targetMountDir);
            Cfs cfsInfo = cfsService.genCfs(mountInfo, clusterId);
            cfsService.insert(cfsInfo);

        } else if ("delete".equalsIgnoreCase(action)) {
            if (!isMounted) {
                // 已经删除，保持幂等性
                log.info("The cfs is not mounted, clusterId: {}, cfsId: {}, {}, {}",
                        clusterId, cfsId, targetMountTarget, targetMountDir);
                throw new CommonExceptions.RequestInvalidException(
                        "The cfs mount dir is not mounted or not exist");
            }
            cfsService.deleteByMountInfo(clusterId, cfsId, targetMountTarget, targetMountDir);
        } else {
            throw new CommonExceptions.RequestInvalidException(
                    "The operation " + action + " is not supported.");
        }
    }

    /**
     * {@inheritDoc}
     * 更新存储信息。包括添加或删除存储。
     * 先检查 cfs 挂载点是否存在，再检查是否已经挂载，最后根据操作类型（add、delete）进行挂载或卸载。
     * 如果操作类型为 add，则会检查挂载路径是否合法（不能覆盖系统目录）。
     * 如果操作类型为 delete，则会直接删除对应的挂载点。
     * 如果操作类型不支持，则抛出 RequestInvalidException。
     *
     * @param clusterId 集群ID
     * @param request   更新请求，包含多个存储操作（add、delete）和存储信息
     * @return UpdateStorageResponse 更新响应，包含集群ID
     * @throws RequestInvalidException   请求参数错误
     * @throws ResourceNotExistException 资源不存在
     * @throws RequestInvalidException   请求无效
     * @throws CfsServiceException       CFS服务异常
     */
    @Override
    public BatchUpdateStorageResponse batchUpdateStorage(String clusterId, BatchUpdateStorageRequest request,
            String action) {
        // 判断集群是否为空
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        if (cluster == null) {
            log.error("cluster is not found, id: {}", clusterId);
            throw new RequestInvalidException("cluster is not found, id: " + clusterId);
        }
        // 混合云集群不支持挂载、卸载操作
        if (ClusterType.HYBRID.nameLowerCase().equalsIgnoreCase(cluster.getClusterType())) {
            throw new RequestInvalidException("Hybrid cluster does not support storage operation");
        }
        List<Cfs> cfsList = cfsService.findByClusterId(clusterId);
        String defaultCfsMountPoint = "";
        String defaultCfsMountDir = "";
        List<CfsVO> cfsVOList = cfsList.stream()
                .map(ServiceUtil::convertToCfsVO)
                .collect(Collectors.toList());
        for (CfsVO cfsVO : cfsVOList) {
            if (cfsVO.getMountTarget().endsWith("baidubce.com")) {
                defaultCfsMountPoint = cfsVO.getMountTarget();
                defaultCfsMountDir = cfsVO.getMountDir();
            }
        }

        for (StorageInfo storageInfo : request.getStorageInfo()) {
            // 1. 检查 cfs 挂载点是否存在（参数校验）
            checkMountInfo(storageInfo, cluster, defaultCfsMountPoint, defaultCfsMountDir);
        }

        BatchUpdateStorageResponse response = new BatchUpdateStorageResponse();
        response.setStorageResults(new ArrayList<>());
        for (StorageInfo storageInfo : request.getStorageInfo()) {
            StorageResult opResult = new StorageResult();
            opResult.setStorageInfo(storageInfo);
            opResult.setSuccess(true);

            String cfsId = storageInfo.getCfsId();
            String mountPoint = storageInfo.getRemoteAddr();
            String mountDir = storageInfo.getRemoteDirectory();
            String localDir = storageInfo.getLocalDirectory();

            // 2. 检查是否已经挂载
            String targetMountTarget = mountPoint + ":" + mountDir;
            String targetMountDir = localDir;

            Boolean isMounted = false;
            for (CfsVO cfs : cfsVOList) {
                if (targetMountTarget.equals(cfs.getMountTarget()) && targetMountDir.equals(cfs.getMountDir())) {
                    isMounted = true;
                }
            }
            try {
                // 3. 挂载or卸载
                if ("batchCreate".equalsIgnoreCase(action)) {
                    if (isMounted) {
                        // 已经存在，保持幂等性
                        log.info("The cfs is already mounted, clusterId: {}, cfsId: {}, {}, {}",
                                clusterId, cfsId, targetMountTarget, targetMountDir);
                        throw new CommonExceptions.RequestInvalidException("The cfs mount dir is already mounted");
                    }
                    // 创建挂载点
                    MountInfo mountInfo = new MountInfo();
                    mountInfo.setCfsId(cfsId);
                    mountInfo.setMountPoint(targetMountTarget);
                    mountInfo.setMountDirectory(targetMountDir);
                    Cfs cfsInfo = cfsService.genCfs(mountInfo, clusterId);
                    cfsService.insert(cfsInfo);

                } else if ("batchDelete".equalsIgnoreCase(action)) {
                    if (!isMounted) {
                        // 已经删除，保持幂等性
                        log.info("The cfs is not mounted, clusterId: {}, cfsId: {}, {}, {}",
                                clusterId, cfsId, targetMountTarget, targetMountDir);
                        throw new CommonExceptions.RequestInvalidException(
                                "The cfs mount dir is not mounted or not exist");
                    }
                    cfsService.deleteByMountInfo(clusterId, cfsId, targetMountTarget, targetMountDir);
                } else {
                    throw new CommonExceptions.RequestInvalidException(
                            "The operation " + action + " is not supported.");
                }
            } catch (Exception e) {
                log.error(
                        "Failed to update storage, clusterId: {}, cfsId: {}, mountPoint: {}, mountDir: {}, errMsg: {}",
                        clusterId, cfsId, targetMountTarget, targetMountDir, e.getMessage());
                opResult.setSuccess(false);
                opResult.setErrMsg(e.getMessage());
            } finally {
                response.getStorageResults().add(opResult);
            }
        }

        return response;
    }

    /**
     * @Description: 检查存储信息是否符合要求，包括挂载点、路径等。如果不符合要求则抛出相应的异常。
     * @Param storageInfo StorageInfo类型，包含存储信息，包括 cfsId、挂载点、路径、本地路径等。
     * @Param cluster Cluster类型，包含集群信息，包括集群ID、vpcID等。
     * @Param defaultCfsMountPoint String类型，默认挂载点。
     * @Param defaultCfsMountDir String类型，默认挂载路径。
     * @Return void 无返回值，如果存储信息不符合要求则抛出相应的异常。
     */
    private void checkMountInfo(StorageInfo storageInfo, Cluster cluster, String defaultCfsMountPoint,
            String defaultCfsMountDir) {
        String cfsId = storageInfo.getCfsId();
        String mountPoint = storageInfo.getRemoteAddr();
        String mountDir = storageInfo.getRemoteDirectory();
        String localDir = storageInfo.getLocalDirectory();
        // 参数校验
        if (StringUtils.isEmpty(mountPoint) || StringUtils.isEmpty(mountDir) || StringUtils.isEmpty(localDir)) {
            throw new CommonExceptions.RequestInvalidException("The mountPoint or mountDir or localDir is empty.");
        }
        // 必须是有效路径
        ClusterServiceV2.isValidMountDirectory(mountDir);
        ClusterServiceV2.isValidMountDirectory(localDir);
        // cfsId 校验
        List<MountTarget> cfsMountPoints;
        try {
            cfsMountPoints = cfsGateway.getCfsMountPoint(cfsId);
        } catch (WebClientResponseException e) {
            throw new CommonException.CfsServiceException(e.getResponseBodyAsString());
        } catch (Exception e) {
            throw new CommonException.CfsServiceException(e.getMessage());
        }
        // 没有挂载点，报错
        if (CollectionUtils.isEmpty(cfsMountPoints)) {
            throw new CommonExceptions.RequestInvalidException("The cfsId " + cfsId +
                    " does not have mount point.");
        }
        // 判断挂载路径是否在挂载点中
        MountTarget mountTarget = null;
        for (MountTarget target : cfsMountPoints) {
            if (mountPoint.equals(target.getDomain())) {
                mountTarget = target;
                break;
            }
        }
        if (mountTarget == null) {
            throw new CommonException.ResourceNotExistException(mountPoint + " does not exist.");
        }
        // 判断挂载的 cfs 是否和集群在同一个 vpc 中
        SubnetVo subnet = subnetGateway.getSubnet(mountTarget.getSubnetId());
        if (!subnet.getVpcShortId().equalsIgnoreCase(cluster.getVpcId())) {
            throw new CommonExceptions.RequestInvalidException("The cfsMountPoint " + mountPoint +
                    "subnet vpcId " + subnet.getVpcShortId() + " does not match " + cluster.getVpcId());
        }
        // 是不是默认挂载点
        if (mountPoint.equals(defaultCfsMountPoint)) {
            if (("/".equals(mountDir)) && ("/chpcdata".equals(localDir))) {
                throw new RequestInvalidException("CFS 目录 " + defaultCfsMountPoint + ":" + mountDir
                        + " " + localDir + "是默认目录，不允许操作.");
            }
            if ((mountDir.equals("/chpc/" + cluster.getClusterId() + "/home"))
                    && (localDir.equals(defaultCfsMountDir))) {
                throw new RequestInvalidException("CFS 目录 " + defaultCfsMountPoint + ":" + mountDir
                        + " " + localDir + "是默认目录，不允许操作.");
            }
            if ((mountDir.equals("/chpc/" + cluster.getClusterId() + "/home/<USER>"))
                    && (localDir.equals(cluster.getSoftwareDir()))) {
                throw new RequestInvalidException("CFS 目录 " + defaultCfsMountPoint + ":" + mountDir
                        + " " + localDir + "是默认目录，不允许操作.");
            }
        }

    }

}
