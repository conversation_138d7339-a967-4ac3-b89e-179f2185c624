package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.common.SoftwareStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.SoftwareRecord;
import com.baidu.bce.logic.chpc.model.Task;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 * <p>
 * OOS启动节点调度器失败后，会创建这个任务
 */
@Slf4j
@Component(value = "group_deleted_task")
public class GroupDeletedTask extends AbstractSchedulerTask {

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    TagsDAOGateway tagsDAOGateway;

    @Resource
    SoftwareRecordDAOGateway softwareRecordDAOGateway;

    @Override
    public Map<String, Object> executeTask(final Task task, final Cluster cluster, final Queue queue) {

        // oos执行失败，如果有chpc-app-server的安装记录，删除该记录
        SoftwareRecord softwareByNameAndVersion = softwareRecordDAOGateway
                .findSoftwareByNameAndVersion(cluster.getClusterId(), ChpcConstant.CHPC_APP_SERVER, "1.0.1");
        if (softwareByNameAndVersion != null && SoftwareStatus.INSTALLING.nameLowerCase().equals(softwareByNameAndVersion.getStatus())) {
            SoftwareRecord record = new SoftwareRecord();
            record.setStatus(SoftwareStatus.INSTALLING.nameLowerCase());
            record.setClusterId(cluster.getClusterId());
            record.setName(ChpcConstant.CHPC_APP_SERVER);
            record.setVersion("1.0.1");
            record.setDeleted(true);
            softwareRecordDAOGateway.update(record);

            SoftwareRecord vncRecord = new SoftwareRecord();
            vncRecord.setStatus(SoftwareStatus.INSTALLING.nameLowerCase());
            vncRecord.setClusterId(cluster.getClusterId());
            vncRecord.setName(ChpcConstant.CHPC_VNC_SERVER);
            vncRecord.setVersion("1.8.0");
            vncRecord.setDeleted(true);
            softwareRecordDAOGateway.update(vncRecord);
        }
        // todo 目前每个COS资源栈只能属于一个队列，因此删除队列时，可以删除整个栈
        //  后续通过COS资源栈扩缩容来管理，仅删除（缩容）队列里面的虚机
        Map<String, List<Instance>> groupByCosStackId = instanceService.findBy(queue.getClusterId(), queue.getQueueId())
                .stream()
                .collect(Collectors.groupingBy(Instance::getCosStackId));

        log.debug("[GROUP_DELETED_TASK] the need delete cos stack is: {}", JacksonUtil.toJson(groupByCosStackId));

        cosGateway.deleteStack(task.getCosStackId());
        for (String cosStackId : groupByCosStackId.keySet()) {
            if (StringUtil.isEmpty(cosStackId)) {
                continue;
            }

            if (task.getCosStackId().equals(cosStackId)) {
                groupByCosStackId.get(cosStackId)
                        .forEach(ins -> instanceService.delete(ins.getInstanceId()));
                break;
            }

        }
        // 只有创建集群时，启动集群失败，才删除集群
        if (TaskSourceType.GROUP_STARTED_TASK.name().equalsIgnoreCase(task.getSource())) {
            Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
            // 手动扩容或自动扩容启动失败，对列状态需要更新为正常
            if (extraMap.containsKey(ChpcConstant.MANUAL_SCALING_FLAG)
                    || extraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)) {
                queueDAOGateway.updateStatus(task.getQueueId(), QueueStatus.ACTIVE.nameLowerCase());
            } else {
                // 创建集群时，启动失败，删除集群和对列
                log.debug("ready to delete cluster and queue when create cluster in start phase");
                clusterDAOGateway.deleteWithStatus(cluster.getClusterId(), ClusterStatus.START_FAILED);
                queueDAOGateway.deleteWithStatus(task.getQueueId(), QueueStatus.START_FAILED);
                tagsDAOGateway.deleteByClusterId(cluster.getClusterId(), "queue");
                tagsDAOGateway.deleteByClusterId(cluster.getClusterId(), "localNode");
            }
        }

        // 已经改为先置状态
        // groupService.delete(task.getGroupId());

        return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
    }


}
