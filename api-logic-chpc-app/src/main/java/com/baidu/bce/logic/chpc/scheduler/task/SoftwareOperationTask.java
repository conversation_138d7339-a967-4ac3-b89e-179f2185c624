package com.baidu.bce.logic.chpc.scheduler.task;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.bcc.BccInstance;
import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterOosTaskStatus;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.SoftwareStatus;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.SoftwareRecord;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.oos.CreateOosExecutionRequest;
import com.baidu.bce.logic.chpc.oos.GetOosExecutionResponse;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component(value = "software_operation_task")
public class SoftwareOperationTask extends AbstractSchedulerTask {

    @Value("${software.checkArch:true}")
    private boolean checkSoftwareArch;

    @Value("${bce.web.commons.gray.enabled:false}")
    private boolean grayEnabled;

    @Value("${bce.web.commons.bos.bucket.endpoint}")
    private String bosEndpoint;

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    SoftwareRecordDAOGateway softwareRecordDAOGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    BccGateway bccGateway;

    /**
     * {@inheritDoc}
     * 根据任务类型和资源状态来操作软件。如果软件已经安装或者卸载成功，则返回成功；否则返回处理中。
     * 如果软件安装失败或者卸载失败，则返回失败。
     *
     * @param task    任务对象
     * @param cluster 集群对象
     * @param queue   队列对象
     * @return {@code Map<String, Object>} 包含任务状态和其他信息的Map对象
     *         - key: TASK_STATUS, value: Integer, 任务状态值，参考 TaskStatus 枚举类
     *         - key: SOFTWARE_NAME, value: String, 软件名称
     *         - key: SOFTWARE_VERSION, value: String, 软件版本号
     *         - key: TASK_ID, value: Long, 任务 ID
     *         - key: ACCOUNT_ID, value: Long, 用户 ID
     */
    @Override
    Map<String, Object> executeTask(Task task, Cluster cluster, Queue queue) {
        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
        String softwareName = (String) extraMap.get(ChpcConstant.SOFTWARE_NAME);
        String version = (String) extraMap.get(ChpcConstant.SOFTWARE_VERSION);
        String installedInstacneId = (String) extraMap.get(ChpcConstant.INSTANCE_ID);

        log.debug("begin to operate software clusterId: {}", cluster.getClusterId());
        List<Instance> instances = instanceDAOGateway.findByClusterId(cluster.getClusterId());
        Instance master = null;
        for (Instance instance : instances) {
            if (InstanceNodeType.MASTER.getType().equals(instance.getNodeType())) {
                master = instance;
                break;
            }
        }
        if (instances.size() == 0 || null == master) {
            String reason = String.format(
                    "[%s] can't find master instance, clusterId: %s", task.getTaskId(), cluster.getClusterId());
            log.error("=====>{}", reason);
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            extraMap.put(ChpcConstant.TASK_FAILED_REASON, reason);
            return extraMap;
        }
        // 查找软件安装、卸载记录
        log.debug("=====> clusterId: {}, softwareName: {} version: {} installedInstacneId: {}", cluster.getClusterId(),
                softwareName, version, installedInstacneId);
        SoftwareRecord record = softwareRecordDAOGateway.findSoftwareByNameAndVersionAndInstanceId(
                cluster.getClusterId(),
                softwareName,
                version,
                installedInstacneId);
        if (record == null) {
            // *没有安装记录，任务直接失败
            String reason = String.format(
                    "[%s] can't find software record, clusterId: %s, %s %s",
                    task.getTaskId(),
                    cluster.getClusterId(),
                    softwareName,
                    version);
            log.error("=====>{}", reason);
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            extraMap.put(ChpcConstant.TASK_FAILED_REASON, reason);

            return extraMap;
        }

        // 如果是CHPC_APP_SERVER，选择登录节点
        if (ChpcConstant.CHPC_APP_SERVER.equals(record.getName())
                || ChpcConstant.CHPC_VNC_SERVER.equals(record.getName())) {
            master = null;
            for (Instance instance : instances) {
                if (InstanceNodeType.LOGIN.getType().equals(instance.getNodeType())) {
                    master = instance;
                    break;
                }
            }
            if (null == master) {
                log.error("=====>can't find login clusterId: {}", cluster.getClusterId());
                extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
                return extraMap;
            }
        }
        // *如果设置了 installed_node, 指定为该节点
        if (record.getInstalledInstanceId() != null && !record.getInstalledInstanceId().isEmpty()) {
            for (Instance instance : instances) {
                if (instance.getInstanceId().equals(record.getInstalledInstanceId())) {
                    master = instance;
                    break;
                }
            }
        }

        String status = checkOrStartInstance(task, master, record, cluster);
        if (SoftwareStatus.INSTALLED.nameLowerCase().equals(status)
                || SoftwareStatus.UNINSTALLED.nameLowerCase().equals(status)) {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
            log.debug(
                    "=====>{} software operation succeed, clusterId: {}, {} {}",
                    task.getTaskId(),
                    cluster.getClusterId(),
                    softwareName,
                    version);
            return extraMap;
        }

        if (SoftwareStatus.INSTALL_FAILED.nameLowerCase().equals(status)
                || SoftwareStatus.UNINSTALL_FAILED.nameLowerCase().equals(status)) {
            String reason = String.format(
                    "[%s] software operation failed, clusterId: %s, %s %s",
                    task.getTaskId(),
                    cluster.getClusterId(),
                    softwareName,
                    version);
            log.error("=====>{}", reason);
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            extraMap.put(ChpcConstant.TASK_FAILED_REASON, reason);
            return extraMap;
        }
        extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
        return extraMap;
    }

    /**
     * @Description: 获取实例的架构信息，如果不是检查软件架构，则返回"amd64"。
     * @Param instance Instance - 实例对象，包含实例ID等信息
     * @Return String - 返回实例的架构信息，可能为"amd64"或者实际的架构信息
     * @Throws Exception 无异常抛出异常
     */
    public String getInstanceArch(Instance instance) {
        if (!checkSoftwareArch) {
            return "amd64";
        }

        List<String> instanceIds = new ArrayList<>();
        instanceIds.add(instance.getInstanceId());
        List<BccInstance> bccInstances = bccGateway.getBccInstances(instanceIds);

        String instanceArch = "amd64";

        if (bccInstances.size() == 1) {
            BccInstance masterInstance = bccInstances.get(0);
            instanceArch = masterInstance.getOsArch();
        }
        if (instanceArch.contains("amd64") || instanceArch.contains("x86")) {
            instanceArch = "amd64";
        }

        return instanceArch;
    }

    /**
     * 生成OOS执行请求，包含实例ID、状态、软件名称、版本号和调度类型等信息
     *
     * @param instance      实例对象，包含实例ID和主机架构信息
     * @param status        实例状态，如"RUNNING"或"STOPPED"等
     * @param softwareName  软件名称，如"hadoop"或"spark"等
     * @param version       软件版本号，如"2.7.3"或"2.1.0"等
     * @param schedulerType 调度类型，如"normal"或"high"等
     *
     * @return CreateOosExecutionRequest
     *         返回一个CreateOosExecutionRequest对象，包含OOS执行请求的相关信息
     * @throws BceException 当获取主机架构信息时发生异常时抛出此异常
     */
    private CreateOosExecutionRequest generateOosExecutionRequest(
            Instance instance, String status, String softwareName, String version, Cluster cluster) {

        String masterArch;
        try {
            masterArch = getInstanceArch(instance);
            log.info("=====>masterArch: {}", masterArch);
        } catch (Exception e) {
            log.error("getInstanceArch error: {}", e);
            throw new BceException(e.getMessage());
        }

        Map<String, String> idMaps = new HashMap<>();
        idMaps.put("instanceId", instance.getInstanceId());

        List<Map<String, String>> idLists = new ArrayList<>();
        idLists.add(idMaps);

        Map<String, Object> properties = new HashMap<>();
        properties.put("content", generateCommandContent(instance, softwareName, status, version, cluster, masterArch));
        properties.put("user", "root");
        properties.put("workDir", "/");
        // 待执行命令的虚机列表
        properties.put("__workerSelectors__", idLists);

        CreateOosExecutionRequest.Operator operator = new CreateOosExecutionRequest.Operator();
        operator.setName("cloud_assist_shell");
        operator.setOperator("BCE::Agent::ExecuteShell");
        operator.setDescription("exec shell command");
        operator.setProperties(properties);

        CreateOosExecutionRequest.Template template = new CreateOosExecutionRequest.Template();
        template.setName("BCE-BCC-BulkyRunCommand");
        template.setLinear(true);
        template.setOperators(Collections.singletonList(operator));

        CreateOosExecutionRequest request = new CreateOosExecutionRequest();
        request.setTemplate(template);
        return request;
    }

    /**
     * 生成命令内容。
     *
     * @param softwareName  软件名称
     * @param status        软件状态（安装中或卸载中）
     * @param version       软件版本号
     * @param schedulerType 调度器类型
     * @param masterArch    主机架构
     * @return 返回一个包含命令内容的字符串，格式为："mkdir -p /opt/chpc/download && rm -rf
     *         /opt/chpc/download/%s* && wget %s/release/pkg/%s/%s.tar.gz -O
     *         /opt/chpc/download/%s.tar.gz && tar -zxvf
     *         /opt/chpc/download/%s.tar.gz -C
     *         /opt/chpc/download && pushd /opt/chpc/download/%s && bash %s %s %s &&
     *         popd"
     */
    private String generateCommandContent(
            Instance instance, String softwareName, String status, String version, Cluster cluster, String masterArch) {

        StringBuilder result = new StringBuilder();

        String softwareFile = String.format("%s_%s_%s", softwareName, version, masterArch);
        String script;
        if (SoftwareStatus.INSTALLING.nameLowerCase().equalsIgnoreCase(status)) {
            script = "install.sh";
        } else {
            script = "uninstall.sh";
        }
        String region = regionConfiguration.getCurrentRegion();
        if (bosEndpoint.contains("sandbox")) {
            region = "sandbox";
        }
        if (grayEnabled) {
            region += "test";
        }
        String command = String.format(
                "wget %s/release/pkg/amd64/operate.sh -O operate.sh && bash operate.sh %s %s %s %s %s %s %s",
                bosEndpoint,
                softwareFile,
                bosEndpoint,
                masterArch,
                script,
                cluster.getSchedulerType(),
                region,
                cluster.getSoftwareDir());
        log.info("=====>command: {}", command);
        result.append(command);
        if (InstanceNodeType.LOGIN.getType().equals(instance.getNodeType())) {
            String installchpcAppServerCmd = String.format(" --region %s", region);
            installchpcAppServerCmd += String.format(" --scheduler %s", cluster.getSchedulerType());
            installchpcAppServerCmd += String.format(" --port %s", instance.getPortalPort());
            if (instance.getPublicIp() != null && !instance.getPublicIp().isEmpty()) {
                installchpcAppServerCmd += String.format(" --eip %s", instance.getPublicIp());
            }
            installchpcAppServerCmd += String.format(" --cluster_id %s", cluster.getClusterId());
            installchpcAppServerCmd += String.format(" --version %s", version);
            result.append(installchpcAppServerCmd);
        }

        return result.toString();
    }

    /**
     * @Description 检查或开始实例的软件安装或卸载操作，并返回状态。
     *              如果实例正在安装中或卸载中，则会发送 OOS 命令；否则，返回 INSTALLING 状态。
     *              如果实例正在安装中，则会更新软件记录的 OOS 执行 ID，并返回 INSTALLING 状态；
     *              如果实例正在卸载中，则会更新软件记录的 OOS 执行 ID，并返回 UNINSTALLING 状态。
     *              如果 OOS 执行成功，则会更新软件记录的状态为 INSTALLED 或 UNINSTALLED，并返回对应的状态；
     *              如果 OOS 执行失败，则会更新软件记录的状态为 INSTALL_FAILED 或
     *              UNINSTALL_FAILED，并返回对应的状态。
     *
     * @param task          Task 任务对象
     * @param instance      Instance 实例对象
     * @param record        SoftwareRecord 软件记录对象
     * @param schedulerType String 调度器类型，用于生成 OOS 命令
     * @return String
     *         返回软件状态，INSTALLING、INSTALLED、UNINSTALLING、UNINSTALLED、INSTALL_FAILED、UNINSTALL_FAILED
     * @throws Exception 可能抛出异常，包括调用 OOS 服务时的异常和数据库更新异常
     */
    private String checkOrStartInstance(Task task, Instance instance, SoftwareRecord record, Cluster cluster) {

        if (SoftwareStatus.INSTALLING.nameLowerCase().equalsIgnoreCase(record.getStatus())
                || SoftwareStatus.UNINSTALLING.nameLowerCase().equalsIgnoreCase(record.getStatus())) {

            String oosExecutionId = record.getOosExecutionId();
            if (oosExecutionId.isEmpty()) {
                // 执行 oos
                CreateOosExecutionRequest request = generateOosExecutionRequest(
                        instance, record.getStatus(), record.getName(), record.getVersion(), cluster);

                log.debug(
                        "begin to send oos command, instance id: {}, content: {}",
                        instance.getInstanceId(),
                        JacksonUtil.toJson(request));

                try {
                    oosExecutionId = oosGateway.createExecution(request);
                } catch (Exception e) {
                    log.error(
                            "call oos service failed in softawre installing, instanceId:{}, exception:{}",
                            instance.getInstanceId(),
                            e);
                }

                record.setOosExecutionId(oosExecutionId);
                softwareRecordDAOGateway.update(record);

                log.debug("success to send oos, instance id: {}", instance.getInstanceId());
                return record.getStatus();
            } else {
                // 查询 oos
                GetOosExecutionResponse.Result response = oosGateway.getExecutionById(oosExecutionId);

                if (response == null || ClusterOosTaskStatus.FAILED.name().equalsIgnoreCase(response.getState())) {

                    String state = response == null ? "" : response.getState();
                    String reason = response == null ? "" : response.getReason();

                    log.debug("oos response, id:{}, status:{}, output:{}", oosExecutionId, state, reason);

                    record.setMsg(
                            String.format(
                                    "oos exec failed, id: %s, status: %s, output: %s", oosExecutionId, state, reason));

                    if (SoftwareStatus.INSTALLING.nameLowerCase().equalsIgnoreCase(record.getStatus())) {
                        record.setStatus(SoftwareStatus.INSTALL_FAILED.nameLowerCase());
                        softwareRecordDAOGateway.update(record);
                        return SoftwareStatus.INSTALL_FAILED.nameLowerCase();
                    } else {
                        record.setStatus(SoftwareStatus.UNINSTALL_FAILED.nameLowerCase());
                        softwareRecordDAOGateway.update(record);
                        return SoftwareStatus.UNINSTALL_FAILED.nameLowerCase();
                    }
                }
                log.debug(
                        "oos response, id:{}, status:{}, output:{}",
                        oosExecutionId,
                        response.getState(),
                        response.getReason());
                if (ClusterOosTaskStatus.SUCCESS.name().equalsIgnoreCase(response.getState())) {

                    record.setMsg("");
                    if (SoftwareStatus.INSTALLING.nameLowerCase().equalsIgnoreCase(record.getStatus())) {
                        record.setStatus(SoftwareStatus.INSTALLED.nameLowerCase());
                        softwareRecordDAOGateway.update(record);
                        return SoftwareStatus.INSTALLED.nameLowerCase();
                    } else {
                        // todo 卸载失败，直接删除记录么？
                        record.setStatus(SoftwareStatus.UNINSTALLED.nameLowerCase());
                        record.setDeleted(true);
                        softwareRecordDAOGateway.update(record);
                        return SoftwareStatus.UNINSTALLED.nameLowerCase();
                    }
                }
            }
        }

        return InstanceStatus.INSTALLING.nameLowerCase();
    }

    /**
     * {@inheritDoc}
     * 在任务执行完成后，根据任务状态和软件名称、版本更新软件记录的状态。
     * 如果任务失败，则将软件记录的状态设置为安装失败或卸载失败，并保存错误原因；
     * 如果任务正常执行，则不进行处理。
     *
     * @param task    任务对象
     * @param paraMap 参数映射，包含任务状态（字符串类型）、软件名称（字符串类型）、软件版本（字符串类型）等信息
     * @throws Exception 可能会抛出异常，例如数据库操作异常等
     */
    public void afterExecuteTask(final Task task, final Map<String, Object> paraMap) {
        String executeStatus = String.valueOf(paraMap.get(ChpcConstant.TASK_STATUS));
        String softwareName = (String) paraMap.get(ChpcConstant.SOFTWARE_NAME);
        String version = (String) paraMap.get(ChpcConstant.SOFTWARE_VERSION);
        String installedInstacneId = (String) paraMap.get(ChpcConstant.INSTANCE_ID);

        if (TaskStatus.FAILED.getValue().equalsIgnoreCase(executeStatus)) {
            String reason = (String) paraMap.get(ChpcConstant.TASK_FAILED_REASON);
            SoftwareRecord record = softwareRecordDAOGateway.findSoftwareByNameAndVersionAndInstanceId(
                    task.getClusterId(),
                    softwareName,
                    version,
                    installedInstacneId);
            if (record != null) {
                if (SoftwareStatus.INSTALL_FAILED.nameLowerCase().equalsIgnoreCase(record.getStatus())
                        || SoftwareStatus.UNINSTALL_FAILED.nameLowerCase().equalsIgnoreCase(record.getStatus())) {
                    log.debug("task failed, but software record is already updated");
                } else if (SoftwareStatus.INSTALLING.nameLowerCase().equalsIgnoreCase(record.getStatus())) {
                    record.setStatus(SoftwareStatus.INSTALL_FAILED.nameLowerCase());
                    record.setMsg(reason);
                    softwareRecordDAOGateway.update(record);
                } else {
                    record.setStatus(SoftwareStatus.UNINSTALL_FAILED.nameLowerCase());
                    record.setMsg(reason);
                    softwareRecordDAOGateway.update(record);
                }
            }
        }
    }
}
