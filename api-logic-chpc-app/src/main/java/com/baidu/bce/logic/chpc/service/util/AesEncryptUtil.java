package com.baidu.bce.logic.chpc.service.util;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
@Slf4j
@Component
public class AesEncryptUtil {
    private static final String ALGORITHM = "AES";
    private static final String ALGORITHM_FULL = "AES/ECB/PKCS5Padding";
    private static final String CHARSET_NAME = "UTF-8";
    private static final char[] HEX_CHARS = {'0', '1', '2', '3', '4', '5', '6', '7',
            '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
    private static final String AES_KEY = "IdsGHHX2yYShLzT1";



    public static String encrypt(String content) {
        try {
            // AES encrypt operation
            byte[] bytes = doAes(content.getBytes(CHARSET_NAME), AES_KEY, Cipher.ENCRYPT_MODE);
            // AES 加密后的字节数组转换为 16 进制字符串
            return bytes2HexStr(bytes);
        } catch (Exception e) {
            return null;
        }
    }
    /**
     * AES 对称加密解密 Operation
     *
     */
    public static byte[] doAes(byte[] contentBytes, String hexKey, int mode) {
        try {
            // 生成 AES 密钥
            SecretKeySpec secretKeySpec = new SecretKeySpec(hexKey.getBytes(CHARSET_NAME), ALGORITHM);
            Cipher cipher = Cipher.getInstance(ALGORITHM_FULL);
            // init 密码器，加密（ENCRYPT_MODE）or 解密（DECRYPT_MODE）
            cipher.init(mode, secretKeySpec);
            return cipher.doFinal(contentBytes);
        } catch (Exception e) {
            return null;
        }
    }

    public static String bytes2HexStr(byte[] bytes) {
        if (bytes.length == 0) {
            return null;
        }
        char[] hexChars = new char[bytes.length << 1];
        int index = 0;
        for (byte b : bytes) {
            hexChars[index++] = HEX_CHARS[b >>> 4 & 0xf];
            hexChars[index++] = HEX_CHARS[b & 0xf];
        }
        return new String(hexChars);
    }
}