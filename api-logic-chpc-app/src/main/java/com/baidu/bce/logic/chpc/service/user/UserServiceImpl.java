package com.baidu.bce.logic.chpc.service.user;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendDomainUserListResponse;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.request.user.UserModifyRequest;
import com.baidu.bce.logic.chpc.model.response.user.User;
import com.baidu.bce.logic.chpc.model.response.user.UserCommonResponse;
import com.baidu.bce.logic.chpc.service.UserService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @Resource
    BackendGateway backendGateway;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Override
    public List<User> getDomainUserList(String clusterId, String userName, String authority) {

        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());

        if (cluster == null || cluster.getSchedulePlugin() == 0) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
        List<User> userList = getDomainUsers(clusterId, cluster);
        if ((StringUtils.isNotEmpty(userName))) {
            List<User> flteredList = new ArrayList<>();
            for (int i = 0; i < userList.size(); i++) {
                if (userList.get(i).getUserName().contains(userName)) {
                    flteredList.add(userList.get(i));
                }
            }
            userList.clear();
            userList.addAll(flteredList);
        }
        if ((StringUtils.isNotEmpty(authority))) {
            List<User> flteredList = new ArrayList<>();
            for (int i = 0; i < userList.size(); i++) {
                if (userList.get(i).getAuthority().contains(authority)) {
                    flteredList.add(userList.get(i));
                }
            }
            userList.clear();
            userList.addAll(flteredList);
        }

        Collections.sort(userList, (user1, user2) -> user1.getUserName().compareTo(user2.getUserName()));
        return userList;
    }

    private List<User> getDomainUsers(String clusterId, Cluster cluster) {
        List<User> userList = new ArrayList<>();
        String arguments = "";
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            // 混合云集群暂时从file中读取
            arguments = arguments + " --data-source=file";
        } else {
            // 共有云集群暂时从file中读取opneldap中读取
            arguments = arguments + " --data-source=openldap";
        }
        // 拿到用户信息
        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "domain_user_get", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "get domain user info failed, " + resp.getMessage(), "scheduler plugin");
        }
        ObjectMapper mapper = new ObjectMapper();
        BackendDomainUserListResponse res;
        try {
            res = mapper.readValue(resp.getData(), BackendDomainUserListResponse.class);
        } catch (Exception e) {
            log.error("json unmarshal BackendDomainUserListResponse failed", e);
            return userList;
        }

        for (int i = 0; i < res.getUserList().size(); i++) {
            User user = new User();
            user.setUserName(res.getUserList().get(i).getUserName());
            user.setAuthority(res.getUserList().get(i).getAuthority());
            userList.add(user);
        }
        return userList;
    }

    @Override
    public UserCommonResponse addDomainUser(String clusterId, String userName, String userPassword, String authority) {

        if ("root".equals(userName)) {
            throw new CommonException.RequestInvalidException("无法操作root用户");
        }
        if (!("sudo".equals(authority) || "common".equals(authority))) {
            throw new CommonException.RequestInvalidException("非法权限组");
        }
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());

        if (cluster == null || cluster.getSchedulePlugin() == 0) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }

        List<User> userList = getDomainUsers(clusterId, cluster);
        for (int i = 0; i < userList.size(); i++) {
            if (userName.equals(userList.get(i).getUserName())) {
                throw new CommonException.RequestInvalidException(userName + "用户已经存在");
            }
        }

        String arguments = "";
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            // 混合云集群不支持用户管理
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        } else {
            arguments = arguments + " -n" + userName + " -p" + userPassword + " -a" + authority + " -s" + cluster.getSchedulerType();
        }
        // 拿到用户信息
        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "domain_user_add", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "add domain user failed, " + resp.getMessage(), "scheduler plugin");
        }
        UserCommonResponse addUserResponse = new UserCommonResponse();
        addUserResponse.setUserName(userName);
        addUserResponse.setClusterName(cluster.getName());
        // bct 操作详情
        String bctMsg = String.format("集群%s（ID: %s），添加用户%s",
                cluster.getName(), cluster.getClusterId(), userName);
        addUserResponse.setMessage(bctMsg);
        return addUserResponse;
    }

    @Override
    public UserCommonResponse updateDomainUser(String clusterId, UserModifyRequest request) {
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());

        if (cluster == null || cluster.getSchedulePlugin() == 0) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
        if ((StringUtils.isNotEmpty(request.getAuthority())) &&
                (!("sudo".equals(request.getAuthority()) || "common".equals(request.getAuthority())))) {
            throw new CommonException.RequestInvalidException("非法权限组");
        }
        boolean isExist = false;

        List<User> userList = getDomainUsers(clusterId, cluster);
        for (int i = 0; i < userList.size(); i++) {
            if (request.getUserName().equals(userList.get(i).getUserName())) {
                isExist = true;
                break;
            }
        }

        if (!isExist) {
            throw new CommonException.RequestInvalidException(request.getUserName() + "用户不存在");
        }

        String arguments = "";
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            // 混合云集群不支持用户管理
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        } else {
            arguments = arguments + " -n" + request.getUserName();
            if (StringUtils.isNotEmpty(request.getUserPassword())) {
                arguments += " -p" + request.getUserPassword();
            }
            if (StringUtils.isNotEmpty(request.getAuthority())) {
                arguments += " -a" + request.getAuthority();
            }
        }
        // 拿到用户信息
        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "domain_user_update", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "update domain user failed, " + resp.getMessage(), "scheduler plugin");
        }
        UserCommonResponse addUserResponse = new UserCommonResponse();
        addUserResponse.setUserName(request.getUserName());
        addUserResponse.setClusterName(cluster.getName());
        // bct 操作详情
        StringBuilder bctMsg = new StringBuilder();
        bctMsg.append(String.format("集群%s（ID: %s），用户名: %s", cluster.getName(),
                cluster.getClusterId(), request.getUserName()));
        if (StringUtils.isNotEmpty(request.getAuthority())) {
            bctMsg.append(String.format("，修改用户权限为%s", request.getAuthority()));
        }else if (StringUtils.isNotEmpty(request.getUserPassword())){
            bctMsg.append("，更改了密码");
        }
        addUserResponse.setMessage(bctMsg.toString());
        return addUserResponse;
    }

    @Override
    public UserCommonResponse deleteDomainUser(String clusterId, String userName) {
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());

        if (cluster == null || cluster.getSchedulePlugin() == 0) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }

        boolean isExist = false;

        List<User> userList = getDomainUsers(clusterId, cluster);
        for (int i = 0; i < userList.size(); i++) {
            if (userName.equals(userList.get(i).getUserName())) {
                isExist = true;
                break;
            }
        }

        if (!isExist) {
            throw new CommonException.RequestInvalidException(userName + "用户不存在");
        }

        String arguments = "";
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            // 混合云集群不支持用户管理
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        } else {
            arguments = arguments + " -n" + userName;
        }
        // 拿到用户信息
        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "domain_user_del", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "delete domain user failed, " + resp.getMessage(), "scheduler plugin");
        }
        UserCommonResponse addUserResponse = new UserCommonResponse();
        addUserResponse.setUserName(userName);
        addUserResponse.setClusterName(cluster.getName());
        // bct 操作详情
        String bctMsg = String.format("集群%s（ID: %s），删除用户%s",
                cluster.getName(), cluster.getClusterId(), userName);
        addUserResponse.setMessage(bctMsg);
        return addUserResponse;
    }

    @Override
    public BaseResponse changeDomainUserQos(String clusterID, String username, String qos) {
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterID, getAccountId());
        if (cluster == null || cluster.getSchedulePlugin() == 0) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            // 混合云集群不支持用户管理
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }

        String arguments = "";
        arguments = arguments + " -n " + username;
        arguments = arguments + " -q " + qos;

        // 拿到用户信息
        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterID, "domain_user_change_qos", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "delete domain user failed, " + resp.getMessage(), "scheduler plugin");
        }

        return new BaseResponse();
    }
}
