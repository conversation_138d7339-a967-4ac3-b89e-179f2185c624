package com.baidu.bce.logic.chpc.service.cluster;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.directory.api.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.internalsdk.iam.model.BatchVerifyResults;
import com.baidu.bce.internalsdk.iam.model.VerifyResult;
import com.baidu.bce.logic.chpc.annotation.AspectResultHolder;
import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.autoscaling.gateway.IAutoScalingDAOGateway;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.autoscaling.model.ExecuteAutoScalingInfo;
import com.baidu.bce.logic.chpc.autoscaling.model.SetAutoScalingRequest;
import com.baidu.bce.logic.chpc.bcm.model.BatchQueryRequest;
import com.baidu.bce.logic.chpc.bcm.model.BatchQueryResponse;
import com.baidu.bce.logic.chpc.bcm.model.Dimension;
import com.baidu.bce.logic.chpc.cfs.FileSystem;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.chpc.cluster.domainservice.ClusterServiceV2;
import com.baidu.bce.logic.chpc.cluster.model.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.ClusterHeartbeatRequest;
import com.baidu.bce.logic.chpc.cluster.model.ClusterHeartbeatRequest.HeartbeatInfo;
import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.cluster.model.MountInfo;
import com.baidu.bce.logic.chpc.cluster.model.QueueInfo;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterErrorMessage;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.InstanceAttribution;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.SoftwareStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.common.ZoneUtil;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.config.LockConfig;
import com.baidu.bce.logic.chpc.config.SchedulerThreadLocalHolder;
import com.baidu.bce.logic.chpc.cos.CreateStackResponse;
import com.baidu.bce.logic.chpc.domainservice.CfsService;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkspaceDAOGateway;
import com.baidu.bce.logic.chpc.model.Cfs;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.SoftwareRecord;
import com.baidu.bce.logic.chpc.model.Workspace;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterUpdateRequest;
import com.baidu.bce.logic.chpc.model.response.cfs.CfsVO;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterDetailResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterVO;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceVO;
import com.baidu.bce.logic.chpc.model.response.queue.QueueVO;
import com.baidu.bce.logic.chpc.model.response.user.User;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.securitygroup.gateway.SecurityGroupGateway;
import com.baidu.bce.logic.chpc.service.ChpcClusterServiceImpl;
import com.baidu.bce.logic.chpc.service.IAutoScalingService;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;
import com.baidu.bce.logic.chpc.service.IInstanceService;
import com.baidu.bce.logic.chpc.service.bcm.BcmServiceImpl;
import com.baidu.bce.logic.chpc.service.queue.QueueServiceImpl;
import com.baidu.bce.logic.chpc.service.user.UserServiceImpl;
import com.baidu.bce.logic.chpc.service.util.AesEncryptUtil;
import com.baidu.bce.logic.chpc.service.util.DiskTypeUtil;
import com.baidu.bce.logic.chpc.service.util.PasswordUtil;
import com.baidu.bce.logic.chpc.service.util.SchedulerUserConfig;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.chpc.software.model.SoftwareVersion;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logic.chpc.vpc.gateway.VpcGateway;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.logic.core.util.UuidUtil;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.baidubce.services.bcc.model.securitygroup.ListSecurityGroupsRequest;
import com.baidubce.services.bcc.model.securitygroup.ListSecurityGroupsResponse;
import com.baidubce.services.esg.model.ListEsgRequest;
import com.baidubce.services.esg.model.ListEsgResponse;
import com.baidubce.services.vpc.model.GetVpcResponse;
import com.fasterxml.jackson.databind.ObjectMapper;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ClusterServiceImpl implements IChpcClusterService {

    @Resource
    ClusterServiceV2 clusterServiceV2;

    @Resource
    ChpcClusterServiceImpl chpcClusterService;

    @Resource
    IAutoScalingService iAutoScalingService;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    QueueDAOGateway queueDAOGateway;

    @Resource
    TagsDAOGateway tagsDAOGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    TagsGateway tagsGateway;

    @Resource
    private WorkspaceDAOGateway workspaceDAOGateway;

    @Resource
    IAutoScalingDAOGateway autoScalingDAOGateway;

    @Resource
    QueueServiceImpl queueServiceImpl;

    @Resource
    CfsService cfsService;

    @Resource
    CfsDAOGateway cfsDAOGateway;

    @Resource
    InstanceService instanceService;

    @Resource
    IInstanceService instanceServiceImpl;

    @Resource
    ZoneUtil zoneUtil;

    @Resource
    TaskService taskService;

    @Resource
    LockConfig lockConfig;

    @Resource
    SchedulerUserConfig schedulerUserConfig;

    @Resource
    SoftwareRecordDAOGateway softwareRecordDAOGateway;

    @Resource
    UserServiceImpl userService;

    @Resource
    BcmServiceImpl bcmService;

    @Resource
    PasswordUtil passwordUtil;

    @Resource
    VpcGateway vpcGateway;

    @Resource
    SecurityGroupGateway securityGroupGateway;

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Autowired
    CfsGateway cfsGateway;

    /**
     * {@inheritDoc}
     * 创建集群并返回 ClusterResponse。如果是混合云集群，则不插入 queue；如果是公有云集群或者混合云集群使用 cfsid 或
     * useNfs，则插入 cfs；
     * 最后插入任务到 task 表中，并设置 taskId 到 ClusterResponse 中。
     *
     * @param clusterCreateRequest 集群创建请求对象
     * @return ClusterResponse 包含集群信息的响应对象
     * @throws Exception 可能会抛出异常
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ClusterResponse createCluster(ClusterCreateRequest clusterCreateRequest) {

        // *1.创建安全组
        // 获取安全组Id，如果为空，使用或创建默认安全组
        if (StringUtils.isEmpty(clusterCreateRequest.getSecurityGroupId())) {
            try {
                String securityGroupId = clusterServiceV2.getOrCreateSecurityGroup(
                        clusterCreateRequest.getVpcId(),
                        clusterCreateRequest.getSecurityGroupType(),
                        clusterCreateRequest.getSshPort(),
                        clusterCreateRequest.getPortalPort());
                clusterCreateRequest.setSecurityGroupId(securityGroupId);
            } catch (Exception e) {
                // *创建安全组失败，直接就报错了
                log.error("failed to get or create security group, error is:{}", e);
                throw new BceException(e.getMessage());
            }
        }
        // *2.插入集群数据
        String stackName = "stack-" + UuidUtil.generateShortUuid();
        ClusterResponse response = clusterServiceV2.createCluster(clusterCreateRequest, stackName);

        // *3.插入队列信息
        List<String> queueIds = new ArrayList<>();
        List<String> queueNames = new ArrayList<>();
        List<String> cudaVersions = new ArrayList<>();
        List<String> gpuDriverVersions = new ArrayList<>();
        List<String> cudnnVersions = new ArrayList<>();
        String defaulQueueId = "";
        for (QueueInfo queueInfo : clusterCreateRequest.getQueueList()) {
            Queue queue = queueServiceImpl.genQueue(queueInfo, clusterCreateRequest, response.getClusterId());
            queueDAOGateway.insert(queue);

            if (queueInfo.getTags() != null) {
                // 队列标签是chpc维护，这里直接绑定，如果集群创建失败，再删除
                for (Tag tag : queueInfo.getTags()) {
                    try {
                        tagsDAOGateway.insert(LogicUserService.getAccountId(), response.getClusterId(), "queue", queueInfo.getQueueName(),
                                tag.getTagKey(), tag.getTagValue());
                    } catch (Exception e) {
                        log.error("insert queue tags, clusterId : {} ,err : {}", response.getClusterId(), e);
                    }
                }
            }
            queueInfo.setQueueId(queue.getQueueId());
            queueIds.add(queueInfo.getQueueId());
            queueNames.add(queueInfo.getQueueName());
            if (queueInfo.getComputeSpec() != null) {
                cudaVersions.add(queueInfo.getComputeSpec().getCudaVersion());
                gpuDriverVersions.add(queueInfo.getComputeSpec().getGpuDriverVersion());
                cudnnVersions.add(queueInfo.getComputeSpec().getCudnnVersion());
            } else {
                cudaVersions.add("");
                gpuDriverVersions.add("");
                cudnnVersions.add("");
            }
            if (queueInfo.getIsDefault()) {
                defaulQueueId = queueInfo.getQueueId();
            }
            if (queueInfo.getIsAutoScale()) {
                // 插入自动伸缩策略
                SetAutoScalingRequest request = new SetAutoScalingRequest();
                request.setClusterId(response.getClusterId());
                request.setClusterName(clusterCreateRequest.getClusterName());
                request.setAsName(ServiceUtil.generateCustomString(response.getClusterId(), queueInfo.getQueueName()));
                request.setEnableAutoGrow(queueInfo.getEnableAutoGrow());
                request.setEnableAutoShrink(queueInfo.getEnableAutoShrink());
                request.setHostNamePrefix(queueInfo.getHostnamePrefix());
                request.setImageId(queueInfo.getComputeSpec().getImageId());
                request.setMaxNodesInQueue(queueInfo.getMaxNodesInQueue());
                request.setMaxScalePerCycle(queueInfo.getMaxScalePerCycle());
                request.setMinNodesInQueue(queueInfo.getMinNodesInQueue());
                request.setQueueName(queueInfo.getQueueName());
                request.setSpec(queueInfo.getComputeSpec().getSpec());
                request.setSubnetId(queueInfo.getComputeSpec().getSubnetId());
                // 自动伸缩增加可能存在的gpu相关信息
                request.setGpuDriverVersion(queueInfo.getComputeSpec().getGpuDriverVersion());
                request.setCudaVersion(queueInfo.getComputeSpec().getCudaVersion());
                request.setCudnnVersion(queueInfo.getComputeSpec().getCudnnVersion());
                request.setSecurityGroupId(clusterCreateRequest.getSecurityGroupId());
                request.setZoneName(clusterCreateRequest.getZoneName());
                request.setSystemDiskType(
                        DiskTypeUtil.getDiskType(queueInfo.getComputeSpec().getSystemDisk().getStorageType()));
                request.setSystemDiskSize(queueInfo.getComputeSpec().getSystemDisk().getSize());
                request.setDataDiskList(new ArrayList<>());
                if (queueInfo.getComputeSpec() != null && queueInfo.getComputeSpec().getDataDiskList() != null) {
                    for (DiskInfo dataDisk : queueInfo.getComputeSpec().getDataDiskList()) {
                        DiskInfo acDataDisk = new DiskInfo();
                        acDataDisk.setStorageType(DiskTypeUtil.getDiskType(dataDisk.getStorageType()));
                        acDataDisk.setSize(dataDisk.getSize());
                        acDataDisk.setCdsNum(dataDisk.getCdsNum());
                        request.getDataDiskList().add(acDataDisk);
                    }
                }

                iAutoScalingService.generateAutoScaling(request, queue);
            }
        }

        // *4.插入 task
        Map<String, Object> extrMap = new HashMap<>();
        extrMap.put(ChpcConstant.QUEUE_IDS, queueIds);
        extrMap.put(ChpcConstant.QUEUE_NAMES, queueNames);
        extrMap.put(ChpcConstant.CHPC_CUDA_VERSION, cudaVersions);
        extrMap.put(ChpcConstant.CHPC_GPU_DRIVER_VERSION, gpuDriverVersions);
        extrMap.put(ChpcConstant.CHPC_CUDNN_VERSION, cudnnVersions);
        extrMap.put(ChpcConstant.SOFTWARE_LIST, clusterCreateRequest.getSoftwareList());
        extrMap.put(ChpcConstant.COS_STACK_NAME, stackName);
        // 集群标签是云上tag
        if (clusterCreateRequest.getTags() != null && clusterCreateRequest.getTags().size() > 0) {
            extrMap.put(ChpcConstant.CLUSTER_TAGS, clusterCreateRequest.getTags());
        }

        String taskUuid = UUID.randomUUID().toString();
        taskService.insert(TaskType.GROUP_RESOURCE_SAVE_TASK,
                response.getClusterId(), defaulQueueId,
                TaskSourceType.INIT_CLUSTER.getTaskSourceType(),
                taskUuid, extrMap, "");

        // *6.插入 cfs 信息
        // 公有云集群使用 cfsid 来判断，混合云集群使用 usenfs 来判断
        if (clusterCreateRequest.getMountList() != null) {
            if (CollectionUtils.isNotEmpty(clusterCreateRequest.getMountList())) {
                for (MountInfo mountInfo : clusterCreateRequest.getMountList()) {
                    Cfs cfs = cfsService.genCfs(mountInfo, response.getClusterId());
                    cfsDAOGateway.insert(cfs);
                }
            }
        }

        // *7.cos 请求
        // 多个队列合并成一个 cos 请求
        String cosStackId = "";
        try {
            CreateStackResponse createStackResponse = clusterServiceV2.createClusterInCos(clusterCreateRequest,
                    stackName);
            if (!createStackResponse.isSuccess()) {
                throw new CommonException.CosServiceException(createStackResponse.getMsg());
            }
            cosStackId = createStackResponse.getResult().getId();
        } catch (Exception e) {
            log.error("create cos stack error", response.getClusterId(), e);
            throw new CommonException.CosServiceException("create cos stack error");
        }
        if (StringUtils.isEmpty(cosStackId)) {
            log.error("create cos stack error, cose stack id is null", response.getClusterId());
            throw new CommonException.CosServiceException("create cos stack error, cose stack id is null");
        }
        log.info("{} create cos stack success, cosStackId: {}", response.getClusterId(), cosStackId);

        // *8.更新 cos stackId 到 t_chpc_task
        Boolean success = taskService.updateCosStackIdByTaskUuid(taskUuid, cosStackId);
        log.debug("update cos stackId to t_chpc_task success: {}", success);

        response.setStackId(cosStackId);
        response.setTaskId(taskUuid);
        // bct消息
        String bctMsg = String.format("成功创建%s集群（ID: %s），地域：%s，%s%s集群",
                clusterCreateRequest.getClusterName(), response.getClusterId(),
                clusterCreateRequest.getZoneName(), clusterCreateRequest.getSchedulerType(),
                StringUtils.equalsIgnoreCase(clusterCreateRequest.getClusterType(), ClusterType.CLOUD.nameLowerCase()) ? "公有云" : "混合云");
        response.setMessage(bctMsg);
        return response;
    }

    /**
     * {@inheritDoc}
     * 删除集群，如果管理节点在线，则先停止集群，否则直接删除集群。
     * 返回值包括任务ID和集群状态。
     * 如果集群存在未删除的工作空间，则抛出CommonExceptions.RequestInvalidException异常。
     * 如果集群存在未被删除的实例，则抛出CommonExceptions.RequestInvalidException异常。
     * 如果集群已经处于删除中状态，则抛出CommonExceptions.RequestInvalidException异常。
     *
     * @param clusterId 集群ID
     * @param force     是否强制删除，true表示强制删除，false表示非强制删除
     * @return ClusterResponse 包含任务ID和集群状态的集群响应对象
     * @throws CommonExceptions.RequestInvalidException 如果集群存在未删除的工作空间、未被删除的实例或集群已经处于删除中状态时抛出此异常
     * @throws RuntimeException                         其他运行时异常
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ClusterResponse deleteCluster(String clusterId, boolean force) {
        List<Workspace> workspaceList = workspaceDAOGateway.getWorkspaceByClusterId(clusterId);
        if (workspaceList != null && workspaceList.size() > 0) {
            throw new CommonExceptions.RequestInvalidException(
                    "集群:" + workspaceList.get(0).getClusterName() + "存在未删除的工作空间:"
                            + workspaceList.get(0).getName());
        }
        String clusterName = clusterServiceV2.validateDeleteCluster(clusterId);
        String taskId = UUID.randomUUID().toString();
        Instance managerInstance = instanceServiceImpl.getClusterManagerInstanceOrNull(clusterId);
        ClusterResponse clusterResponse = new ClusterResponse();
        if (null != managerInstance
                && InstanceStatus.RUNNING.name().equalsIgnoreCase(managerInstance.getStatus())) {
            // 管理节点存在且在线，则先停止集群
            try {
                clusterResponse = clusterServiceV2.stopCluster(clusterId, force);
                Map<String, Object> extra = new HashMap<>(2);
                extra.put(ChpcConstant.BACKEND_TASK_ID, clusterResponse.getTaskId());
                extra.put(ChpcConstant.CLUSTER_DELETED_TASK, ChpcConstant.TYPE_EXISTED);

                taskService.insert(TaskType.BACKEND_ASYNC_TASK,
                        clusterId, "",
                        TaskSourceType.STOP_CLUSTER.getTaskSourceType(),
                        taskId,
                        extra);
            } catch (Exception e) {
                log.error("stop cluster failed, force delete", e);
                taskService.insert(
                        TaskType.CLUSTER_DELETED_TASK,
                        clusterId, "",
                        TaskSourceType.STOP_CLUSTER.getTaskSourceType(),
                        taskId);
            }
        } else {
            // 管理节点不存在或者已下线，则直接删除集群
            log.warn(String.format("can not stop cluster, because cluster %s manager instance not online", clusterId));
            taskService.insert(
                    TaskType.CLUSTER_DELETED_TASK,
                    clusterId, "",
                    TaskSourceType.STOP_CLUSTER.getTaskSourceType(),
                    taskId);
        }
        clusterDAOGateway.updateClusterStatus(clusterId, ClusterStatus.DELETING.nameLowerCase());

        // 解绑tag
        try {
            CreateAndAssignTagRequest request = new CreateAndAssignTagRequest();
            List<AssignResource> resources = new ArrayList<>();
            AssignResource resource = new AssignResource();
            resource.setServiceType("CHPC");
            resource.setResourceId(clusterId);
            resource.setResourceUuid(clusterId);
            resource.setRegion(regionConfiguration.getCurrentRegion());
            resource.setAssociationType("floating");
            resources.add(resource);
            request.setResources(resources);
            tagsGateway.createAndAssignTag(request);
        } catch (Exception e) {
            log.debug(
                    "unbind clustertags fail,clusterId:{} ,err:{}", clusterId, e.getMessage());
        }
        clusterResponse.setClusterId(clusterId);
        clusterResponse.setStatus(ClusterStatus.DELETING.nameLowerCase());
        clusterResponse.setTaskId(taskId);
        clusterResponse.setClusterName(clusterName);
        String bctMsg = String.format("成功删除%s集群（ID:%s）", clusterName, clusterId);
        clusterResponse.setMessage(bctMsg);
        return clusterResponse;
    }

    /**
     * @Description: 获取指定集群的详细信息，包括集群名称、是否开启高可用、逻辑区域、集群状态、错误消息、集群类型等。
     * 对于混合云集群，还会添加聚合字段，包括节点数（运行中、总数、异常节点）、CPU 使用量（12
     * 小时内的平均值）、安装的软件列表和用户列表。
     * @Param clusterId 集群 ID，不能为空
     * @Return ClusterDetailResponse 返回一个 ClusterDetailResponse 对象，包含了集群的详细信息
     * 如果 clusterId 为空或者不存在该集群，则返回 null
     */
    @Override
    public ClusterDetailResponse getCluster(String clusterId) {
        if (StringUtils.isEmpty(clusterId)) {
            return null;
        }
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        log.error("[getCluster {}]: debug {}", clusterId, cluster.toString());

        ClusterDetailResponse response = BeanCopyUtil.copyObject(cluster, ClusterDetailResponse::new);
        response.setClusterName(cluster.getName());
        response.setEnableHa(cluster.isEnableHa());
        String apiZoneName = zoneUtil.getApiZoneName(response.getLogicalZone());
        response.setLogicalZone(apiZoneName);

        response.setCreatedDone(cluster.getCreatedDone());
        syncClusterStatus(cluster);
        response.setSchedulerHost(cluster.getSchedulerHost());
        response.setSchedulerIp(cluster.getSchedulerIp());
        response.setStatus(cluster.getStatus());
        response.setErrorMessage(cluster.getErrorMessage());
        response.setClusterType(cluster.getClusterType());
        response.setForbidDelete(cluster.getForbidDelete());

        String clusterIdForGet = cluster.getClusterId();

        List<QueueVO> queues = new ArrayList<>();
        if (ClusterStatus.ERROR.nameLowerCase().equalsIgnoreCase(cluster.getStatus())
                && ClusterType.HYBRID.nameLowerCase().equalsIgnoreCase(cluster.getClusterType())) {
            // 混合云集群，状态出错时，跳过查询队列，避免报错
            log.debug("[getCluster {}]: skip listQueues", clusterId);
        } else {
            try {
                queues = queueServiceImpl.listQueues(clusterIdForGet);
            } catch (Exception e) {
                log.error("[getCluster {}]: listQueues failed: {}", clusterId, e);
            }
        }
        response.setQueueList(queues);

        List<Instance> instances = new ArrayList<>();
        if (ClusterStatus.ERROR.nameLowerCase().equalsIgnoreCase(cluster.getStatus())
                && ClusterType.HYBRID.nameLowerCase().equalsIgnoreCase(cluster.getClusterType())) {
            // 混合云集群，状态出错时，跳过查询节点，避免报错
            log.debug("[getCluster {}]: skip listInstance", clusterId);
        } else {
            try {
                instances = instanceServiceImpl.getClusterInstances(clusterIdForGet, "", "", "", "", "", false);
                // response.setInstanceList(instances.stream()
                // .map(ServiceUtil::convertToInstanceVO)
                // .collect(Collectors.toList()));
                List<InstanceVO> instanceVOS = new ArrayList<>();
                // 控制面支持远程连接的节点需要从bcc同步字段信息
                for (Instance instance : instances) {
                    instanceVOS.add(ServiceUtil.convertToInstanceVO(instance));
                    if (StringUtils.equalsIgnoreCase(instance.getNodeType(), InstanceNodeType.MASTER.getType())
                            || StringUtils.equalsIgnoreCase(instance.getNodeType(), InstanceNodeType.PROXY.getType())) {
                        List<Instance> res = instanceServiceImpl.encapsulateBccInfo(Arrays.asList(instance), true);
                        if (!res.isEmpty()) {
                            response.setManagerNode(res.get(0));
                        }
                    } else if (StringUtils.equalsIgnoreCase(instance.getNodeType(), InstanceNodeType.LOGIN.getType())) {
                        List<Instance> res = instanceServiceImpl.encapsulateBccInfo(Arrays.asList(instance), true);
                        if (!res.isEmpty()) {
                            response.setLoginNode(res.get(0));
                        }
                    }
                }
                response.setInstanceList(instanceVOS);
            } catch (Exception e) {
                log.error("[getCluster {}]: getClusterInstances failed: {}", clusterId, e);
            }
        }

        List<Cfs> cfsList = cfsService.findByClusterId(clusterIdForGet);
        if (CollectionUtils.isNotEmpty(cfsList)) {
            for (Cfs cfs : cfsList) {
                String cfsId = cfs.getCfsId();
                String fileSystemName = "";
                if (Strings.isNotEmpty(cfsId)) {
                    List<FileSystem> fileSystem;
                    try {
                        fileSystem = cfsGateway.getCfsFileSystem(cfs.getCfsId());
                        // 没有挂载点，报错
                        if (CollectionUtils.isEmpty(fileSystem)) {
                            log.error("The cfsId " + cfsId + " does not have file system");
                        } else if (fileSystem.size() > 0) {
                            fileSystemName = fileSystem.get(0).getFsName();
                            cfs.setName(fileSystemName);
                        }
                    } catch (WebClientResponseException e) {
                        log.error("get cfs failed, cfsId={}, errorMsg={}", cfsId, e.getResponseBodyAsString());
                    } catch (Exception e) {
                        log.error("get cfs failed, cfsId={}, errorMsg={}", cfsId, e.getMessage());
                    }
                }
            }
            List<CfsVO> cfsVOList = cfsList.stream()
                    .map(ServiceUtil::convertToCfsVO)
                    .collect(Collectors.toList());

            response.setCfsList(cfsVOList);
        }
        // 增加聚合字段
        log.debug("=====>getCluster: add aggregate field");
        // 1. 查询节点数（运行中、总数、异常节点）
        int runningNodeNum = 0;
        int totalNodeNum = instances.size();
        int exceptionNodeNum = 0;
        for (Instance instance : instances) {
            if (instance.getStatus().contains("offline") || instance.getStatus().contains("down")
                    || instance.getStatus().contains("unknown")) {
                exceptionNodeNum++;
            } else {
                runningNodeNum++;
            }
        }
        log.debug("[getCluster {}]: runningNodeNum: {}, totalNodeNum: {}, exceptionNodeNum: {}", clusterId,
                runningNodeNum,
                totalNodeNum, exceptionNodeNum);
        response.setRunningNodeNum(runningNodeNum);
        response.setTotalNodeNum(totalNodeNum);
        response.setErrorNodeNum(exceptionNodeNum);

        // 2. 查 bcm，12h 使用的 cpu 核数
        BatchQueryRequest request = new BatchQueryRequest();
        request.setScope("BCE_CHPC");
        request.setUserId(getAccountId());
        List<String> metricNames = new ArrayList<>();
        metricNames.add("ClusterCpuUsed");
        request.setMetricNames(metricNames);
        // 获取当前的UTC时间
        ZonedDateTime currentUtcTime = ZonedDateTime.now(ZoneId.of("UTC"));
        // 格式化当前UTC时间的输出
        DateTimeFormatter formatter = DateTimeFormatter.ISO_INSTANT;
        String currentUtcTimeString = currentUtcTime.format(formatter);
        // 计算12小时之前的UTC时间
        ZonedDateTime twelveHoursAgo = currentUtcTime.minusHours(12);
        // 格式化12小时之前UTC时间的输出
        String twelveHoursAgoString = twelveHoursAgo.format(formatter);
        request.setStartTime(twelveHoursAgoString);
        request.setEndTime(currentUtcTimeString);
        request.setCycle(60 * 60 * 12);
        Dimension dimension = new Dimension();
        dimension.setName("ClusterId");
        List<String> value = new ArrayList<>();
        value.add(clusterId);
        dimension.setValue(value);
        request.setDimensions(Collections.singletonList(dimension));
        List<String> statistics = new ArrayList<>();
        statistics.add("average");
        request.setStatistics(statistics);

        try {
            log.debug("bcmReq: {}", request);
            BatchQueryResponse bcmRes = bcmService.batchQuery(request);
            log.debug("bcmRes: {}", bcmRes);
            if (bcmRes != null && CollectionUtils.isNotEmpty(bcmRes.getMetrics())) {
                // 获取最近12小时的平均值
                Double averageValue = bcmRes.getMetrics().get(0).getDataPoints().stream()
                        .mapToDouble(dataPoint -> dataPoint.getAverage())
                        .average()
                        .getAsDouble();
                log.debug("[getCluster {}]: averageValue: {}", clusterId, averageValue);
                response.setCpuUsed12h(averageValue);
            }
        } catch (Exception e) {
            log.error("[getCluster {}]: query bcm failed, e: {}", clusterId, e);
        }

        // 3. 查安装的软件列表
        List<SoftwareVersion> softwareList = new ArrayList<>();
        List<SoftwareRecord> records = softwareRecordDAOGateway.findSoftwareByClusterId(clusterId);
        for (SoftwareRecord record : records) {
            // 如果软件记录状态是 installed，就加入到 softwareList 中
            if (SoftwareStatus.INSTALLED.nameLowerCase().equals(record.getStatus())) {
                if (record.getInstalledInstanceId() != null && StringUtils.isNotEmpty(record.getInstalledInstanceId())) {
                    // 安装在计算节点的软件忽略
                    continue;
                }
                SoftwareVersion software = new SoftwareVersion();
                software.setName(record.getName());
                software.setVersion(record.getVersion());
                softwareList.add(software);
            }
        }
        response.setSoftwareList(softwareList);
        log.debug("[getCluster {}]: softwareList: {}", clusterId, softwareList);
        // 4. 用户列表
        try {
            List<User> userList = userService.getDomainUserList(clusterId, null, null);
            response.setUserList(userList);
        } catch (Exception e) {
            log.error("[getCluster {}]: get user list failed, e: {}", clusterId, e);
        }
        // 查询子网和安全组名称

        if (StringUtils.isNotEmpty(response.getVpcId())) {
            GetVpcResponse vpc = null;
            try {
                vpc = vpcGateway.getVpc(response.getVpcId());
            } catch (Exception e) {
                log.error("[getCluster {}]: get vpc failed", clusterId);
            }
            if (vpc != null) {

                response.setVpcName(vpc.getVpc().getName());

                response.setVpcCidr(vpc.getVpc().getCidr());

                if (StringUtils.isNotEmpty(response.getSubnetId())) {
                    for (int i = 0; i < vpc.getVpc().getSubnets().size(); i++) {
                        if (response.getSubnetId().equals(vpc.getVpc().getSubnets().get(i).getSubnetId())) {
                            response.setSubnetsName(vpc.getVpc().getSubnets().get(i).getName());
                            response.setSubnetsCidr(vpc.getVpc().getSubnets().get(i).getCidr());
                        }
                    }
                }
            }
        }


        if (response.getSecurityGroupId().startsWith("esg")) {
            // 企业安全组
            ListEsgRequest esgRequest = new ListEsgRequest();
            ListEsgResponse listEsgResponse = null;
            try {
                listEsgResponse = securityGroupGateway.listEnterpriseSecurityGroups(esgRequest);
            } catch (Exception e) {
                log.error("[getCluster {}]: get SecurityGroup failed", clusterId);
            }

            if (listEsgResponse != null) {
                for (int i = 0; i < listEsgResponse.getEnterpriseSecurityGroups().size(); i++) {
                    if (response.getSecurityGroupId().equals(listEsgResponse.getEnterpriseSecurityGroups().get(i).getId())) {
                        response.setSecurityGroupName(listEsgResponse.getEnterpriseSecurityGroups().get(i).getName());
                    }
                }
            }
        } else if (response.getSecurityGroupId().startsWith("g")) {
            // 普通安全组
            ListSecurityGroupsRequest listSecurityGroupsRequest = new ListSecurityGroupsRequest();
            listSecurityGroupsRequest.setVpcId(response.getVpcId());
            ListSecurityGroupsResponse listSecurityGroupsResponse = null;
            try {
                listSecurityGroupsResponse = securityGroupGateway.listSecurityGroups(listSecurityGroupsRequest);
            } catch (Exception e) {
                log.error("[getCluster {}]: get SecurityGroup failed", clusterId);
            }
            if (listSecurityGroupsResponse != null) {
                for (int i = 0; i < listSecurityGroupsResponse.getSecurityGroups().size(); i++) {
                    if (response.getSecurityGroupId().equals(listSecurityGroupsResponse.getSecurityGroups().get(i).getId())) {
                        response.setSecurityGroupName(listSecurityGroupsResponse.getSecurityGroups().get(i).getName());
                    }
                }
            }
        }

        // 查询集群绑定的tag

        FullTagListRequest fullTagListRequest = new FullTagListRequest();
        List<String> regions = new ArrayList<>();
        regions.add(regionConfiguration.getCurrentRegion());
        fullTagListRequest.setRegions(regions);
        List<String> resourceIds = new ArrayList<>();
        resourceIds.add(clusterId);
        fullTagListRequest.setResourceIds(resourceIds);
        fullTagListRequest.setResourceUuids(resourceIds);
        List<String> serviceTypes = new ArrayList<>();
        serviceTypes.add("CHPC");
        fullTagListRequest.setServiceTypes(serviceTypes);
        try {
            TagAssociationFulls tagAssociationFulls = tagsGateway.listTags(fullTagListRequest);
            List<Tag> tags = new ArrayList<>();
            for (TagAssociationFull tagAssociationFull : tagAssociationFulls.getTagAssociationFulls()) {
                Tag tag = new Tag();
                tag.setTagKey(tagAssociationFull.getTagKey());
                tag.setTagValue(tagAssociationFull.getTagValue());
                tags.add(tag);
            }
            response.setTags(tags);
        } catch (Exception e) {
            log.error("[getCluster {}]: get tags failed, err {}", clusterId, e.getMessage());
        }
        log.debug("[getCluster {}]: userList: {}", clusterId, response.getUserList());
        return response;
    }

    /**
     * {@inheritDoc}
     * 根据当前用户的accountId查询所有集群，并对集群进行权限校验。
     * 如果用户没有权限，则会被过滤掉。
     * 如果用户有权限，则将集群信息转换成ClusterVO类型返回。
     * 支持批量鉴权，如果批量鉴权结果中存在用户无权限的集群，则会被过滤掉。
     * 如果clusterName不为空，则只返回名称包含clusterName的集群。
     *
     * @param clusterName 集群名称，可以为null或者空字符串，表示返回所有集群
     * @return 包含所有用户有权限的集群信息的列表，如果没有集群，则返回一个空列表
     * @throws RuntimeException 如果鉴权出现异常，则抛出RuntimeException异常
     */
    @Override
    @PermissionVerify(resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_CLUSTER_LIST, permissions = {
            PermissionConstant.CHPC_READ, PermissionConstant.CLUSTER_READONLY})
    public List<ClusterVO> getClusters(String clusterName) {
        List<Cluster> clusters = clusterDAOGateway.findByAccountIdAll(LogicUserService.getAccountId());
        List<Cluster> finalClusters = new ArrayList<>();

        // 获取批量鉴权结果
        Object aspectResult = AspectResultHolder.getAspectResult();
        AspectResultHolder.clear();
        if (aspectResult != null) {
            log.debug("Aspect result: " + aspectResult);
            // 过滤用户有权限的集群
            List<BatchVerifyResults.BatchVerifyResult> results = (List<BatchVerifyResults.BatchVerifyResult>) aspectResult;
            BatchVerifyResults.BatchVerifyResult result = results.get(0);
            if (result == null || CollectionUtils.isEmpty(result.getResult())) {
                log.debug("size of sub verify results is 0");
            } else if (clusters.size() != result.getResult().size()) {
                log.debug("size of iam result is not equal to cluster list");
            } else {
                List<VerifyResult> iamRes = result.getResult();
                for (int i = 0; i < iamRes.size(); i++) {
                    VerifyResult subResult = iamRes.get(i);
                    if (subResult != null) {
                        if (PermissionConstant.ALLOW_PERMISSION.equals(subResult.getEffect())) {
                            // 如果clusterName不为空，则只返回名称包含clusterName的集群
                            if (StringUtils.isNotEmpty(clusterName) && !clusters.get(i).getName().equals(clusterName)) {
                                continue;
                            }
                            finalClusters.add(clusters.get(i));

                        }
                    }
                }
            }
        } else {
            // 跳过鉴权
            // 遍历 clusters
            for (int i = 0; i < clusters.size(); i++) {
                // 如果clusterName不为空，则只返回名称包含clusterName的集群
                if (StringUtils.isNotEmpty(clusterName) && !clusters.get(i).getName().equals(clusterName)) {
                    continue;
                }
                finalClusters.add(clusters.get(i));
            }
        }

        // for (Cluster cluster : finalClusters) {
        // syncClusterStatus(cluster);
        // }
        syncClustersStatus(finalClusters);

        return finalClusters.stream().map(chpcClusterService::convertToClusterVO).collect(Collectors.toList());
    }

    @Override
    public ClusterResponse updateCluster(String clusterId, ClusterUpdateRequest request) {
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        syncClusterStatus(cluster);
        ClusterResponse response = chpcClusterService.updateCluster(clusterId, request);
        response.setStatus(cluster.getStatus());
        // bct获取信息补充clusterName
        response.setClusterName(cluster.getName());
        List<String> bctMsgs = new ArrayList<>();
        bctMsgs.add(String.format("集群名称%s（ID: %s）", cluster.getName(), clusterId));
        if (request.getForbidDelete() != null && request.getForbidDelete()) {
            bctMsgs.add("开启集群删除保护状态");
        } else if (request.getForbidDelete() != null) {
            bctMsgs.add("关闭集群删除保护状态");
        }
        if (StringUtils.isNotEmpty(request.getClusterDescription())) {
            bctMsgs.add(String.format("修改集群描述信息为: %s", request.getClusterDescription()));
        }
        if (StringUtils.isNotEmpty(request.getClusterName())) {
            bctMsgs.add(String.format("集群名称，从%s变为%s", cluster.getName(), request.getClusterName()));
        }
        if (request.getMaxCpus() != null) {
            bctMsgs.add(String.format("集群最大CPU数，从%d变为%d", cluster.getMaxCpus(), request.getMaxCpus()));
        }
        if (request.getMaxNodes() != null) {
            bctMsgs.add(String.format("集群最大节点数，从%d变为%d", cluster.getMaxNodes(), request.getMaxNodes()));
        }

        // 集群直接覆盖
        if (request.getTags() != null) {
            // 解绑tag
            try {
                List<com.baidu.bce.logical.tag.sdk.model.Tag> tags = new ArrayList<>();
                if (request.getTags().size() > 0) {
                    tags = BeanCopyUtil.copyListProperties(request.getTags(), com.baidu.bce.logical.tag.sdk.model.Tag::new);
                    tagsGateway.createTags(tags);
                }
                CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
                List<AssignResource> resources = new ArrayList<>();
                AssignResource resource = new AssignResource();
                resource.setServiceType("CHPC");
                resource.setResourceId(clusterId);
                resource.setResourceUuid(clusterId);
                resource.setTags(tags);
                resource.setRegion(regionConfiguration.getCurrentRegion());
                resource.setAssociationType("floating");
                resources.add(resource);
                createAndAssignTagRequest.setResources(resources);
                tagsGateway.createAndAssignTag(createAndAssignTagRequest);

                // 需要更新集群资源标签：节点/队列


                if (request.getAssociatedResources()) {

                    // 节点对应的tag
                    Map<String, List<com.baidu.bce.logical.tag.sdk.model.Tag>> instanceTags = new HashMap<>();
                    // instanceId和instanceUuid
                    Map<String, String> instanceUuids = new HashMap<>();
                    List<Instance> instances = instanceServiceImpl.getClusterInstances(cluster.getClusterId(), "", "", "", "", "", false);
                    FullTagListRequest fullTagListRequest = new FullTagListRequest();
                    List<String> regions = new ArrayList<>();
                    regions.add(regionConfiguration.getCurrentRegion());
                    fullTagListRequest.setRegions(regions);
                    List<String> resourceIds = new ArrayList<>();
                    List<String> resourceUuids = new ArrayList<>();
                    for (Instance instance : instances) {
                        if (InstanceAttribution.CLOUD.getType().equals(instance.getAttribution())) {
                            resourceIds.add(instance.getInstanceId());
                            resourceUuids.add(instance.getInstanceUuid());
                            instanceTags.put(instance.getInstanceId(), new ArrayList<>(tags));
                            instanceUuids.put(instance.getInstanceId(), instance.getInstanceUuid());
                        }
                    }
                    fullTagListRequest.setResourceIds(resourceIds);
                    fullTagListRequest.setResourceUuids(resourceUuids);
                    List<String> serviceTypes = new ArrayList<>();
                    serviceTypes.add("BCC");
                    fullTagListRequest.setServiceTypes(serviceTypes);
                    TagAssociationFulls tagAssociationFulls = null;
                    try {
                        tagAssociationFulls = tagsGateway.listTags(fullTagListRequest);
                    } catch (Exception e) {
                        log.error("[instances {}]: get tags failed, err {}", resourceIds, e.getMessage());
                    }

                    for (TagAssociationFull tagAssociationFull : tagAssociationFulls.getTagAssociationFulls()) {
                        if (instanceTags.containsKey(tagAssociationFull.getResourceId())) {
                            if (!containsKey(instanceTags.get(tagAssociationFull.getResourceId()), tagAssociationFull.getTagKey())) {
                                com.baidu.bce.logical.tag.sdk.model.Tag tag = new com.baidu.bce.logical.tag.sdk.model.Tag();
                                tag.setTagKey(tagAssociationFull.getTagKey());
                                tag.setTagValue(tagAssociationFull.getTagValue());
                                instanceTags.get(tagAssociationFull.getResourceId()).add(tag);
                            }
                        }
                    }


                    CreateAndAssignTagRequest instanceCreateAndAssignTagRequest = new CreateAndAssignTagRequest();
                    List<AssignResource> instanceResources = new ArrayList<>();
                    for (Map.Entry<String, List<com.baidu.bce.logical.tag.sdk.model.Tag>> instanceTagList : instanceTags.entrySet()) {
                        AssignResource instanceResource = new AssignResource();
                        instanceResource.setServiceType("BCC");
                        instanceResource.setResourceId(instanceTagList.getKey());
                        instanceResource.setResourceUuid(instanceUuids.get(instanceTagList.getKey()));
                        instanceResource.setTags(instanceTagList.getValue());
                        instanceResource.setRegion(regionConfiguration.getCurrentRegion());
                        instanceResource.setAssociationType("floating");
                        instanceResources.add(instanceResource);
                    }
                    instanceCreateAndAssignTagRequest.setResources(instanceResources);
                    tagsGateway.createAndAssignTag(instanceCreateAndAssignTagRequest);


                    // 队列关联标签
                    List<QueueVO> queues = queueServiceImpl.listQueues(clusterId);

                    for (QueueVO queueVO : queues) {
                        List<Tag> queueTags = tagsDAOGateway.findTags(getAccountId(), clusterId, "queue", null);
                        for (int i = 0; i < request.getTags().size(); i++) {
                            // 如果存在key,删除原来的key
                            if (containsTagKey(queueTags, request.getTags().get(i).getTagKey())) {
                                tagsDAOGateway.deleteByClusterIdAndKey(clusterId, "queue", request.getTags().get(i).getTagKey());
                            }
                            tagsDAOGateway.insert(LogicUserService.getAccountId(), clusterId, "queue", queueVO.getQueueName(),
                                    tags.get(i).getTagKey(), tags.get(i).getTagValue());
                        }
                    }

                    // 本地节点关联标签
                    for (Instance instance : instances) {
                        if (InstanceAttribution.LOCAL.getType().equals(instance.getAttribution())) {
                            List<Tag> localNodeTags = tagsDAOGateway.findTags(getAccountId(), clusterId, "localNode", null);
                            for (int i = 0; i < request.getTags().size(); i++) {
                                // 如果存在key,删除原来的key
                                if (containsTagKey(localNodeTags, request.getTags().get(i).getTagKey())) {
                                    tagsDAOGateway.deleteByClusterIdAndKey(clusterId, "localNode", request.getTags().get(i).getTagKey());
                                }
                                tagsDAOGateway.insert(LogicUserService.getAccountId(), clusterId, "localNode", instance.getHostName(),
                                        tags.get(i).getTagKey(), tags.get(i).getTagValue());

                            }
                        }
                    }

                }
            } catch (Exception e) {
                log.debug(
                        "unbind clustertags fail,clusterId:{} ,err:{}", clusterId, e.getMessage());
            }
        }
        response.setMessage(String.join("，", bctMsgs));
        return response;
    }

    private Boolean contains(List<Tag> tags, String tagKey, String tagValue) {
        for (Tag tag : tags) {
            if (tag.getTagKey().equals(tagKey) && tag.getTagValue().equals(tagValue)) {
                return true;
            }
        }
        return false;
    }


    private Boolean containsTagKey(List<Tag> tags, String tagKey) {
        for (Tag tag : tags) {
            if (tag.getTagKey().equals(tagKey)) {
                return true;
            }
        }
        return false;
    }

    private Boolean containsKey(List<com.baidu.bce.logical.tag.sdk.model.Tag> tags, String tagKey) {
        for (com.baidu.bce.logical.tag.sdk.model.Tag tag : tags) {
            if (tag.getTagKey().equals(tagKey)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public ClusterResponse resetPassword(String clusterId, String password) {

        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        syncClusterStatus(cluster);
        // oos执行passwd
        String aesPassword = AesEncryptUtil.encrypt(password);
        Map<String, Object> extraMap = new HashMap<>();
        // 密码原文
        extraMap.put(ChpcConstant.PASSWORD, password);
        // AES加密后，存入db
        extraMap.put(ChpcConstant.AESPASSWORD, aesPassword);
        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.RESET_PASSWORD_TASK,
                clusterId, "",
                TaskSourceType.RESET_PASSWORD_TO_CLUSTER.getTaskSourceType(),
                taskId, extraMap, null);

        // 修改管理节点和登录节点的状态
        List<Instance> instances = instanceDAOGateway.findByClusterId(cluster.getClusterId());
        for (Instance instance : instances) {
            if ("master".equals(instance.getNodeType())) {
                instanceService.updateStatusAndExecutionId(instance.getInstanceId(),
                        "", InstanceStatus.RESETING.nameLowerCase());
            }
            if ("login".equals(instance.getNodeType())) {
                instanceService.updateStatusAndExecutionId(instance.getInstanceId(),
                        "", InstanceStatus.RESETING.nameLowerCase());
            }
        }

        ClusterResponse clusterResponse = new ClusterResponse();
        clusterResponse.setClusterId(clusterId);
        clusterResponse.setClusterName(cluster.getName());
        // bct 操作详情
        String bctMsg = String.format("集群%s（ID: %s），重置了集群密码", cluster.getName(), clusterId);
        clusterResponse.setMessage(bctMsg);
        return clusterResponse;
    }

    @Override
    public ClusterResponse updateClusterPassword(String clusterId, String rsaPassword) {
        clusterDAOGateway.updateClusterPassword(clusterId, rsaPassword);
        ClusterResponse clusterResponse = new ClusterResponse();
        clusterResponse.setClusterId(clusterId);
        return clusterResponse;
    }

    /**
     * {@inheritDoc}
     * 实现了BaseService接口的heartbeat方法，用于处理集群心跳请求。
     * 首先校验签名是否正确，然后查询集群信息，校验集群心跳时间是否正确，最后根据伸缩信息增加或删除节点。
     *
     * @param request 包含集群心跳信息的ClusterHeartbeatRequest对象
     * @return 一个BaseResponse对象，表示处理结果
     * @throws BceException 如果签名不正确或者其他错误发生，抛出BceException异常
     */
    @Override
    public BaseResponse heartbeat(ClusterHeartbeatRequest request) {
        // 校验签名
        HeartbeatInfo heartbeatInfo = request.getHeartbeatInfo();
        String signature = request.getSignature();
        ObjectMapper mapper = new ObjectMapper();
        String hbStr;
        try {
            hbStr = mapper.writeValueAsString(heartbeatInfo);
        } catch (Exception e) {
            log.error("failed to writeValueAsString, error is: ", e);
            throw new BceException(e.getMessage());
        }
        String sigStr = "CHPC-HEARTBEAT-SIGNATURE:" + hbStr;
        MessageDigest md;
        try {
            md = MessageDigest.getInstance("MD5");
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalArgumentException(e);
        }
        byte[] sig = md.digest(sigStr.getBytes(StandardCharsets.UTF_8));
        StringBuilder builder = new StringBuilder();
        for (byte b : sig) {
            builder.append(String.format("%02x", b));
        }
        if (!builder.toString().equalsIgnoreCase(signature)) {
            log.warn("signature is not correct, str:{}, sig:{}, signature:{}",
                    sigStr, builder.toString(), signature);
            return new BaseResponse();
        }
        // 查询集群信息
        Cluster cluster = clusterDAOGateway.findByClusterId(
                heartbeatInfo.getClusterInfo().getClusterId(), null);
        if (cluster == null) {
            log.warn("no cluster found, clusterId:{}", heartbeatInfo.getClusterInfo().getClusterId());
            return new BaseResponse();
        }
        // 校验集群心跳时间
        if (cluster.getHeartbeat() >= heartbeatInfo.getTimestamp()) {
            log.warn("cluster heartbeat time is not correct, clusterId:{}, last heartbeat:{}, new heartbeat:{}",
                    cluster.getClusterId(), cluster.getHeartbeat(), heartbeatInfo.getTimestamp());
            return new BaseResponse();
        }
        // 更新集群心跳时间
        clusterDAOGateway.updateClusterHeartbeat(cluster.getClusterId(), heartbeatInfo.getTimestamp());
        // 根据自动伸缩信息增加或删除节点
        handleAutoScalingInfo(heartbeatInfo);
        return new BaseResponse();
    }

    /**
     * @param heartbeatInfo 心跳信息，包含clusterInfo和autoScalingInfo两个字段。
     * @throws Exception 如果在执行过程中出现异常，会抛出该异常。
     * @Description 处理心跳中的自动伸缩信息，包括获取队列、自动伸缩信息、加锁、执行自动伸缩任务、解锁等操作。
     * 如果自动伸缩信息不为空，则进行以下操作：
     * 1. 获取队列信息，判断是否允许自动伸缩。
     * 2. 获取自动伸缩信息，判断自动伸缩状态是否为正常。
     * 3. 生成自动伸缩锁key，并使用锁配置类进行加锁。
     * 4. 获取最新的自动伸缩信息，因为for循环执行时间不可控，需要防止AutoScaling被其他实例修改。
     * 5. 设置用户AccountId，并将用户token放入线程变量中。
     * 6.
     * 执行自动伸缩任务，包括构造ExecuteAutoScalingInfo对象，并调用iAutoScalingService.executeHeartbeatAutoScalingTask方法。
     * 7. 移除用户信息，并清空线程变量中的用户token。
     * 8. 解锁。
     * 9. 如果发生异常，记录日志。
     */
    private void handleAutoScalingInfo(HeartbeatInfo heartbeatInfo) {
        if (heartbeatInfo.getAutoScalingInfo() == null) {
            return;
        }
        try {
            for (ClusterHeartbeatRequest.AutoScalingInfo info : heartbeatInfo.getAutoScalingInfo()) {
                // 获取队列信息
                Queue queue = queueDAOGateway.getByName(heartbeatInfo.getClusterInfo().getClusterId(),
                        info.getQueueName());
                if (queue == null) {
                    log.warn("queue not found, clusterId:{}, queueName:{}",
                            heartbeatInfo.getClusterInfo().getClusterId(), info.getQueueName());
                    continue;
                }
                if (!queue.getIsAutoScale()) {
                    log.info("queue is not allowed autoScaling, clusterId:{}, queueName:{}",
                            heartbeatInfo.getClusterInfo().getClusterId(), info.getQueueName());
                    continue;
                }

                if (!queue.getHasAutoScale()) {
                    log.info("queue has no autoScaling, clusterId:{}, queueName:{}",
                            heartbeatInfo.getClusterInfo().getClusterId(), info.getQueueName());
                    continue;
                }

                // 获取自动伸缩信息
                AutoScaling autoScaling = autoScalingDAOGateway.getByClusterIdAndQueueId(
                        heartbeatInfo.getClusterInfo().getClusterId(),
                        queue.getQueueId(),
                        null);
                if (autoScaling == null) {
                    log.warn("no autoScaling found, clusterId:{}, queueName:{}",
                            heartbeatInfo.getClusterInfo().getClusterId(), info.getQueueName());
                    continue;
                }
                // 自动伸缩状态为正常才执行
                if (autoScaling.getStatus() != AutoScalingStatus.NORMAL) {
                    continue;
                }

                // 加锁
                String lockKey = generateAutoScalingLock(autoScaling);
                if (!lockConfig.tryLock(lockKey)) {
                    continue;
                }
                // 获得锁后，获取最新的AutoScaling信息，因为for循环执行时间不可控，需要防止AutoScaling被其他实例修改
                AutoScaling refreshedAutoScaling = autoScalingDAOGateway.getByClusterIdAndQueueId(
                        autoScaling.getClusterId(),
                        autoScaling.getQueueId(),
                        null);
                if (refreshedAutoScaling == null || refreshedAutoScaling.getStatus() != AutoScalingStatus.NORMAL) {
                    log.info("autoScaling is null or status is not normal, clusterId:{}, queueId:{}",
                            autoScaling.getClusterId(),
                            autoScaling.getQueueId());
                    continue;
                }
                autoScaling = refreshedAutoScaling;

                // 保存用户AccountId
                SchedulerThreadLocalHolder.setAccountId(autoScaling.getAccountId());
                schedulerUserConfig.setUserToken(autoScaling.getAccountId());

                // 执行自动伸缩任务
                try {
                    ExecuteAutoScalingInfo i = new ExecuteAutoScalingInfo();
                    i.setMessage(info.getMessage());
                    i.setQueueName(info.getQueueName());
                    i.setScaleCount(info.getScaleCount());
                    i.setShrinkCycle(info.getShrinkCycle());
                    if (info.getNodes() != null && !info.getNodes().isEmpty()) {
                        List<ExecuteAutoScalingInfo.Node> nodes = new ArrayList<>();
                        for (ClusterHeartbeatRequest.Node node : info.getNodes()) {
                            ExecuteAutoScalingInfo.Node n = new ExecuteAutoScalingInfo.Node();
                            n.setHostIP(node.getHostIP());
                            n.setStatus(node.getStatus());
                            n.setNodeID(node.getNodeID());
                            n.setNodeName(node.getNodeName());
                            n.setSpec(node.getSpec());
                            n.setQueue(node.getQueue());
                            nodes.add(n);
                        }
                        i.setNodes(nodes);
                    }

                    iAutoScalingService.executeHeartbeatAutoScalingTask(autoScaling, queue, i);
                } catch (Exception e) {
                    log.error("autoScaling failed to execute, asId: {}, exception: {}", autoScaling.getAsId(), e);
                }

                // 移除用户信息
                SchedulerThreadLocalHolder.clear();
                schedulerUserConfig.removeUserToken();

                // 解锁
                lockConfig.unLock(lockKey);

            }

        } catch (Exception e) {
            log.error("error in executing AutoScaling task", e);
        }
    }

    private String generateAutoScalingLock(AutoScaling autoScaling) {

        return String.format("AutoScaling-%s-%s-%s",
                autoScaling.getAsId(), autoScaling.getClusterId(), autoScaling.getQueueId());
    }

    /**
     * @Description 根据集群管理实例的状态，组装集群的状态。如果集群处于活跃状态，则需要检查管理实例的状态，如果管理实例已过期或者不是运行中、快照处理中、镜像处理中、安装中、充值中等状态，则将集群状态更新为错误状态。
     * @Param cluster 包含集群信息的对象，包括集群ID和当前状态。
     * @Return void 无返回值。
     */
    private void assembleClusterStatusByManagerInstance(Cluster cluster, Instance masterInstance) {

        Instance managerInstance;
        if (null == masterInstance) {
            managerInstance = instanceServiceImpl.getClusterManagerInstanceOrNull(cluster.getClusterId());
        } else {
            managerInstance = masterInstance;
        }
        if (managerInstance != null) {
            cluster.setSchedulerIp(managerInstance.getSchedulerIp());
            cluster.setSchedulerHost(managerInstance.getSchedulerHost());
        }
        if (ClusterStatus.ACTIVE.nameLowerCase().equalsIgnoreCase(cluster.getStatus())) {
            if (null == managerInstance) {
                log.error("no manager found for cluster:{}, change cluster status to error",
                        cluster.getClusterId());
                cluster.setStatus(ClusterStatus.ERROR.nameLowerCase());
                if (null == cluster.getErrorMessage() || "".equals(cluster.getErrorMessage())) {
                    cluster.setErrorMessage("no manager found for cluster: " + cluster.getClusterId());
                }
                return;
            }
            if (InstanceStatus.EXPIRED.nameLowerCase().equalsIgnoreCase(managerInstance.getStatus())) {
                log.error("change cluster status to expired, clusterId:{}, manager:{}",
                        cluster.getClusterId(),
                        managerInstance.getInstanceId());
                cluster.setStatus(ClusterStatus.EXPIRED.nameLowerCase());
            } else if (!InstanceStatus.RUNNING.nameLowerCase().equalsIgnoreCase(managerInstance.getStatus())
                    && !InstanceStatus.SNAPSHOTPROCESSING.nameLowerCase().equalsIgnoreCase(managerInstance.getStatus())
                    && !InstanceStatus.IMAGEPROCESSING.nameLowerCase().equalsIgnoreCase(managerInstance.getStatus())
                    && !InstanceStatus.INSTALLING.nameLowerCase().equalsIgnoreCase(managerInstance.getStatus())
                    && !InstanceStatus.RESETING.nameLowerCase().equalsIgnoreCase(managerInstance.getStatus())
                    && !InstanceStatus.RESETED.nameLowerCase().equalsIgnoreCase(managerInstance.getStatus())
                    && !InstanceStatus.RECHARGING.nameLowerCase().equalsIgnoreCase(managerInstance.getStatus())) {
                log.error("manager status is {}, change cluster status to error, clusterId:{}, manager:{}",
                        managerInstance.getStatus(),
                        cluster.getClusterId(),
                        managerInstance.getInstanceId());

                cluster.setStatus(ClusterStatus.ERROR.nameLowerCase());
                if (null == cluster.getErrorMessage() || "".equals(cluster.getErrorMessage())) {
                    cluster.setErrorMessage("manager status is:" + managerInstance.getStatus() + "so cluster: "
                            + cluster.getClusterId() + " status to error");
                }
            }

        }

    }

    // 根据心跳时间更新集群状态
    private void updateClusterStatusByHeartbeat(Cluster cluster) {
        // 兼容老集群，未更新过心跳时间的跳过检查
        if (cluster.getHeartbeat() == 0) {
            return;
        }
        if (ClusterStatus.ACTIVE.nameLowerCase().equalsIgnoreCase(cluster.getStatus())) {
            // 心跳时间超过10分钟且集群状态为 ACTIVE 时，修改集群状态为 ERROR
            if (Instant.now().getEpochSecond() - cluster.getHeartbeat() > 10 * 60) {
                log.info("heartbeat expired, change cluster status to ERROR, clusterId:{}, heartbeat:{}",
                        cluster.getClusterId(), cluster.getHeartbeat());
                cluster.setStatus(ClusterStatus.ERROR.nameLowerCase());
                if (cluster.getCreatedDone()) {
                    // 创建完成之后，心跳机制才能更新集群状态
                    clusterDAOGateway.updateClusterWithErrorMessage(cluster.getClusterId(),
                            ClusterStatus.ERROR.nameLowerCase(), ClusterErrorMessage.HEARTBEAT_EXPIRED);
                }
            }
        } else if (ClusterStatus.ERROR.nameLowerCase().equalsIgnoreCase(cluster.getStatus()) &&
                ClusterErrorMessage.HEARTBEAT_EXPIRED.equalsIgnoreCase(cluster.getErrorMessage())) {
            // 心跳时间不超过10分钟且集群状态为 ERROR 时，修改集群状态为 ACTIVE
            if (Instant.now().getEpochSecond() - cluster.getHeartbeat() <= 10 * 60) {
                log.info("heartbeat active, change cluster status to ACTIVE, clusterId:{}, heartbeat:{}",
                        cluster.getClusterId(), cluster.getHeartbeat());
                cluster.setStatus(ClusterStatus.ACTIVE.nameLowerCase());
                if (cluster.getCreatedDone()) {
                    // 创建完成之后，心跳机制才能更新集群状态
                    clusterDAOGateway.updateClusterWithErrorMessage(cluster.getClusterId(),
                            ClusterStatus.ACTIVE.nameLowerCase(), "");
                }
            }
        }
    }

    /**
     * 同步集群状态，包括更新集群状态和组装集群状态。
     *
     * @param cluster 集群对象
     */
    private void syncClusterStatus(Cluster cluster) {
        updateClusterStatusByHeartbeat(cluster);
        assembleClusterStatusByManagerInstance(cluster, null);
    }

    /**
     * @Description: 同步集群状态，包括更新集群状态、查询 master 节点并组装集群状态
     * @Param clusters list<Cluster> - 需要同步的集群列表
     * @Return void - 无返回值
     */
    private void syncClustersStatus(List<Cluster> clusters) {
        List<String> clusterIds = new ArrayList<>();
        for (Cluster cluster : clusters) {
            updateClusterStatusByHeartbeat(cluster);
            clusterIds.add(cluster.getClusterId());
        }
        // 查询所有的 master 节点
        LinkedHashMap<String, Instance> masterInstances = instanceServiceImpl.getClusterManagerInstances(clusterIds);
        if (masterInstances == null || masterInstances.isEmpty()) {
            return;
        }
        for (Cluster cluster : clusters) {
            Instance master = masterInstances.get(cluster.getClusterId());
            assembleClusterStatusByManagerInstance(cluster, master);
        }
    }
}