package com.baidu.bce.logic.chpc.scheduler.task;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.annotation.Resource;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.WorkflowRunStatus;
import com.baidu.bce.logic.chpc.gateway.WorkflowRunDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunResResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.WorkflowRunStatusResponse;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.model.WorkflowRun;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component(value = "run_workflow_task")
public class WorkflowRunTask extends AbstractSchedulerTask {

    @Resource
    WorkflowRunDAOGateway workflowRunDAOGateway;

    @Override
    Map<String, Object> executeTask(Task task, Cluster cluster, Queue queue) {
        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
        String runUuid = (String) extraMap.get(ChpcConstant.WORKFLOW_RUN_UUID);
        String runId = (String) extraMap.get(ChpcConstant.WORKFLOW_RUNID);

        log.debug("cluster: {}, workflow runUuid: {}", cluster.getClusterId(), runUuid);
        WorkflowRunStatusResponse workflowRunStatus = null;
        WorkflowRun workflowRun = workflowRunDAOGateway.getWorkflowRun(runId, getAccountId());
        Duration duration = Duration.between(workflowRun.getCreatedTime(), LocalDateTime.now());
        // 查询工作流执行状态接口
        try {
            workflowRunStatus = backendGateway.getWorkflowRunStatus(cluster.getClusterId(), runUuid);
        } catch (BceInternalResponseException e) {
            return checkOverDuration(extraMap, runId, duration);
        }
        if (workflowRunStatus == null) {
            return checkOverDuration(extraMap, runId, duration);
        }
        WorkflowRunResResponse workflowRunResResponse = backendGateway.getWorkflowRunRes(cluster.getClusterId(), runUuid);


        if (!workflowRun.getStatus().equals(workflowRunStatus.getStatus())) {
            // 只保留 UTF-8 字符集中的字符和中文
            Pattern pattern = Pattern.compile("[^\\u0000-\\uFFFF\\u4E00-\\u9FFF]+");
            Matcher matcher = pattern.matcher(JacksonUtil.toJson(workflowRunResResponse.getOutputs()));
            String outputs = matcher.replaceAll("");
            workflowRunDAOGateway.updateWorkflowRunStatus(runId, workflowRunStatus.getStatus(), outputs);
        }

        // 工作流run已经是终态
        if (WorkflowRunStatus.Succeeded.name().equals(workflowRunStatus.getStatus()) ||
                WorkflowRunStatus.Failed.name().equals(workflowRunStatus.getStatus()) ||
                WorkflowRunStatus.Aborted.name().equals(workflowRunStatus.getStatus())) {

            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
            return extraMap;

        }

        extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
        return extraMap;

    }

    @NotNull
    private Map<String, Object> checkOverDuration(Map<String, Object> extraMap, String runId, Duration duration) {
        if (duration.toMinutes() > 2) {
            // 访问异常,设置任务为failed
            workflowRunDAOGateway.updateWorkflowRunStatus(runId, WorkflowRunStatus.Failed.name(), null);
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
            return extraMap;
        } else {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
            return extraMap;
        }
    }
}
