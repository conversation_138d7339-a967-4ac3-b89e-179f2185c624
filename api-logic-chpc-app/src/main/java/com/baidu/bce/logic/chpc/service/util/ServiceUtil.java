package com.baidu.bce.logic.chpc.service.util;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.model.Cfs;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterInstanceForCreate;
import com.baidu.bce.logic.chpc.model.request.instance.InstanceAddRequest;
import com.baidu.bce.logic.chpc.model.response.cfs.CfsVO;
import com.baidu.bce.logic.chpc.model.response.queue.QueueVO;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceVO;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.baidu.bce.logic.core.util.UuidUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

import java.util.Collections;
import java.util.List;
import java.util.Random;

import org.apache.commons.lang3.StringUtils;

/**
 * @Author: lilu24
 * @Date: 2023-01-11
 */

@Slf4j
@Data
public class ServiceUtil {

    private static final int MAX_LENGTH = 63;
    private static final int RANDOM_CHAR_LENGTH = 8;
    private static final String ALLOWED_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789_-";
    private static final String RANDOM_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

    private static final Random RANDOM = new Random();

    public static InstanceVO convertToInstanceVO(Instance instance) {

        InstanceVO instanceVO = BeanCopyUtil.copyObject(instance, InstanceVO::new);
        instanceVO.setQueueName(instance.getQueueName());
        instanceVO.setNodeType(instance.getNodeType());
        instanceVO.setSpec(instance.getSpec());
        instanceVO.setSubnetId(instance.getSubnetId());

        return instanceVO;
    }

    public static QueueVO convertToGroupVO(Queue queue) {

        QueueVO queueVO = new QueueVO();
        queueVO.setQueueName(queue.getName());
        queueVO.setDescription(queue.getDescription());
        queueVO.setIsDefault(queue.getIsDefault());
        queueVO.setStatus(queue.getStatus());
        queueVO.setIsAutoScale(queue.getIsAutoScale());
        queueVO.setHasAutoScale(queue.getHasAutoScale());
        queueVO.setQueueId(queue.getQueueId());
        return queueVO;
    }


    public static CfsVO convertToCfsVO(Cfs cfs) {

        return BeanCopyUtil.copyObject(cfs, CfsVO::new);
    }

    public static com.baidu.bce.internalsdk.cos.model.ClusterCreateRequest convertToCosRequest(
            InstanceAddRequest request, Cluster cluster, Queue queue) {


        ClusterCreateRequest clusterCreateRequest = new ClusterCreateRequest();

        clusterCreateRequest.setClusterName(cluster.getName() + UuidUtil.generateShortUuid());
        clusterCreateRequest.setVpcId(cluster.getVpcId());
        clusterCreateRequest.setSchedulerType(cluster.getSchedulerType());
        clusterCreateRequest.setSecurityGroupId(cluster.getSecurityGroupId());
        clusterCreateRequest.setImageId(queue.getDefaultImageId());


        ClusterInstanceForCreate instance = new ClusterInstanceForCreate();
        instance.setPurchaseCount(request.getComputeNodeNum());
        instance.setRootDiskSizeInGb(request.getRootDiskSizeInGb());
        instance.setRootDiskStorageType(request.getRootDiskStorageType());
        instance.setCdsDisks(request.getCdsDisks());
        instance.setInstanceNodeType(InstanceNodeType.COMPUTE.getType());
        instance.setSubnetId(request.getSubnetId());
        instance.setSpec(request.getSpec());

        clusterCreateRequest.setClusterInstanceForCreates(Collections.singletonList(instance));

        return JacksonUtil.decode(JacksonUtil.toJson(clusterCreateRequest),
                com.baidu.bce.internalsdk.cos.model.ClusterCreateRequest.class);
    }

    public static boolean isClusterUsingSchedulerPlugin(ClusterDAOGateway clusterDAOGateway, String clusterId) {
        if (StringUtils.isEmpty(clusterId)) {
            return false;
        }
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        if (cluster == null) {
            return false;
        }
        if (cluster.getSchedulePlugin() == 0) {
            return false;
        }
        return true;
    }

    public static boolean isClusterHybrid(ClusterDAOGateway clusterDAOGateway, String clusterId) {
        if (StringUtils.isEmpty(clusterId)) {
            return false;
        }
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        if (cluster == null) {
            return false;
        }
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            return true;
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    public static List<String> castToList(Object obj) {
        if (obj instanceof List) {
            return (List<String>) obj;
        } else {
            log.warn("[AddNodeDebug] Cannot cast List<String>");
            return new java.util.ArrayList<>();
        }
    }

    public static List<Tag> castToTags(Object obj) {
        if (obj instanceof List) {
            return (List<Tag>) obj;
        } else {
            log.warn("[AddNodeDebug] Cannot cast List<String>");
            return new java.util.ArrayList<>();
        }
    }

    public static String generateCustomString(String clusterId, String queueName) {
        if (clusterId == null || queueName == null) {
            throw new IllegalArgumentException("Cluster ID and Queue Name cannot be null");
        }

        String baseString = clusterId + "-" + queueName;
        if (baseString.length() + RANDOM_CHAR_LENGTH > MAX_LENGTH) {
            throw new IllegalArgumentException("Combined length of Cluster Name and Queue Name is too long");
        }

        String randomChars = generateRandomChars(RANDOM_CHAR_LENGTH);
        String result = baseString + "-" + randomChars;

        if (!isValidString(result)) {
            throw new IllegalArgumentException("Generated string is not valid");
        }

        return result;
    }

    public static boolean isValidString(String str) {
        return str.length() >= 1 && str.length() <= MAX_LENGTH
                && Character.isLetterOrDigit(str.charAt(0))
                && str.chars().allMatch(ch -> ALLOWED_CHARS.indexOf(ch) >= 0);
    }

    private static String generateRandomChars(int length) {
        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            sb.append(RANDOM_CHARS.charAt(RANDOM.nextInt(RANDOM_CHARS.length())));
        }
        return sb.toString();
    }

}
