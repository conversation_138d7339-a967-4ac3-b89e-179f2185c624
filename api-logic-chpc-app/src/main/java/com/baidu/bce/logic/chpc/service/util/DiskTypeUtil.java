package com.baidu.bce.logic.chpc.service.util;


import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;


@Slf4j
@Component
public class DiskTypeUtil {

    /**
     * 将console的磁盘类型和后端磁盘类型做转换
     */
    public static String getDiskType(String diskType) {
        if ("premium_ssd".equals(diskType)) {
            return "cloud_hp1";
        } else if ("ssd".equals(diskType)) {
            return "hp1";
        } else if ("ENHANCED_SSD_PL1".equals(diskType)) {
            return "enhanced_ssd_pl1";
        }
        return diskType;
    }

    /**
     * 后端返回的磁盘类型转换为前端展示类型
     */
    public static String diskType2Console(String diskType) {
        if ("cloud_hp1".equals(diskType)) {
            return "premium_ssd";
        } else if ("hp1".equals(diskType)) {
            return "ssd";
        } else if ("enhanced_ssd_pl1".equals(diskType)) {
            return "ENHANCED_SSD_PL1";
        }
        return diskType;
    }
}
