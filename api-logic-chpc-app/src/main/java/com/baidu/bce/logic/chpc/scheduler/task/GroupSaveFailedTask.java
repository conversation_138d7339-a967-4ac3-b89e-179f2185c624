package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 * <p>
 * 节点创建失败后，会创建此任务
 */
@Slf4j
@Component(value = "group_save_failed_task")
public class GroupSaveFailedTask extends AbstractSchedulerTask {

    @Resource
    TagsDAOGateway tagsDAOGateway;

    @Override
    public Map<String, Object> executeTask(final Task task, final Cluster cluster, final Queue queue) {
        log.debug("[DeleteFailedTask] the cluster of {} is {}", cluster.getClusterId(), queue.getQueueId());

        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);

        log.debug("GroupSaveFailedTask task: {}", task);

        // 手动扩容或自动扩容会更新对列状态为waiting_to_join,创建失败后，需要更新对列状态为active
        if (extraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)
                || extraMap.containsKey(ChpcConstant.MANUAL_SCALING_FLAG)) {
            queueDAOGateway.updateStatus(queue.getQueueId(), QueueStatus.ACTIVE.nameLowerCase());
        } else {
            clusterService.updateClusterStatus(cluster.getClusterId(), ClusterStatus.CREATE_FAILED.nameLowerCase());
            queueDAOGateway.deleteWithStatus(queue.getQueueId(), QueueStatus.CREATE_FAILED);
            tagsDAOGateway.deleteByClusterId(cluster.getClusterId(), "queue");
            tagsDAOGateway.deleteByClusterId(cluster.getClusterId(), "localNode");

        }
        return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
    }


}
