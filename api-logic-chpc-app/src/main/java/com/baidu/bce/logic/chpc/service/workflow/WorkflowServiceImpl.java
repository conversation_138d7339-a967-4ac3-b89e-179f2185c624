package com.baidu.bce.logic.chpc.service.workflow;

import com.baidu.bce.logic.chpc.common.CromwellFileType;
import com.baidu.bce.logic.chpc.common.WorkflowRunStatus;
import com.baidu.bce.logic.chpc.cromwell.DepFile;
import com.baidu.bce.logic.chpc.cromwell.Generator;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowCreateRequest;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowParseRequest;
import com.baidu.bce.logic.chpc.cromwell.model.WorkflowUpdateRequest;
import com.baidu.bce.logic.chpc.gateway.WorkflowDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkflowRunDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkflowfileDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkspaceDAOGateway;
import com.baidu.bce.logic.chpc.model.Workflow;
import com.baidu.bce.logic.chpc.model.WorkflowRun;
import com.baidu.bce.logic.chpc.model.Workflowfile;
import com.baidu.bce.logic.chpc.model.Workspace;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowParse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowVO;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowParseResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowUpdateResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowGetResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowCreateResponse;
import com.baidu.bce.logic.chpc.model.response.workflow.WorkflowDeleteResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.NameisExistResponse;
import com.baidu.bce.logic.chpc.service.IWorkflowService;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.io.File;
import java.io.InputStreamReader;
import java.io.FileWriter;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.BufferedWriter;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class WorkflowServiceImpl implements IWorkflowService {

    @Resource
    WorkflowDAOGateway workflowDAOGateway;

    @Resource
    WorkspaceDAOGateway workspaceDAOGateway;

    @Resource
    WorkflowfileDAOGateway workflowfileDAOGateway;

    @Resource
    WorkflowRunDAOGateway workflowRunDAOGateway;

    @Resource
    private Generator workflowGenerator;

    @Override
    public WorkflowCreateResponse createWorkflow(WorkflowCreateRequest workflowCreateRequest) {
        String accountId = LogicUserService.getAccountId();

        Workspace workspace = workspaceDAOGateway.getWorkspace(workflowCreateRequest.getWorkspaceId(), accountId);
        if (workspace == null) {
            throw new CommonExceptions.RequestInvalidException("workspace not found!");
        }

        List<Workflow> getWorkflowList = workflowDAOGateway.getWorkflowByName(workspace.getWorkspaceId(), workflowCreateRequest.getName(), accountId);

        if (getWorkflowList != null && getWorkflowList.size() > 0) {
            throw new CommonExceptions.RequestInvalidException("工作流名称:" + workflowCreateRequest.getName() + " 已经存在");
        }

        Workflow workflow = workflowGenerator.genWorkflow(workflowCreateRequest);
        workflow.setAccountId(accountId);
        boolean res = workflowDAOGateway.createWorkflow(workflow);
        if (!res) {
            throw new CommonExceptions.RequestInvalidException("insert workflow into db failed!");
        }

        // 解析depFile
        List<Workflowfile> workflowfileList = workflowGenerator.genWorkflowfileList(workflow.getWorkflowId(), workflowCreateRequest);
        res = workflowfileDAOGateway.createWorkflowfile(workflowfileList);
        if (!res) {
            throw new CommonExceptions.RequestInvalidException("insert workflow into db failed!");
        }


        WorkflowCreateResponse workflowCreateResponse = new WorkflowCreateResponse();
        workflowCreateResponse.setName(workflow.getName());
        workflowCreateResponse.setDescription(workflow.getDescription());
        workflowCreateResponse.setLanguage(workflow.getLanguage());
        workflowCreateResponse.setLanguageVersion(workflow.getLanguageVersion());
        workflowCreateResponse.setWorkflowId(workflow.getWorkflowId());
        workflowCreateResponse.setVersion(workflow.getVersion());
        workflowCreateResponse.setIntroduction(workflow.getIntroduction());
        workflowCreateResponse.setDocument(workflow.getDocument());

        return workflowCreateResponse;
    }

    @Override
    public List<WorkflowVO> listWorkflow(String workspaceId, String name) {
        String accountId = LogicUserService.getAccountId();
        List<Workflow> workflowList = workflowDAOGateway.listWorkflow(workspaceId, name, accountId);
        List<WorkflowVO> workflowVOList = new ArrayList<>();
        for (int i = 0; i < workflowList.size(); i++) {
            WorkflowVO workflowVO = new WorkflowVO();
            workflowVO.setName(workflowList.get(i).getName());
            workflowVO.setWorkflowId(workflowList.get(i).getWorkflowId());
            workflowVO.setDescription(workflowList.get(i).getDescription());
            workflowVO.setLanguage(workflowList.get(i).getLanguage());
            workflowVO.setLanguageVersion(workflowList.get(i).getLanguageVersion());
            workflowVO.setVersion(workflowList.get(i).getVersion());
            workflowVO.setIntroduction(workflowList.get(i).getIntroduction());
            workflowVO.setDocument(workflowList.get(i).getDocument());
            workflowVO.setInputs(workflowList.get(i).getInputs());
            workflowVO.setUpdatedTime(workflowList.get(i).getUpdatedTime());
            workflowVO.setCreatedTime(workflowList.get(i).getCreatedTime());
            workflowVOList.add(workflowVO);
        }
        return workflowVOList;
    }

    @Override
    public WorkflowGetResponse getWorkflow(String workflowId, Long version) {
        String accountId = LogicUserService.getAccountId();
        Workflow workflow = null;
        if (version == null) {
            workflow = workflowDAOGateway.getWorkflow(workflowId, accountId);
        } else {
            workflow = workflowDAOGateway.getWorkflowByVersion(workflowId, accountId, version);
        }
        if (workflow == null) {
            throw new CommonExceptions.RequestInvalidException("workflow not found!");
        }
        List<Workflowfile> workflowfileList = workflowfileDAOGateway.getWorkflowfileByVersion(workflowId, workflow.getVersion());
        WorkflowGetResponse workflowGetResponse = new WorkflowGetResponse();
        workflowGetResponse.setName(workflow.getName());
        workflowGetResponse.setDescription(workflow.getDescription());
        workflowGetResponse.setVersion(workflow.getVersion());
        workflowGetResponse.setLanguage(workflow.getLanguage());
        workflowGetResponse.setLanguageVersion(workflow.getLanguageVersion());
        workflowGetResponse.setWorkspaceId(workflow.getWorkspaceId());
        workflowGetResponse.setIntroduction(workflow.getIntroduction());
        workflowGetResponse.setDocument(workflow.getDocument());
        workflowGetResponse.setInputs(workflow.getInputs());
        workflowGetResponse.setCreatedTime(workflow.getCreatedTime());
        workflowGetResponse.setUpdatedTime(workflow.getUpdatedTime());
        List<DepFile> depFiles = new ArrayList<>();
        for (Workflowfile workflowfile : workflowfileList) {
            if (CromwellFileType.MAIN_FILE.nameLowerCase().equals(workflowfile.getType())) {
                workflowGetResponse.setMainFilePath(workflowfile.getPath());
                workflowGetResponse.setMainFileContent(workflowfile.getContent());
            } else if (CromwellFileType.CONFIG_FILE.nameLowerCase().equals(workflowfile.getType())) {
                workflowGetResponse.setConfigFilePath(workflowfile.getPath());
                workflowGetResponse.setConfigFileContent(workflowfile.getContent());
            } else {
                DepFile depFile = new DepFile();
                depFile.setDepFilePath(workflowfile.getPath());
                depFile.setDepFileContent(workflowfile.getContent());
                depFiles.add(depFile);
            }
        }
        if (depFiles.size() > 0) {
            workflowGetResponse.setDepFiles(depFiles);
        }
        return workflowGetResponse;
    }

    @Override
    public WorkflowDeleteResponse deleteWorkflow(String workflowId) {
        String accountId = LogicUserService.getAccountId();
        // 非终态的工作流不允许删除
        List<WorkflowRun> workflowRunList = workflowRunDAOGateway.getWorkflowRunById(null, workflowId, "", null, accountId);
        for (WorkflowRun workflowRun : workflowRunList) {
            if (!(WorkflowRunStatus.Succeeded.name().equals(workflowRun.getStatus()) || WorkflowRunStatus.Failed.name().equals(workflowRun.getStatus())
                    || WorkflowRunStatus.Aborted.name().equals(workflowRun.getStatus()))) {
                throw new CommonExceptions.RequestInvalidException("Not final state, cannot be deleted");
            }
        }
        workflowDAOGateway.deleteWorkflow(workflowId, accountId);
        workflowfileDAOGateway.deleteWorkflowfile(workflowId);
        WorkflowDeleteResponse workflowDeleteResponse = new WorkflowDeleteResponse();
        workflowDeleteResponse.setWorkflowId(workflowId);
        return workflowDeleteResponse;
    }

    @Override
    public WorkflowUpdateResponse updateWorkflow(String workflowId, WorkflowUpdateRequest workflowUpdateRequest) {
        String accountId = LogicUserService.getAccountId();

        // 获取最新版本
        Workflow workflowDB = workflowDAOGateway.getWorkflow(workflowId, accountId);
        if (workflowDB == null) {
            throw new CommonExceptions.RequestInvalidException("No workflow found");
        }
        Workflow workflow = new Workflow();
        workflow.setWorkflowId(workflowId);
        workflow.setInputs(workflowUpdateRequest.getInputs());

        workflow.setWorkflowId(workflowDB.getWorkflowId());
        workflow.setWorkspaceId(workflowDB.getWorkspaceId());
        workflow.setName(workflowDB.getName());
        workflow.setDescription(workflowUpdateRequest.getDescription());
        workflow.setVersion(workflowDB.getVersion() + 1);
        workflow.setLanguage(workflowDB.getLanguage());
        workflow.setAccountId(accountId);
        workflow.setLanguageVersion(workflowDB.getLanguageVersion());
        workflow.setIntroduction(workflowUpdateRequest.getIntroduction());
        workflow.setDocument(workflowUpdateRequest.getDocument());
        if (workflowUpdateRequest.getInputs() != null && !"".equals(workflowUpdateRequest.getInputs())) {
            workflow.setInputs(workflowUpdateRequest.getInputs());
        }
        boolean res = workflowDAOGateway.createWorkflow(workflow);
        if (!res) {
            throw new CommonExceptions.RequestInvalidException("update workflow into db failed!");
        }

        // 解析depFile
        List<Workflowfile> workflowfileList = workflowGenerator.genUpdateWorkflowfileList(workflowId, workflowUpdateRequest, workflowDB.getVersion() + 1);
        if (workflowfileList.size() > 0) {
            res = workflowfileDAOGateway.createWorkflowfile(workflowfileList);
            if (!res) {
                throw new CommonExceptions.RequestInvalidException("insert workflowfile into db failed!");
            }
        }

        WorkflowUpdateResponse workflowUpdateResponse = new WorkflowUpdateResponse();
        workflowUpdateResponse.setName(workflowDB.getName());
        workflowUpdateResponse.setDescription(workflow.getDescription());
        workflowUpdateResponse.setVersion(workflow.getVersion());
        workflowUpdateResponse.setWorkflowId(workflowDB.getWorkflowId());
        return workflowUpdateResponse;
    }

    @Override
    public WorkflowParseResponse parseWorksflow(WorkflowParseRequest request) {

        WorkflowParseResponse workflowParseResponse = new WorkflowParseResponse();

        if (verification(request, workflowParseResponse)) {
            return workflowParseResponse;
        }

        String basePath = "/home/<USER>/bce-api-service/bce-api-modules/api-logic-chpc/parseWorksflow/";

        String uuid = UUID.randomUUID().toString();

        String parsePath = basePath + uuid + "/";
        File deleteFolder = new File(parsePath);

        File folder = new File(parsePath);

        // 检查文件夹是否存在
        if (!folder.exists()) {
            // 文件夹不存在，创建新文件夹
            boolean created = folder.mkdirs();
            if (!created) {
                throw new CommonExceptions.RequestInvalidException("parseWorkflow failed!");
            }
        }


        // 将转义字符替换为实际换行符和缩进
        String processedString = request.getMainFileContent().replace("\\n", "\n").replace("\\t", "\t");

        String filePath = request.getMainFilePath();

        // 将处理后的字符串写入文件
        if (filePath.startsWith(".")) {
            filePath = filePath.substring(1);
        }
        String mainFilePath = parsePath + filePath;
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(mainFilePath))) {
            writer.write(processedString);
        } catch (IOException e) {
            deleteFolder(deleteFolder);
            e.printStackTrace();
        }

        if (request.getDepFiles() != null) {
            for (int i = 0; i < request.getDepFiles().size(); i++) {
                // 将转义字符替换为实际换行符和缩进
                String depString = request.getDepFiles().get(i).getDepFileContent().replace("\\n", "\n").replace("\\t", "\t");


                // 将处理后的字符串写入文件
                filePath = request.getDepFiles().get(i).getDepFilePath();

                // 将处理后的字符串写入文件
                if (filePath.startsWith(".")) {
                    filePath = filePath.substring(1);
                }
                String tmpfilePath = parsePath + filePath;
                try (BufferedWriter writer = new BufferedWriter(new FileWriter(tmpfilePath))) {
                    writer.write(depString);
                } catch (IOException e) {
                    deleteFolder(deleteFolder);
                    e.printStackTrace();
                }
            }
        }

        // 获取input，并返回

        try {
            // 创建命令列表
            ProcessBuilder builder = new ProcessBuilder("java", "-jar",
                    "/home/<USER>/bce-api-service/bce-api-modules/api-logic-chpc/womtool-47.jar", "inputs", mainFilePath);

            // 启动进程并执行命令
            Process process = builder.start();
            int exitCode = process.waitFor();

            // 未通过校验
            if (exitCode != 0) {

                BufferedReader reader = new BufferedReader(new InputStreamReader(process.getErrorStream()));
                String line;
                StringBuilder output = new StringBuilder();
                while (reader.ready() && (line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
                workflowParseResponse.setMessage(output.toString());
                deleteFolder(deleteFolder);
                return workflowParseResponse;
            }

            // 通过校验，从进程的输入流读取输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            StringBuilder output = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                output.append(line);
            }
            Type type = new TypeToken<Map<String, String>>() {
            }.getType();

            Gson gson = new Gson();
            Map<String, String> jsonObject = gson.fromJson(output.toString(), type);
            List<WorkflowParse> workflowParseList = new ArrayList<>();
            // 遍历所有键值对
            for (Map.Entry<String, String> entry : jsonObject.entrySet()) {
                WorkflowParse workflowParse = new WorkflowParse();
                String key = entry.getKey();
                String beforeLastDot = "";
                String afterLastDot = "";
                int lastIndex = key.lastIndexOf('.');
                if (lastIndex != -1) { // 如果存在点号
                    beforeLastDot = key.substring(0, lastIndex); // 取得最后一个点号之前的部分
                    afterLastDot = key.substring(lastIndex + 1); // 取得最后一个点号之后的部分
                }
                workflowParse.setTaskName(beforeLastDot);
                workflowParse.setVariableName(afterLastDot);
                String value = entry.getValue();
                Pattern pattern = Pattern.compile("([a-zA-Z]+) \\(optional, default = (.+?)\\)");
                Matcher matcher = pattern.matcher(value);
                if (matcher.matches()) {
                    String dataType = matcher.group(1);
                    String defaultValue = matcher.group(2);
                    value = defaultValue.replaceAll("\\\"", "\"");
                    workflowParse.setVariableType(dataType);
                    workflowParse.setVariableValue(value);
                } else {
                    // 没有匹配说明只有变量，没有赋值
                    workflowParse.setVariableType(value);
                }
                workflowParseList.add(workflowParse);
            }
            workflowParseResponse.setInputs(workflowParseList);
        } catch (IOException | InterruptedException e) {
            deleteFolder(deleteFolder);
            e.printStackTrace();
        }

        deleteFolder(deleteFolder);
        return workflowParseResponse;
    }

    private boolean verification(WorkflowParseRequest request, WorkflowParseResponse workflowParseResponse) {
        String isRepeat = isRepeat(request);
        if (!"".equals(isRepeat)) {
            workflowParseResponse.setMessage(isRepeat + "文件名重复");
            return true;
        }
        if (request.getMainFileContent() == null || "".equals(request.getMainFileContent())) {
            workflowParseResponse.setMessage(request.getMainFilePath() + "  文件不能为空");
            return true;
        }else {
            Pattern pattern = Pattern.compile("[^\\u0000-\\uFFFF\\u4E00-\\u9FFF]+");
            Matcher matcher = pattern.matcher(request.getMainFileContent());

            if (matcher.find()) {
                StringBuilder invalidCharacters = new StringBuilder();
                do {
                    invalidCharacters.append(matcher.group());
                } while (matcher.find());
                workflowParseResponse.setMessage(request.getMainFilePath() + " 文件下存在非法字符" + invalidCharacters);
                return true;
            }
        }

        if (!request.getMainFilePath().matches("^\\.\\/[^\\/]+\\.wdl$")) {
            workflowParseResponse.setMessage(request.getMainFilePath() + " : 路径非法");
            return true;
        }


        if (request.getDepFiles() != null && request.getDepFiles().size() > 0) {
            for (int i = 0; i < request.getDepFiles().size(); i++) {
                if ("".equals(request.getDepFiles().get(i).getDepFileContent())) {
                    workflowParseResponse.setMessage(request.getDepFiles().get(i).getDepFilePath() + " 文件不能为空");
                    return true;
                } else if (!request.getDepFiles().get(i).getDepFilePath().matches("^\\.\\/[^\\/]+\\.wdl$")) {
                    workflowParseResponse.setMessage(request.getDepFiles().get(i).getDepFilePath() + " : 路径非法");
                    return true;
                }
                Pattern pattern = Pattern.compile("[^\\u0000-\\uFFFF\\u4E00-\\u9FFF]+");
                Matcher matcher = pattern.matcher(request.getDepFiles().get(i).getDepFileContent());
                if (matcher.find()) {
                    StringBuilder invalidCharacters = new StringBuilder();
                    do {
                        invalidCharacters.append(matcher.group());
                    } while (matcher.find());
                    workflowParseResponse.setMessage(request.getDepFiles().get(i).getDepFilePath() + " 文件下存在非法字符" + invalidCharacters);
                    return true;
                }

                if (matcher.find()) {
                    StringBuilder invalidCharacters = new StringBuilder();
                    do {
                        invalidCharacters.append(matcher.group());
                    } while (matcher.find());
                    workflowParseResponse.setMessage(request.getMainFilePath() + " 文件下存在非法字符" + invalidCharacters);
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public NameisExistResponse workflowNameisExist(String workspaceId, String workflowName) {
        String accountId = LogicUserService.getAccountId();
        NameisExistResponse nameisExistResponse = new NameisExistResponse();
        List<Workflow> getWorkflowList = workflowDAOGateway.getWorkflowByName(workspaceId, workflowName, accountId);
        if (getWorkflowList != null && getWorkflowList.size() > 0) {
            nameisExistResponse.setIsExist(true);
        } else {
            nameisExistResponse.setIsExist(false);
        }
        return nameisExistResponse;
    }

    public boolean deleteFolder(File folder) {
        if (folder.isDirectory()) {
            File[] files = folder.listFiles();
            if (files != null) {
                for (File file : files) {
                    deleteFolder(file);
                }
            }
        }
        return folder.delete();
    }

    public String isRepeat(WorkflowParseRequest request) {
        Set<String> set = new HashSet<>();
        set.add(request.getMainFilePath());
        if (request.getDepFiles() != null && request.getDepFiles().size() > 0) {
            for (int i = 0; i < request.getDepFiles().size(); i++) {
                if (set.contains(request.getDepFiles().get(i).getDepFilePath())) {
                    return request.getDepFiles().get(i).getDepFilePath();
                } else {
                    set.add(request.getDepFiles().get(i).getDepFilePath());
                }
            }
        }
        return "";
    }

}
