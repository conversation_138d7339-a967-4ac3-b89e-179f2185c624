package com.baidu.bce.logic.chpc.scheduler;

import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.logic.chpc.skyform.GetTenantResponse;
import com.baidu.bce.logic.chpc.skyform.ListJobsResponse;
import com.baidu.bce.logic.chpc.skyform.gateway.SkyFormGateway;
import com.baidu.bce.logic.chpc.billing.LegacyChargeDataRequest;
import com.baidu.bce.logic.chpc.billing.gateway.ResourceChargeGateway;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.SaasResourceType;
import com.baidu.bce.logic.chpc.config.LockConfig;
import com.baidu.bce.logic.chpc.config.SchedulerThreadLocalHolder;
import com.baidu.bce.logic.chpc.gateway.JobDataDAOGateway;
import com.baidu.bce.logic.chpc.gateway.OrderDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SaasResourceDAOGateway;
import com.baidu.bce.logic.chpc.model.JobData;
import com.baidu.bce.logic.chpc.model.Order;
import com.baidu.bce.logic.chpc.model.SaasResource;
import com.baidu.bce.logic.chpc.service.IBillingService;
import com.baidu.bce.logic.chpc.service.util.SchedulerUserConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

@Component
@Slf4j
public class SkyFormBillingTask {
    @Resource
    SkyFormGateway skyformGateway;

    @Resource
    OrderDAOGateway orderDAOGateway;

    @Resource
    JobDataDAOGateway jobDataDAOGateway;

    @Resource
    LockConfig lockConfig;

    @Resource
    ResourceChargeGateway resourceChargeGateway;

    @Resource
    IBillingService iBillingService;

    @Resource
    SaasResourceDAOGateway saasResourceDAOGateway;

    @Resource
    SchedulerUserConfig schedulerUserConfig;

    @Resource
    private ThreadPoolTaskExecutor skyformBillingTaskThreadPool;

    // 每个小时的第0分钟触发
    @Scheduled(cron = "0 0/60 * * * ? ")
    public void execSkyFormBillingTask() {

        String lockKey = generateSkyFormBillingLock();

        if (!lockConfig.tryLock(lockKey)) {
            log.debug("sky tryLock failed: {}", lockKey);
            return;
        }

        log.debug("success to get lock: {}", lockKey);
        List<Order> orderList = orderDAOGateway.findAll(SaasResourceType.CHPC.getName());

        for (Order order : orderList) {
            this.executeSkyformBilling(order);

        }

        // 移除用户信息
        SchedulerThreadLocalHolder.clear();
        schedulerUserConfig.removeUserToken();
        // 解锁
        lockConfig.unLock(lockKey);

    }

    private void executeSkyformBilling(Order order) {
        skyformBillingTaskThreadPool.execute(() -> {
            // 订单未创建完成
            if (!order.getStatus().equals(OrderStatus.CREATED.toString())) {
                return;
            }

            log.debug("chargeData orderId: {}", order.getOrderId());
            SaasResource saasResource = saasResourceDAOGateway.findByAccountId(order.getAccountId(), SaasResourceType.SKYFORM.getName());
            if (null == saasResource) {
                return;
            }


            // 保存用户AccountId
            SchedulerThreadLocalHolder.setAccountId(order.getAccountId());
            schedulerUserConfig.setUserToken(order.getAccountId());

            double standardSum = 0;

            // db中查询用户非终态的作业,从查询天云获取作业最新状态

            List<JobData> unfinishedTasks = jobDataDAOGateway.findUnfinishedTasks(order.getAccountId());

            // 再次查询任务
            for (JobData jobData : unfinishedTasks) {
                log.debug("skyform unfinishedTasks is {}", jobData.getJobId());
                ListJobsResponse listJobsResponse = skyformGateway.listJobById(jobData.getJobId());
                if (listJobsResponse.getRetObj() != null && listJobsResponse.getRetObj().getContent() != null && listJobsResponse.getRetObj().getContent().size() > 0) {
                    ListJobsResponse.Content content = listJobsResponse.getRetObj().getContent().get(0);
                    standardSum = getStandardSum(standardSum, jobData.getRunTime(), content);

                    // 更新到db中
                    jobDataDAOGateway.updateByJobId(jobData.getJobId(), content.getRunTime(), content.getJobStatus());
                }

            }


            SimpleDateFormat outputFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSZ");

            // 获取前一个小时的整点
            Calendar calendar = Calendar.getInstance();
            Date now = calendar.getTime();

            calendar.setTime(now);

            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);

            Date currentHour = calendar.getTime();

            calendar.add(Calendar.HOUR_OF_DAY, -1);
            Date previousHour = calendar.getTime();


            // 天云目前使用上海时区
            outputFormat.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai"));

            String beginSubmitTime = outputFormat.format(previousHour);

            String endSubmitTime = outputFormat.format(currentHour);

            GetTenantResponse tenantByTenantName = skyformGateway.getTenantByTenantName(order.getAccountId());

            if (null == tenantByTenantName.getRetObj()) {
                return;
            }

            // 前一个小时，新提交的作业列表
            ListJobsResponse listJobsResponse = skyformGateway.listJobs(tenantByTenantName.getRetObj().getUuid(), beginSubmitTime, endSubmitTime);

            // 计算用量，新增db记录

            if (listJobsResponse.getRetObj() != null && listJobsResponse.getRetObj().getContent() != null) {
                for (ListJobsResponse.Content content : listJobsResponse.getRetObj().getContent()) {
                    // runtime出现负值，说明任务异常，跳过
                    if (content.getRunTime().startsWith("-")) {
                        continue;
                    }

                    JobData jobData = new JobData();
                    jobData.setAccountId(order.getAccountId());
                    jobData.setPoolId(content.getPoolId());
                    jobData.setJobId(content.getJobId());
                    jobData.setJobStatus(content.getJobStatus());
                    jobData.setJobCpu(content.getRequestCpu());
                    jobData.setJobMemory(content.getRequestMem());
                    jobData.setRunTime(content.getRunTime());


                    Boolean success = jobDataDAOGateway.insert(jobData);

                    if (success) {
                        standardSum = getStandardSum(standardSum, "0", content);
                    }
                }
            }
            long chargeDataTime = previousHour.getTime() / 1000;
            long orderUpdatedTime = order.getUpdatedTime().plusHours(8).atZone(ZoneId.of("Asia/Shanghai")).toInstant().getEpochSecond();
            // 上报时间戳必须大于资源创建时间
            if (chargeDataTime < orderUpdatedTime) {
                chargeDataTime = orderUpdatedTime;
            }
            // 统计用量，上报计费
            if (standardSum != 0) {
                LegacyChargeDataRequest legacyChargeDataRequest = iBillingService.makeChargeData(order.getAccountId(), "CHPC", "standard",
                        saasResource.getResourceUuid(), String.valueOf(standardSum), "Count", chargeDataTime);
                log.debug("skyform chargeDataRequest is {}", legacyChargeDataRequest.toString());
                resourceChargeGateway.charge(legacyChargeDataRequest);
            }
        });
    }

    private double getStandardSum(double standardSum, String runTime, ListJobsResponse.Content content) {
        long increaseRunTime = calculateDifference(content.getRunTime(), runTime);
        int requestCpu = 0;
        if (content.getRequestCpu() != null) {
            requestCpu = Integer.parseInt(content.getRequestCpu());
        }
        double requestMemory = 0.0;
        if (content.getRequestMem() != null) {
            if (content.getRequestMem().endsWith("M")) {
                String requestMem = (content.getRequestMem().substring(0, (content.getRequestMem().length() - 1)));
                requestMemory = Double.parseDouble(requestMem) / 1000;
            } else if (content.getRequestMem().endsWith("G")) {
                String requestMem = (content.getRequestMem().substring(0, (content.getRequestMem().length() - 1)));
                requestMemory = Double.parseDouble(requestMem);
            } else {
                requestMemory = Double.parseDouble(content.getRequestMem()) / 1000 / 1000;
            }
        }

        requestMemory = Math.ceil(requestMemory / ChpcConstant.NUCLEAR_STORAGE_RATIO);

        long standard = Math.max(requestCpu, (int) requestMemory);

        standardSum += (double) standard * increaseRunTime / (60 * 60);
        return standardSum;
    }


    private String generateSkyFormBillingLock() {

        return String.format("SkyFormBilling-lock");
    }

    private long calculateDifference(String str1, String str2) {

        if (str1.equals(str2)) {
            return 0;
        }
        long num1 = Long.parseLong(str1);
        long num2 = Long.parseLong(str2);

        long difference = num1 - num2;

        return difference;
    }

}
