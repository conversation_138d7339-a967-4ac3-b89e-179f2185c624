package com.baidu.bce.logic.chpc.scheduler.task;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterSchedulerType;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ComputeNode;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;

import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Slf4j
@Component(value = "group_join_task")
public class GroupJoinTask extends AbstractSchedulerTask {

    @Override
    public Map<String, Object> executeTask(final Task task, final Cluster cluster, final Queue queue) {

        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);

        if (StringUtil.isEmpty(task.getCosStackId())) {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            log.debug("[AddNodeDebug] the task is invalid:{} with empty stackId, clusterId:{}", task,
                    cluster.getClusterId());
            return extraMap;
        }
        log.debug("[AddNodeDebug-stackId:{}] start to execute group join task", task.getCosStackId());

        List<Instance> instances = instanceService.findByCosStackId(cluster.getClusterId(), queue.getQueueId(),
                task.getCosStackId());

        if (CollectionUtils.isEmpty(instances)) {
            log.debug(
                    "[AddNodeDebug] the task is invalid:{} with empty instances, stackId:{}, clusterId:{}, queueId:{}, queueName:{}",
                    task, task.getCosStackId(), cluster.getClusterId(), queue.getQueueId(), queue.getName());
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            return extraMap;
        }

        log.debug(
                "[AddNodeDebug] start to execute group join task, stackId:{}, clusterId:{}, queueId:{}, queueName:{}, schedulerType:{}",
                task.getCosStackId(), cluster.getClusterId(), queue.getQueueId(), queue.getName(),
                cluster.getSchedulerType());

        // sag类型的调度集群需要先向master节点注册信息
        if (ClusterSchedulerType.SGE.getName().equalsIgnoreCase(cluster.getSchedulerType())) {
            this.registerInfoToSgeCluster(cluster, queue, instances);
        }
        // pbs类型的调度集群需要先向master节点注册信息
        if (ClusterSchedulerType.PBS.getName().equalsIgnoreCase(cluster.getSchedulerType()) ||
                ClusterSchedulerType.OPENPBS.getName().equalsIgnoreCase(cluster.getSchedulerType())) {
            this.registerInfoToPbsCluster(cluster, queue, instances);
        }

        if (!task.getCosStackId().startsWith("st-exist-") &&
                ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            if (ClusterSchedulerType.PBS.getName().equalsIgnoreCase(cluster.getSchedulerType()) ||
                    ClusterSchedulerType.OPENPBS.getName().equalsIgnoreCase(cluster.getSchedulerType())) {

                log.debug(
                        "[AddNodeDebug] start to fill extraMap in join task, stackId:{}, clusterId:{}, queueId:{}, queueName:{}, schedulerType:{}",
                        task.getCosStackId(), cluster.getClusterId(), queue.getQueueId(), queue.getName(),
                        cluster.getSchedulerType());
                extraMap.put(ChpcConstant.QUEUE_NAME, queue.getName());
                List<String> instanceHostnames = new ArrayList<>();
                List<String> instanceSpecs = new ArrayList<>();
                for (Instance instance : instances) {
                    if (!StringUtil.isEmpty(instance.getHostName()) && !StringUtil.isEmpty(instance.getSpec())) {
                        instanceHostnames.add(instance.getHostName());
                        instanceSpecs.add(instance.getSpec());
                    }
                }
                extraMap.put(ChpcConstant.INSTANCE_HOSTNAME_LIST, instanceHostnames);
                extraMap.put(ChpcConstant.INSTANCE_SPEC_LIST, instanceSpecs);
            }
        }

        log.debug("[AddNodeDebug-stackId:{}] finish group join task, clusterId:{}", task.getCosStackId(),
                cluster.getClusterId());

        return extraMap;
    }

    private void registerInfoToSgeCluster(Cluster cluster, Queue queue, List<Instance> instances) {

        for (Instance instance : instances) {
            ComputeNode computeNode = new ComputeNode();

            computeNode.setHostIp(instance.getPrivateIp());
            computeNode.setNodeId(instance.getInstanceId());
            computeNode.setHostName(instance.getHostName());
            computeNode.setSpec(instance.getSpec());
            computeNode.setQueueName(queue.getName());

            backendGateway.addNode(cluster.getClusterId(), queue.getName(), computeNode);
        }
    }

    private void registerInfoToPbsCluster(Cluster cluster, Queue queue, List<Instance> instances) {

        log.debug("[AddNodeDebug] start to register node to pbs cluster, clusterId:{}, queueId:{}, queueName:{}",
                cluster.getClusterId(), queue.getQueueId(), queue.getName());

        for (Instance instance : instances) {
            BackendActionProxyResponse resp = null;
            try {
                log.debug(
                        "[AddNodeDebug] start to register cloud node, clusterId:{}, instanceId:{}, instanceIp:{}, instanceHost:{}, instanceSpec:{}",
                        cluster.getClusterId(), instance.getInstanceId(), instance.getPrivateIp(),
                        instance.getHostName(), instance.getSpec());
                resp = backendGateway.actionProxy(cluster.getClusterId(), "node_register",
                        String.format("--host=%s", instance.getHostName()));
                if (resp.getCode() != 200) {
                    log.debug("[AddNodeDebug] register cloud node failed, clusterId:{} errmsg:{}",
                            cluster.getClusterId(), resp.getMessage());
                    throw new CommonException.RelatedServiceException(
                            "register cloud node failed, " + resp.getMessage(), "scheduler plugin");
                }
            } catch (WebClientResponseException e) {
                log.debug("[AddNodeDebug] register cloud node failed, clusterId:{} errmsg:{}", cluster.getClusterId(),
                        e.getResponseBodyAsString());
                if (e.getResponseBodyAsString().contains("already exist")) {
                    log.debug("[AddNodeDebug] cloud node already exist, clusterId:{}", cluster.getClusterId());
                    continue;
                }
                if (!e.getResponseBodyAsString().contains("has no node list")) {
                    throw new CommonException.RelatedServiceException(
                            "register cloud node failed, " + e.getResponseBodyAsString(), "scheduler plugin");
                }
            } catch (Exception e) {
                log.debug("[AddNodeDebug] register cloud node failed, clusterId:{} errmsg:{}", cluster.getClusterId(),
                        e.getMessage());
                if (e.getMessage().contains("already exist")) {
                    log.debug("[AddNodeDebug] cloud node already exist, clusterId:{}", cluster.getClusterId());
                    continue;
                }
                if (!e.getMessage().contains("has no node list")) {
                    throw new CommonException.RelatedServiceException(
                            "register cloud node failed, " + e.getMessage(), "scheduler plugin");
                }
            }

            try {
                resp = backendGateway.actionProxy(cluster.getClusterId(), "node_create",
                        String.format("--host=%s --queue=%s --spec=%s", instance.getHostName(), queue.getName(),
                                instance.getSpec()));
                if (resp.getCode() != 200) {
                    log.debug("[AddNodeDebug] create cloud node failed, clusterId:{} errmsg:{}",
                            cluster.getClusterId(),
                            resp.getMessage());
                    throw new CommonException.RelatedServiceException(
                            "create cloud node failed, " + resp.getMessage(), "scheduler plugin");
                }
            } catch (WebClientResponseException e) {
                log.debug("[AddNodeDebug] create cloud node failed, clusterId:{} errmsg:{}", cluster.getClusterId(),
                        e.getResponseBodyAsString());
                if (e.getResponseBodyAsString().contains("node exist")) {
                    log.debug("[AddNodeDebug] cloud node node exist, clusterId:{}", cluster.getClusterId());
                    continue;
                }
                throw new CommonException.RelatedServiceException(
                        "create cloud node failed, " + e.getResponseBodyAsString(), "scheduler plugin");
            } catch (Exception e) {
                log.debug("[AddNodeDebug] create cloud node failed, clusterId:{} errmsg:{}", cluster.getClusterId(),
                        e.getMessage());
                if (e.getMessage().contains("node exist")) {
                    log.debug("[AddNodeDebug] cloud node node exist, clusterId:{}", cluster.getClusterId());
                    continue;
                }
                throw new CommonException.RelatedServiceException(
                        "create cloud node failed, " + e.getMessage(), "scheduler plugin");
            }
        }

        log.debug("[AddNodeDebug] finish to register node to pbs cluster, clusterId:{}, queueId:{}, queueName:{}",
                cluster.getClusterId(), queue.getQueueId(), queue.getName());

    }

    @Override
    public void afterExecuteTask(final Task task, final Map<String, Object> paraMap) {

        paraMap.put(ChpcConstant.SOURCE_TASK_ID, task.getTaskId());
        taskService.insert(
                TaskType.GROUP_STARTED_TASK,
                task.getClusterId(),
                task.getQueueId(),
                TaskSourceType.ADD_INSTANCE_TO_GROUP.getTaskSourceType(),
                task.getTaskUuid(),
                paraMap,
                task.getCosStackId());

        log.debug("[AddNodeDebug-stackId:{}] insert group started task", task.getCosStackId());
    }
}
