package com.baidu.bce.logic.chpc.service.util;

import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import org.springframework.context.annotation.Configuration;


@Configuration
public class SchedulerUserConfig {


    public void setUserToken(String accountId) {
        Token.TokenResult.User.Domain domain = new Token.TokenResult.User.Domain();
        domain.setId(accountId);

        Token.TokenResult.User user = new Token.TokenResult.User();
        user.setId(accountId);
        user.setDomain(domain);

        Token.TokenResult token = new Token.TokenResult();
        token.setUser(user);
        LogicUserService.setSubjectToken(token);
    }

    public void removeUserToken() {
        LogicUserService.removeSubjectToken();
    }


}
