package com.baidu.bce.logic.chpc.scheduler;

import java.util.List;
import java.util.Map;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterOosTaskStatus;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.config.SchedulerThreadLocalHolder;
import com.baidu.bce.logic.chpc.domainservice.ClusterService;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.oos.GetOosExecutionResponse;
import com.baidu.bce.logic.chpc.oos.gateway.OosGateway;
import com.baidu.bce.logic.chpc.service.util.SchedulerUserConfig;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class OosAsyncTask {

    @Resource
    private TaskService taskService;

    @Resource
    private OosGateway oosGateway;

    @Resource
    protected InstanceService instanceService;

    @Resource
    private InstanceDAOGateway instanceDAOGateway;

    @Resource
    SchedulerUserConfig schedulerUserConfig;

    @Resource
    private ClusterService clusterService;

    private Integer maxRetryTimes = 7;

    @Scheduled(fixedDelay = 5 * 1000)
    public void execOosAsyncTask() {

        try {
            List<Task> tasks =
                taskService.getByTypeAndStatus(TaskType.OOS_ASYNC_TASK.getTaskType(), TaskStatus.PROCESSING.getValue());

            for (Task task : tasks) {
                Cluster cluster = clusterService.findByAll(task.getClusterId());
                // 保存用户AccountId
                SchedulerThreadLocalHolder.setAccountId(cluster.getAccountId());
                schedulerUserConfig.setUserToken(cluster.getAccountId());
                this.executeOosAsyncTask(task);
                // 移除用户信息
                SchedulerThreadLocalHolder.clear();
                schedulerUserConfig.removeUserToken();
            }
        } catch (Exception e) {
            log.debug("failed to run scheduled cluster failed task, exception is ", e);
        }
    }

    private void executeOosAsyncTask(Task task) {
        try {
            // 获取OOS执行ID
            Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
            String oosExecutionId = (String) extraMap.get(ChpcConstant.OOS_EXECUTION_ID);
            String instanceId = (String) extraMap.get(ChpcConstant.INSTANCE_ID);
            Integer retryTimes = (Integer) extraMap.get(ChpcConstant.TASK_RETRY_TIMES);
            Instance instance = instanceService.findBy(instanceId);
            GetOosExecutionResponse.Result response = new GetOosExecutionResponse.Result();
            try {
                // 查询OOS执行任务
                response = oosGateway.getExecutionById(oosExecutionId);
                log.debug(
                    "[OOS_ASYNC_TASK] oos response, id :{}, status:{}, output:{}",
                    oosExecutionId,
                    response.getState(),
                    response.getReason());
            } catch (WebClientResponseException e) {
                log.warn(
                    "[OOS_ASYNC_TASK] failed to get oos execution by id: {}, exception is {}",
                    oosExecutionId,
                    e.getResponseBodyAsString());
                retryTimes++;
                if (retryTimes > maxRetryTimes) {
                    // 更新节点状态
                    instance.setStatus(InstanceStatus.RUNNING.nameLowerCase());
                    List<String> statusList = List.of(InstanceStatus.OPERATING.nameLowerCase());
                    instanceDAOGateway.updateByStatus(instance, statusList);
                    // 更新任务状态
                    taskService.updateStatus(task.getTaskId(), TaskStatus.TIMEOUT.getValue());
                } else {
                    extraMap.put(ChpcConstant.TASK_RETRY_TIMES, retryTimes);
                    taskService.updateExtra(task.getTaskId(), extraMap);
                }
                return;
            } catch (Exception e) {
                log.warn(
                    "[OOS_ASYNC_TASK] failed to get oos execution by id: {}, exception is {}",
                    oosExecutionId,
                    e.getMessage());
                retryTimes++;
                if (retryTimes > maxRetryTimes) {
                    // 更新节点状态
                    instance.setStatus(InstanceStatus.RUNNING.nameLowerCase());
                    List<String> statusList = List.of(InstanceStatus.OPERATING.nameLowerCase());
                    instanceDAOGateway.updateByStatus(instance, statusList);
                    // 更新任务状态
                    taskService.updateStatus(task.getTaskId(), TaskStatus.TIMEOUT.getValue());
                } else {
                    extraMap.put(ChpcConstant.TASK_RETRY_TIMES, retryTimes);
                    taskService.updateExtra(task.getTaskId(), extraMap);
                }
                return;
            }
            // 判断OOS执行状态
            if (ClusterOosTaskStatus.SUCCESS.name().equalsIgnoreCase(response.getState())) {
                // 更新节点状态
                instance.setStatus(InstanceStatus.RUNNING.nameLowerCase());
                List<String> statusList = List.of(InstanceStatus.OPERATING.nameLowerCase());
                instanceDAOGateway.updateByStatus(instance, statusList);
                // 更新任务状态
                taskService.updateStatus(task.getTaskId(), TaskStatus.SUCCEED.getValue());
            } else if (ClusterOosTaskStatus.FAILED.name().equalsIgnoreCase(response.getState())) {
                // 更新节点状态
                instance.setStatus(InstanceStatus.RUNNING.nameLowerCase());
                List<String> statusList = List.of(InstanceStatus.OPERATING.nameLowerCase());
                instanceDAOGateway.updateByStatus(instance, statusList);
                // 更新任务状态
                taskService.updateStatus(task.getTaskId(), TaskStatus.FAILED.getValue());
            } else {
                retryTimes++;
                if (retryTimes > maxRetryTimes) {
                    // 更新节点状态
                    instance.setStatus(InstanceStatus.RUNNING.nameLowerCase());
                    List<String> statusList = List.of(InstanceStatus.OPERATING.nameLowerCase());
                    instanceDAOGateway.updateByStatus(instance, statusList);
                    // 更新任务状态
                    taskService.updateStatus(task.getTaskId(), TaskStatus.TIMEOUT.getValue());
                } else {
                    extraMap.put(ChpcConstant.TASK_RETRY_TIMES, retryTimes);
                    taskService.updateExtra(task.getTaskId(), extraMap);
                }
            }
        } catch (Exception e) {
            log.error("failed to execute task: {}, exception is {}", task, e);
        }
    }
}
