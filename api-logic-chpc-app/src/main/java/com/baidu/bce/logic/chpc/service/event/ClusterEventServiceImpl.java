package com.baidu.bce.logic.chpc.service.event;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

import java.net.HttpURLConnection;
import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.EventType;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.domainservice.ClusterService;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterEventDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionScriptResponse;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Event;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.response.event.ClusterEvent;
import com.baidu.bce.logic.chpc.model.response.event.ClusterEventList;
import com.baidu.bce.logic.chpc.model.scheduler.SchedulerCommonResponse;
import com.baidu.bce.logic.chpc.scheduler.model.SharedStorage;
import com.baidu.bce.logic.chpc.service.IClusterEventService;
import com.baidu.bce.logic.chpc.sheduler.MountConfig;
import com.baidu.bce.logic.chpc.sheduler.MountConfigUpdateRequest;
import com.baidu.bce.logic.chpc.sheduler.SchedulerConfigUpdateRequest;
import com.fasterxml.jackson.core.io.JsonStringEncoder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class ClusterEventServiceImpl implements IClusterEventService {
    @Resource
    ClusterEventDAOGateway clusterEventDAOGateway;

    @Resource
    BackendGateway backendGateway;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    CfsDAOGateway cfsDAOGateway;

    @Resource
    private InstanceService instanceService;

    @Resource
    private ClusterService clusterService;

    @Resource
    private InstanceDAOGateway instanceDAOGateway;

    @Override
    public ClusterEventList getClusterEvent(String clusterId) {
        List<Event> eventList = clusterEventDAOGateway.findByClusterId(clusterId);
        ClusterEventList clusterEventListRes = new ClusterEventList();
        List<ClusterEvent> clusterEventList = new ArrayList<>();
        for (int i = 0; i < eventList.size(); i++) {
            ClusterEvent clusterEvent = new ClusterEvent();
            clusterEvent.setName(eventList.get(i).getName());
            clusterEvent.setCreatedTime(eventList.get(i).getCreatedTime());
            clusterEvent.setUpdatedTime(eventList.get(i).getUpdatedTime());
            clusterEvent.setStatus(eventList.get(i).getStatus());
            clusterEvent.setErrMsg(eventList.get(i).getErrMsg());
            clusterEventList.add(clusterEvent);
        }
        clusterEventListRes.setEventList(clusterEventList);
        clusterEventListRes.setClusterId(clusterId);
        return clusterEventListRes;
    }

    /**
     * {@inheritDoc}
     * 修改集群的调度器配置，包括调度器的ip和host。如果调度器的ip或host发生变化，则会先调用插件设置并检查调度器服务的ip和host，然后更新代理节点的调度器ip和调度器host，最后检查调度器服务配置和代理节点权限。
     * 如果检查失败，则更新集群状态为待授权状态，并抛出相应的异常。否则，更新集群状态为活跃状态。
     *
     * @param clusterId 集群ID
     * @param request   调度器配置更新请求，包括调度器的ip和host
     * @return SchedulerCommonResponse 调度器公共响应，包括集群ID
     * @throws CommonException.RelatedServiceException 如果调度器服务检查失败或者更新集群状态失败，抛出相应的异常
     */
    @Override
    public SchedulerCommonResponse modifyClusterSchedulerConfig(String clusterId,
            SchedulerConfigUpdateRequest request) {

        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        preCheck(cluster);

        Instance master = instanceDAOGateway.findMasterInstance(clusterId);

        if (master == null) {
            // 没有master节点
            clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SCHEDULER, EventType.FAILED,
                    "no manager found for cluster: " + cluster.getClusterId());
            throw new CommonException.RelatedServiceException(
                    "no manager found for cluster: ", cluster.getClusterId());
        }

        String arguments = request.getSchedulerIp() + " " + request.getSchedulerHost();

        // 调度器的ip和host变化
        if (!(master.getSchedulerIp().equals(request.getSchedulerIp()))
                || !(master.getSchedulerHost().equals(request.getSchedulerHost()))) {

            // 调用插件设置并检查调度器服务的ip和host
            BackendActionScriptResponse resp = backendGateway.actionScript(clusterId, EventType.ADD_SCHEDULER_INFO,
                    arguments);
            if (resp.getCode() != HttpURLConnection.HTTP_OK) {
                clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SCHEDULER, EventType.FAILED,
                        resp.getMessage());
                throw new CommonException.RelatedServiceException(
                        "add_scheduler_info failed, " + resp.getMessage(), "scheduler plugin");
            }
            // 更新代理节点的调度器ip和调度器host
            instanceService.updateSchedulerConfig(clusterId, InstanceNodeType.MASTER.getType(),
                    request.getSchedulerIp(), request.getSchedulerHost());
        }

        // 检查调度器服务配置
        BackendActionScriptResponse resp = backendGateway.actionScript(cluster.getClusterId(),
                EventType.CHECK_SCHEDULER_INFO, arguments);
        if (resp.getCode() != HttpURLConnection.HTTP_OK) {
            clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SCHEDULER, EventType.FAILED,
                    resp.getMessage());
            // 如果 resp.getMessage() 中包含 scheduler is not available，则为 ip 设置错误
            if (resp.getMessage().contains("scheduler is not available")
                    || resp.getMessage().contains("Read timed out")
                    || resp.getMessage().contains("pbs qmgr command failed")) {
                throw new CommonException.RelatedServiceException("调度器连接失败", "scheduler plugin");
            } else {
                throw new CommonException.RelatedServiceException(
                        "check_scheduler_info failed, " + resp.getMessage(), "scheduler plugin");
            }
        }

        // 检查代理节点权限
        resp = backendGateway.actionScript(cluster.getClusterId(), EventType.CHECK_PROXY_ROLE, "");
        if (resp.getCode() != HttpURLConnection.HTTP_OK) {
            clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SCHEDULER, EventType.FAILED,
                    resp.getMessage());
            clusterService.updateClusterWithErrorMessage(cluster.getClusterId(),
                    ClusterStatus.TO_BE_AUTHORIZED.nameLowerCase(), resp.getMessage());
            log.error("check_proxy_role failed, " + resp.getMessage(), "scheduler plugin");
        } else {
            // 更新调度器检查事件
            clusterEventDAOGateway.update(clusterId, EventType.CHECK_SCHEDULER, EventType.SUCCEED, "");

            // 调度器服务检查通过后，直接更新集群为已经就绪状态
            clusterService.updateClusterStatus(cluster.getClusterId(), ClusterStatus.ACTIVE.nameLowerCase());
        }

        SchedulerCommonResponse schedulerCommonResponse = new SchedulerCommonResponse();
        schedulerCommonResponse.setClusterId(clusterId);
        return schedulerCommonResponse;
    }

    /**
     * 判断是否为有效的挂载选项。
     * 有效的挂载选项应该以 "-t nfs" 或 "-t nfs4" 开头，后面跟着空格、-o 和一系列字符。
     *
     * @param mountOption 要判断的挂载选项，类型为字符串
     * @return true 如果挂载选项有效；false 如果挂载选项无效
     */
    public static boolean isValidMountOption(String mountOption) {
        // 正则表达式，匹配以-t nfs 或 -t nfs4 开头，后面跟着空格、-o 和一系列字符的字符串
        String regex = "^-t\\s+(nfs|nfs4)\\s+-o\\s+.*$";
        return mountOption.matches(regex);
    }

    /**
     * @Description:
     *               修改集群NFS配置，包括挂载目标、挂载路径和挂载选项等。
     *               如果请求中的挂载列表为空，则不使用共享存储；
     *               如果请求中的挂载列表不为空，则需要先检查共享存储，然后根据挂载列表进行挂载操作。
     *               如果共享存储已经存在，则直接返回；如果共享存储不存在，则创建共享存储。
     *               如果挂载列表中有多个挂载，则按照顺序进行挂载操作。
     *               如果挂载失败，则更新集群状态为创建中，并返回错误信息。
     *
     * @Param clusterId string 集群ID
     * @Param request MountConfigUpdateRequest 挂载配置更新请求，包括挂载目标、挂载路径和挂载选项等
     *
     * @Return SchedulerCommonResponse Scheduler通用响应，包括集群ID和一些公共信息
     *
     * @Throws CommonException.RequestInvalidException 请求无效异常，当挂载参数不合法时抛出该异常
     * @Throws CommonException.RelatedServiceException 相关服务异常，当挂载NFS或者检查NFS失败时抛出该异常
     */
    @Override
    public SchedulerCommonResponse modifyClusterNfsConfig(String clusterId, MountConfigUpdateRequest request) {

        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        preCheck(cluster);

        SchedulerCommonResponse schedulerCommonResponse = new SchedulerCommonResponse();
        schedulerCommonResponse.setClusterId(clusterId);

        Event event = clusterEventDAOGateway.findByClusterIdAndEvent(clusterId, EventType.CHECK_SHARED_STORAGE);

        if (request.getMountList() == null || request.getMountList().size() == 0) {
            // *mountList 为空，则不使用共享存储
            clusterEventDAOGateway.update(clusterId, EventType.CHECK_SHARED_STORAGE, EventType.SUCCEED, "变更为不使用共享存储");
            clusterService.updateClusterStatus(cluster.getClusterId(), ClusterStatus.CREATING.nameLowerCase());
            return schedulerCommonResponse;
        }

        // 不存在共享存储event
        if (event == null) {
            return schedulerCommonResponse;
        }

        for (int i = 0; i < request.getMountList().size(); i++) {

            MountConfig mountConfig = request.getMountList().get(i);
            if (mountConfig.getSoftwareDir() == null || StringUtils.isEmpty(mountConfig.getSoftwareDir())) {
                mountConfig.setSoftwareDir(mountConfig.getMountDir() + "/software");
            }

            List<SharedStorage> sharedStorageList = new ArrayList<>();
            SharedStorage storage = new SharedStorage();
            storage.setStorageProtocol("nfs");
            storage.setMountTarget(mountConfig.getMountTarget());
            storage.setMountDir(mountConfig.getMountDir());
            if (mountConfig.getMountTarget() == null || mountConfig.getMountDir() == null) {
                throw new CommonException.RequestInvalidException("mount_nfs failed, mountTarget or mountDir is null");
            }
            String mountOption = mountConfig.getMountOption();
            // 将 mountOption 中的空格转为^
            if (mountOption != null && !StringUtils.isEmpty(mountOption)) {
                // *挂载参数是否正确
                // 判断 mountOption 是不是 -t nfs -o xxxx 的形式
                if (!isValidMountOption(mountOption)) {
                    throw new CommonException.RequestInvalidException(
                            "mount_nfs failed, mountOption is not valid");
                }
                mountOption = mountOption.replaceAll(" ", "^");
            }
            storage.setMountOption(mountOption);
            sharedStorageList.add(storage);

            String sharedStorage = new String(
                    new JsonStringEncoder().quoteAsString(JacksonUtil.toJson(sharedStorageList)));
            String arguments = cluster.getClusterId() + " " +
                    mountConfig.getSoftwareDir() + String.format(" '%s'", sharedStorage);

            // 调用插件设置并检查共享存储
            BackendActionScriptResponse resp = backendGateway.actionScript(clusterId, EventType.MOUNT_NFS, arguments);
            if (resp.getCode() != HttpURLConnection.HTTP_OK) {
                clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SHARED_STORAGE, EventType.FAILED,
                        resp.getMessage());
                throw new CommonException.RelatedServiceException(
                        "mount_nfs failed, " + resp.getMessage(), "scheduler plugin");
            }

            // 更新代理节点的软件安装目录
            clusterDAOGateway.updatesoftwareDirByClusterId(clusterId, mountConfig.getSoftwareDir());

            // 更新挂载信息
            cfsDAOGateway.updateByClusterId(clusterId, mountConfig.getMountTarget(), mountConfig.getMountDir(),
                    mountConfig.getMountOption());

            arguments = mountConfig.getMountTarget() + " " + mountConfig.getMountDir() + " "
                    + mountConfig.getSoftwareDir();

            // 调用插件，检查共享存储配置
            resp = backendGateway.actionScript(cluster.getClusterId(), EventType.CHECK_NFS, arguments);
            if (resp.getCode() != HttpURLConnection.HTTP_OK) {
                clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SHARED_STORAGE, EventType.FAILED,
                        resp.getMessage());
                throw new CommonException.RelatedServiceException(
                        "check_nfs failed, " + resp.getMessage(), "scheduler plugin");
            }
        }
        // 更新调度器检查事件
        clusterEventDAOGateway.update(clusterId, EventType.CHECK_SHARED_STORAGE, EventType.SUCCEED, "");

        // 更新集群为创建中状态
        clusterService.updateClusterStatus(cluster.getClusterId(), ClusterStatus.CREATING.nameLowerCase());

        return schedulerCommonResponse;
    }

    /**
     * @description 预检查，确保参数正确且支持该功能
     * @param cluster Cluster 集群对象，包含schedulePlugin和clusterType属性
     * @throws CommonException.RequestInvalidException 如果参数错误或不支持该功能则抛出此异常
     */
    private void preCheck(Cluster cluster) {

        if (cluster == null || cluster.getSchedulePlugin() == 0) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
    }
}
