package com.baidu.bce.logic.chpc.service.saas;

import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.ResourceMapping;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.logic.chpc.billing.gateway.BillingGateway;
import com.baidu.bce.logic.chpc.skyform.GetUserResponse;
import com.baidu.bce.logic.chpc.skyform.gateway.SkyFormGateway;
import com.baidu.bce.logic.chpc.common.SaasResourceType;
import com.baidu.bce.logic.chpc.common.ServiceStatusType;
import com.baidu.bce.logic.chpc.gateway.OrderDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SaasResourceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ServiceStatusDAOGateway;
import com.baidu.bce.logic.chpc.model.Order;
import com.baidu.bce.logic.chpc.model.SaasResource;
import com.baidu.bce.logic.chpc.model.ServiceStatus;
import com.baidu.bce.logic.chpc.model.request.saas.ServiceCreateRequest;
import com.baidu.bce.logic.chpc.model.response.billing.OrderCreateResponse;
import com.baidu.bce.logic.chpc.model.response.saas.ServiceCreateResponse;
import com.baidu.bce.logic.chpc.model.response.saas.ServiceLoginResponse;
import com.baidu.bce.logic.chpc.service.IBillingService;
import com.baidu.bce.logic.chpc.service.ISaasService;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getUserId;

@Slf4j
@Service
public class SkyformServiceImpl implements ISaasService {

    @Resource
    OrderDAOGateway orderDAOGateway;

    @Resource
    SaasResourceDAOGateway saasResourceDAOGateway;

    @Resource
    IBillingService billingService;

    @Resource
    BillingGateway billingGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    SkyFormGateway skyformGateway;

    @Resource
    ServiceStatusDAOGateway serviceStatusDAOGateway;

    @Autowired
    IamLogicService iamLogicService;

    // 创建的是服务资源（天云服务）
    @Override
    public ServiceCreateResponse createService(ServiceCreateRequest request) {
        if (request.getAccountId().isEmpty()) {
            throw new CommonExceptions.RequestInvalidException("accountId is required");
        }

        ServiceCreateResponse response = new ServiceCreateResponse();


        //  查询是否已经有订单
        Order order = orderDAOGateway.findByAccountIdAndServiceType(request.getAccountId(),
                SaasResourceType.CHPC.name());
        if (order == null) {
            // 创建订单
            CreateOrderRequest<CreateNewTypeOrderItem> orderRequest = new CreateOrderRequest<CreateNewTypeOrderItem>();
            orderRequest.setOrderType("NEW");
            orderRequest.setRegion(regionConfiguration.getCurrentRegion());

            List<CreateNewTypeOrderItem> requestItemList = new LinkedList<>();
            CreateNewTypeOrderItem requestItem = new CreateNewTypeOrderItem();
            requestItem.setServiceType(SaasResourceType.CHPC.name()); // *计费的产品名称：CHPC
            requestItem.setProductType("postpay"); // *计费类型：后付费
            requestItem.setKey("skyform-service"); // *具体的业务名称：天云服务
            requestItem.setCount(1);
            requestItemList.add(requestItem);
            orderRequest.setItems(requestItemList);
            log.debug("item: {}", orderRequest);

            OrderCreateResponse orderCreateResponse = billingService.newOrder(orderRequest, request.getAccountId());
            if (orderCreateResponse.getStatus().equals(OrderStatus.CREATE_FAILED)) {
                throw new CommonExceptions.RequestInvalidException(
                        String.format("create order failed: %s", orderCreateResponse.getReason()));
            }
            // 记录pfsId和filesetId
            SaasResource serviceResource = new SaasResource();
            serviceResource.setAccountId(request.getAccountId());
            serviceResource.setOrderId(orderCreateResponse.getOrderId());
            serviceResource.setResourceUuid(UUID.randomUUID().toString());
            serviceResource.setPfsId(request.getPfsId());
            serviceResource.setFilesetId(request.getFilesetId());
            serviceResource.setResourceType(SaasResourceType.SKYFORM.getName());
            serviceResource.setStatus(ResourceStatus.RUNNING.toString());
            saasResourceDAOGateway.insert(serviceResource);
            log.info("add service resource for orderId: {} {}", orderCreateResponse.getOrderId(),
                    orderCreateResponse.getStatus().toString());

            // 更新订单状态
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            List<ResourceMapping> resourceMappings = new LinkedList<>();
            ResourceMapping resourceMapping = new ResourceMapping();
            resourceMapping.setId(serviceResource.getResourceUuid());
            resourceMapping.setKey(requestItem.getKey());
            resourceMapping.setStatus(ResourceStatus.RUNNING);
            resourceMappings.add(resourceMapping);
            updateOrderRequest.setResources(resourceMappings);
            updateOrderRequest.setStatus(OrderStatus.CREATED);
            try {
                com.baidu.bce.internalsdk.order.model.Order updateOrder = billingGateway.updateOrder(request.getAccountId(),
                orderCreateResponse.getOrderId(),
                        updateOrderRequest);
                log.debug("{}", updateOrder);
            } catch (BceInternalResponseException exceptionResponse) {
                log.error("=====>update status error: {}, {}.", exceptionResponse.getCode(),
                        exceptionResponse.getMessage());
                log.debug("{}", exceptionResponse);
            } catch (Exception e) {
                log.error("=====>update status error: {}", e);
            }

            // 写入数据库
            Order orderDO = new Order();
            orderDO.setOrderId(orderCreateResponse.getOrderId());
            orderDO.setAccountId(request.getAccountId());
            orderDO.setServiceType(orderRequest.getItems().get(0).getServiceType());
            String productType = orderRequest.getItems().get(0).getProductType();
            orderDO.setProductType(productType);
            orderDO.setStatus(OrderStatus.CREATED.toString());
            orderDO.setItemKey(orderRequest.getItems().get(0).getKey());

            orderDAOGateway.insert(orderDO);

            response.setOrderId(serviceResource.getOrderId());
            response.setStatus(serviceResource.getStatus());
        } else {
            response.setOrderId(order.getOrderId());
            response.setStatus(order.getStatus());
        }

        return response;
    }

    @Override
    public ServiceLoginResponse loginService() {
        String accountId = LogicUserService.getAccountId();
        ServiceStatus serviceStatus = serviceStatusDAOGateway.findBy(Long.valueOf(2), accountId);
        if (serviceStatus == null) {
            // 服务未开通，无法登录
            throw new CommonExceptions.RequestInvalidException("please activate the service first");
        } else if (!serviceStatus.getStatus().equals(ServiceStatusType.ACTIVATED.nameLowerCase())) {
            // 服务开通中，无法登录
            throw new CommonExceptions.RequestInvalidException("please wait for the service to be fully activated");
        }
        ServiceLoginResponse response = new ServiceLoginResponse();

        String userId = LogicUserService.getUserId();
        log.debug("loginService accountId: {} userId: {}", accountId, userId);
        GetUserResponse userByloginName = skyformGateway.getUserByloginName(userId);

        if (null != userByloginName.getRetObj() && userByloginName.getRetObj().size() > 0) {
            String token = skyformGateway.getUserToken(getUserId());
            response.setToken(token);
            return response;
        } else {
            throw new CommonExceptions.RequestInvalidException("user does not exist");
        }

    }

}
