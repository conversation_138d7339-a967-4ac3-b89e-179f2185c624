package com.baidu.bce.logic.chpc.scheduler.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2022-12-22
 */
@Data
public class CommandContent {

    @JsonProperty("region")
    private String region;

    @JsonProperty("enable_ha")
    private boolean enableHa;

    @JsonProperty("enable_monitor")
    private boolean enableMonitor;

    @JsonProperty("scheduler_type")
    private String schedulerType;

    @JsonProperty("node_type")
    private String nodeType;

    @JsonProperty("cluster_name")
    private String clusterName;

    @JsonProperty("cluster_id")
    private String clusterId;

    @JsonProperty("master_hostname")
    private String masterHostname;

    @JsonProperty("backup_hostnames")
    private List<String> backupHostnames;

    @JsonProperty("login_hostname")
    private String loginHostname;

    @JsonProperty("sharedstorage_list")
    private List<SharedStorage> sharedStorageList;

    @JsonProperty("queue_list")
    private List<Queue> queueList;
}
