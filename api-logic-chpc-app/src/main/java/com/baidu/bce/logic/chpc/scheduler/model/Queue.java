package com.baidu.bce.logic.chpc.scheduler.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2022-12-22
 */
@Data
public class Queue {

    @JsonProperty("is_default")
    private boolean isDefault;

    private String name;

    private List<Node> node;

    private Integer maxNodes;

    private Integer minNodes;

    private Integer maxScalePerCycle;

    private Boolean enableAutoGrow;
    
    private Boolean enableAutoShrink;

    private String spec;
}
