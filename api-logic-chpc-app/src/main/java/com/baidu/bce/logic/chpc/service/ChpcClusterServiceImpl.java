package com.baidu.bce.logic.chpc.service;


import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.CommonValidateUtil;
import com.baidu.bce.logic.chpc.common.GlobalUuidUtil;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.SchedulerPriorityRange;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.common.ZoneUtil;
import com.baidu.bce.logic.chpc.domainservice.CfsService;
import com.baidu.bce.logic.chpc.domainservice.ClusterService;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.model.Cfs;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterCfsForCreate;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterInstanceForCreate;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterOperationRequest;
import com.baidu.bce.logic.chpc.model.request.cluster.ClusterUpdateRequest;
import com.baidu.bce.logic.chpc.model.response.cfs.CfsVO;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterDetailResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterListResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterVO;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceVO;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
@Service
@Slf4j
public class ChpcClusterServiceImpl implements ChpcClusterService {


    @Resource
    private ClusterService clusterService;

    @Resource
    private InstanceService instanceService;

    @Resource
    private QueueDAOGateway queueDAOGateway;

    @Resource
    private GlobalUuidUtil globalUuidUtil;

    @Resource
    private CfsService cfsService;

    @Resource
    private TaskService taskService;

    @Resource
    private BackendGateway backendGateway;

    @Resource
    ZoneUtil zoneUtil;


    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ClusterResponse createCluster(ClusterCreateRequest clusterCreateRequest) {

        // 校验BCC参数
        validateInstanceFromRequest(clusterCreateRequest);


        // 封装参数，请求COS
        com.baidu.bce.internalsdk.cos.model.ClusterResponse cosCusterResponse =
                clusterService.createClusterResourceWithCos(this.convertToCosRequest(clusterCreateRequest));


        ClusterResponse clusterResponse = BeanCopyUtil.copyObject(cosCusterResponse, ClusterResponse::new);

        // 保存到DB中
        Cluster cluster = this.generateWithClusterResponse(clusterCreateRequest, clusterResponse);
        clusterService.insert(cluster);
        clusterResponse.setClusterId(cluster.getClusterId());

        // 创建集群对应的默认队列
        Queue defaultQueue = this.generateGroup(clusterCreateRequest, clusterResponse);
        queueDAOGateway.insert(defaultQueue);


        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.GROUP_RESOURCE_SAVE_TASK,
                cluster.getClusterId(), defaultQueue.getQueueId(),
                TaskSourceType.INIT_CLUSTER.getTaskSourceType(),
                taskId);

        clusterResponse.setTaskId(taskId);
        return clusterResponse;
    }


    /**
     * BCC校验规则
     * 1.实例列表不能为空
     * 2.有且只能又1个master节点
     * 3.最多只能包含只能包含1个登录节点
     */
    private void validateInstanceFromRequest(ClusterCreateRequest request) {


        List<ClusterInstanceForCreate> bccForCreates = request.getClusterInstanceForCreates();

        if (CollectionUtils.isEmpty(bccForCreates)) {

            throw new CommonExceptions.RequestInvalidException(
                    "The BCC instance required by the cluster cannot be empty");
        }

        Map<String, List<ClusterInstanceForCreate>> groupByInstanceNodeType =
                bccForCreates.stream()
                        .collect(Collectors.groupingBy(ClusterInstanceForCreate::getInstanceNodeType));

        int masterCount = this.countInstance(groupByInstanceNodeType.get(InstanceNodeType.MASTER.getType()));
        if (masterCount != 1) {

            throw new CommonExceptions.RequestInvalidException(
                    "The cluster must and can only contain one master node");
        }

        int loginCount = this.countInstance(groupByInstanceNodeType.get(InstanceNodeType.LOGIN.getType()));
        if (loginCount > 1) {

            throw new CommonExceptions.RequestInvalidException(
                    "The cluster can only contain one login node at most");
        }

    }

    private com.baidu.bce.internalsdk.cos.model.ClusterCreateRequest convertToCosRequest(
            ClusterCreateRequest clusterCreateRequest) {

        return JacksonUtil.decode(JacksonUtil.toJson(clusterCreateRequest),
                com.baidu.bce.internalsdk.cos.model.ClusterCreateRequest.class);
    }

    private int countInstance(List<ClusterInstanceForCreate> instances) {

        if (CollectionUtils.isEmpty(instances)) {
            return 0;
        }

        int count = 0;
        for (ClusterInstanceForCreate instance : instances) {
            count = count + instance.getPurchaseCount();
        }

        return count;
    }


    private Cluster generateWithClusterResponse(ClusterCreateRequest clusterCreateRequest,
                                                ClusterResponse clusterResponse) {

        ClusterInstanceForCreate masterInstance = clusterCreateRequest.getClusterInstanceForCreates()
                .stream()
                .filter(ins -> InstanceNodeType.MASTER.getType().equalsIgnoreCase(ins.getInstanceNodeType()))
                .findFirst().get();


        Cluster cluster = new Cluster();

        cluster.setClusterId(clusterService.generateClusterId());
        cluster.setName(clusterCreateRequest.getClusterName());
        cluster.setDescription(clusterCreateRequest.getDescription());
        cluster.setVpcId(clusterCreateRequest.getVpcId());
        cluster.setSubnetId(masterInstance.getSubnetId());
        cluster.setStatus(clusterResponse.getStatus());
        cluster.setSchedulerType(clusterCreateRequest.getSchedulerType());
        cluster.setEnableHa(clusterCreateRequest.getOpenedHa());
        cluster.setAccountId(LogicUserService.getAccountId());
        cluster.setSecurityGroupId(clusterCreateRequest.getSecurityGroupId());

        Map<String, String> extras = new HashMap<>();

        // 保存cfs的挂载地址
        if (CollectionUtils.isNotEmpty(clusterCreateRequest.getClusterCfsForCreates())) {

            for (ClusterCfsForCreate cfs : clusterCreateRequest.getClusterCfsForCreates()) {
                String cfsNameKey = String.format("%s-%s", cfs.getName(), cfs.getMountDir());
                extras.put(cfsNameKey, cfs.getMountDir());
            }
        }

        cluster.setExtra(JacksonUtil.toJson(extras));

        return cluster;
    }

    private Queue generateGroup(ClusterCreateRequest clusterCreateRequest,
                                ClusterResponse clusterResponse) {

        ClusterInstanceForCreate computeInstance = clusterCreateRequest.getClusterInstanceForCreates()
                .stream()
                .filter(ins -> InstanceNodeType.MASTER.getType().equalsIgnoreCase(ins.getInstanceNodeType()))
                .findFirst().orElse(null);

        Queue queue = new Queue();
        queue.setClusterId(clusterResponse.getClusterId());
        queue.setName("defaultQueue");
        queue.setQueueId(globalUuidUtil.generateQueueId());
        queue.setIsDefault(true);
        queue.setDefaultSpec(computeInstance != null ? computeInstance.getSpec() : "");
        queue.setDefaultImageId(clusterCreateRequest.getImageId());
        queue.setDefaultUserData("");
        queue.setStatus(clusterResponse.getStatus());

        return queue;
    }

    @Override
    public ClusterDetailResponse getClusterDetail(String clusterId) {
        if (StringUtils.isEmpty(clusterId)) {
            throw new CommonExceptions.RequestInvalidException("The cluster id is empty!");
        }

        log.debug("begin to get cluster detail, cluster id : {}", clusterId);

        Cluster cluster = clusterService.findBy(clusterId);
        return this.getClusterDetail(cluster);
    }

    private ClusterDetailResponse getClusterDetail(Cluster cluster) {

        ClusterDetailResponse response = this.initDetailResponse(cluster);
        List<InstanceVO> instanceVOList = new ArrayList<>();

        String clusterId = cluster.getClusterId();

        List<Queue> queues = queueDAOGateway.listByClusterId(clusterId);
        response.setQueueList(queues.stream()
                .map(ServiceUtil::convertToGroupVO)
                .collect(Collectors.toList()));


        for (Queue queue : queues) {
            List<Instance> instances = instanceService.findBy(clusterId, queue.getQueueId());
            instanceVOList.addAll(instances.stream()
                    .map(ServiceUtil::convertToInstanceVO)
                    .collect(Collectors.toList()));
        }

        response.setInstanceList(instanceVOList);

        List<Cfs> cfsList = cfsService.findByClusterId(clusterId);
        if (CollectionUtils.isNotEmpty(cfsList)) {
            List<CfsVO> cfsVOList = cfsList.stream()
                    .map(ServiceUtil::convertToCfsVO)
                    .collect(Collectors.toList());

            response.setCfsList(cfsVOList);
        }
        return response;
    }

    private ClusterDetailResponse initDetailResponse(Cluster cluster) {

        ClusterDetailResponse response = BeanCopyUtil.copyObject(cluster, ClusterDetailResponse::new);
        response.setClusterName(cluster.getName());
        response.setEnableHa(cluster.isEnableHa());
        return response;
    }

    @Override
    public ClusterListResponse listCluster() {

        List<Cluster> clusters = clusterService.findByAccountId(this.getAccountId());

        ClusterListResponse response = new ClusterListResponse();
        List<ClusterVO> clusterList = clusters.stream()
                .map(this::convertToClusterVO)
                .collect(Collectors.toList());

        response.setClusterList(clusterList);
        response.setCount(clusterList.size());
        return response;
    }

    /**
     * @Description: 将传入的 Cluster 对象转换为 ClusterVO 对象，并返回该对象。
     * 包括属性名称的映射和一些特殊处理（如错误信息）。
     * @Param cluster - Cluster 类型，需要被转换的 Cluster 对象
     * @Return ClusterVO - ClusterVO 类型，转换后的 ClusterVO 对象
     */
    public ClusterVO convertToClusterVO(Cluster cluster) {
        ClusterVO clusterVO = new ClusterVO();

        clusterVO.setClusterId(cluster.getClusterId());
        clusterVO.setClusterName(cluster.getName());
        clusterVO.setDescription(cluster.getDescription());
        clusterVO.setCreatedTime(cluster.getCreatedTime());
        clusterVO.setZoneName(zoneUtil.getApiZoneName(cluster.getLogicalZone()));

        clusterVO.setChargeType(cluster.getChargeType());
        clusterVO.setSchedulerType(cluster.getSchedulerType());
        clusterVO.setSchedulerVersion(cluster.getSchedulerVersion());
        clusterVO.setSchedulePlugin(cluster.getSchedulePlugin());
        clusterVO.setImageId(cluster.getImageId());

        clusterVO.setStatus(cluster.getStatus());
        // active 状态时不返回错误信息
        if (!ClusterStatus.ACTIVE.nameLowerCase().equalsIgnoreCase(cluster.getStatus())) {
            clusterVO.setErrorMessage(cluster.getErrorMessage());
        }
        clusterVO.setClusterType(cluster.getClusterType());

        clusterVO.setJobPriorityRange(SchedulerPriorityRange.schedulerPriorityRangeMap.get(cluster.getSchedulerType()));

        clusterVO.setForbidDelete(cluster.getForbidDelete());

        return clusterVO;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ClusterResponse deleteCluster(String clusterId, boolean force) {


        BackendCommonResponse commonResponse =
                backendGateway.stopCluster(clusterId, force);
        CommonValidateUtil.validBackendCommonResponse(commonResponse);

        clusterService.updateClusterStatus(clusterId, ClusterStatus.DELETING.nameLowerCase());


        Map<String, Object> extra = new HashMap<>(2);
        extra.put(ChpcConstant.BACKEND_TASK_ID, commonResponse.getTaskId());
        extra.put(ChpcConstant.CLUSTER_DELETED_TASK, ChpcConstant.TYPE_EXISTED);

        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.BACKEND_ASYNC_TASK,
                clusterId, "",
                TaskSourceType.STOP_CLUSTER.getTaskSourceType(),
                taskId,
                extra);


        ClusterResponse response = new ClusterResponse();
        response.setClusterId(clusterId);
        response.setStatus(ClusterStatus.DELETING.nameLowerCase());
        response.setTaskId(taskId);
        return response;
    }


    public void updateClusterPassword(String clusterId, String password) {
        clusterService.updateClusterPassword(clusterId, password);
    }

    /**
     * {@inheritDoc}
     * 更新集群信息，包括名称和描述。如果请求中包含禁止删除标记，则将该标记设置为true。
     * 返回一个包含集群ID、状态和更新后的集群信息的ClusterResponse对象。
     *
     * @param clusterId 集群ID
     * @param request   集群更新请求，包含集群名称、描述和禁止删除标记（可选）
     * @return ClusterResponse 包含集群ID、状态和更新后的集群信息的ClusterResponse对象
     * @throws IllegalArgumentException 当请求中的集群名称或描述超过限制长度时抛出此异常
     * @throws NotFoundException        当指定的集群不存在时抛出此异常
     */
    @Override
    public ClusterResponse updateCluster(String clusterId, ClusterUpdateRequest request) {


        Cluster cluster = clusterService.findBy(clusterId);
        validateUpdateRequest(request, cluster);
        if (request.getMaxCpus() != null || request.getMaxNodes() != null) {
            if (request.getMaxCpus() == null) {
                request.setMaxCpus(cluster.getMaxCpus());
            }
            if (request.getMaxNodes() == null) {
                request.setMaxNodes(cluster.getMaxNodes());
            }
            BackendCommonResponse commonResponse =
                    backendGateway.modifyCluster(clusterId, request.getMaxCpus(), request.getMaxNodes());
            CommonValidateUtil.validBackendCommonResponse(commonResponse);
        }
        clusterService.updateCluster(clusterId,
                request.getClusterName(),
                request.getMaxCpus(),
                request.getMaxNodes(),
                request.getClusterDescription(),
                request.getForbidDelete());

        ClusterResponse response = new ClusterResponse();

        response.setClusterId(clusterId);
        response.setStatus(cluster.getStatus());
        return response;
    }

    @Override
    @ValidateAuthentication
    public ClusterResponse startCluster(String clusterId, ClusterOperationRequest request) {

        backendGateway.startCluster(clusterId);

        clusterService.updateClusterStatus(clusterId,
                ClusterStatus.ACTIVE.nameLowerCase());

        ClusterResponse response = new ClusterResponse();
        response.setClusterId(clusterId);
        response.setStatus(ClusterStatus.ACTIVE.nameLowerCase());
        return response;
    }

    @Override
    @ValidateAuthentication
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ClusterResponse stopCluster(String clusterId, ClusterOperationRequest request) {

        BackendCommonResponse commonResponse =
                backendGateway.stopCluster(clusterId, request.getForce());
        CommonValidateUtil.validBackendCommonResponse(commonResponse);


        clusterService.updateClusterStatus(clusterId,
                ClusterStatus.STOPPING.nameLowerCase());

        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.BACKEND_ASYNC_TASK,
                clusterId, "",
                TaskSourceType.STOP_CLUSTER.getTaskSourceType(),
                taskId,
                Collections.singletonMap(ChpcConstant.BACKEND_TASK_ID, commonResponse.getTaskId()));


        ClusterResponse response = new ClusterResponse();
        response.setClusterId(clusterId);
        response.setStatus(ClusterStatus.STOPPING.nameLowerCase());
        response.setTaskId(taskId);
        return response;
    }

    private void validateUpdateRequest(ClusterUpdateRequest request, Cluster cluster) {

        String clusterName = request.getClusterName();
        String clusterDescription = request.getClusterDescription();

        boolean needUpdateName = StringUtils.isNotEmpty(clusterName) &&
                ObjectUtils.notEqual(clusterName, cluster.getName());

        // 当前不支持修改集群名称，后续api-server支持了，再去掉此限制
        if (needUpdateName) {
            throw new CommonExceptions.RequestInvalidException(
                    "Currently, modifying the cluster name is not supported.");
        }

        // boolean needUpdateDescription = StringUtils.isNotEmpty(clusterDescription) &&
        //         ObjectUtils.notEqual(clusterDescription, cluster.getDescription());

        // if (!needUpdateDescription) {

        //     throw new CommonExceptions.RequestInvalidException("description should be different.");
        // }
    }


    private String getAccountId() {
        return LogicUserService.getAccountId();
    }
}
