package com.baidu.bce.logic.chpc.service.job;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.internalsdk.iam.model.BatchVerifyResults;
import com.baidu.bce.internalsdk.iam.model.VerifyResult;
import com.baidu.bce.internalsdk.order.model.FlavorItem;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.annotation.AspectResultHolder;
import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.CommonValidateUtil;
import com.baidu.bce.logic.chpc.common.HelixConstant;
import com.baidu.bce.logic.chpc.common.HelixJobStatus;
import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.common.SaasResourceType;
import com.baidu.bce.logic.chpc.common.UserCallbackTaskStatus;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.common.holder.AccessKeyThreadHolder;
import com.baidu.bce.logic.chpc.domainservice.UserCallbackTaskService;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.HelixJobDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.JobTemplateDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendJobListResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendJobPriorityAlterResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendJobPriorityAlterResult;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.HostDetail;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.JobDetail;
import com.baidu.bce.logic.chpc.job.model.BatchJobRequest;
import com.baidu.bce.logic.chpc.job.model.JobTemplateRequest;
import com.baidu.bce.logic.chpc.job.model.SubmitJobRequest;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.HelixJob;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Job;
import com.baidu.bce.logic.chpc.model.JobTemplate;
import com.baidu.bce.logic.chpc.model.UserCallbackTask;
import com.baidu.bce.logic.chpc.model.response.billing.OrderCreateResponse;
import com.baidu.bce.logic.chpc.model.response.job.AlterJobPriorityResponse;
import com.baidu.bce.logic.chpc.model.response.job.AlterJobPriorityResult;
import com.baidu.bce.logic.chpc.model.response.job.BatchJobResponse;
import com.baidu.bce.logic.chpc.model.response.job.CommonHelixJobResponse;
import com.baidu.bce.logic.chpc.model.response.job.JobTemplatesResponse;
import com.baidu.bce.logic.chpc.model.response.job.ListAvailableTagsResponse;
import com.baidu.bce.logic.chpc.model.response.job.ListClusterJobsResponse;
import com.baidu.bce.logic.chpc.model.response.job.SubmitJobResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueVO;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.service.IBillingService;
import com.baidu.bce.logic.chpc.service.JobService;
import com.baidu.bce.logic.chpc.service.util.AesEncryptUtil;
import com.baidu.bce.logic.chpc.service.util.PasswordUtil;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.chpc.tag.UpdateTagsRequest;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.reflect.TypeToken;
import jakarta.annotation.Resource;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.function.BiFunction;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getSubjectToken;

@Slf4j
@Service
public class JobServiceImpl implements JobService {

    @Resource
    BackendGateway backendGateway;

    @Value("${helix.helixfold3.cluster}")
    private String helixfold3Cluster;

    @Value("${helix.helixvs.cluster}")
    private String helixvsCluster;

    @Value("${helix.helixfold3.price}")
    private Double helixfold3Price;

    @Value("${helix.helixcpu.price}")
    private Double helixCPUPrice;

    @Value("${bus.global-register:false}")
    private boolean globalBusRegistered;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    HelixJobDAOGateway helixJobDAOGateway;
    @Resource
    UserCallbackTaskService userCallbackTaskService;

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Resource
    IBillingService billingService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    private JobTemplateDAOGateway jobTemplateDAOGateway;
    @Autowired
    private PasswordUtil passwordUtil;

    /**
     * {@inheritDoc}
     * <p>
     * 列出集群中的作业。
     * 如果集群使用调度插件，则可以进行鉴权。
     * 否则，将抛出CommonException.RequestInvalidException异常。
     *
     * @param clusterId   集群ID
     * @param stateFilter 作业状态过滤器，可为null或者JobStateEnum中的任意值
     * @param timeStart   起始时间，可为null或者非空且大于0的Long类型值
     * @param timeEnd     结束时间，可为null或者非空且大于0的Long类型值
     * @param jobId       作业ID，可为null或者非空字符串
     * @param user        用户名，可为null或者非空字符串
     * @param queue       队列名，可为null或者非空字符串
     * @param orderDesc   是否按照降序排序，true表示降序，false表示升序，默认为true
     * @param page        页码，从1开始，必须大于等于1，默认为1
     * @param size        每页大小，必须大于等于1，默认为10
     * @return ListClusterJobsResponse 包含作业列表和总数、页码和每页大小的ListClusterJobsResponse对象
     * @throws CommonException.RequestInvalidException 如果集群不使用调度插件，将抛出此异常
     * @throws BceException                            如果请求失败，将抛出此异常
     */
    @Override
    @PermissionVerify(
            resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_JOB_LIST,
            permissions = {PermissionConstant.CHPC_READ, PermissionConstant.JOB_ALL}
    )
    public ListClusterJobsResponse listClusterJob(
            String clusterId,
            Integer stateFilter,
            Long timeStart,
            Long timeEnd,
            String jobId,
            String user,
            String queue,
            Boolean orderDesc,
            Integer page,
            Integer size) {

        if (!ServiceUtil.isClusterUsingSchedulerPlugin(clusterDAOGateway, clusterId)) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }

        String arguments = "";
        arguments = arguments + " --state-filter " + stateFilter;
        if (timeStart != null && timeStart != 0) {
            arguments = arguments + " --time-start " + timeStart;
        }
        if (timeEnd != null && timeEnd != 0) {
            arguments = arguments + " --time-end " + timeEnd;
        }
        if (jobId != null && !jobId.isEmpty()) {
            arguments = arguments + " --job-id " + jobId;
        }
        if (user != null && !user.isEmpty()) {
            arguments = arguments + " --user " + user;
        }
        if (queue != null && !queue.isEmpty()) {
            arguments = arguments + " --queue " + queue;
        }
        if (orderDesc) {
            arguments = arguments + " --order-desc ";
        }
        arguments = arguments + " --page " + page;
        arguments = arguments + " --size " + size;

        // 鉴权
        Object aspectResult = AspectResultHolder.getAspectResult();
        AspectResultHolder.clear();
        if (aspectResult != null) {
            log.debug("Aspect result: " + aspectResult);
            @SuppressWarnings("unchecked")
            List<BatchVerifyResults.BatchVerifyResult> results = (List<BatchVerifyResults.BatchVerifyResult>) aspectResult;
            BatchVerifyResults.BatchVerifyResult result = results.get(0);
            if (result == null || 1 != result.getResult().size()) {
                throw new CommonException.RequestInvalidException("size of iam result of job detail list is not 1");
            } else {
                List<VerifyResult> iamRes = result.getResult();
                VerifyResult subResult = iamRes.get(0);
                if (subResult == null) {
                    throw new CommonException.RequestInvalidException(
                            "subResult of iam result of job detail list is null");
                }
                if (!PermissionConstant.ALLOW_PERMISSION.equals(subResult.getEffect())) {
                    // 普通用户权限，只返回自己创建的job
                    arguments = arguments + " --specified-user " + LogicUserService.getSubjectToken().getUserName();
                }
            }
        }

        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "job_detail_list", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "list job detail failed, " + resp.getMessage(), "scheduler plugin");
        }
        ObjectMapper mapper = new ObjectMapper();
        BackendJobListResponse res = new BackendJobListResponse();
        try {
            res = mapper.readValue(resp.getData(), BackendJobListResponse.class);
        } catch (Exception e) {
            log.error("json unmarshal BackendJobListResponse failed", e);
            throw new BceException(e.getMessage());
        }
        ListClusterJobsResponse response = new ListClusterJobsResponse();
        List<Job> jobList = new ArrayList<>();
        for (JobDetail jobDetail : res.getJobs()) {
            Job job = new Job();
            job.setClusterId(clusterId);
            job.setJobId(jobDetail.getJobId());
            job.setJobName(jobDetail.getJobName());
            job.setJobState(jobDetail.getJobState());
            job.setJobArray(jobDetail.getJobArray());
            job.setPriority(jobDetail.getPriority());
            job.setQueue(jobDetail.getQueue());
            job.setJobOwner(jobDetail.getJobOwner());
            job.setOutputPath(jobDetail.getOutputPath());
            job.setErrorPath(jobDetail.getErrorPath());
            job.setComment(jobDetail.getComment());
            job.setExecHost(jobDetail.getExecHost());
            // 从 jobDetail.getExecHost()
            // 中获取节点信息，格式为：instance-nwkxwkkz-2/0*64+instance-nwkxwkkz-3/0*64+instance-tv9n2dkt-1/0*64+instance-tv9n2dkt-2/0*64
            List<HostDetail> hostList = new ArrayList<>();
            if (jobDetail.getExecHost() != null && !jobDetail.getExecHost().isEmpty()) {
                String[] execHosts = jobDetail.getExecHost().split("\\+");
                for (String execHost : execHosts) {
                    String[] hostAndCores = execHost.split("/");
                    String hostName = "";
                    HostDetail hostDetail = new HostDetail();
                    if (hostAndCores.length == 2) {
                        // int cores = Integer.parseInt(hostAndCores[1].replace("*", ""));
                        hostName = hostAndCores[0];
                    } else if (hostAndCores.length == 1) {
                        hostName = execHost;
                    }
                    hostDetail.setHostName(hostName);
                    // 数据库中根据 hostName 查找节点信息
                    Instance cloudInstance = instanceDAOGateway.findByHostNameIgnoreDeleted(hostName);
                    if (cloudInstance != null) {
                        hostDetail.setInstanceId(cloudInstance.getInstanceId());
                        hostDetail.setInstanceUuid(cloudInstance.getInstanceUuid());
                        hostDetail.setPrivateIp(cloudInstance.getPrivateIp());
                    }
                    // 根据 hostName 判断 hostList 中是否已经有，没有的话再插入
                    if (hostList.size() > 0) {
                        boolean hasHost = false;
                        for (HostDetail host : hostList) {
                            if (host.getHostName() != null
                                    && hostDetail.getHostName() != null
                                    && host.getHostName().equals(hostDetail.getHostName())) {
                                hasHost = true;
                                break;
                            }
                        }
                        if (!hasHost) {
                            hostList.add(hostDetail);
                        }
                    } else {
                        hostList.add(hostDetail);
                    }
                }
            }
            job.setHostList(hostList);
            job.setChpcInputPath(jobDetail.getChpcInputPath());
            job.setChpcOutputPath(jobDetail.getChpcOutputPath());
            job.setCtime(jobDetail.getCtime());
            job.setMtime(jobDetail.getMtime());
            job.setStime(jobDetail.getStime());
            job.setTags(jobDetail.getTags());
            jobList.add(job);
        }
        response.setJobs(jobList);
        response.setTotalCount(res.getTotalCnt());
        response.setPageNo(page);
        response.setPageSize(size);
        return response;
    }

    @Override
    public AlterJobPriorityResponse alterClusterJobPriority(
            String clusterId, List<String> jobIds, Long priority, String reason) {
        if (!ServiceUtil.isClusterUsingSchedulerPlugin(clusterDAOGateway, clusterId)) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
        // 获取集群信息补充bct审计信息
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        if (cluster == null) {
            throw new CommonException.RequestInvalidException("cluster is not exist");
        }
        List<AlterJobPriorityResult> succeededJobs = new ArrayList<AlterJobPriorityResult>();
        List<AlterJobPriorityResult> failedJobs = new ArrayList<AlterJobPriorityResult>();
        List<UserCallbackTask> userCallbackTasks =
                userCallbackTaskService.getByJobIdAndStatus(clusterId, jobIds, UserCallbackTaskStatus.WAITING.getValue());
        if (userCallbackTasks.size() > 0) {
            for (UserCallbackTask task : userCallbackTasks) {
                String[] jobIdArray = task.getJobId().split(",");
                for (String jobId : jobIdArray) {
                    AlterJobPriorityResult result = new AlterJobPriorityResult();
                    result.setJobId(jobId);
                    result.setJobState("失败");
                    result.setReason("作业处于用户自定义流程（如审批流程）中，请稍后再试");
                    failedJobs.add(result);
                }
            }
        }

        List<String> jobIdsToOperate = new ArrayList<String>();
        for (String jobId : jobIds) {
            boolean jobIdFailed = false;
            for (AlterJobPriorityResult failedJob : failedJobs) {
                if (jobId.equals(failedJob.getJobId())) {
                    jobIdFailed = true;
                    break;
                }
            }
            if (!jobIdFailed) {
                jobIdsToOperate.add(jobId);
            }
        }

        boolean isAsyncOperation = false;
        if (jobIdsToOperate.size() > 0) {
            String arguments = "";
            String uuid = UUID.randomUUID().toString();
            arguments = arguments + " --job " + String.join(",", jobIdsToOperate);
            arguments = arguments + " --priority " + priority;
            arguments = arguments + " --task-id " + uuid;
            if (reason != null) {
                arguments = arguments + " --reason \"" + reason.replaceAll("\"", "\\\\\"") + "\"";
            }
            arguments = arguments + " --user " + getSubjectToken().getUserName();

            BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "alter_job_priority", arguments);
            if (resp.getCode() != 200) {
                throw new CommonException.RelatedServiceException(
                        "alter job priority failed, " + resp.getMessage(), "scheduler plugin");
            }
            if (resp.isAsyncOperation()) {
                for (String jobId : jobIdsToOperate) {
                    userCallbackTaskService.insert(
                            clusterId,
                            "",
                            "",
                            jobId,
                            "alter_job_priority",
                            arguments,
                            uuid,
                            getSubjectToken().getUserName(),
                            resp.getAsyncOperationType());
                }
            }
            ObjectMapper mapper = new ObjectMapper();
            BackendJobPriorityAlterResponse res = new BackendJobPriorityAlterResponse();
            try {
                res = mapper.readValue(resp.getData(), BackendJobPriorityAlterResponse.class);
            } catch (Exception e) {
                log.error("json unmarshal BackendJobListResponse failed", e);
                return null;
            }

            for (BackendJobPriorityAlterResult failedJob : res.getFailedJobs()) {
                AlterJobPriorityResult result = new AlterJobPriorityResult();
                result.setJobId(failedJob.getJobId());
                result.setJobState(failedJob.getJobState());
                result.setReason(failedJob.getReason());
                failedJobs.add(result);
            }
            for (BackendJobPriorityAlterResult succeededJob : res.getSucceededJobs()) {
                AlterJobPriorityResult result = new AlterJobPriorityResult();
                result.setJobId(succeededJob.getJobId());
                result.setJobState(succeededJob.getJobState());
                result.setReason("");
                succeededJobs.add(result);
            }
            isAsyncOperation = resp.isAsyncOperation();
        }

        AlterJobPriorityResponse response = new AlterJobPriorityResponse();
        response.setAsyncOperation(isAsyncOperation);
        response.setSucceededList(succeededJobs);
        response.setFailedList(failedJobs);
        // 新增的bct信息
        response.setClusterName(cluster.getName());
        String bctMessage = String.format("调整作业%s优先级为%d", succeededJobs.stream().map(AlterJobPriorityResult::getJobId)
                .collect(Collectors.joining("，")), priority);
        response.setMessage(bctMessage);
        return response;
    }

    @Override
    public SubmitJobResponse submitJob(String clusterId, SubmitJobRequest request) {
        // 校验参数，重写校验函数避免影响helix
        // parseParameters(clusterId, request);
        validateJobSubmitParameters(clusterId, request);
        Map<String, Object> paramMap = new HashMap<>();
        // String userName = getSubjectToken().getUserName();
        // ldap用户 非iam用户
        String userName = request.getUsername();
        paramMap.put("CHPC_USERNAME", userName);
        paramMap.put("CHPC_APPLICATION", "common");
        if (!StringUtils.isEmpty(request.getJobName())) {
            paramMap.put("CHPC_JOB_NAME", request.getJobName());
        }
        if (!StringUtils.isEmpty(request.getJobCmd())) {
            paramMap.put("CHPC_JOB_CMD", request.getJobCmd());
        }
        if (!StringUtils.isEmpty(request.getQueue())) {
            paramMap.put("CHPC_QUEUE", request.getQueue());
        }
        if (!StringUtils.isEmpty(request.getPostCmd())) {
            paramMap.put("CHPC_POST_CMD", request.getPostCmd());
        }
        if (!StringUtils.isEmpty(request.getStdoutPath())) {
            paramMap.put("CHPC_STDOUT_PATH", request.getStdoutPath());
        }
        if (!StringUtils.isEmpty(request.getStderrPath())) {
            paramMap.put("CHPC_STDERR_PATH", request.getStderrPath());
        }
        if (!StringUtils.isEmpty(request.getBosFilePath())) {
            paramMap.put("CHPC_BOS_FILE_PATH", request.getBosFilePath());
        }
        if (!StringUtils.isEmpty(request.getDecompressCmd())) {
            paramMap.put("CHPC_DECOMPRESS_CMD", request.getDecompressCmd());
        }
        if (request.getNhosts() != null) {
            paramMap.put("CHPC_NHOSTS", request.getNhosts());
        }
        if (request.getNcpus() != null) {
            paramMap.put("CHPC_NCPUS", request.getNcpus());
        }
        if (request.getLimitTimeInMinutes() != 0) {
            paramMap.put("CHPC_WALLTIME", request.getLimitTimeInMinutes());
        }
        if (request.getEnvVars() != null) {
            for (Map.Entry<String, String> entry : request.getEnvVars().entrySet()) {
                paramMap.put(entry.getKey(), entry.getValue());
            }
        }
        log.debug("submit job param: {}", paramMap.toString());
        String arguments = "";
        ObjectMapper mapper = new ObjectMapper();
        String backendJobParam;
        try {
            backendJobParam = mapper.writeValueAsString(paramMap);
        } catch (Exception e) {
            log.error("failed to writeValueAsString, error is: ", e);
            throw new BceException(e.getMessage());
        }
        arguments = arguments + " --param " + "\'" + backendJobParam + "\'";

        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "job_submit", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "submit job failed, " + resp.getMessage(), "scheduler plugin");
        }
        mapper = new ObjectMapper();
        String jobId = "";
        try {
            jobId = mapper.readValue(resp.getData(), String.class);
        } catch (Exception e) {
            log.error("json unmarshal BackendJobListResponse failed", e);
            return null;
        }
        if (StringUtils.isNotEmpty(jobId) && request.getTags() != null && request.getTags().size() > 0) {
            // 初始tag，新增到登录节点的db中
            BackendCommonResponse commonResponse =
                    backendGateway.modifyTags(clusterId, jobId, request.getTags());
            CommonValidateUtil.validBackendCommonResponse(commonResponse);
        }
        SubmitJobResponse submitJobResp = new SubmitJobResponse();
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        if (cluster != null) {
            // bct msg
            String bctMsg = String.format("user:%s在集群%s（ID:%s）提交一个作业，作业ID:%s", request.getUsername(),
                    cluster.getName(), clusterId, jobId);
            submitJobResp.setClusterName(cluster.getName());
            submitJobResp.setMessage(bctMsg);
        }
        submitJobResp.setJobId(jobId);
        return submitJobResp;
    }

    @Override
    public CommonHelixJobResponse submitHelixJob(SubmitJobRequest request) {
        if (!globalBusRegistered) {
            throw new CommonException.RequestInvalidException("该地域目前不支持此接口");
        }

        CommonHelixJobResponse submitHelixJobResponse;
        switch (request.getJobProduct()) {
            case HelixConstant.HELIXFOLD3, HelixConstant.HF3AGAB, HelixConstant.MINIPROTEIN_DESIGN, HelixConstant.ANTIBODY_DESIGN:
                parseParameters(helixfold3Cluster, request);
                submitHelixJobResponse = submitHelixfold3JobResponse(request);
                return submitHelixJobResponse;
            case HelixConstant.HELIXVS, HelixConstant.HELIXVS_SYN, HelixConstant.LINEAR_DESIGN:
                parseParameters(helixvsCluster, request);
                submitHelixJobResponse = submitHelixCPUJobResponse(request);
                return submitHelixJobResponse;
            case HelixConstant.LINEAR_FOLD, HelixConstant.LINEAR_PARTITION:
                parseParameters(helixvsCluster, request);
                submitHelixJobResponse = submitHelixLinearFoldPartitionJobResponse(request);
                return submitHelixJobResponse;
            default:
                log.error("jobProduct {} not supported", request.getJobProduct());
                throw new CommonExceptions.RequestInvalidException("unsupported products: " + request.getJobProduct());
        }
    }


    @NotNull
    private CommonHelixJobResponse submitHelixCPUJobResponse(SubmitJobRequest request) {
        Gson gson = new Gson();

        JsonObject jsonObject = gson.fromJson(request.getEnvVars().get("extra_params"), JsonObject.class);

        BigInteger githubUserId = jsonObject.get("github_real_id").getAsBigInteger();

        Double jobChargeAmount = jsonObject.get("billing_unit_count").getAsDouble();

        if (jobChargeAmount < 0) {
            throw new CommonExceptions.RequestInvalidException("非法参数billing_unit_count,必须>=0:" + jobChargeAmount);
        }

        String taskId = "";

        JsonArray itemsArray = jsonObject.get("items").getAsJsonArray();
        for (JsonElement itemElement : itemsArray) {
            JsonObject itemObject = itemElement.getAsJsonObject();
            taskId = itemObject.get("task_id").getAsString();
        }

        HelixJob helixJobDO = helixJobDAOGateway.findByTaskId(taskId);

        // 作业id已经存在
        if (helixJobDO != null) {
            throw new CommonExceptions.RequestInvalidException("任务id已经存在:" + taskId);
        }

        String accountId = LogicUserService.getAccountId();
        JsonElement element = jsonObject.get("iam_account_id");
        if (element != null && element.isJsonPrimitive() && element.getAsJsonPrimitive().isString()) {
            accountId = element.getAsString();
        } else {
            // 如果iam_account_id为空，设置iam_account_id
            jsonObject.addProperty("iam_account_id", LogicUserService.getAccountId());
            request.getEnvVars().put("extra_params", jsonObject.toString());
        }
        
        // 作业先记录db
        HelixJob helixJob = new HelixJob();
        helixJob.setClusterId(helixvsCluster);
        helixJob.setAccountId(accountId);
        helixJob.setPlatformId(String.valueOf(githubUserId));
        helixJob.setTaskId(taskId);
        helixJob.setAction(HelixJobStatus.USER_SUBMIT);
        helixJob.setJobProduct(request.getJobProduct());
        helixJob.setJobStatus(HelixJobStatus.TO_BE_SUBMITTED);
        helixJob.setJobChargeAmount(String.valueOf(jobChargeAmount));
        helixJob.setJobPrice(String.valueOf(jobChargeAmount * helixCPUPrice));
        helixJob.setExtra(JacksonUtil.toJson(request));

        try {
            Boolean insert = helixJobDAOGateway.insert(helixJob);
            if (!insert) {
                throw new CommonExceptions.RequestInvalidException("提交任务失败，请稍后重试");
            }
        } catch (Exception e) {
            throw new CommonExceptions.RequestInvalidException("任务id已经存在:" + taskId);
        }

        // 创建订单,将taskId传入订单。如果订单失败，删除db记录
        CreateOrderRequest<CreateNewTypeOrderItem> orderRequest = new CreateOrderRequest<CreateNewTypeOrderItem>();
        orderRequest.setOrderType("NEW");

        // helixfoldvs 全局订单
        orderRequest.setRegion("global");

        // 一次性订单，直接支付
        orderRequest.setIsDirectPay(true);

        List<CreateNewTypeOrderItem> requestItemList = new LinkedList<>();
        CreateNewTypeOrderItem requestItem = new CreateNewTypeOrderItem();
        requestItem.setServiceType(SaasResourceType.CHPC.name());
        requestItem.setProductType("prepay");
        requestItem.setSubProductType("project"); // *计费类型：一次性计费
        requestItem.setKey(HelixConstant.HELIX_CPU_ORDER_KEY);
        LinkedHashSet<FlavorItem> flavor = new LinkedHashSet();
        FlavorItem subServiceTypeflavorItem = new FlavorItem();
        subServiceTypeflavorItem.setName("subServiceType");
        subServiceTypeflavorItem.setValue("helix-cpu");
        subServiceTypeflavorItem.setScale(new BigDecimal(1));
        flavor.add(subServiceTypeflavorItem);
        FlavorItem versionflavorItem = new FlavorItem();
        versionflavorItem.setName("Version_0");
        versionflavorItem.setValue("version");
        versionflavorItem.setScale(BigDecimal.valueOf(jobChargeAmount));
        flavor.add(versionflavorItem);
        requestItem.setFlavor(flavor);
        requestItem.setCount(1);
        requestItem.setTime(new BigDecimal(1));
        requestItem.setTimeUnit("DAY");
        requestItem.setExtra(helixJob.getTaskId());
        requestItemList.add(requestItem);
        orderRequest.setItems(requestItemList);
        OrderCreateResponse orderCreateResponse = billingService.newOrder(orderRequest, LogicUserService.getAccountId());

        if (StringUtils.isNotEmpty(orderCreateResponse.getOrderId())) {
            // 更新订单id到db中
            Boolean success = helixJobDAOGateway.update(helixJob.getTaskId(), orderCreateResponse.getOrderId(), "", "", "", "", false);
            if (!success) {
                throw new CommonExceptions.RequestInvalidException("update helix order failed!");
            }
        } else {
            helixJobDAOGateway.delete(helixJob.getTaskId());
            String errorMsg = orderCreateResponse.getReason();
            // 欠费，反馈用户
            if ("Insufficient balance.".equals(errorMsg)) {
                errorMsg = "余额不足，请检查您是否欠费";
            }
            throw new CommonExceptions.RequestInvalidException(errorMsg);
        }

        // 返回taskId
        CommonHelixJobResponse submitHelixJobResponse = new CommonHelixJobResponse();
        submitHelixJobResponse.setTaskId(taskId);
        submitHelixJobResponse.setOrderId(orderCreateResponse.getOrderId());
        return submitHelixJobResponse;
    }

    @NotNull
    private CommonHelixJobResponse submitHelixfold3JobResponse(SubmitJobRequest request) {
        Gson gson = new Gson();

        JsonObject jsonObject = gson.fromJson(request.getEnvVars().get("extra_params"), JsonObject.class);

        Double jobChargeAmount = jsonObject.get("billing_unit_count").getAsDouble();

        if (jobChargeAmount < 0) {
            throw new CommonExceptions.RequestInvalidException("非法参数billing_unit_count,必须>=0:" + jobChargeAmount);
        }

        int nTokens = jsonObject.get("n_tokens").getAsInt();

        BigInteger githubUserId = jsonObject.get("github_real_id").getAsBigInteger();

        String taskId = "";

        JsonArray itemsArray = jsonObject.get("items").getAsJsonArray();
        for (JsonElement itemElement : itemsArray) {
            JsonObject itemObject = itemElement.getAsJsonObject();
            taskId = itemObject.get("task_id").getAsString();
        }

        HelixJob helixJobDO = helixJobDAOGateway.findByTaskId(taskId);

        // 作业id已经存在
        if (helixJobDO != null) {
            throw new CommonExceptions.RequestInvalidException("任务id已经存在:" + taskId);
        }

        String accountId = LogicUserService.getAccountId();
        JsonElement element = jsonObject.get("iam_account_id");
        if (element != null && element.isJsonPrimitive() && element.getAsJsonPrimitive().isString()) {
            accountId = element.getAsString();
        } else {
            // 如果iam_account_id为空，设置iam_account_id
            jsonObject.addProperty("iam_account_id", LogicUserService.getAccountId());
            request.getEnvVars().put("extra_params", jsonObject.toString());
        }

        // 作业先记录db
        HelixJob helixJob = new HelixJob();
        helixJob.setClusterId(helixfold3Cluster);
        helixJob.setAccountId(accountId);
        helixJob.setPlatformId(String.valueOf(githubUserId));
        helixJob.setTaskId(taskId);
        helixJob.setAction(HelixJobStatus.USER_SUBMIT);
        helixJob.setJobProduct(request.getJobProduct());
        helixJob.setJobStatus(HelixJobStatus.TO_BE_SUBMITTED);
        helixJob.setJobChargeAmount(String.valueOf(jobChargeAmount));
        helixJob.setJobPrice(String.valueOf(jobChargeAmount * helixfold3Price));
        helixJob.setJobTokenLength(String.valueOf(nTokens));
        helixJob.setExtra(JacksonUtil.toJson(request));

        try {
            Boolean insert = helixJobDAOGateway.insert(helixJob);
            if (!insert) {
                throw new CommonExceptions.RequestInvalidException("提交任务失败，请稍后重试");
            }
        } catch (Exception e) {
            throw new CommonExceptions.RequestInvalidException("任务id已经存在:" + taskId);
        }

        // 创建订单,将taskId传入订单。如果订单失败，删除db记录
        CreateOrderRequest<CreateNewTypeOrderItem> orderRequest = new CreateOrderRequest<CreateNewTypeOrderItem>();
        orderRequest.setOrderType("NEW");

        // helixfold3 全局订单
        orderRequest.setRegion("global");

        // 一次性订单，直接支付
        orderRequest.setIsDirectPay(true);

        List<CreateNewTypeOrderItem> requestItemList = new LinkedList<>();
        CreateNewTypeOrderItem requestItem = new CreateNewTypeOrderItem();
        requestItem.setServiceType(SaasResourceType.CHPC.name()); // *计费的产品名称：CHPC
        requestItem.setProductType("prepay"); // *计费类型：预付费
        requestItem.setSubProductType("project"); // *计费类型：一次性计费
        requestItem.setKey(HelixConstant.HELIXFOLD3_ORDER_KEY);
        LinkedHashSet<FlavorItem> flavor = new LinkedHashSet();
        FlavorItem subServiceTypeflavorItem = new FlavorItem();
        subServiceTypeflavorItem.setName("subServiceType");
        subServiceTypeflavorItem.setValue("HelixFold3");
        subServiceTypeflavorItem.setScale(new BigDecimal(1));
        flavor.add(subServiceTypeflavorItem);
        FlavorItem versionflavorItem = new FlavorItem();
        versionflavorItem.setName("version");
        versionflavorItem.setValue(String.valueOf(jobChargeAmount));
        versionflavorItem.setScale(new BigDecimal(1));
        flavor.add(versionflavorItem);
        requestItem.setFlavor(flavor);
        requestItem.setCount(1);
        requestItem.setTime(new BigDecimal(1));
        requestItem.setTimeUnit("DAY");
        requestItem.setExtra(helixJob.getTaskId());
        requestItemList.add(requestItem);
        orderRequest.setItems(requestItemList);
        log.debug("item: {}", orderRequest);
        OrderCreateResponse orderCreateResponse = billingService.newOrder(orderRequest, LogicUserService.getAccountId());

        if (StringUtils.isNotEmpty(orderCreateResponse.getOrderId())) {
            // 更新订单id到db中
            Boolean success = helixJobDAOGateway.update(helixJob.getTaskId(), orderCreateResponse.getOrderId(), "", "", "", "", false);
            if (!success) {
                throw new CommonExceptions.RequestInvalidException("update helix order failed!");
            }
        } else {
            helixJobDAOGateway.delete(helixJob.getTaskId());
            String errorMsg = orderCreateResponse.getReason();
            // 欠费，反馈用户
            if ("Insufficient balance.".equals(errorMsg)) {
                errorMsg = "余额不足，请检查您是否欠费";
            }
            throw new CommonExceptions.RequestInvalidException(errorMsg);
        }

        // 返回taskId
        CommonHelixJobResponse submitHelixJobResponse = new CommonHelixJobResponse();
        submitHelixJobResponse.setTaskId(taskId);
        submitHelixJobResponse.setOrderId(orderCreateResponse.getOrderId());
        return submitHelixJobResponse;
    }

    @NotNull
    private CommonHelixJobResponse submitHelixLinearFoldPartitionJobResponse(SubmitJobRequest request) {
        Gson gson = new Gson();

        JsonObject jsonObject = gson.fromJson(request.getEnvVars().get("extra_params"), JsonObject.class);

        Double jobChargeAmount = jsonObject.get("billing_unit_count").getAsDouble();

        if (jobChargeAmount < 0) {
            throw new CommonExceptions.RequestInvalidException("非法参数billing_unit_count,必须>=0:" + jobChargeAmount);
        }

        BigInteger githubUserId = jsonObject.get("github_real_id").getAsBigInteger();

        String taskId = "";

        JsonArray itemsArray = jsonObject.get("items").getAsJsonArray();
        for (JsonElement itemElement : itemsArray) {
            JsonObject itemObject = itemElement.getAsJsonObject();
            taskId = itemObject.get("task_id").getAsString();
        }

        HelixJob helixJobDO = helixJobDAOGateway.findByTaskId(taskId);

        // 作业id已经存在
        if (helixJobDO != null) {
            throw new CommonExceptions.RequestInvalidException("任务id已经存在:" + taskId);
        }

        String accountId = LogicUserService.getAccountId();
        JsonElement element = jsonObject.get("iam_account_id");
        if (element != null && element.isJsonPrimitive() && element.getAsJsonPrimitive().isString()) {
            accountId = element.getAsString();
        } else {
            // 如果iam_account_id为空，设置iam_account_id
            jsonObject.addProperty("iam_account_id", LogicUserService.getAccountId());
            request.getEnvVars().put("extra_params", jsonObject.toString());
        }

        // 作业先记录db
        HelixJob helixJob = new HelixJob();
        helixJob.setClusterId(helixvsCluster);
        helixJob.setAccountId(accountId);
        helixJob.setPlatformId(String.valueOf(githubUserId));
        helixJob.setTaskId(taskId);
        helixJob.setAction(HelixJobStatus.USER_SUBMIT);
        helixJob.setJobProduct(request.getJobProduct());
        helixJob.setJobStatus(HelixJobStatus.TO_BE_SUBMITTED);
        helixJob.setJobChargeAmount(String.valueOf(jobChargeAmount));
        helixJob.setJobPrice(String.valueOf(jobChargeAmount * helixfold3Price));
        helixJob.setExtra(JacksonUtil.toJson(request));

        try {
            Boolean insert = helixJobDAOGateway.insert(helixJob);
            if (!insert) {
                throw new CommonExceptions.RequestInvalidException("提交任务失败，请稍后重试");
            }
        } catch (Exception e) {
            throw new CommonExceptions.RequestInvalidException("任务id已经存在:" + taskId);
        }

        // 创建订单,将taskId传入订单。如果订单失败，删除db记录
        CreateOrderRequest<CreateNewTypeOrderItem> orderRequest = new CreateOrderRequest<CreateNewTypeOrderItem>();
        orderRequest.setOrderType("NEW");

        // helixfold3 全局订单
        orderRequest.setRegion("global");

        // 一次性订单，直接支付
        orderRequest.setIsDirectPay(true);

        List<CreateNewTypeOrderItem> requestItemList = new LinkedList<>();
        CreateNewTypeOrderItem requestItem = new CreateNewTypeOrderItem();
        requestItem.setServiceType(SaasResourceType.CHPC.name()); // *计费的产品名称：CHPC
        requestItem.setProductType("prepay"); // *计费类型：预付费
        requestItem.setSubProductType("project"); // *计费类型：一次性计费
        requestItem.setKey(HelixConstant.HELIXFOLD3_ORDER_KEY);
        LinkedHashSet<FlavorItem> flavor = new LinkedHashSet();
        FlavorItem subServiceTypeflavorItem = new FlavorItem();
        subServiceTypeflavorItem.setName("subServiceType");
        subServiceTypeflavorItem.setValue("HelixFold3");
        subServiceTypeflavorItem.setScale(new BigDecimal(1));
        flavor.add(subServiceTypeflavorItem);
        FlavorItem versionflavorItem = new FlavorItem();
        versionflavorItem.setName("version");
        versionflavorItem.setValue(String.valueOf(jobChargeAmount));
        versionflavorItem.setScale(new BigDecimal(1));
        flavor.add(versionflavorItem);
        requestItem.setFlavor(flavor);
        requestItem.setCount(1);
        requestItem.setTime(new BigDecimal(1));
        requestItem.setTimeUnit("DAY");
        requestItem.setExtra(helixJob.getTaskId());
        requestItemList.add(requestItem);
        orderRequest.setItems(requestItemList);
        log.debug("item: {}", orderRequest);
        OrderCreateResponse orderCreateResponse = billingService.newOrder(orderRequest, LogicUserService.getAccountId());

        if (StringUtils.isNotEmpty(orderCreateResponse.getOrderId())) {
            // 更新订单id到db中
            Boolean success = helixJobDAOGateway.update(helixJob.getTaskId(), orderCreateResponse.getOrderId(), "", "", "", "", false);
            if (!success) {
                throw new CommonExceptions.RequestInvalidException("update helix order failed!");
            }
        } else {
            helixJobDAOGateway.delete(helixJob.getTaskId());
            String errorMsg = orderCreateResponse.getReason();
            // 欠费，反馈用户
            if ("Insufficient balance.".equals(errorMsg)) {
                errorMsg = "余额不足，请检查您是否欠费";
            }
            throw new CommonExceptions.RequestInvalidException(errorMsg);
        }

        // 返回taskId
        CommonHelixJobResponse submitHelixJobResponse = new CommonHelixJobResponse();
        submitHelixJobResponse.setTaskId(taskId);
        submitHelixJobResponse.setOrderId(orderCreateResponse.getOrderId());
        return submitHelixJobResponse;
    }

    @Override
    public CommonHelixJobResponse cancelHelixJob(String taskId, String action) {
        if (!globalBusRegistered) {
            throw new CommonException.RequestInvalidException("该地域目前不支持此接口");
        }

        if (!"cancel".equals(action)) {
            throw new CommonExceptions.RequestInvalidException("不支持的action: " + action);
        }

        String regex = "^[a-zA-Z0-9_]*$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(taskId);
        if (!matcher.matches()) {
            throw new CommonExceptions.RequestInvalidException("非法的taskId: " + action);
        }

        CommonHelixJobResponse submitHelixJobResponse = new CommonHelixJobResponse();
        submitHelixJobResponse.setTaskId(taskId);

        HelixJob helixJob = helixJobDAOGateway.findByTaskId(taskId);

        // 作业id不存在
        if (helixJob == null) {
            throw new CommonExceptions.RequestInvalidException("任务id不存在:" + taskId);
        }

        // 兼容多次调用
        if (HelixJobStatus.USER_CANCEL.equals(helixJob.getAction())) {
            return submitHelixJobResponse;
        }

        // 1、先更新db，如果插件执行失败，依赖订单执行器一直回调
        helixJobDAOGateway.update(taskId, "", "", HelixJobStatus.CANCELLED, HelixJobStatus.USER_CANCEL, "", false);

        // 2、调用插件stop任务
        if (StringUtils.isNotEmpty(helixJob.getJobId())) {

            String arguments = " --job-id " + "\'" + helixJob.getJobId() + "\' --force";
            try {
                BackendActionProxyResponse resp = backendGateway.actionProxy(helixJob.getClusterId(), "job_stop", arguments);
                if (resp.getCode() != 200) {
                    throw new CommonException.RelatedServiceException(
                            "cancel job failed, " + resp.getMessage(), "scheduler plugin");
                }
            } catch (WebClientResponseException e) {
                // cluser-api正常，但是scancel失败了，说明任务已经是终态了
                if (e.getResponseBodyAsString().contains("调度器插件脚本调用失败")) {
                    // 更新作业为提交失败
                    throw new CommonException.RequestInvalidException(
                            "任务已经是终态,无法取消");
                }
                // cluster-api异常
                throw new CommonException.RelatedServiceException(
                        "集群异常，请稍后重试: " + e.getResponseBodyAsString(), "scheduler plugin");
            } catch (Exception e) {
                // cluser-api正常，但是scancel失败了，说明任务已经是终态了
                if (e.getMessage().contains("调度器插件脚本调用失败")) {
                    // 更新作业为提交失败
                    throw new CommonException.RequestInvalidException(
                            "任务已经是终态,无法取消");
                }
                // cluster-api异常
                throw new CommonException.RelatedServiceException(
                        "集群异常，请稍后重试: " + e.getMessage(), "scheduler plugin");
            }
        }

        // 3、返回taskId
        return submitHelixJobResponse;
    }

    @Override
    public ListAvailableTagsResponse listAvailableTags(String clusterId) {
        if (!ServiceUtil.isClusterUsingSchedulerPlugin(clusterDAOGateway, clusterId)) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        if (cluster == null) {
            throw new CommonException.RequestInvalidException("cluster is not exist");
        }
        ListAvailableTagsResponse availableTagsResponse = new ListAvailableTagsResponse();
        try {
            com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ListAvailableTagsResponse listAvailableTagsResponse = backendGateway.listAvailableTags(clusterId);

            if (listAvailableTagsResponse.getTags() != null) {
                availableTagsResponse.setResult(listAvailableTagsResponse.getTags());
                availableTagsResponse.setPageNo(1);
                availableTagsResponse.setPageSize(listAvailableTagsResponse.getTags().size());
                availableTagsResponse.setTotalCount(listAvailableTagsResponse.getTags().size());
            }
        } catch (Exception e) {
            // 说明没有可用tags
            log.error("cluster is no available tags");
        }

        return availableTagsResponse;
    }

    @Override
    public BaseResponse mdifyTags(String clusterId, String jobId, UpdateTagsRequest request) {
        if (!ServiceUtil.isClusterUsingSchedulerPlugin(clusterDAOGateway, clusterId)) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        if (cluster == null) {
            throw new CommonException.RequestInvalidException("cluster is not exist");
        }
        if (request.getTags() != null) {
            BackendCommonResponse commonResponse =
                    backendGateway.modifyTags(clusterId, jobId, request.getTags());
            CommonValidateUtil.validBackendCommonResponse(commonResponse);
        }
        BaseResponse response = new BaseResponse();

        response.setClusterName(cluster.getName());
        return response;
    }

    private void parseParameters(String clusterId, SubmitJobRequest request) {

        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<SubmitJobRequest>> violations = validator.validate(request);
        if (!violations.isEmpty()) {
            throw new CommonExceptions.RequestInvalidException(violations.iterator().next().getMessage());
        }
        if (request.getNhosts() != null) {
            if (request.getNhosts() < 0 || request.getNhosts() > 10000) {
                throw new CommonExceptions.RequestInvalidException("nhosts must be >= 0 and <= 10000");
            }
        }
        if (request.getLimitTimeInMinutes() != null) {
            if (request.getLimitTimeInMinutes() < 0 || request.getLimitTimeInMinutes() > 518400) {
                throw new CommonExceptions.RequestInvalidException("limitTimeInMinutes must be >= 0 and <= 518400");
            }
        }
        if (request.getEnvVars() != null) {
            if (request.getEnvVars().size() > 1000) {
                throw new CommonExceptions.RequestInvalidException("envVars size must be <= 1000");
            }
        }
        if (!StringUtils.isEmpty(request.getQueue())) {
            // 校验队列是否存在和状态
            BackendActionProxyResponse resp = new BackendActionProxyResponse();
            List<QueueVO> queueList = new ArrayList<>();
            try {
                String arguments = "--console -q " + request.getQueue();
                resp = backendGateway.actionProxy(clusterId, "queue_list", arguments);
            } catch (Exception e) {
                log.error("queue not exist");
                throw new CommonExceptions.RequestInvalidException("queue not exist");
            }
            try {
                ObjectMapper mapper = new ObjectMapper();
                queueList =
                        mapper.readValue(
                                resp.getData(), mapper.getTypeFactory().constructCollectionType(List.class, QueueVO.class));
            } catch (Exception e) {
                log.error("json unmarshal queueList failed", e);
                throw new CommonExceptions.InternalServerErrorException();
            }
            if (!QueueStatus.ACTIVE.nameLowerCase().equals(queueList.get(0).getStatus())) {
                throw new CommonExceptions.RequestInvalidException("queue is not active");
            }
            QueueVO queue = queueList.get(0);
            Boolean isAuthorized = false;
            for (String user : queue.getUserList()) {
                if (user.equals(getSubjectToken().getUserName())) {
                    isAuthorized = true;
                    break;
                }
            }
            if (!isAuthorized) {
                throw new CommonExceptions.RequestInvalidException(
                        "user is not authorized to submit job to this queue");
            }
        }

        if (!StringUtils.isEmpty(request.getBosFilePath())) {
            // 校验bos路径格式 https://$bucket.$region.bcebos.com/$object?authorization=xxx
            // 提取bucket和object
            Pattern pattern =
                    Pattern.compile(
                            "https://([^\\.]+)\\." + regionConfiguration.getCurrentRegion() + "\\.bcebos\\.com/([^\\?]+)");
            Matcher matcher = pattern.matcher(request.getBosFilePath());
            if (matcher.find()) {
                String bucket = matcher.group(1);
                String object = matcher.group(2);
                // 校验 bucket 长度为 3-63，允许小写字母、数字和 -，开头和结尾必须是小写字母或数字
                if (!Pattern.matches("^[a-z0-9]([a-z0-9\\-]{1,61}[a-z0-9])?$", bucket)) {
                    throw new CommonExceptions.RequestInvalidException("bucket format error");
                }
                // 校验 object 长度不超过 1024
                if (object.length() > 1024) {
                    throw new CommonExceptions.RequestInvalidException("object length error");
                }
            } else {
                throw new CommonExceptions.RequestInvalidException("bos path format error");
            }
        }
    }

    // 通用作业提交参数验证
    private void validateJobSubmitParameters(String clusterId, SubmitJobRequest request) {
        if (request == null) {
            throw new CommonExceptions.RequestInvalidException();
        }
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        Validator validator = factory.getValidator();
        Set<ConstraintViolation<SubmitJobRequest>> violations = validator.validate(request);
        if (!violations.isEmpty()) {
            throw new CommonExceptions.RequestInvalidException(violations.iterator().next().getMessage());
        }
        // 如果没有指定提交作业的用户直接throw
        if (StringUtils.isEmpty(request.getUsername())) {
            throw new CommonExceptions.RequestInvalidException("没有指定提交作业的用户名");
        }

        if (StringUtils.isEmpty(request.getPassword())) {
            throw new CommonExceptions.RequestInvalidException("域用户密码不能为空");
        }

        if (StringUtils.isEmpty(request.getJobCmd()) || StringUtils.isEmpty(request.getQueue()) ||
                StringUtils.isEmpty(request.getUsername())) {
            log.error("jobCmd or queueName or username is empty");
            throw new CommonExceptions.RequestInvalidException();
        }
        // 填写则节点数至少为1
        if (request.getNhosts() != null) {
            if (request.getNhosts() < 1 || request.getNhosts() > 10000) {
                throw new CommonExceptions.RequestInvalidException("nhosts must be >= 1 and <= 10000");
            }
        }
        // 不填则默认使用一个节点
        if (request.getNhosts() == null) {
            request.setNhosts(1);
        }
        // 填写则cpu至少为1
        if (request.getNcpus() != null) {
            if (request.getNcpus() <= 0) {
                throw new CommonExceptions.RequestInvalidException("cpu数量至少为1");
            }
        }
        // 不填默认使用一个CPU
        if (request.getNcpus() == null) {
            request.setNcpus(1);
        }
        if (request.getLimitTimeInMinutes() != null) {
            if (request.getLimitTimeInMinutes() < 0 || request.getLimitTimeInMinutes() > 518400) {
                throw new CommonExceptions.RequestInvalidException("limitTimeInMinutes must be >= 0 and <= 518400");
            }
        }
        if (request.getEnvVars() != null) {
            if (request.getEnvVars().size() > 1000) {
                throw new CommonExceptions.RequestInvalidException("envVars size must be <= 1000");
            }
        }
        log.debug("ak:{}", AccessKeyThreadHolder.getAccessKey());

        String plainTextPassword = request.getPassword();
        // aes加密传到cluster-api
        String cipherTextPassword = AesEncryptUtil.encrypt(plainTextPassword);
        // 自测
        // String cipherTextPassword = request.getPassword();
        BackendCommonResponse backendCommonResponse;
        // 验证域账号是否正确
        try {
            // 如果异常直接抛出
            backendCommonResponse = backendGateway.authDomainUser(clusterId,
                    request.getUsername(), cipherTextPassword);
        } catch (Exception e) {
            // cluster-api无法收到请求
            throw new CommonExceptions.InternalServerErrorException();
        }
        // cluster-api执行失败
        log.info("user:{} auth success", request.getUsername());
        if (backendCommonResponse.getCode() != 200) {
            throw new CommonExceptions.RequestInvalidException(backendCommonResponse.getMessage());
        }

        if (!StringUtils.isEmpty(request.getQueue())) {
            // 校验队列是否存在和状态
            BackendActionProxyResponse resp = new BackendActionProxyResponse();
            List<QueueVO> queueList = new ArrayList<>();
            try {
                String arguments = "--console -q " + request.getQueue();
                resp = backendGateway.actionProxy(clusterId, "queue_list", arguments);
            } catch (Exception e) {
                log.error("queue not exist");
                throw new CommonExceptions.RequestInvalidException("queue not exist");
            }
            try {
                ObjectMapper mapper = new ObjectMapper();
                queueList =
                        mapper.readValue(
                                resp.getData(), mapper.getTypeFactory().constructCollectionType(List.class, QueueVO.class));
            } catch (Exception e) {
                log.error("json unmarshal queueList failed", e);
                throw new CommonExceptions.InternalServerErrorException();
            }
            if (!QueueStatus.ACTIVE.nameLowerCase().equals(queueList.get(0).getStatus())) {
                throw new CommonExceptions.RequestInvalidException("queue is not active");
            }
            QueueVO queue = queueList.get(0);
            Boolean isAuthorized = false;
            for (String user : queue.getUserList()) {
                if (user.equals(request.getUsername())) {
                    isAuthorized = true;
                    break;
                }
            }
            if (!isAuthorized) {
                throw new CommonExceptions.RequestInvalidException(
                        "user is not authorized to submit job to this queue");
            }
        }

        if (!StringUtils.isEmpty(request.getBosFilePath())) {
            // 校验bos路径格式 https://$bucket.$region.bcebos.com/$object?authorization=xxx
            // 提取bucket和object
            Pattern pattern =
                    Pattern.compile(
                            "https://([^\\.]+)\\." + regionConfiguration.getCurrentRegion() + "\\.bcebos\\.com/([^\\?]+)");
            Matcher matcher = pattern.matcher(request.getBosFilePath());
            if (matcher.find()) {
                String bucket = matcher.group(1);
                String object = matcher.group(2);
                // 校验 bucket 长度为 3-63，允许小写字母、数字和 -，开头和结尾必须是小写字母或数字
                if (!Pattern.matches("^[a-z0-9]([a-z0-9\\-]{1,61}[a-z0-9])?$", bucket)) {
                    throw new CommonExceptions.RequestInvalidException("bucket format error");
                }
                // 校验 object 长度不超过 1024
                if (object.length() > 1024) {
                    throw new CommonExceptions.RequestInvalidException("object length error");
                }
            } else {
                throw new CommonExceptions.RequestInvalidException("bos path format error");
            }
        }
    }


    @Override
    public BaseResponse createJobTemplate(String clusterId, JobTemplateRequest jobTemplateRequest) {
        if (jobTemplateRequest == null) {
            throw new CommonExceptions.RequestInvalidException("request parameter cannot empty");
        }
        if (StringUtils.isEmpty(jobTemplateRequest.getName())) {
            throw new CommonExceptions.RequestInvalidException("模版名不能为空");
        }
        JobTemplate jobTemplate = jobTemplateDAOGateway.findByTemplateNameAndClusterId(jobTemplateRequest.getName(), clusterId);
        if (jobTemplate != null) {
            throw new CommonExceptions.RequestInvalidException("集群已存在同名模版，请修改名称");
        }
        Gson gson = new Gson();
        JobTemplate template = new JobTemplate();
        BeanCopyUtil.copyProperties(jobTemplateRequest, template);
        // class的字段名称不一样，做一下转换
        if (jobTemplateRequest.getQueue() != null) {
            template.setQueueName(jobTemplateRequest.getQueue());
        }
        if (jobTemplateRequest.getLimitTimeInMinutes() != null) {
            template.setWalltime(jobTemplateRequest.getLimitTimeInMinutes());
        }
        if (jobTemplateRequest.getEnvVars() != null) {
            String envString = gson.toJson(jobTemplateRequest.getEnvVars());
            template.setEnvVars(envString);
        }
        template.setName(jobTemplateRequest.getName());
        template.setClusterId(clusterId);
        try {
            Boolean res = jobTemplateDAOGateway.insertTemplate(template);
            if (!res) {
                log.error("insert job template failed");
                throw new CommonExceptions.InternalServerErrorException();
            }
        } catch (Exception e) {
            // 唯一key冲突才会抛出异常
            log.error("insert job template failed, maybe unique index conflict", e);
            throw new CommonExceptions.RequestInvalidException("集群中已存在同名模版");
        }
        BaseResponse res = new BaseResponse();
        return res;
    }

    @Override
    public JobTemplatesResponse queryTemplateList(String clusterId) {
        Gson gson = new Gson();
        JobTemplatesResponse jobTemplatesResponse = new JobTemplatesResponse();
        // 查询集群下所有的模版
        List<JobTemplate> jobTemplates = jobTemplateDAOGateway.findTemplatesByCluster(clusterId);
        // 如果结果为空直接返回
        if (CollectionUtils.isEmpty(jobTemplates)) {
            return jobTemplatesResponse;
        }

        // 将JobTemplate转换为JobTemplatesResponse.JobTemplateResponse
        List<JobTemplatesResponse.JobTemplateResponse> templateList = jobTemplates.stream().map(jobTemplate -> {
            JobTemplatesResponse.JobTemplateResponse jobTemplateResponse = new JobTemplatesResponse.JobTemplateResponse();
            BeanCopyUtil.copyProperties(jobTemplate, jobTemplateResponse);
            // 转换字段名
            jobTemplateResponse.setQueue(jobTemplate.getQueueName());
            jobTemplateResponse.setLimitTimeInMinutes(jobTemplate.getWalltime());
            Map<String, String> envMap = gson.fromJson(jobTemplate.getEnvVars(), new TypeToken<>() {
            });
            jobTemplateResponse.setEnvVars(envMap);
            return jobTemplateResponse;
        }).collect(Collectors.toList());

        jobTemplatesResponse.setTemplateList(templateList);
        return jobTemplatesResponse;
    }

    @Override
    public BaseResponse modifyJobTemplate(String clusterId, JobTemplateRequest jobTemplateRequest) {
        if (jobTemplateRequest == null) {
            throw new CommonExceptions.RequestInvalidException("request parameter cannot empty");
        }
        if (StringUtils.isEmpty(jobTemplateRequest.getName())) {
            throw new CommonExceptions.RequestInvalidException("模版名不能为空");
        }
        JobTemplate jobTemplate = jobTemplateDAOGateway.findByTemplateNameAndClusterId(jobTemplateRequest.getName(), clusterId);
        if (jobTemplate == null) {
            throw new CommonExceptions.RequestInvalidException("集群中不存在该模版");
        }
        Gson gson = new Gson();
        JobTemplate template = new JobTemplate();
        BeanCopyUtil.copyProperties(jobTemplateRequest, template);
        // 转换字段名
        if (jobTemplateRequest.getQueue() != null) {
            template.setQueueName(jobTemplateRequest.getQueue());
        }
        if (jobTemplateRequest.getLimitTimeInMinutes() != null) {
            template.setWalltime(jobTemplateRequest.getLimitTimeInMinutes());
        }
        if (jobTemplateRequest.getEnvVars() != null) {
            template.setEnvVars(gson.toJson(jobTemplateRequest.getEnvVars()));
        }
        template.setClusterId(clusterId);
        template.setName(jobTemplateRequest.getName());
        try {
            Boolean res = jobTemplateDAOGateway.updateTemplate(template);
            if (!res) {
                log.error("update job template failed. maybe job template not exist");
                throw new CommonExceptions.InternalServerErrorException();
            }
        } catch (Exception e) {
            log.error("update job template failed", e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        return new BaseResponse();
    }

    @Override
    public BaseResponse deleteJobTemplate(String clusterId, String templateName) {
        if (StringUtils.isEmpty(templateName)) {
            throw new CommonExceptions.RequestInvalidException("模版名不能为空");
        }
        JobTemplate jobTemplate = jobTemplateDAOGateway.findByTemplateNameAndClusterId(templateName, clusterId);
        if (jobTemplate == null) {
            throw new CommonExceptions.RequestInvalidException("集群中不存在该模版");
        }
        try {
            Boolean res = jobTemplateDAOGateway.deleteTemplate(clusterId, templateName);
            if (!res) {
                log.error("delete job template failed. maybe job template not exist");
                throw new CommonExceptions.InternalServerErrorException();
            }
        } catch (Exception e) {
            log.error("delete job template failed", e);
            throw new CommonExceptions.InternalServerErrorException();
        }
        return new BaseResponse();
    }

    @Override
    public BatchJobResponse batchProcessJob(String clusterId, String action, BatchJobRequest batchJobRequest) {
        if (StringUtils.isEmpty(action)) {
            throw new CommonExceptions.RequestInvalidException();
        }
        List<String> jobIds = batchJobRequest.getJobIds();
        if (CollectionUtils.isEmpty(jobIds)) {
            throw new CommonExceptions.RequestInvalidException("jobIds cannot be empty");
        }
        if (jobIds.size() > 10) {
            throw new CommonExceptions.RequestInvalidException("一次最多操作10个作业");
        }
        BiFunction<String, String, BackendActionProxyResponse> actionFunc = getJobProcessFunc(action);
        // 如果不存在对应的处理函数
        if (actionFunc == null) {
            throw new CommonExceptions.RequestInvalidException();
        }
        List<BatchJobResponse.JobItem> successItems = new ArrayList<>();
        List<BatchJobResponse.JobItem> failedItems = new ArrayList<>();
        // 对每一个job单独处理
        for (int i = 0; i < jobIds.size(); ++i) {
            BatchJobResponse.JobItem jobItem = new BatchJobResponse.JobItem();
            String jobId = jobIds.get(i);
            jobItem.setJobId(jobId);
            if (StringUtils.isEmpty(jobId)) {
                jobItem.setMsg("作业id为空");
                failedItems.add(jobItem);
                // 继续处理下一个
                continue;
            }
            try {
                // 调用cluster-api处理作业
                BackendActionProxyResponse resp = actionFunc.apply(clusterId, jobId);
                if (resp.getCode() != 200) {
                    // cluster-api执行作业处理失败
                    jobItem.setMsg(resp.getMessage());
                    failedItems.add(jobItem);
                } else {
                    // 作业处理成功
                    jobItem.setMsg("success");
                    successItems.add(jobItem);
                }
            } catch (CommonException.RelatedServiceException e) {
                log.error("job process error, action: {}, jobsIds:{}", action, jobIds, e);
                jobItem.setMsg(e.getMessage());
                failedItems.add(jobItem);
                // 如果是调度脚本失败继续处理后面的job（可能是作业id有问题）
                if (e.getCode().contains("scheduler plugin")) {
                    continue;
                }
                // 连接cluster-api异常or未知异常，后续作业不进行处理直接标记为失败(可能是master节点有问题)
                for (int j = i + 1; j < jobIds.size(); ++j) {
                    BatchJobResponse.JobItem fJobItem = new BatchJobResponse.JobItem();
                    fJobItem.setJobId(jobIds.get(j));
                    failedItems.add(fJobItem);
                }
                break;
            }
        }
        BatchJobResponse response = new BatchJobResponse();
        response.setSuccessJobItems(successItems);
        response.setFailedJobItems(failedItems);
        // bct msg
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        if (cluster != null) {
            response.setClusterName(cluster.getName());
            String successMsg = successItems.stream().map(BatchJobResponse.JobItem::getJobId).collect(Collectors.joining("，"));
            String failedMsg = failedItems.stream().map(BatchJobResponse.JobItem::getJobId).collect(Collectors.joining("，"));
            String bctMsg = String.format("集群:%s(ID:%s)，成功%s作业:%s，失败作业:%s", cluster.getName(),
                    clusterId, action, successMsg, failedMsg);
            response.setClusterName(cluster.getName());
            response.setMessage(bctMsg);
        }
        return response;
    }


    private BiFunction<String, String, BackendActionProxyResponse> getJobProcessFunc(String action) {
        return switch (action) {
            case "stopJob" -> this::stopJob;
            case "deleteJob" -> this::deleteJob;
            default -> null;
        };
    }

    private BackendActionProxyResponse stopJob(String clusterId, String jobId) {
        String arguments = " --job-id " + "\'" + jobId + "\'";
        return backendGateway.actionProxy(clusterId, "job_stop", arguments);
    }

    private BackendActionProxyResponse deleteJob(String clusterId, String jobId) {
        String arguments = " --job-id " + "\'" + jobId + "\'";
        return backendGateway.actionProxy(clusterId, "job_delete", arguments);
    }

}
