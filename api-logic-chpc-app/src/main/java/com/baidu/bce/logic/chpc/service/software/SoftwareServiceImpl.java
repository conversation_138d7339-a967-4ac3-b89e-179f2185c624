package com.baidu.bce.logic.chpc.service.software;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.logic.chpc.bcc.BccInstance;
import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.SoftwareStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Software;
import com.baidu.bce.logic.chpc.model.SoftwareRecord;
import com.baidu.bce.logic.chpc.service.ISoftwareService;
import com.baidu.bce.logic.chpc.software.model.InstallSoftwareRequest;
import com.baidu.bce.logic.chpc.software.model.InstallSoftwareResponse;
import com.baidu.bce.logic.chpc.software.model.QuerySoftwareForNodeRequest;
import com.baidu.bce.logic.chpc.software.model.QuerySoftwareForNodeResponse;
import com.baidu.bce.logic.chpc.software.model.SoftwareInfo;
import com.baidu.bce.logic.chpc.software.model.SoftwareQueryResponse;
import com.baidu.bce.logic.chpc.software.model.SoftwareVersion;
import com.baidu.bce.logic.chpc.software.model.SoftwareVersionList;
import com.baidu.bce.logic.chpc.software.model.UnInstallSoftwareRequest;
import com.baidu.bce.logic.chpc.software.model.UnInstallSoftwareResponse;
import com.baidu.bce.logic.chpc.software.model.VersionInfo;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SoftwareServiceImpl implements ISoftwareService {

    @Value("${software.checkArch:true}")
    private boolean checkSoftwareArch;

    @Resource
    SoftwareDAOGateway softwareDAOGateway;

    @Resource
    SoftwareRecordDAOGateway softwareRecordDAOGateway;

    @Resource
    TaskService taskService;

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Resource
    BccGateway bccGateway;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    /**
     * 获取实例的架构类型，如果不能确定则返回"amd64"
     *
     * @param instance Instance类型，表示ECS实例对象
     * @return String类型，返回实例的架构类型，默认为"amd64"
     */
    public String getInstanceArch(Instance instance) {
        if (!checkSoftwareArch) {
            return "amd64";
        }
        List<String> instanceIds = new ArrayList<>();
        instanceIds.add(instance.getInstanceId());
        List<BccInstance> bccInstances = bccGateway.getBccInstances(instanceIds);

        String instanceArch = "amd64";

        if (bccInstances.size() == 1) {
            BccInstance masterInstance = bccInstances.get(0);
            instanceArch = masterInstance.getOsArch();
        }
        if (instanceArch.contains("amd64") || instanceArch.contains("x86")) {
            instanceArch = "amd64";
        }

        return instanceArch;
    }

    /**
     * {@inheritDoc}
     * 根据集群 id 和名称查询软件列表，包括已安装、可安装和已卸载的软件。
     * 首先根据集群 id 查询集群的机器架构，然后查询安装记录，将安装记录按照软件名-版本号分组，
     * 如果不存在对应的安装记录，则视为可安装状态；接着查询软件列表，过滤出支持当前集群机器架构的软件，
     * 最后合并展示结果，将软件列表按照软件名分组，每个软件下面都会显示所有版本，包括已安装、可安装和已卸载的版本。
     *
     * @param clusterId 集群 id
     * @param name      软件名，如果为空字符串或者空白字符串，则返回所有软件列表，否则只返回指定名称的软件列表
     * @return {@link SoftwareQueryResponse} 软件查询响应，包含软件列表
     * @throws BceException 如果获取管理节点信息异常，则抛出此异常
     */
    @Override
    public SoftwareQueryResponse getSoftwareList(String clusterId, String name) {
        String masterArch = "amd64";
        Map<String, VersionInfo> versionInfos = new HashMap<>();
        if (clusterId != null && StringUtils.isNotEmpty(clusterId)) {
            // 根据 clusterId 查询集群的机器架构
            List<Instance> instances = instanceDAOGateway.findByClusterId(clusterId);
            if (instances.isEmpty()) {
                log.error("getMasterInstance error: cluster instance is empty");
                throw new BceException("获取管理节点信息异常");
            }
            Instance masterInstance = instances
                    .stream()
                    .filter(v -> v.getNodeType().equalsIgnoreCase(InstanceNodeType.MASTER.getType()))
                    .findFirst()
                    .get();
            if (masterInstance == null) {
                log.error("getMasterInstance error: master instance is empty");
                throw new BceException("获取管理节点信息异常");
            }

            try {
                masterArch = getInstanceArch(masterInstance);
                log.info("=====>masterArch: {}", masterArch);
            } catch (Exception e) {
                log.error("getInstanceArch error: {}", e);
                throw new BceException(String.format("获取管理节点信息异常：%s", e.getMessage()));
            }

            // 查询安装记录
            List<SoftwareRecord> records = softwareRecordDAOGateway.findSoftwareByClusterId(clusterId);
            for (SoftwareRecord record : records) {
                log.info("record: {}", record);
                String key = String.format("%s-%s", record.getName(), record.getVersion());
                VersionInfo versionInfo = new VersionInfo();
                versionInfo.setStatus(record.getStatus());
                versionInfo.setValue(record.getVersion());
                versionInfo.setCreatedTime(record.getCreatedTime());
                versionInfo.setMsg(record.getMsg());

                versionInfos.put(key, versionInfo);
            }
            log.info("versionInfos: {}", versionInfos);
        }

        // 查询软件列表
        List<Software> softwareInfos;
        if (name.isEmpty()) {
            softwareInfos = softwareDAOGateway.findAll();
        } else {
            softwareInfos = softwareDAOGateway.findSoftwareByNameOrCategory(name);
        }
        log.info("softwareInfos: {}", softwareInfos);

        // 合并展示
        SoftwareQueryResponse response = new SoftwareQueryResponse();
        response.setSoftwareList(new ArrayList<>());

        Map<String, SoftwareInfo> softwareInfosMap = new HashMap<>();
        for (Software s : softwareInfos) {
            log.info("software: {}", s.getName());
            if (!s.getSupportArch().contains(masterArch)) {
                log.info(
                        "software {} {} arch: {} is not supported by cluster instance arch: {}, ignore",
                        s.getName(),
                        s.getVersion(),
                        s.getSupportArch(),
                        masterArch);
                continue;
            }

            // 过滤安装在计算节点上的软件
            if (s.getNodeType() != null
                    && InstanceNodeType.COMPUTE.getType().equalsIgnoreCase(s.getNodeType())) {
                continue;
            }

            SoftwareInfo softwareInfo;
            // 判断软件是否在结果集中
            if (softwareInfosMap.containsKey(s.getName())) {
                softwareInfo = softwareInfosMap.get(s.getName());
            } else {
                softwareInfo = new SoftwareInfo();
                softwareInfo.setName(s.getName());
                softwareInfo.setCategory(s.getCategory());
                softwareInfo.setDescription(s.getDescription());
                softwareInfo.setStatus(SoftwareStatus.INSTALLABLE.nameLowerCase());
            }
            log.info("softwareInfo: {}", softwareInfo);
            // 判断 version 是否在结果集中
            if (softwareInfo.getVersionList() == null) {
                softwareInfo.setVersionList(new ArrayList<>());
            }
            boolean exists = softwareInfo.getVersionList().stream().anyMatch(v -> v.getValue().equals(s.getVersion()));

            if (!exists) {
                if (versionInfos.containsKey(String.format("%s-%s", s.getName(), s.getVersion()))) {
                    // 在安装记录中存在，使用安装记录的状态
                    VersionInfo v = versionInfos.get(String.format("%s-%s", s.getName(), s.getVersion()));
                    softwareInfo.getVersionList().add(v);
                    softwareInfo.setStatus(v.getStatus());
                } else {
                    // 没有安装记录，则为可安装状态
                    VersionInfo versionInfo = new VersionInfo();
                    versionInfo.setValue(s.getVersion());
                    versionInfo.setStatus(SoftwareStatus.INSTALLABLE.nameLowerCase());
                    versionInfo.setMsg("");
                    softwareInfo.getVersionList().add(versionInfo);
                }
            }
            softwareInfosMap.put(s.getName(), softwareInfo);
        }

        for (SoftwareInfo softwareInfo : softwareInfosMap.values()) {
            response.getSoftwareList().add(softwareInfo);
        }

        return response;
    }

    /**
     * {@inheritDoc}
     * 根据集群ID和安装软件请求对象，安装指定的软件。如果软件已经安装过，则会被忽略。
     * 安装软件时，首先会查询该集群下是否有正在安装或卸载的任务，如果有则会等待任务完成后再安装。
     * 安装软件时，如果软件版本已经存在于安装列表中，则会直接忽略。
     * 安装软件时，如果软件不存在于可安装列表中，则会抛出异常。
     * 安装软件时，如果软件已经安装过，则会直接返回。
     * 安装软件时，如果软件已经安装过但是安装失败，则会自动删除之前的安装记录，并重新安装。
     *
     * @param clusterId 集群ID
     * @param request   安装软件请求对象，包含需要安装的软件名称和版本号
     * @return 安装软件响应对象，包含安装结果和安装列表
     * @throws BceException 如果软件不存在于可安装列表中，则抛出异常
     */
    @Override
    public InstallSoftwareResponse installSoftware(String clusterId, InstallSoftwareRequest request) {
        List<SoftwareVersionList> installList = new ArrayList<>();
        if (request != null && request.getSoftwareList() != null) {
            for (SoftwareVersionList softwareInfo : request.getSoftwareList()) {
                String softwareName = softwareInfo.getName();

                List<Instance> instances = instanceDAOGateway.findByClusterId(clusterId);
                // chpc-app-server安装必须有登录节点
                if (ChpcConstant.CHPC_APP_SERVER.equals(softwareName)
                        || ChpcConstant.CHPC_VNC_SERVER.equals(softwareName)) {
                    if (instances.isEmpty()) {
                        log.error("getLoginInstance error: cluster instance is empty");
                        throw new BceException("chpc-app-server、vncserver 等安装需要有登录节点");
                    }
                    Optional<Instance> optionalLoginInstance = instances
                            .stream()
                            .filter(v -> v.getNodeType().equalsIgnoreCase(InstanceNodeType.LOGIN.getType()))
                            .findFirst();

                    if (!optionalLoginInstance.isPresent()) {
                        log.error("getLoginInstance error: master instance is empty");
                        throw new BceException("chpc-app-server、vncserver 等安装需要有登录节点");
                    }
                }

                // 指定了 instanceId，要判断是否在集群中
                if (request.getInstanceId() != null) {
                    if (instances.isEmpty()) {
                        log.error("getInstance error: cluster instance is empty");
                        throw new BceException("集群节点列表为空");
                    }
                    Optional<Instance> optionalInstance = instances
                            .stream()
                            .filter(v -> v.getInstanceId().equalsIgnoreCase(request.getInstanceId()))
                            .findFirst();
                    if (!optionalInstance.isPresent()) {
                        log.error("getLoginInstance error: master instance is empty");
                        throw new BceException("节点不存在，无法安装软件");
                    }
                }

                boolean exists = installList.stream().anyMatch(v -> v.getName().equals(softwareName));
                SoftwareVersionList softwareVersion;

                if (!exists) {
                    softwareVersion = new SoftwareVersionList();
                    softwareVersion.setName(softwareName);
                    installList.add(softwareVersion);
                } else {
                    softwareVersion = installList.stream().filter(v -> v.getName().equals(softwareName)).findFirst()
                            .get();
                }
                List<String> versions = new ArrayList<>();
                for (String version : softwareInfo.getVersions()) {
                    // *判断软件是否在可安装列表中，不存在的话不能安装
                    Software software = softwareDAOGateway.findSoftwareByNameAndVersion(softwareName, version);
                    if (software == null) {
                        log.error(
                                "software can't install, softwareName: {}, version: {} is not in the list",
                                softwareName,
                                version);
                        throw new BceException(String.format("%s:%s 不存在，无法安装", softwareName, version));
                    }
                    // *查询是否有安装中或者卸载中的任务
                    SoftwareRecord record = softwareRecordDAOGateway.findSoftwareByNameAndVersionAndInstanceId(
                            clusterId,
                            softwareName,
                            version,
                            request.getInstanceId());
                    if (record != null) {
                        if (record.getStatus().equals(SoftwareStatus.INSTALL_FAILED.nameLowerCase())
                                || record.getStatus().equals(SoftwareStatus.UNINSTALL_FAILED.nameLowerCase())) {
                            // 安装失败或者卸载失败，可以重新安装，将之前的记录自动删除掉
                            record.setDeleted(true);
                            softwareRecordDAOGateway.update(record);
                        } else {
                            log.warn(
                                    "software can't install, clusterId: {}, softwareName: {}, version: {}, status: {}",
                                    clusterId,
                                    softwareName,
                                    version,
                                    record.getStatus());
                            continue;
                        }
                    }
                    // 插入安装记录
                    record = new SoftwareRecord();
                    record.setClusterId(clusterId);
                    record.setName(softwareName);
                    record.setVersion(version);
                    record.setStatus(SoftwareStatus.INSTALLING.nameLowerCase());
                    // 增加安装节点信息
                    record.setInstalledInstanceId(request.getInstanceId());
                    softwareRecordDAOGateway.insert(record);

                    boolean versionExists = versions.stream().anyMatch(v -> v.equals(version));
                    if (!versionExists) {
                        log.debug("version: {} is not in the list, add it", version);
                        versions.add(version);
                    }

                    // 进入异步任务
                    // 插入软件安装信息
                    Map<String, Object> extraMap = new HashMap<>();
                    extraMap.put(ChpcConstant.SOFTWARE_NAME, softwareName);
                    extraMap.put(ChpcConstant.SOFTWARE_VERSION, version);
                    extraMap.put(ChpcConstant.INSTANCE_ID, request.getInstanceId());

                    String taskId = UUID.randomUUID().toString();
                    taskService.insert(
                            TaskType.SOFTWARE_OPERATION_TASK,
                            clusterId,
                            "",
                            TaskSourceType.INSTALL_SOFTWARE.getTaskSourceType(),
                            taskId,
                            extraMap,
                            null);
                }

                softwareVersion.setVersions(versions);
            }
        }
        InstallSoftwareResponse response = new InstallSoftwareResponse();
        response.setSoftwareList(new ArrayList<>());
        response.getSoftwareList().addAll(installList);
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        if (cluster != null) {
            response.setClusterName(cluster.getName());
            // bct 操作详情
            String bctMsg;
            if (request != null && request.getInstanceId() != null) {
                bctMsg = String.format("集群%s（ID: %s），节点（%s），安装了%s软件",
                        cluster.getName(), cluster.getClusterId(), request.getInstanceId(),
                        installList.stream().map(SoftwareVersionList::getName).collect(Collectors.joining("，")));
            } else {
                bctMsg = String.format("集群%s（ID: %s），安装了%s软件",
                        cluster.getName(), cluster.getClusterId(),
                        installList.stream().map(SoftwareVersionList::getName).collect(Collectors.joining("，")));
            }
            response.setMessage(bctMsg);
        }
        return response;
    }

    /**
     * {@inheritDoc}
     * 根据请求参数中的集群ID和软件版本信息，将对应的软件从集群中卸载。
     * 如果软件已经被卸载过，则不会再次卸载。
     * 如果软件没有安装过，则不会进行卸载操作。
     * 如果软件正在安装或卸载中，则不会进行卸载操作。
     * 如果软件安装失败或卸载失败，则不会进行卸载操作。
     * 卸载完成后，返回一个包含所有卸载软件信息的UnInstallSoftwareResponse对象。
     *
     * @param clusterId 集群ID，非空字符串
     * @param request   卸载软件请求，包含软件名称和版本信息，非空对象
     * @return UnInstallSoftwareResponse
     *         包含所有卸载软件信息的UnInstallSoftwareResponse对象，不为null
     * @throws BceException 如果软件不存在于可安装列表中，则抛出BceException异常
     */
    @Override
    public UnInstallSoftwareResponse unInstallSoftware(String clusterId, UnInstallSoftwareRequest request) {
        List<SoftwareVersionList> uninstallList = new ArrayList<>();

        for (SoftwareVersionList softwareInfo : request.getSoftwareList()) {
            String softwareName = softwareInfo.getName();

            // 指定了 instanceId，要判断是否在集群中
            if (request.getInstanceId() != null) {
                List<Instance> instances = instanceDAOGateway.findByClusterId(clusterId);

                if (instances.isEmpty()) {
                    log.error("getInstance error: cluster instance is empty");
                    throw new BceException("集群节点列表为空");
                }
                Optional<Instance> optionalInstance = instances
                        .stream()
                        .filter(v -> v.getInstanceId().equalsIgnoreCase(request.getInstanceId()))
                        .findFirst();
                if (!optionalInstance.isPresent()) {
                    log.error("getLoginInstance error: master instance is empty");
                    throw new BceException("节点不存在，无法卸载软件");
                }
            }

            boolean exists = uninstallList.stream().anyMatch(v -> v.getName().equals(softwareName));
            SoftwareVersionList softwareVersion;
            if (!exists) {
                softwareVersion = new SoftwareVersionList();
                softwareVersion.setName(softwareName);
                uninstallList.add(softwareVersion);
            } else {
                softwareVersion = uninstallList.stream().filter(v -> v.getName().equals(softwareName)).findFirst()
                        .get();
            }
            List<String> versions = new ArrayList<>();
            for (String version : softwareInfo.getVersions()) {
                // *判断软件是否在可安装列表中，不存在的话不能安装
                Software software = softwareDAOGateway.findSoftwareByNameAndVersion(softwareName, version);
                if (software == null) {
                    log.error(
                            "software can't uninstall, softwareName: {}, version: {} is not in the list",
                            softwareName,
                            version);
                    throw new BceException(String.format("%s:%s 不存在，无法卸载", softwareName, version));
                }

                // 查询是否有安装中或者卸载中的任务
                SoftwareRecord record = softwareRecordDAOGateway.findSoftwareByNameAndVersionAndInstanceId(clusterId,
                        softwareName,
                        version, request.getInstanceId());
                if (record == null) {
                    log.warn(
                            "software can't uninstall, it's not installed, clusterId: {}, softwareName: {}, version: {}",
                            clusterId,
                            softwareName,
                            version);
                    continue;
                }
                // 只有状态是安装成功，或者卸载失败，才可以进行卸载
                if (record.getStatus().equals(SoftwareStatus.INSTALLED.nameLowerCase())
                        || record.getStatus().equals(SoftwareStatus.UNINSTALL_FAILED.nameLowerCase())) {
                    record.setDeleted(true);
                    softwareRecordDAOGateway.update(record);
                } else {
                    log.warn(
                            "software can't uninstall, clusterId: {}, softwareName: {}, version: {}, status: {}",
                            clusterId,
                            softwareName,
                            version,
                            record.getStatus());
                    continue;
                }

                // 插入安装记录
                record = new SoftwareRecord();
                record.setClusterId(clusterId);
                record.setName(softwareName);
                record.setVersion(version);
                record.setStatus(SoftwareStatus.UNINSTALLING.nameLowerCase());
                // 增加安装节点信息
                record.setInstalledInstanceId(request.getInstanceId());
                softwareRecordDAOGateway.insert(record);

                boolean versionExists = versions.stream().anyMatch(v -> v.equals(version));
                if (!versionExists) {
                    log.debug("version: {} is not in the list, add it", version);
                    versions.add(version);
                }

                // 进入异步任务
                // 插入软件安装信息
                Map<String, Object> extraMap = new HashMap<>();
                extraMap.put(ChpcConstant.SOFTWARE_NAME, softwareName);
                extraMap.put(ChpcConstant.SOFTWARE_VERSION, version);
                extraMap.put(ChpcConstant.INSTANCE_ID, request.getInstanceId());

                String taskId = UUID.randomUUID().toString();
                taskService.insert(
                        TaskType.SOFTWARE_OPERATION_TASK,
                        clusterId,
                        "",
                        TaskSourceType.UNINSTALL_SOFTWARE.getTaskSourceType(),
                        taskId,
                        extraMap,
                        null);
            }
            softwareVersion.setVersions(versions);
        }

        UnInstallSoftwareResponse response = new UnInstallSoftwareResponse();
        response.setSoftwareList(new ArrayList<>());
        response.getSoftwareList().addAll(uninstallList);
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        if (cluster != null) {
            response.setClusterName(cluster.getName());
            // bct 操作详情
            String bctMsg;
            if (request != null && request.getInstanceId() != null) {
                bctMsg = String.format("集群%s（ID: %s），节点（%s），卸载了%s软件",
                        cluster.getName(), cluster.getClusterId(), request.getInstanceId(),
                        uninstallList.stream().map(SoftwareVersionList::getName).collect(Collectors.joining("，")));
            } else {
                bctMsg = String.format("集群%s（ID: %s），卸载了%s软件",
                        cluster.getName(), cluster.getClusterId(),
                        uninstallList.stream().map(SoftwareVersionList::getName).collect(Collectors.joining("，")));
            }
            response.setMessage(bctMsg);
        }
        return response;
    }

    // 新增一个函数，将 List<Software> 转化为 InstallSoftwareRequest
    // 函数为公有静态函数
    // 多个 software 可能有相同的 name，但 version 不同
    // 返回的 InstallSoftwareRequest 中，softwareList 的 name 相同，但 version 不同
    public InstallSoftwareRequest softwareInfo2InstallSoftwareRequest(
            List<SoftwareVersion> softwareList) {
        if (softwareList == null || softwareList.isEmpty()) {
            return null;
        }
        InstallSoftwareRequest request = new InstallSoftwareRequest();
        List<SoftwareVersionList> softwareVersionList = new ArrayList<>(); // 注意这里使用了SoftwareVersion的列表
        Map<String, List<SoftwareVersion>> softwareMap = new LinkedHashMap<>();

        for (SoftwareVersion software : softwareList) {
            List<SoftwareVersion> versionList = softwareMap.get(software.getName());
            if (versionList == null) {
                versionList = new ArrayList<>();
                softwareMap.put(software.getName(), versionList);
            }
            versionList.add(software);
        }

        for (Entry<String, List<SoftwareVersion>> entry : softwareMap.entrySet()) {
            SoftwareVersionList info = new SoftwareVersionList();
            info.setName(entry.getKey());
            List<String> versions = new ArrayList<>();
            for (SoftwareVersion software : entry.getValue()) {
                versions.add(software.getVersion());
            }
            info.setVersions(versions);
            softwareVersionList.add(info); // 将SoftwareVersion对象添加到列表中
        }

        request.setSoftwareList(softwareVersionList); // 设置InstallSoftwareRequest的softwareList属性
        return request;
    }

    /**
     * @Description:
     *               根据实例ID获取节点上的软件安装记录，并返回对应的软件列表。
     *               软件列表包含每个软件的版本列表和版本状态。
     *
     * @Param clusterId string - 集群ID
     * @Param request QuerySoftwareForNodeRequest - 查询请求参数，包括需要查询的软件列表和实例ID
     *
     * @Return QuerySoftwareForNodeResponse - 查询结果，包括软件列表和版本列表
     *
     * @Override 重写父类方法
     */
    @Override
    public QuerySoftwareForNodeResponse getSoftwareRecordByInstance(String clusterId,
            QuerySoftwareForNodeRequest request) {
        QuerySoftwareForNodeResponse response = new QuerySoftwareForNodeResponse();
        response.setSoftwareList(new ArrayList<>());

        if (request != null && request.getSoftwareList() != null) {
            // 获取节点上的软件安装记录
            List<SoftwareRecord> records = softwareRecordDAOGateway.findSoftwareByInstanceId(clusterId,
                    request.getInstanceId());

            Map<String, SoftwareInfo> softwareInfosMap = new HashMap<>();
            for (SoftwareVersionList s : request.getSoftwareList()) {
                String softwareName = s.getName();
                // 初始化软件信息，如果已经存在则直接使用
                SoftwareInfo softwareInfo;
                if (softwareInfosMap.containsKey(softwareName)) {
                    softwareInfo = softwareInfosMap.get(softwareName);
                } else {
                    softwareInfo = new SoftwareInfo();
                    softwareInfo.setName(s.getName());
                    // softwareInfo.setStatus(SoftwareStatus.INSTALLABLE.nameLowerCase());
                }
                if (softwareInfo.getVersionList() == null) {
                    softwareInfo.setVersionList(new ArrayList<>());
                }

                for (String version : s.getVersions()) {
                    String softwareStatus = SoftwareStatus.INSTALLABLE.nameLowerCase();
                    // 查询在安装记录中是否存在
                    Optional<SoftwareRecord> targetRecord = records.stream()
                            .filter(record -> record.getName().equals(softwareName)
                                    && record.getVersion().equals(version))
                            .findFirst();
                    if (targetRecord.isPresent()) {
                        // 如果有安装记录，就用记录中的状态
                        softwareStatus = targetRecord.get().getStatus();
                    }

                    // 构造结果
                    VersionInfo versionInfo = new VersionInfo();
                    versionInfo.setValue(version);
                    versionInfo.setStatus(softwareStatus);
                    versionInfo.setMsg("");
                    softwareInfo.getVersionList().add(versionInfo);

                    softwareInfosMap.put(softwareName, softwareInfo);
                }
            }
            for (SoftwareInfo softwareInfo : softwareInfosMap.values()) {
                response.getSoftwareList().add(softwareInfo);
            }
        }
        return response;
    }
}
