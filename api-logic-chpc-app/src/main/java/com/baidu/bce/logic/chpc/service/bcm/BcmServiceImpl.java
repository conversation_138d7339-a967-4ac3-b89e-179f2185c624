package com.baidu.bce.logic.chpc.service.bcm;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.bcm.BcmBatchQueryRequest;
import com.baidu.bce.logic.chpc.bcm.BcmBatchQueryResponse;
import com.baidu.bce.logic.chpc.bcm.BcmDataPoint;
import com.baidu.bce.logic.chpc.bcm.BcmDataResult;
import com.baidu.bce.logic.chpc.bcm.BcmDimension;
import com.baidu.bce.logic.chpc.bcm.BcmPartialQueryRequest;
import com.baidu.bce.logic.chpc.bcm.BcmPartialQueryResponse;
import com.baidu.bce.logic.chpc.bcm.gateway.BcmGateway;
import com.baidu.bce.logic.chpc.bcm.model.BatchQueryRequest;
import com.baidu.bce.logic.chpc.bcm.model.BatchQueryResponse;
import com.baidu.bce.logic.chpc.bcm.model.DataPoint;
import com.baidu.bce.logic.chpc.bcm.model.DataResult;
import com.baidu.bce.logic.chpc.bcm.model.Dimension;
import com.baidu.bce.logic.chpc.bcm.model.PartialQueryRequest;
import com.baidu.bce.logic.chpc.bcm.model.PartialQueryResponse;
import com.baidu.bce.logic.chpc.bcm.model.PartialQueryResponse.PageResponse;
import com.baidu.bce.logic.chpc.bcm.model.ResultDimension;
import com.baidu.bce.logic.chpc.service.IBcmService;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BcmServiceImpl implements IBcmService {

    @Resource
    BcmGateway bcmGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    public void getDimension(int i,
            List<Dimension> dimensions,
            List<BcmDimension> current,
            List<List<BcmDimension>> result) {
        if (i == 0) {
            current = new ArrayList<BcmDimension>();
        }
        if (i == dimensions.size()) {
            result.add(current);
            return;
        }
        Dimension currentDimenion = dimensions.get(i);

        for (int j = 0; j < currentDimenion.getValue().size(); j++) {
            BcmDimension bcmDimension = new BcmDimension();
            bcmDimension.setName(currentDimenion.getName());
            bcmDimension.setValue(currentDimenion.getValue().get(j));

            List<BcmDimension> tmp = new ArrayList<BcmDimension>(current);
            tmp.add(bcmDimension);
            getDimension(i + 1, dimensions, tmp, result);
        }
        return;
    }

    @Override
    public BatchQueryResponse batchQuery(BatchQueryRequest request) {
        // *1.组合构造 dimension
        List<List<BcmDimension>> finalDimensions = new ArrayList<>();
        getDimension(0, request.getDimensions(), null, finalDimensions);

        List<String> metrcis = request.getMetricNames();

        List<String> finalMetrics = new ArrayList<>(metrcis);
        // todo 暂时只支持 cpuUsage
        if (finalMetrics.contains("QueueCpuUsage")) {
            if (!finalMetrics.contains("QueueCpuIdle")) {
                finalMetrics.add("QueueCpuIdle");
            }
            if (!finalMetrics.contains("QueueCpuTotal")) {
                finalMetrics.add("QueueCpuTotal");
            }
        }

        // *2.请求 bcm
        BcmBatchQueryRequest bcmRequest = new BcmBatchQueryRequest();
        bcmRequest.setScope(request.getScope());
        bcmRequest.setUserId(request.getUserId());
        bcmRequest.setRegion(regionConfiguration.getCurrentRegion());
        bcmRequest.setMetricNames(finalMetrics);
        bcmRequest.setStartTime(request.getStartTime());
        bcmRequest.setEndTime(request.getEndTime());
        bcmRequest.setCycle(request.getCycle());
        bcmRequest.setDimensions(finalDimensions);
        bcmRequest.setStatistics(request.getStatistics());
        bcmRequest.setType("Cluster");

        log.info("batchQuery bcm request: {}", bcmRequest);

        BcmBatchQueryResponse bcmResponse = bcmGateway.batchQuery(bcmRequest);

        // *3.汇聚数据
        List<DataResult> metrics = new ArrayList<>();
        Map<String, DataResult> metricMap = new HashMap<>();

        for (BcmDataResult bcmMetric : bcmResponse.getMetrics()) {
            if (bcmMetric.getMetricName().contains("QueueCpuUsage")) {
                continue;
            }
            // *3.1获取要合并的 metric
            DataResult metric;
            if (metricMap.containsKey(bcmMetric.getMetricName())) {
                // metricName 已经存在，直接获取
                metric = metricMap.get(bcmMetric.getMetricName());
            } else {
                // metricName 不存在，构造一个新的 DataResult
                metric = new DataResult();
                metric.setRegion(bcmMetric.getRegion());
                metric.setScope(bcmMetric.getScope());
                metric.setUserId(bcmMetric.getUserId());
                metric.setResourceId(bcmMetric.getResourceId());
                metric.setMetricName(bcmMetric.getMetricName());

                List<ResultDimension> dimensions = new ArrayList<>();
                for (int i = 0; i < bcmMetric.getDimensions().size(); i++) {
                    dimensions.add(new ResultDimension(bcmMetric.getDimensions().get(i).getName(),
                            bcmMetric.getDimensions().get(i).getValue()));
                }
                metric.setDimensions(dimensions);

                List<DataPoint> dataPoints = new ArrayList<>();
                for (int i = 0; i < bcmMetric.getDataPoints().size(); i++) {
                    DataPoint dataPoint = new DataPoint(bcmMetric.getDataPoints().get(i).getTimestamp());
                    dataPoints.add(dataPoint);
                }
                metric.setDataPoints(dataPoints);
            }

            // *3.2汇聚 dimension
            for (BcmDimension bcmDim : bcmMetric.getDimensions()) {
                for (ResultDimension dim : metric.getDimensions()) {
                    log.debug("=====>bcmDim: {}, dim: {}", bcmDim, dim);
                    // 找到相同的 key
                    if (!dim.getName().equals(bcmDim.getName())) {
                        continue;
                    }
                    // value 如果不包含，则追加
                    if (!dim.getValue().contains(bcmDim.getValue())) {
                        dim.setValue(String.format("%s-%s", dim.getValue(), bcmDim.getValue()));
                    }
                }
            }

            // *3.2汇聚 dataPoints
            // *这里认为数据量和没个数据的时间戳是一样的，所以直接按照顺序汇聚
            log.debug("bcmMetric size: {}, metric size: {}", bcmMetric.getDataPoints().size(),
                    metric.getDataPoints().size());
            for (int i = 0; i < bcmMetric.getDataPoints().size(); i++) {
                // log.debug("bcmMetric: {}, metric: {}", bcmMetric.getDataPoints().get(i),
                // metric.getDataPoints().get(i));
                DataPoint dataPoint = metric.getDataPoints().get(i);
                if (bcmMetric.getDataPoints().get(i).getAverage() != null) {
                    dataPoint.setAverage(
                            bcmMetric.getDataPoints().get(i).getAverage() + (dataPoint.getAverage() == null ? 0.0
                                    : dataPoint.getAverage()));
                }
                if (bcmMetric.getDataPoints().get(i).getSum() != null) {
                    dataPoint.setSum(bcmMetric.getDataPoints().get(i).getSum()
                            + (dataPoint.getSum() == null ? 0.0 : dataPoint.getSum()));
                }
                if (bcmMetric.getDataPoints().get(i).getMaximum() != null) {
                    dataPoint.setMaximum(bcmMetric.getDataPoints().get(i).getMaximum()
                            + (dataPoint.getMaximum() == null ? 0.0 : dataPoint.getMaximum()));
                }
                if (bcmMetric.getDataPoints().get(i).getMinimum() != null) {
                    dataPoint.setMinimum(bcmMetric.getDataPoints().get(i).getMinimum()
                            + (dataPoint.getMinimum() == null ? 0.0 : dataPoint.getMinimum()));
                }
                if (bcmMetric.getDataPoints().get(i).getSampleCount() != null) {
                    dataPoint.setSampleCount(
                            bcmMetric.getDataPoints().get(i).getSampleCount()
                                    + (dataPoint.getSampleCount() == null ? 0 : dataPoint.getSampleCount()));
                }

            }
            metricMap.put(bcmMetric.getMetricName(), metric);
        }

        // *4.补充空的数据
        for (String metricName : request.getMetricNames()) {
            if (metricName.contains("QueueCpuUsage")) {
                DataResult metricIdle = metricMap.get("QueueCpuIdle");
                DataResult metricTotal = metricMap.get("QueueCpuTotal");
                DataResult metricUsage = new DataResult();
                metricUsage.setRegion(regionConfiguration.getCurrentRegion());
                metricUsage.setScope(request.getScope());
                metricUsage.setUserId(request.getUserId());
                metricUsage.setMetricName(metricName);
                List<ResultDimension> dimension = new ArrayList<ResultDimension>();
                for (Dimension dim : request.getDimensions()) {
                    dimension.add(new ResultDimension(dim.getName(), String.join("-", dim.getValue())));
                }
                metricUsage.setDimensions(dimension);
                // 计算填充数据
                List<DataPoint> dataPoint = new ArrayList<DataPoint>();
                if (metricIdle != null && metricIdle.getDataPoints() != null) {
                    for (int i = 0; i < metricIdle.getDataPoints().size(); i++) {
                        DataPoint data = new DataPoint(metricIdle.getDataPoints().get(i).getTimestamp());
                        if (metricTotal.getDataPoints().get(i).getAverage() != null) {
                            if (metricTotal.getDataPoints().get(i).getAverage() != 0) {
                                data.setAverage(
                                        (metricTotal.getDataPoints().get(i).getAverage()
                                                - metricIdle.getDataPoints().get(i)
                                                        .getAverage())
                                                / metricTotal.getDataPoints().get(i).getAverage() * 100);
                            } else {
                                data.setAverage(0.0);
                            }
                        }
                        if (metricTotal.getDataPoints().get(i).getSum() != null) {
                            if (metricTotal.getDataPoints().get(i).getSum() != 0) {
                                data.setSum(
                                        (metricTotal.getDataPoints().get(i).getSum()
                                                - metricIdle.getDataPoints().get(i).getSum())
                                                / metricTotal.getDataPoints().get(i).getSum() * 100);
                            } else {
                                data.setSum(0.0);
                            }
                        }
                        if (metricTotal.getDataPoints().get(i).getMaximum() != null) {
                            if (metricTotal.getDataPoints().get(i).getMaximum() != 0) {
                                data.setMaximum(
                                        (metricTotal.getDataPoints().get(i).getMaximum()
                                                - metricIdle.getDataPoints().get(i).getMaximum())
                                                / metricTotal.getDataPoints().get(i).getMaximum() * 100);
                            } else {
                                data.setMaximum(0.0);
                            }
                        }
                        if (metricTotal.getDataPoints().get(i).getMinimum() != null) {
                            if (metricTotal.getDataPoints().get(i).getMinimum() != 0) {
                                data.setMinimum(
                                        (metricTotal.getDataPoints().get(i).getMinimum()
                                                - metricIdle.getDataPoints().get(i).getMinimum())
                                                / metricTotal.getDataPoints().get(i).getMinimum() * 100);
                            } else {
                                data.setMinimum(0.0);
                            }
                        }
                        dataPoint.add(data);
                    }
                    metricUsage.setDataPoints(dataPoint);
                    metricMap.put(metricName, metricUsage);
                }
            }

            DataResult metric;
            if (!metricMap.containsKey(metricName)) {
                metric = new DataResult();
                metric.setRegion(regionConfiguration.getCurrentRegion());
                metric.setScope(request.getScope());
                metric.setUserId(request.getUserId());
                metric.setMetricName(metricName);
                List<ResultDimension> dimension = new ArrayList<ResultDimension>();
                for (Dimension dim : request.getDimensions()) {
                    dimension.add(new ResultDimension(dim.getName(), String.join("-", dim.getValue())));
                }
                metric.setDimensions(dimension);
                metric.setDataPoints(new ArrayList<DataPoint>());
                metricMap.put(metricName, metric);
            } else {
                metric = metricMap.get(metricName);
                boolean isUsage = metric.getMetricName().toLowerCase().contains("usage");
                // 遍历 metric 的 dataPoints, 如果数据小于0，设置为0
                for (DataPoint dataPoint : metric.getDataPoints()) {
                    if (dataPoint.getAverage() != null && dataPoint.getAverage() < 0) {
                        dataPoint.setAverage(0.0);
                    }
                    if (isUsage && dataPoint.getAverage() != null && dataPoint.getAverage() > 100) {
                        dataPoint.setAverage(100.0);
                    }
                    try {
                        if (!dataPoint.getAverage().isNaN()) {
                            dataPoint.setAverage(
                                    BigDecimal.valueOf(dataPoint.getAverage()).setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue());
                        }
                    } catch (Exception e) {
                        log.error("dataPoint.getAverage() {}", e.getMessage());
                    }

                    if (dataPoint.getSum() != null && dataPoint.getSum() < 0) {
                        dataPoint.setSum(0.0);
                    }
                    if (isUsage && dataPoint.getSum() != null && dataPoint.getSum() > 100) {
                        dataPoint.setSum(100.0);
                    }
                    try {
                        if (!dataPoint.getSum().isNaN()) {
                            dataPoint.setSum(
                                    BigDecimal.valueOf(dataPoint.getSum()).setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue());
                        }
                    } catch (Exception e) {
                        log.error("dataPoint.getSum() {}", e.getMessage());
                    }

                    if (dataPoint.getMaximum() != null && dataPoint.getMaximum() < 0) {
                        dataPoint.setMaximum(0.0);
                    }
                    if (isUsage && dataPoint.getMaximum() != null && dataPoint.getMaximum() > 100) {
                        dataPoint.setMaximum(100.0);
                    }
                    try {
                        if (!dataPoint.getMaximum().isNaN()) {
                            dataPoint.setMaximum(
                                    BigDecimal.valueOf(dataPoint.getMaximum()).setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue());
                        }
                    } catch (Exception e) {
                        log.error("dataPoint.getMaximum() {}", e.getMessage());
                    }

                    if (dataPoint.getMinimum() != null && dataPoint.getMinimum() < 0) {
                        dataPoint.setMinimum(0.0);
                    }
                    if (isUsage && dataPoint.getMinimum() != null && dataPoint.getMinimum() > 100) {
                        dataPoint.setMinimum(100.0);
                    }
                    try {
                        if (!dataPoint.getMinimum().isNaN()) {
                            dataPoint.setMinimum(
                                    BigDecimal.valueOf(dataPoint.getMinimum()).setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue());
                        }
                    } catch (Exception e) {
                        log.error("dataPoint.getMinimum() {}", e.getMessage());
                    }

                }
            }
            metrics.add(metric);

        }

        // *5.构造 response
        BatchQueryResponse response = new BatchQueryResponse();
        response.setRequestId(bcmResponse.getRequestId());
        response.setCode(bcmResponse.getCode());
        response.setMessage(bcmResponse.getMessage());
        response.setMetrics(metrics);

        return response;
    }

    /**
     *@see com.alibaba.cloud.tracing.aliyun.bcm.BcmService#partialQuery(PartialQueryRequest)
     *
     * 根据指定的参数，发起部分查询请求。
     * 该方法会递归地处理所有的 dimension，并将其与请求参数中的 dimension 进行比较。
     * 如果 dimension 不相等，则将其添加到最后一个 dimension 中。
     * 然后，该方法会向 BCM 网关发送多个请求，每个请求都包含一个 dimension。
     * 最后，该方法会将所有的请求结果进行汇聚，并返回一个 PartialQueryResponse 对象。
     *
     * @param request PartialQueryRequest 类型，表示请求参数，包含了必要的信息。
     * @return PartialQueryResponse 类型，表示返回值，包含了请求结果和状态信息。
     * @throws Exception 可能会抛出异常，例如网络连接失败或者BCM网关响应失败。
     */
    @Override
    public PartialQueryResponse partialQuery(PartialQueryRequest request) {
        // *1.组合构造 dimension
        List<List<BcmDimension>> finalDimensions = new ArrayList<>();
        getDimension(0, request.getDimensions(), null, finalDimensions);

        // *2.请求 bcm（这里要多次请求）
        String requestId = "";
        List<BcmDataResult> bcmMetrics = new ArrayList<>();
        for (List<BcmDimension> dimensions : finalDimensions) {
            try {
                log.info("partialQuery dimension: {}", dimensions);

                BcmPartialQueryRequest bcmRequest = new BcmPartialQueryRequest();
                bcmRequest.setScope(request.getScope());
                bcmRequest.setUserId(request.getUserId());
                bcmRequest.setMetricName(request.getMetricName());
                bcmRequest.setStartTime(request.getStartTime());
                bcmRequest.setEndTime(request.getEndTime());
                bcmRequest.setCycle(request.getCycle());
                bcmRequest.setDimensions(dimensions);
                bcmRequest.setStatistics(request.getStatistics());
                bcmRequest.setResourceType("Cluster");
                bcmRequest.setPageNo(request.getPageNo());
                bcmRequest.setPageSize(request.getPageSize());

                log.info("partialQuery bcm request: {}", bcmRequest);

                BcmPartialQueryResponse bcmResponse = bcmGateway.partialQuery(bcmRequest);

                if (!bcmResponse.getCode().equals("success")) {
                    log.error("partialQuery bcm response failed: {}", bcmResponse);
                    continue;
                }
                if (bcmResponse.getResult() != null && bcmResponse.getResult().getResult() != null) {
                    // 临时汇聚多次请求的结果
                    bcmMetrics.addAll(bcmResponse.getResult().getResult());
                    requestId = bcmResponse.getRequestId();
                } else {
                    log.error("partialQuery bcm response has null: {}", bcmResponse);
                }
            } catch (Exception e) {
                log.error("partialQuery bcm response exception: {}", e);
            }

        }

        // *3.汇聚数据
        List<DataResult> metrics = new ArrayList<>();
        Map<String, DataResult> metricMap = new HashMap<>();

        for (BcmDataResult bcmMetric : bcmMetrics) {
            // *3.1获取要合并的 metric
            DataResult metric;
            List<BcmDimension> additionalDim = new ArrayList<>();

            // *获取所有不在 request 中的 dimension
            // *按照 dimension 中的 name 进行排序
            for (BcmDimension dim : bcmMetric.getDimensions()) {
                Boolean exits = false;
                for (Dimension reqDim : request.getDimensions()) {
                    if (dim.getName().equals(reqDim.getName())) {
                        exits = true;
                        break;
                    }
                }
                if (!exits) {
                    additionalDim.add(dim);
                }
            }
            log.info("=====>additionalDim: {}", additionalDim);
            // 对 additionalDim 按照 name 进行排序
            Collections.sort(additionalDim, Comparator.comparing(BcmDimension::getName));

            // 拼接 key
            String key = String.format("%s_%s", bcmMetric.getMetricName(), additionalDim);
            if (metricMap.containsKey(key)) {
                // metricName 已经存在，直接获取
                metric = metricMap.get(key);
            } else {
                // metricName 不存在，构造一个新的 DataResult
                metric = new DataResult();
                metric.setRegion(bcmMetric.getRegion());
                metric.setScope(bcmMetric.getScope());
                metric.setUserId(bcmMetric.getUserId());
                metric.setResourceId(bcmMetric.getResourceId());
                metric.setMetricName(bcmMetric.getMetricName());

                List<ResultDimension> dimensions = new ArrayList<>();
                for (int i = 0; i < request.getDimensions().size(); i++) {
                    dimensions.add(new ResultDimension(request.getDimensions().get(i).getName(), ""));
                }
                metric.setDimensions(dimensions);

                List<DataPoint> dataPoints = new ArrayList<>();
                for (int i = 0; i < bcmMetric.getDataPoints().size(); i++) {
                    DataPoint dataPoint = new DataPoint(bcmMetric.getDataPoints().get(i).getTimestamp());
                    dataPoints.add(dataPoint);
                }
                metric.setDataPoints(dataPoints);
            }

            // *3.2汇聚 dimension
            for (BcmDimension bcmDim : bcmMetric.getDimensions()) {
                Boolean inDim = false;
                for (ResultDimension dim : metric.getDimensions()) {
                    // 找到相同的 key
                    if (!dim.getName().equals(bcmDim.getName())) {
                        continue;
                    }
                    inDim = true;
                    // value 如果不包含，则追加
                    if (!dim.getValue().contains(bcmDim.getValue())) {
                        if (dim.getValue().equals("")) {
                            dim.setValue(bcmDim.getValue());
                        } else {
                            dim.setValue(String.format("%s/%s", dim.getValue(), bcmDim.getValue()));
                        }
                    }
                }
                // *不在 request 中的 dimension，进行添加
                if (additionalDim.contains(bcmDim) && !inDim) {
                    metric.getDimensions().add(new ResultDimension(bcmDim.getName(), bcmDim.getValue()));
                    continue;
                }
            }

            // *3.3汇聚 dataPoints
            for (BcmDataPoint bcmDataPoint : bcmMetric.getDataPoints()) {
                for (DataPoint dataPoint : metric.getDataPoints()) {
                    if (!dataPoint.getTimestamp().equals(bcmDataPoint.getTimestamp())) {
                        continue;
                    }
                    // *找到相同的 timestamp，进行叠加
                    if (bcmDataPoint.getAverage() != null) {
                        dataPoint.setAverage(bcmDataPoint.getAverage()
                                + (dataPoint.getAverage() == null ? 0.0 : dataPoint.getAverage()));
                    }
                    try {
                        if (!dataPoint.getAverage().isNaN()) {
                            dataPoint.setAverage(
                                    BigDecimal.valueOf(dataPoint.getAverage()).setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue());
                        }
                    } catch (Exception e) {
                        log.error("dataPoint.getAverage() {}", e.getMessage());
                    }

                    if (bcmDataPoint.getSum() != null) {
                        dataPoint.setSum(
                                bcmDataPoint.getSum() + (dataPoint.getSum() == null ? 0.0 : dataPoint.getSum()));
                    }
                    try {
                        if (!dataPoint.getSum().isNaN()) {
                            dataPoint.setSum(
                                    BigDecimal.valueOf(dataPoint.getSum()).setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue());
                        }
                    } catch (Exception e) {
                        log.error("dataPoint.getSum() {}", e.getMessage());
                    }

                    if (bcmDataPoint.getMaximum() != null) {
                        dataPoint.setMaximum(bcmDataPoint.getMaximum()
                                + (dataPoint.getMaximum() == null ? 0.0 : dataPoint.getMaximum()));
                    }
                    try {
                        if (!dataPoint.getMaximum().isNaN()) {
                            dataPoint.setMaximum(
                                    BigDecimal.valueOf(dataPoint.getMaximum()).setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue());
                        }
                    } catch (Exception e) {
                        log.error("dataPoint.getMaximum() {}", e.getMessage());
                    }

                    if (bcmDataPoint.getMinimum() != null) {
                        dataPoint.setMinimum(bcmDataPoint.getMinimum()
                                + (dataPoint.getMinimum() == null ? 0.0 : dataPoint.getMinimum()));
                    }
                    try {
                        if (!dataPoint.getMinimum().isNaN()) {
                            dataPoint.setMinimum(
                                    BigDecimal.valueOf(dataPoint.getMinimum()).setScale(2, RoundingMode.HALF_UP)
                                            .doubleValue());
                        }
                    } catch (Exception e) {
                        log.error("dataPoint.getMinimum() {}", e.getMessage());
                    }
                    if (bcmDataPoint.getSampleCount() != null) {
                        dataPoint.setSampleCount(bcmDataPoint.getSampleCount()
                                + (dataPoint.getSampleCount() == null ? 0 : dataPoint.getSampleCount()));
                    }
                }
            }
            // *3.4 保存 metric
            metricMap.put(key, metric);
        }

        // *4.构造 response
        for (DataResult metric : metricMap.values()) {
            metrics.add(metric);
        }
        PartialQueryResponse response = new PartialQueryResponse();
        response.setRequestId(requestId);
        response.setCode("success");
        response.setMessage("");

        PageResponse page = new PageResponse();
        page.setPageNo(request.getPageNo());
        page.setPageSize(request.getPageSize());
        page.setTotalCount(metrics.size());
        page.setResult(metrics);
        response.setResult(page);

        return response;
    }

}