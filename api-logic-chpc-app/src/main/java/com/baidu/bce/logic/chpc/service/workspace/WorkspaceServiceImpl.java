package com.baidu.bce.logic.chpc.service.workspace;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import jakarta.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.common.WorkflowRunStatus;
import com.baidu.bce.logic.chpc.common.WorkspaceStatus;
import com.baidu.bce.logic.chpc.cromwell.Generator;
import com.baidu.bce.logic.chpc.cromwell.model.WorkspaceCreateRequest;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkflowDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkflowRunDAOGateway;
import com.baidu.bce.logic.chpc.gateway.WorkspaceDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Workflow;
import com.baidu.bce.logic.chpc.model.WorkflowRun;
import com.baidu.bce.logic.chpc.model.Workspace;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterVO;
import com.baidu.bce.logic.chpc.model.response.workspace.AvailableCluster;
import com.baidu.bce.logic.chpc.model.response.workspace.NameisExistResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceCreateResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceDeleteResponse;
import com.baidu.bce.logic.chpc.model.response.workspace.WorkspaceGetResponse;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;
import com.baidu.bce.logic.chpc.service.IInstanceService;
import com.baidu.bce.logic.chpc.service.IWorkspaceService;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class WorkspaceServiceImpl implements IWorkspaceService {

    @Resource
    WorkflowDAOGateway workflowDAOGateway;

    @Resource
    private WorkspaceDAOGateway workspaceDAOGateway;

    @Resource
    private Generator workspaceGenerator;

    @Resource
    IInstanceService instanceServiceImpl;

    @Resource
    IChpcClusterService chpcClusterService;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    private CfsDAOGateway cfsDAOGateway;

    @Resource
    WorkflowRunDAOGateway workflowRunDAOGateway;


    @Resource
    TaskService taskService;

    @Resource
    InstanceDAOGateway instanceDAOGateway;


    @Override
    public WorkspaceCreateResponse createWorkspace(WorkspaceCreateRequest workspaceCreateRequest) {
        String accountId = LogicUserService.getAccountId();
        // TODO 判断 bos bucket 可用

        Workspace getWorkspace = workspaceDAOGateway.getWorkspaceByName(workspaceCreateRequest.getName(), accountId);

        if (getWorkspace != null && getWorkspace.getName().equals(workspaceCreateRequest.getName())) {
            throw new CommonExceptions.RequestInvalidException("工作空间名称：" + workspaceCreateRequest.getName() + " 已经存在");
        }

        // 判断集群可用性：集群状态必须是active
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(workspaceCreateRequest.getClusterId(), accountId);
        if (cluster == null) {
            throw new CommonExceptions.RequestInvalidException("cluster " + workspaceCreateRequest.getClusterId() + "does not exist");
        }
        if (!(ClusterStatus.ACTIVE.nameLowerCase().equals(cluster.getStatus()))) {
            throw new CommonExceptions.RequestInvalidException("cluster " + workspaceCreateRequest.getClusterId() + " is in " +
                    cluster.getStatus() + " status, cannot install cromwell. ");
        }

        // 是否挂载了 cfs

        if (cfsDAOGateway.countByCluster(workspaceCreateRequest.getClusterId()) == 0) {
            throw new CommonExceptions.RequestInvalidException("cluster " + workspaceCreateRequest.getClusterId() + " need mount cfs. ");
        }


        // 集群主节点内存规格要求>=16g
        List<Instance> instances = instanceDAOGateway.findByClusterId(workspaceCreateRequest.getClusterId());
        Instance master = null;
        for (Instance instance : instances) {
            if ("master".equals(instance.getNodeType())) {
                master = instance;
                break;
            }
        }

        // 管理节点必须是16G及以上
        if (master != null && Integer.parseInt(getSpec(master.getSpec())) < 16) {
            throw new CommonExceptions.RequestInvalidException("cluster " + workspaceCreateRequest.getClusterId() + " Management nodes must have at least 16GB of memory");
        }

        Boolean isInstalled = false;
        // 集群已经成功安装cromwell
        List<Workspace> workspaceList = workspaceDAOGateway.getWorkspaceByClusterId(workspaceCreateRequest.getClusterId());
        for (Workspace workspace : workspaceList) {
            if (WorkspaceStatus.INSTALLED.nameLowerCase().equals(workspace.getStatus())) {
                isInstalled = true;
                break;
            }
        }

        // 更新db
        Workspace workspace = workspaceGenerator.genWorkspace(workspaceCreateRequest);
        workspace.setClusterName(cluster.getName());
        workspace.setAccountId(accountId);
        if (isInstalled) {
            workspace.setStatus(WorkspaceStatus.INSTALLED.nameLowerCase());
        } else {
            workspace.setStatus(WorkspaceStatus.INSTALLING.nameLowerCase());
        }
        workspaceDAOGateway.createWorkspace(workspace);


        if (!isInstalled) {
            // 添加cromwell安装任务
            Map<String, Object> extraMap = new HashMap<>();
            extraMap.put(ChpcConstant.WORKSPACE_ID, workspace.getWorkspaceId());

            String taskId = UUID.randomUUID().toString();
            taskService.insert(TaskType.GROUP_CROMWELL_SAVE_TASK,
                    workspaceCreateRequest.getClusterId(), "",
                    TaskSourceType.ADD_CROMWELL_TO_CLUSTER.getTaskSourceType(),
                    taskId, extraMap, null);
        }

        WorkspaceCreateResponse workspaceCreateResponse = new WorkspaceCreateResponse();
        workspaceCreateResponse.setWorkspaceId(workspace.getWorkspaceId());

        return workspaceCreateResponse;
    }

    @Override
    public List<Workspace> listWorkspace(String name) {
        String accountId = LogicUserService.getAccountId();
        return workspaceDAOGateway.listWorkspace(name, accountId);
    }

    /**
     * {@inheritDoc}
     * 重写父类方法，获取可用的集群列表。包括：
     * 1. 非hybrid集群；
     * 2. 集群状态为active或error；
     * 3. 集群管理实例存在且处于active状态；
     * 4. 集群下至少一个工作空间处于installed状态或者都是installing状态。
     * 返回值为List<AvailableCluster>，包含了所有符合条件的集群信息，每个集群对应一个AvailableCluster对象。
     *
     * @return List<AvailableCluster> 可用的集群列表
     */
    @Override
    public List<AvailableCluster> listAvailableCluster() {
        List<ClusterVO> clusterList = chpcClusterService.getClusters("");
        List<AvailableCluster> availableClusterList = new ArrayList<>();
        for (int i = 0; i < clusterList.size(); i++) {
            if (ClusterType.HYBRID.nameLowerCase().equals(clusterList.get(i).getClusterType())) {
                continue;
            }
            if (!(ClusterStatus.ACTIVE.nameLowerCase().equals(clusterList.get(i).getStatus()))) {
                AvailableCluster availableCluster = new AvailableCluster();
                availableCluster.setClusterId(clusterList.get(i).getClusterId());
                availableCluster.setStatus(ClusterStatus.ERROR.nameLowerCase());
                availableCluster.setName(clusterList.get(i).getClusterName());
                availableClusterList.add(availableCluster);
                continue;
            }
            Instance managerInstance = instanceServiceImpl.getClusterManagerInstanceOrNull(clusterList.get(i).getClusterId());
            if (null == managerInstance) {
                AvailableCluster availableCluster = new AvailableCluster();
                availableCluster.setClusterId(clusterList.get(i).getClusterId());
                availableCluster.setStatus(ClusterStatus.ERROR.nameLowerCase());
                availableCluster.setName(clusterList.get(i).getClusterName());
                availableClusterList.add(availableCluster);
                continue;
            }

            List<Workspace> workspaceList = workspaceDAOGateway.getWorkspaceByClusterId(clusterList.get(i).getClusterId());
            int installingCount = 0;
            boolean installed = false;
            for (Workspace workspace : workspaceList) {
                if (WorkspaceStatus.INSTALLED.nameLowerCase().equals(workspace.getStatus())) {
                    AvailableCluster availableCluster = new AvailableCluster();
                    availableCluster.setClusterId(clusterList.get(i).getClusterId());
                    availableCluster.setName(clusterList.get(i).getClusterName());
                    availableCluster.setStatus(ClusterStatus.ACTIVE.nameLowerCase());
                    availableClusterList.add(availableCluster);
                    installed = true;
                    break;
                } else if (WorkspaceStatus.INSTALLING.nameLowerCase().equals(workspace.getStatus())) {
                    installingCount++;
                }
            }
            AvailableCluster availableCluster = new AvailableCluster();
            availableCluster.setClusterId(clusterList.get(i).getClusterId());
            availableCluster.setName(clusterList.get(i).getClusterName());
            // 没有正在安装的集群
            if (!installed && installingCount == 0) {
                availableCluster.setStatus(ClusterStatus.ACTIVE.nameLowerCase());
            }else {
                availableCluster.setStatus(ClusterStatus.ERROR.nameLowerCase());
            }
            availableClusterList.add(availableCluster);
        }
        return availableClusterList;
    }

    @Override
    public WorkspaceGetResponse getWorkspace(String workspaceId) {
        String accountId = LogicUserService.getAccountId();
        return workspaceToWorkspaceResponse(workspaceDAOGateway.getWorkspace(workspaceId, accountId));
    }

    @Override
    public NameisExistResponse workspaceNameisExist(String workspaceName) {
        String accountId = LogicUserService.getAccountId();
        NameisExistResponse nameisExistResponse = new NameisExistResponse();
        Workspace getWorkspace = workspaceDAOGateway.getWorkspaceByName(workspaceName, accountId);

        if (getWorkspace != null) {
            nameisExistResponse.setIsExist(true);
        } else {
            nameisExistResponse.setIsExist(false);
        }
        return nameisExistResponse;
    }

    @Override
    public WorkspaceDeleteResponse deleteWorkspace(String workspaceId) {
        String accountId = LogicUserService.getAccountId();
        // check是否存在正在运行的工作流
        List<Workflow> workflowList = workflowDAOGateway.listWorkflow(workspaceId, "", accountId);
        for (Workflow workflow : workflowList) {
            // 非终态的工作流不允许删除
            List<WorkflowRun> workflowRunList = workflowRunDAOGateway.getWorkflowRunById(null, workflow.getWorkflowId(), "", null, accountId);
            for (WorkflowRun workflowRun : workflowRunList) {
                if (!(WorkflowRunStatus.Succeeded.name().equals(workflowRun.getStatus()) || WorkflowRunStatus.Failed.name().equals(workflowRun.getStatus())
                        || WorkflowRunStatus.Aborted.name().equals(workflowRun.getStatus()))) {
                    throw new CommonExceptions.RequestInvalidException("Non final state, cannot be deleted");
                }
            }
        }
        workspaceDAOGateway.deleteWorkspace(workspaceId, accountId);

        // 删除工作空间下的工作流
        for (Workflow workflow : workflowList) {
            workflowDAOGateway.deleteWorkflow(workflow.getWorkflowId(), accountId);
        }

        // 删除执行历史
        workflowRunDAOGateway.deleteWorkflowRunByWorkspaceId(workspaceId, accountId);

        WorkspaceDeleteResponse workspaceDeleteResponse = new WorkspaceDeleteResponse();
        workspaceDeleteResponse.setWorkspaceId(workspaceId);
        return workspaceDeleteResponse;
    }

    public WorkspaceGetResponse workspaceToWorkspaceResponse(Workspace workspace) {

        WorkspaceGetResponse workspaceGetResponse = new WorkspaceGetResponse();
        if (workspace == null) {
            throw new CommonExceptions.RequestInvalidException("workspace not found!");
        }
        workspaceGetResponse.setName(workspace.getName());
        workspaceGetResponse.setBosBucket(workspace.getBosBucket());
        workspaceGetResponse.setClusterId(workspace.getClusterId());
        workspaceGetResponse.setDescription(workspace.getDescription());
        workspaceGetResponse.setStatus(workspace.getStatus());
        workspaceGetResponse.setWorkspaceId(workspace.getWorkspaceId());
        workspaceGetResponse.setClusterName(workspace.getClusterName());
        workspaceGetResponse.setUpdatedTime(workspace.getUpdatedTime());

        return workspaceGetResponse;
    }

    public String getSpec(String spec) {
        int lastIndex = -1;
        for (int i = spec.length() - 1; i >= 0; i--) {
            char c = spec.charAt(i);
            if (Character.isLetter(c)) {
                lastIndex = i;
                break;
            }
        }
        String letterAndNumber = spec.substring(lastIndex + 1);
        return letterAndNumber;
    }
}
