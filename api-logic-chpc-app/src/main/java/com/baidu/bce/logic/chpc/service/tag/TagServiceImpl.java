package com.baidu.bce.logic.chpc.service.tag;

import com.baidu.bce.logic.chpc.autoscaling.gateway.IAutoScalingDAOGateway;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.service.ITagService;
import com.baidu.bce.logic.chpc.tag.ResourcesForTag;
import com.baidu.bce.logic.chpc.tag.TagListResourcesRequest;
import com.baidu.bce.logic.chpc.tag.TagListResourcesResponse;
import com.baidu.bce.logic.chpc.tag.TagPage;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

@Slf4j
@Service
public class TagServiceImpl implements ITagService {
    @Resource
    IAutoScalingDAOGateway autoScalingDAOGateway;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    TagsGateway tagsGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Autowired
    CfsGateway cfsGateway;

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Override
    public TagListResourcesResponse tagListResources(TagListResourcesRequest tagListRequest) {
        TagListResourcesResponse tagListResourcesResponse = new TagListResourcesResponse();
        TagPage tagPage = new TagPage();
        if (tagListRequest.getPageNo() == null) {
            tagListRequest.setPageNo(1);
        }
        if (tagListRequest.getPageSize() == null) {
            tagListRequest.setPageSize(10);
        }
        tagPage.setPageNo(tagListRequest.getPageNo());
        tagPage.setPageSize(tagListRequest.getPageSize());


        List<ResourcesForTag> resourcesForTagList = new ArrayList<>();
        List<Cluster> clusterList = clusterDAOGateway.findByAccountIdAll(getAccountId());
        if (clusterList == null || clusterList.size() == 0) {
            tagListResourcesResponse.setPage(tagPage);
        }

        if (clusterList != null) {
            for (int i = 0; i < clusterList.size(); i++) {
                ResourcesForTag clusterForTag = new ResourcesForTag();
                clusterForTag.setName(clusterList.get(i).getName());
                clusterForTag.setResourceUuid(clusterList.get(i).getClusterId());
                clusterForTag.setResourceId(clusterList.get(i).getClusterId());
                clusterForTag.setRegion(regionConfiguration.getCurrentRegion());
                clusterForTag.setCreateTime(clusterList.get(i).getCreatedTime().toString());
                resourcesForTagList.add(clusterForTag);
            }
        }
        List<AutoScaling> autoScalingList = autoScalingDAOGateway.getAll(getAccountId(), "");
        for (int i = 0; i < autoScalingList.size(); i++) {
            ResourcesForTag autoScalingForTag = new ResourcesForTag();
            autoScalingForTag.setName(autoScalingList.get(i).getAsName());
            autoScalingForTag.setResourceUuid(autoScalingList.get(i).getAsId());
            autoScalingForTag.setResourceId(autoScalingList.get(i).getAsId());
            autoScalingForTag.setRegion(regionConfiguration.getCurrentRegion());
            autoScalingForTag.setCreateTime(autoScalingList.get(i).getCreatedTime().toString());
            resourcesForTagList.add(autoScalingForTag);
        }

        final String order = tagListRequest.getOrder();
        String orderby = tagListRequest.getOrderBy();
        if (StringUtils.isEmpty(orderby)) {
            orderby = "createTime";
        }

        switch (orderby) {
            case "name":
                Collections.sort(resourcesForTagList, new Comparator<ResourcesForTag>() {
                    @Override
                    public int compare(ResourcesForTag o1, ResourcesForTag o2) {
                        if (StringUtils.isEmpty(order) || "desc".equals(order)) {
                            return o2.getName().compareTo(o1.getName());
                        }
                        return o1.getName().compareTo(o2.getName());
                    }
                });
                break;
            case "region":
                Collections.sort(resourcesForTagList, new Comparator<ResourcesForTag>() {
                    @Override
                    public int compare(ResourcesForTag o1, ResourcesForTag o2) {
                        if (StringUtils.isEmpty(order) || "desc".equals(order)) {
                            return o2.getRegion().compareTo(o1.getRegion());
                        }
                        return o1.getRegion().compareTo(o2.getRegion());
                    }
                });
                break;
            default:
                Collections.sort(resourcesForTagList, new Comparator<ResourcesForTag>() {
                    @Override
                    public int compare(ResourcesForTag o1, ResourcesForTag o2) {
                        if (StringUtils.isEmpty(order) || "desc".equals(order)) {
                            return o2.getCreateTime().compareTo(o1.getCreateTime());
                        }
                        return o1.getCreateTime().compareTo(o2.getCreateTime());
                    }
                });
        }

        // 标签key如果不是空，只返回绑定的资源
        if (StringUtils.isNotEmpty(tagListRequest.getTagKey())) {
            FullTagListRequest fullTagListRequest = new FullTagListRequest();
            List<String> resourceIds = new ArrayList<>();
            List<String> regions = new ArrayList<>();
            for (int i = 0; i < resourcesForTagList.size(); i++) {
                resourceIds.add(resourcesForTagList.get(i).getResourceId());
            }
            List<String> serviceTypes = new ArrayList<>();
            serviceTypes.add("CHPC");
            regions.add(regionConfiguration.getCurrentRegion());
            fullTagListRequest.setRegions(regions);
            fullTagListRequest.setTagKey(tagListRequest.getTagKey());
            fullTagListRequest.setTagValue(tagListRequest.getTagValue());
            fullTagListRequest.setResourceIds(resourceIds);
            fullTagListRequest.setResourceUuids(resourceIds);
            fullTagListRequest.setServiceTypes(serviceTypes);
            TagAssociationFulls tagAssociationFulls = null;
            try {
                tagAssociationFulls = tagsGateway.listTags(fullTagListRequest);
            } catch (Exception e) {
                log.error("[resourceIds {}]: get tags failed, err {}", resourceIds, e.getMessage());
            }

            if (tagAssociationFulls != null && tagAssociationFulls.getTagAssociationFulls() != null) {
                for (int i = 0; i < resourcesForTagList.size(); i++) {
                    if (!contains(tagAssociationFulls.getTagAssociationFulls(), resourcesForTagList.get(i).getResourceId())) {
                        resourcesForTagList.get(i).setBind(false);
                    }
                }
            }
            for (Iterator<ResourcesForTag> iterator = resourcesForTagList.iterator(); iterator.hasNext(); ) {
                ResourcesForTag resource = iterator.next();
                if (!resource.getBind()) {
                    iterator.remove();
                }
            }
        }


        int totalCount = resourcesForTagList.size();
        List<ResourcesForTag> resourcesForTagListRes = resourcesForTagList.stream().skip((long) (tagListRequest.getPageNo() - 1) * tagListRequest.getPageSize()).
                limit(tagListRequest.getPageSize()).collect(Collectors.toList());

        tagPage.setTotalCount(totalCount);
        tagPage.setOrderBy(orderby);
        tagPage.setOrder(tagListRequest.getOrderBy());
        if (StringUtils.isEmpty(tagListRequest.getOrder())) {
            tagPage.setOrderBy("desc");
        }
        if (StringUtils.isEmpty(tagListRequest.getOrderBy())) {
            tagPage.setOrderBy("createTime");
        }
        tagPage.setResult(resourcesForTagListRes);
        tagListResourcesResponse.setPage(tagPage);

        return tagListResourcesResponse;
    }

    private Boolean contains(List<TagAssociationFull> tagAssociationFulls, String resourceId) {
        for (TagAssociationFull tagAssociationFull : tagAssociationFulls) {
            if (resourceId.equals(tagAssociationFull.getResourceId())) {
                return true;
            }
        }
        return false;
    }
}

