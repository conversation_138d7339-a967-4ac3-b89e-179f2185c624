package com.baidu.bce.logic.chpc.scheduler.task;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import jakarta.annotation.Resource;

import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.chpc.gateway.ChpcGateway;
import com.baidu.bce.logic.chpc.chpc.model.PushAutoScalingBctEventRequest;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.gateway.ClusterEventDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ShrinkNodesResponse;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.core.iam.service.LogicUserService;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component(value = "shrink_task")
public class ShrinkTask extends AbstractSchedulerTask {

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Resource
    ChpcGateway chpcGateway;

    @Resource
    ClusterEventDAOGateway clusterEventDAOGateway;

    @Override
    public Map<String, Object> executeTask(final Task task, final Cluster cluster, final Queue queue) {
        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
        List<String> hostnames = ServiceUtil.castToList(extraMap.get(ChpcConstant.SHRINK_HOSTNAMES));
        int shrinkNum = (int) extraMap.get(ChpcConstant.SHRINK_NUM);
        ShrinkNodesResponse response = new ShrinkNodesResponse();
        try {
            response = backendGateway.shrinkNodes(cluster.getClusterId(), queue.getName(), hostnames, shrinkNum);
            log.debug("[autoshrink] shrinkNodes response: {}", response);
        } catch (Exception e) {
            // 非法调用，返回失败
            if (e instanceof WebClientResponseException) {
                log.warn("[autoshrink] shrinkNodes api-server failed, e: {}, resp: {}", e, response);
                extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
                extraMap.put(ChpcConstant.TASK_FAILED_REASON, e.getMessage());
                String msg =
                    String.format(
                        "当前队列 %d 节点连续空闲 %d 秒，因此自动缩容，释放节点",
                        hostnames.size(),
                        extraMap.get(ChpcConstant.SHRINK_CYCLE));
                extraMap.put(ChpcConstant.SHRINK_MESSAGE, msg);
                return extraMap;
            }
            // api-serverq异常，继续轮询
            log.warn("[autoshrink] shrinkNodes api-server error, e: {}, resp:{}, continue", e, response);
            return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
        }

        // 从数据库获取节点列表
        List<Instance> instanceList =
            instanceDAOGateway.findAllByClusterIdAndHostNames(cluster.getClusterId(), response.getShrinkNodes());

        for (Instance instance : instanceList) {
            bccExternalGateway.deleteServer(instance.getInstanceId());
            instanceService.delete(instance.getInstanceId());
            // 如果COS资源栈下没有节点，删除COS资源栈
            List<Instance> leftInstances =
                instanceService.findByCosStackId(task.getClusterId(), null, instance.getCosStackId());

            if (CollectionUtils.isEmpty(leftInstances)) {
                cosGateway.deleteStack(instance.getCosStackId());
            }
        }

        extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
        extraMap.put(ChpcConstant.SUCC_SHRINK_HOSTNAMES, response.getShrinkNodes());
        List<String> shrinkNodes = new ArrayList<>();
        for (Instance instance : instanceList) {
            shrinkNodes.add(String.format("%s（ID：%s）", instance.getHostName(), instance.getInstanceId()));
        }
        String msg =
            String.format(
                "当前队列 %d 节点连续空闲 %d 秒，因此自动缩容，释放节点 %s",
                hostnames.size(),
                extraMap.get(ChpcConstant.SHRINK_CYCLE),
                String.join("，", shrinkNodes));
        extraMap.put(ChpcConstant.SHRINK_MESSAGE, msg);
        return extraMap;
    }

    @Override
    public void afterExecuteTask(final Task task, final Map<String, Object> paraMap) {
        AutoScaling autoScaling = new AutoScaling();
        autoScaling.setAsId((String) paraMap.get(ChpcConstant.AUTO_SCALING_FLAG));
        autoScaling.setStatus(AutoScalingStatus.NORMAL);
        autoScalingDAOGateway.update(autoScaling);
        String executeStatus = String.valueOf(paraMap.get(ChpcConstant.TASK_STATUS));
        if (TaskStatus.SUCCEED.getValue().equalsIgnoreCase(executeStatus)) {
            // 推送BCT事件
            try {
                List<String> hostnames = ServiceUtil.castToList(paraMap.get(ChpcConstant.SUCC_SHRINK_HOSTNAMES));
                if (!CollectionUtils.isEmpty(hostnames)) {
                    PushAutoScalingBctEventRequest request = new PushAutoScalingBctEventRequest();
                    request.setUserId(LogicUserService.getAccountId());
                    Cluster cluster = clusterService.findByAll(task.getClusterId());
                    request.setClusterId(task.getClusterId());
                    request.setClusterName(cluster.getName());
                    Queue queue = queueDAOGateway.getByQueueId(task.getQueueId());
                    request.setQueueName(queue.getName());
                    request.setSuccess(true);
                    request.setMessage(String.valueOf(paraMap.get(ChpcConstant.SHRINK_MESSAGE)));
                    request.setAsId(autoScaling.getAsId());
                    chpcGateway.pushAutoShrinkBctEvent(request);
                }
            } catch (Exception e) {
                log.warn("[autoshrink] push autoshrink event to BCT server failed, msg: {}", e.getMessage());
            }

        } else if (TaskStatus.FAILED.getValue().equalsIgnoreCase(executeStatus)) {
            // 推送BCT事件
            try {
                PushAutoScalingBctEventRequest request = new PushAutoScalingBctEventRequest();
                request.setUserId(LogicUserService.getAccountId());
                Cluster cluster = clusterService.findByAll(task.getClusterId());
                request.setClusterId(task.getClusterId());
                request.setClusterName(cluster.getName());
                Queue queue = queueDAOGateway.getByQueueId(task.getQueueId());
                request.setQueueName(queue.getName());
                request.setSuccess(false);
                request.setMessage(String.valueOf(paraMap.get(ChpcConstant.SHRINK_MESSAGE)));
                request.setAsId(autoScaling.getAsId());
                chpcGateway.pushAutoShrinkBctEvent(request);
            } catch (Exception e) {
                return;
            }
        }
    }
}
