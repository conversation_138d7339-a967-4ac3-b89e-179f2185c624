package com.baidu.bce.logic.chpc.scheduler.task;

import java.util.HashMap;
import java.util.Map;

import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.autoscaling.gateway.IAutoScalingDAOGateway;
import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.cfs.gateway.CfsGateway;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.config.SchedulerThreadLocalHolder;
import com.baidu.bce.logic.chpc.cos.gateway.CosGateway;
import com.baidu.bce.logic.chpc.domainservice.CfsService;
import com.baidu.bce.logic.chpc.domainservice.ClusterService;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.domainservice.WorkspaceService;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.BccExternalGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.oos.gateway.OosGateway;
import com.baidu.bce.logic.chpc.service.util.SchedulerUserConfig;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Slf4j
public abstract class AbstractSchedulerTask implements SchedulerTaskService {
    // todo 增加timeout识别和处理

    @Resource
    protected ClusterService clusterService;

    @Resource
    protected WorkspaceService workspaceService;

    @Resource
    protected InstanceService instanceService;

    @Resource
    protected QueueDAOGateway queueDAOGateway;

    @Resource
    protected CfsService cfsService;

    @Resource
    protected TaskService taskService;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    protected CosGateway cosGateway;

    @Resource
    protected OosGateway oosGateway;

    @Resource
    protected CfsGateway cfsGateway;

    @Resource
    protected BccExternalGateway bccExternalGateway;

    @Resource
    protected BccGateway bccGateway;

    @Resource
    protected BackendGateway backendGateway;

    @Resource
    protected IAutoScalingDAOGateway autoScalingDAOGateway;

    @Resource
    protected SchedulerUserConfig schedulerUserConfig;

    abstract Map<String, Object> executeTask(final Task task, final Cluster cluster, final Queue queue);

    /**
     * {@inheritDoc}
     * 执行任务，包含获取队列信息、设置用户账号、执行任务、更新任务状态和清理用户账号。
     * 如果任务类型为空或者任务状态为处理中，则直接返回。
     * 否则，先调用beforeExecuteTask方法，再调用executeTask方法执行任务，最后调用afterExecuteTask方法进行后续操作。
     * 在执行任务之前，需要将用户账号设置到SchedulerThreadLocalHolder中，并且通过schedulerUserConfig设置用户token。
     * 在执行完成之后，需要移除SchedulerThreadLocalHolder中的用户账号，并且通过schedulerUserConfig移除用户token。
     *
     * @param task 任务对象，包含任务id、集群id、队列id和其他信息
     * @throws Exception 如果任务执行失败，可能会抛出异常
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void executeTask(Task task) {
        String accountId;
        Cluster cluster = clusterService.findByAll(task.getClusterId());
        if (cluster == null) {
            Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
            accountId = (String) extraMap.get(ChpcConstant.ACCOUNT_ID);
        } else {
            accountId = cluster.getAccountId();
        }
        Queue queue = queueDAOGateway.getByQueueId(task.getQueueId());
        log.debug("get queue from db, clusterId:{}, queueId:{}, queue:{}, taskType:{}, resourceType:{}", 
                task.getClusterId(), task.getQueueId(), queue, task.getTaskType(), TaskSourceType.fromType(task.getSource()));
        
        if (queue == null) {
            // 混合云队列不入库，构造一个默认队列
            log.debug("get queue is null, clusterId:{}, queueId:{}", task.getClusterId(), task.getQueueId());
            queue = new Queue();
            Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
            queue.setName((String) extraMap.get(ChpcConstant.QUEUE_NAME));
            queue.setClusterId(task.getClusterId());
            queue.setQueueId(task.getQueueId());
            queue.setIsDefault(true);
        }

        // 保存用户AccountId
        SchedulerThreadLocalHolder.setAccountId(accountId);
        schedulerUserConfig.setUserToken(accountId);

        beforeExecuteTask(task, new HashMap<>());

        Map<String, Object> resultMap = executeTask(task, cluster, queue);
        log.debug("clusterId:{} task type: {}, resultMap: {}", task.getClusterId(), task.getTaskType(), resultMap);
        log.debug("clusterId:{} task detail: {}", task.getClusterId(), task);

        String executeStatus = String.valueOf(resultMap.get(ChpcConstant.TASK_STATUS));
        if (TaskStatus.PROCESSING.getValue().equalsIgnoreCase(executeStatus)) {
            return;
        }

        taskService.updateStatus(task.getTaskId(), executeStatus);

        afterExecuteTask(task, resultMap);

        schedulerUserConfig.removeUserToken();
    }

    @Override
    public void beforeExecuteTask(final Task task, final Map<String, Object> paraMap) {
    }

    @Override
    public void afterExecuteTask(final Task task, final Map<String, Object> paraMap) {
    }

}
