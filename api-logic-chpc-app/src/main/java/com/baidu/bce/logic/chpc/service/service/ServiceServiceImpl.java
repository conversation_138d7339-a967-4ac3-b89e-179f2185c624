

package com.baidu.bce.logic.chpc.service.service;

import com.baidu.bce.logic.chpc.skyform.AddTenantOrUserResponse;
import com.baidu.bce.logic.chpc.skyform.GetTenantResponse;
import com.baidu.bce.logic.chpc.skyform.GetUserResponse;
import com.baidu.bce.logic.chpc.skyform.gateway.SkyFormGateway;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ServiceStatusType;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.ServiceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ServiceStatusDAOGateway;
import com.baidu.bce.logic.chpc.model.ChpcService;
import com.baidu.bce.logic.chpc.model.ServiceStatus;
import com.baidu.bce.logic.chpc.model.response.services.ServiceAddResponse;
import com.baidu.bce.logic.chpc.model.response.services.ServiceGetResponse;
import com.baidu.bce.logic.chpc.service.IServicesService;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.UUID;
/**
 * @Author: jiazhongxiang
 * @Date: 2023-12-09
 */
@Slf4j
@Service
public class ServiceServiceImpl implements IServicesService {

    @Resource
    private ServiceDAOGateway serviceDAOGateway;


    @Resource
    private ServiceStatusDAOGateway serviceStatusDAOGateway;

    @Resource
    private SkyFormGateway skyFormGateway;

    @Resource
    TaskService taskService;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public ServiceAddResponse subscribeService(Long serviceId) {

        ServiceAddResponse serviceAddResponse = new ServiceAddResponse();
        String accountId = LogicUserService.getAccountId();
        String userId = LogicUserService.getUserId();

        if (!accountId.equals(userId)) {
            throw new CommonExceptions.RequestInvalidException("userId " + userId + " No permission to open services ");
        }
        Boolean isServiceExisted = serviceDAOGateway.isExisted(serviceId);

        if (!isServiceExisted) {
            throw new CommonExceptions.RequestInvalidException("service " + serviceId + " is not exist ");
        }

        Boolean isExisted = serviceStatusDAOGateway.isExisted(serviceId, accountId);
        if (isExisted) {
            serviceAddResponse.setSuccess("true");
            serviceAddResponse.setStatus(200);
            return serviceAddResponse;
        }


        Boolean serviceStatusOk = serviceStatusDAOGateway.insert(serviceId, accountId, ServiceStatusType.ACTIVATED.nameLowerCase());

        if (!serviceStatusOk) {
            throw new CommonExceptions.RequestInvalidException("service " + serviceId + " is not exist ");
        }
        serviceAddResponse.setStatus(200);
        serviceAddResponse.setSuccess("true");
        return serviceAddResponse;
    }

    @Override
    public ServiceAddResponse subscribeServiceSaas(String accountId, String pfsId, String filesetId) {
        String subscribeAccountId = LogicUserService.getAccountId();
        // 只有chpc账号有权限开通
        if (!ChpcConstant.CHPC_ACCOUNTID.equals(subscribeAccountId) && !ChpcConstant.CHPC_SANDBOX_ACCOUNTID.equals(subscribeAccountId)){
            throw new CommonExceptions.RequestInvalidException("accountId " + subscribeAccountId + " No permission to active services ");
        }
        // 1、db查询用户是否已经开通，天云服务
        ServiceStatus serviceStatus = serviceStatusDAOGateway.findBy(Long.valueOf(2), accountId);
        ServiceAddResponse serviceAddResponse = new ServiceAddResponse();
        if (serviceStatus != null) {
            serviceAddResponse.setStatus(200);
            serviceAddResponse.setSuccess("true");
            return serviceAddResponse;
        }

        // 2、db查不到，从天云查询用户是否存在
        GetUserResponse userByloginName = skyFormGateway.getUserByloginName(accountId);

        // 3、已存返回
        if (userByloginName.getRetObj() != null && userByloginName.getRetObj().size() > 0) {
            serviceAddResponse.setStatus(200);
            serviceAddResponse.setSuccess("true");
            return serviceAddResponse;
        }

        // 4、数据库和天云都查不到。返回结果，执行异步task
        serviceStatusDAOGateway.insert(Long.valueOf(2), accountId, ServiceStatusType.ACTIVATING.nameLowerCase());

        // 添加cromwell安装任务
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put(ChpcConstant.ACCOUNT_ID, accountId);
        extraMap.put(ChpcConstant.PFS_ID, pfsId);
        extraMap.put(ChpcConstant.FILESET_ID, filesetId);

        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.ADD_SKYFORM,
                "", "",
                TaskSourceType.ADD_SKYFORM.getTaskSourceType(),
                taskId, extraMap, null);
        serviceAddResponse.setStatus(200);
        serviceAddResponse.setSuccess("true");
        return serviceAddResponse;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public List<ServiceGetResponse> getService(String name) {
        String accountId = LogicUserService.getAccountId();
        String userId = LogicUserService.getUserId();
        List<ServiceGetResponse> serviceGetResponseList = new ArrayList<>();

        List<ChpcService> serviceList = serviceDAOGateway.getALl(name);

        for (com.baidu.bce.logic.chpc.model.ChpcService service : serviceList) {
            ServiceStatus serviceStatus = serviceStatusDAOGateway.findBy(service.getId(), accountId);
            ServiceGetResponse serviceGetResponse = new ServiceGetResponse();

            serviceGetResponse.setName(service.getName());
            serviceGetResponse.setDescription(service.getDescription());
            serviceGetResponse.setServiceId(service.getId());
            if (null != serviceStatus) {
                serviceGetResponse.setStatus(serviceStatus.getStatus());
                // db可以查到，说明是在该地域开通的天云服务
                if (service.getId() == 2 && serviceStatus.getStatus().equals(ServiceStatusType.ACTIVATED.nameLowerCase())) {
                    addUser(accountId, userId, "");
                }
            } else {
                if (service.getId() == 2) {
                    // db查不到，说明不是在该地域开通，直接返回
//                    GetUserResponse userByloginName = skyFormGateway.getUserByloginName(accountId);

                    // db查不到，从天云查询组织用户存在,返回已开通,
//                    if (userByloginName.getRetObj() != null && userByloginName.getRetObj().size() > 0) {
//                        serviceGetResponse.setStatus(ServiceStatusType.ACTIVATED.nameLowerCase());
//                        addUser(accountId, userId, userByloginName.getRetObj().get(0).getTenantId());
//                        serviceGetResponseList.add(serviceGetResponse);
//                    }
                    continue;
                } else {
                    serviceGetResponse.setStatus(ServiceStatusType.UNACTIVATED.nameLowerCase());
                }
            }
            serviceGetResponseList.add(serviceGetResponse);
        }


        return serviceGetResponseList;
    }


    // 子用户开通天云服务
    private void addUser(String accountId, String userId, String tenantId) {
        if (accountId.equals(userId)) {
            return;
        }
        GetUserResponse userByloginName = skyFormGateway.getUserByloginName(userId);
        // 子用户不存在，新建用户
        if (null == userByloginName.getRetObj() || userByloginName.getRetObj().size() == 0) {
            if ("".equals(tenantId)) {
                GetTenantResponse tenantByTenantName = skyFormGateway.getTenantByTenantName(accountId);
                tenantId = tenantByTenantName.getRetObj().getUuid();
            }
            AddTenantOrUserResponse addTenantOrUserResponse = skyFormGateway.addUser(tenantId, userId, "");
            skyFormGateway.activeUser(addTenantOrUserResponse.getRetObj());
            // 设置用户角色
            List<String> roleIds = new ArrayList<>();
            roleIds.add("5");
            skyFormGateway.addUserRole(addTenantOrUserResponse.getRetObj(), roleIds);
        }
    }

}
