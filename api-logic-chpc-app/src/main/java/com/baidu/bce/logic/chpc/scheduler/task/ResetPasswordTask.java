package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterOosTaskStatus;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.oos.CreateOosExecutionRequest;
import com.baidu.bce.logic.chpc.oos.GetOosExecutionResponse;
import com.baidu.bce.logic.chpc.service.IChpcClusterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component(value = "reset_password_task")
public class ResetPasswordTask extends AbstractSchedulerTask {
    @Resource
    private IChpcClusterService chpcClusterService;

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Override
    Map<String, Object> executeTask(Task task, Cluster cluster, Queue queue) {


        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
        String password = (String) extraMap.get(ChpcConstant.PASSWORD);
        String aespassword = (String) extraMap.get(ChpcConstant.AESPASSWORD);

        log.debug("begin to reset password clusterId: {}", cluster.getClusterId());

        List<Instance> instances = instanceDAOGateway.findByClusterId(cluster.getClusterId());
        Instance master = null;
        Instance login = null;
        for (Instance instance : instances) {
            if ("master".equals(instance.getNodeType())) {
                master = instance;
            }
            if ("login".equals(instance.getNodeType())) {
                login = instance;
            }
        }
        if (instances.size() == 0 || null == master) {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            return extraMap;
        }


        String masterStatus = checkOrStartInstance(master, password);

        String loginStatus = "";
        if (login == null) {
            loginStatus = TaskStatus.SUCCEED.getValue();
        } else {
            loginStatus = checkOrStartInstance(login, password);
        }

        // 管理节点和登录节点都重置密码成功
        if (TaskStatus.SUCCEED.getValue().equals(masterStatus) && TaskStatus.SUCCEED.getValue().equals(loginStatus)) {

            instanceService.updateStatusAndExecutionId(master.getInstanceId(),
                    "", InstanceStatus.STARTED.nameLowerCase());

            if (login != null) {
                instanceService.updateStatusAndExecutionId(login.getInstanceId(),
                        "", InstanceStatus.STARTED.nameLowerCase());
            }
            chpcClusterService.updateClusterPassword(cluster.getClusterId(), aespassword);

            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
            return extraMap;
        }

        if (TaskStatus.FAILED.getValue().equals(masterStatus) || TaskStatus.FAILED.getValue().equals(loginStatus)) {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            return extraMap;
        }

        extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
        return extraMap;
    }

    private CreateOosExecutionRequest generateOosExecutionRequest(Instance instance, String password) {

        Map<String, String> idMaps = new HashMap<>();
        idMaps.put("instanceId", instance.getInstanceId());

        List<Map<String, String>> idLists = new ArrayList<>();
        idLists.add(idMaps);

        Map<String, Object> properties = new HashMap<>();
        properties.put("content", generateCommandContent(password));
        properties.put("user", "root");
        properties.put("workDir", "/");
        // 待执行命令的虚机列表
        properties.put("__workerSelectors__", idLists);

        CreateOosExecutionRequest.Operator operator = new CreateOosExecutionRequest.Operator();
        operator.setName("cloud_assist_shell");
        operator.setOperator("BCE::Agent::ExecuteShell");
        operator.setDescription("exec shell command");
        operator.setProperties(properties);

        CreateOosExecutionRequest.Template template = new CreateOosExecutionRequest.Template();
        template.setName("BCE-BCC-BulkyRunCommand");
        template.setLinear(true);
        template.setOperators(Collections.singletonList(operator));

        CreateOosExecutionRequest request = new CreateOosExecutionRequest();
        request.setTemplate(template);
        return request;
    }

    private String generateCommandContent(String password) {


        StringBuilder result = new StringBuilder();

        result.append(String.format("(echo '%s' | sudo -S passwd --stdin root) || (echo root:'%s' | sudo -S chpasswd) && exit 0 || exit 1", password, password));

        return result.toString();
    }


    private String checkOrStartInstance(Instance instance, String password) {


        if (InstanceStatus.RESETING.nameLowerCase().equalsIgnoreCase(instance.getStatus())) {

            CreateOosExecutionRequest request = generateOosExecutionRequest(instance, password);

            log.debug("begin to send oos command, instance id: {}, content: {}",
                    instance.getInstanceId(), JacksonUtil.toJson(request));


            String executionId = "";
            try {
                executionId = oosGateway.createExecution(request);
            } catch (Exception e) {
                log.error("call oos service failed in reset password, instanceId:{}, exception:{}",
                        instance.getInstanceId(), e);
            }

            instanceService.updateStatusAndExecutionId(instance.getInstanceId(),
                    executionId, InstanceStatus.RESETED.nameLowerCase());

            log.debug("success to send oos, instance id: {}", instance.getInstanceId());
            return InstanceStatus.RESETED.nameLowerCase();
        } else if (InstanceStatus.RESETED.nameLowerCase().equalsIgnoreCase(instance.getStatus())) {

            GetOosExecutionResponse.Result response = oosGateway.getExecutionById(instance.getOosExecutionId());

            if (response == null || ClusterOosTaskStatus.FAILED.name().equalsIgnoreCase(response.getState())) {

                String state = response == null ? "" : response.getState();
                String reason = response == null ? "" : response.getReason();

                log.debug("oos response, id :{}, status:{}, output:{}",
                        instance.getOosExecutionId(), state, reason);
                // 失败之后，将节点状态复原
                instanceService.updateStatusAndExecutionId(instance.getInstanceId(),
                        instance.getOosExecutionId(), InstanceStatus.STARTED.nameLowerCase());
                return TaskStatus.FAILED.getValue();
            }
            log.debug("oos response, id :{}, status:{}, output:{}",
                    instance.getOosExecutionId(), response.getState(), response.getReason());
            if (ClusterOosTaskStatus.SUCCESS.name().equalsIgnoreCase(response.getState())) {
                // 重置密码成功
                return TaskStatus.SUCCEED.getValue();
            }


        }

        return InstanceStatus.RESETING.nameLowerCase();
    }
}
