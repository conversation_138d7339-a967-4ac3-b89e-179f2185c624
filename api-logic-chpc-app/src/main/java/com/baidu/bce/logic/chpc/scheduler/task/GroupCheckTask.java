package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.EventType;
import com.baidu.bce.logic.chpc.common.SoftwareStatus;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterEventDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionScriptResponse;
import com.baidu.bce.logic.chpc.model.Cfs;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Event;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.SoftwareRecord;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.Tag;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.net.HttpURLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

@Slf4j
@Component(value = "group_check_task")
public class GroupCheckTask extends AbstractSchedulerTask {
    @Resource
    ClusterEventDAOGateway clusterEventDAOGateway;

    @Resource
    BackendGateway backendGateway;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    CfsDAOGateway cfsDAOGateway;

    @Resource
    TagsGateway tagsGateway;

    @Resource
    private InstanceDAOGateway instanceDAOGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Autowired
    SoftwareRecordDAOGateway softwareRecordDAOGateway;

    @Value("${bce.web.commons.sdk.bcm.endpoint}")
    private String bcmEndpoint;

    private static final String SERVICE_NAME = "BCM";

    /**
     * {@inheritDoc}
     * <p>
     * 执行任务，包括检查网络、共享存储、调度器服务。
     * 如果任何一个步骤出现错误，则更新集群为error状态并返回失败；
     * 如果任何一个步骤未完成，则返回处理中状态；
     * 否则，更新集群为active状态并返回成功。
     *
     * @param task    任务对象
     * @param cluster 集群信息对象
     * @param queue   队列对象
     * @return 包含任务状态的map，其中key为"taskStatus"，value为任务状态值（TaskStatus）
     * 如果任务执行成功，则返回值为TaskStatus.SUCCEED.getValue()；
     * 如果任务执行失败，则返回值为TaskStatus.FAILED.getValue()；
     * 如果任务处于处理中，则返回值为TaskStatus.PROCESSING.getValue()；
     */
    @Override
    Map<String, Object> executeTask(Task task, Cluster cluster, Queue queue) {

        Cluster clusterInfo = clusterDAOGateway.findByClusterIdAll(cluster.getClusterId(), getAccountId());

        // 用户通过手动修正,集群已经是active状态了,直接返回
        if (ClusterStatus.ACTIVE.nameLowerCase().equals(clusterInfo.getStatus())) {
            return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                    TaskStatus.SUCCEED.getValue());
        }

        // 1、检查网络
        Event event = clusterEventDAOGateway.findByClusterIdAndEvent(cluster.getClusterId(), EventType.CHECK_NETWORK);
        if (event != null) {
            if (EventType.READY_TO_START.equals(event.getStatus()) || EventType.PROGRESSING.equals(event.getStatus())) {

                if (EventType.READY_TO_START.equals(event.getStatus())) {
                    clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_NETWORK,
                            EventType.PROGRESSING, event.getErrMsg());
                }

                String modifiedUrl = bcmEndpoint.replaceFirst("^https?://", "");
                modifiedUrl = modifiedUrl.split(":")[0];
                // 调用插件检查网络
                BackendActionScriptResponse resp = backendGateway.actionScript(cluster.getClusterId(),
                        EventType.CHECK_NETWORK_ACTION, modifiedUrl);
                if (resp.getCode() != HttpURLConnection.HTTP_OK) {
                    // 网络检查错误,更新event为FAILED
                    log.error("taskId: {}, clusterId: {}, Check network failed, error: {}", task.getTaskId(),
                            cluster.getClusterId(), resp.getMessage());
                    clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_NETWORK, EventType.FAILED,
                            resp.getMessage());
                    // 网络检查错误,更新集群为error
                    clusterService.updateClusterWithErrorMessage(cluster.getClusterId(),
                            ClusterStatus.ERROR.nameLowerCase(), resp.getMessage());
                    return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                            TaskStatus.FAILED.getValue());
                }

                // 网络正常，更新网络event为succeed
                clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_NETWORK, EventType.SUCCEED, "");
            } else if (EventType.FAILED.equals(event.getStatus())) {
                return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                        TaskStatus.FAILED.getValue());
            }
        }

        // 2、检查共享存储，可选项

        event = clusterEventDAOGateway.findByClusterIdAndEvent(cluster.getClusterId(), EventType.CHECK_SHARED_STORAGE);

        if (event != null) {
            if (EventType.READY_TO_START.equals(event.getStatus()) || EventType.PROGRESSING.equals(event.getStatus())) {
                if (EventType.READY_TO_START.equals(event.getStatus())) {
                    clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SHARED_STORAGE,
                            EventType.PROGRESSING, event.getErrMsg());
                }

                List<Cfs> cfsList = cfsDAOGateway.findBy(cluster.getClusterId(), null);

                if (cfsList == null || cfsList.size() == 0) {
                    // 共享存储不存在
                    clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SHARED_STORAGE,
                            EventType.FAILED,
                            "Shared storage does not exist");
                    // 网络检查错误,更新集群为error
                    clusterService.updateClusterWithErrorMessage(cluster.getClusterId(),
                            ClusterStatus.ERROR.nameLowerCase(),
                            "Shared storage does not exist");
                    return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                            TaskStatus.FAILED.getValue());
                }

                // check全部的共享存储
                for (int i = 0; i < cfsList.size(); i++) {
                    String arguments = cfsList.get(i).getMountTarget() + " " + cfsList.get(i).getMountDir() + " "
                            + clusterInfo.getSoftwareDir();
                    // 调用插件，检查共享存储配置
                    BackendActionScriptResponse resp = backendGateway.actionScript(cluster.getClusterId(),
                            EventType.CHECK_NFS, arguments);
                    if (resp.getCode() != HttpURLConnection.HTTP_OK) {
                        // 共享存储检查错误,等待下一轮任务，或者手动触发修正
                        clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SHARED_STORAGE,
                                EventType.FAILED, resp.getMessage());
                        clusterService.updateClusterWithErrorMessage(cluster.getClusterId(),
                                ClusterStatus.ERROR.nameLowerCase(), resp.getMessage());
                        return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                                TaskStatus.PROCESSING.getValue());
                    }
                }

                // 更新调度器检查事件
                clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SHARED_STORAGE, EventType.SUCCEED,
                        "");
            } else if (EventType.FAILED.equals(event.getStatus())) {
                // event已经失败，等待下一轮任务
                return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                        TaskStatus.PROCESSING.getValue());
            }
        }

        // 3、检查调度器服务

        event = clusterEventDAOGateway.findByClusterIdAndEvent(cluster.getClusterId(), EventType.CHECK_SCHEDULER);
        if (event != null) {
            if (EventType.READY_TO_START.equals(event.getStatus()) || EventType.PROGRESSING.equals(event.getStatus())) {
                if (EventType.READY_TO_START.equals(event.getStatus())) {
                    clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SCHEDULER,
                            EventType.PROGRESSING, event.getErrMsg());
                }

                Instance master = instanceDAOGateway.findMasterInstance(cluster.getClusterId());

                if (master == null) {
                    // 没有master节点
                    clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SCHEDULER, EventType.FAILED,
                            "no manager found for cluster: " + cluster.getClusterId());
                    clusterService.updateClusterWithErrorMessage(cluster.getClusterId(),
                            ClusterStatus.ERROR.nameLowerCase(),
                            "no manager found for cluster: " + cluster.getClusterId());
                    return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                            TaskStatus.FAILED.getValue());
                }

                String arguments = master.getSchedulerIp() + " " + master.getSchedulerHost();

                // 公有云使用master的ip和host
                if (ClusterType.CLOUD.nameLowerCase().equals(cluster.getClusterType())) {
                    arguments = master.getPrivateIp() + " " + master.getHostName();
                }

                // 检查调度器服务配置
                BackendActionScriptResponse resp = backendGateway.actionScript(cluster.getClusterId(),
                        EventType.CHECK_SCHEDULER_INFO, arguments);
                if (resp.getCode() != HttpURLConnection.HTTP_OK) {
                    // 调度器服务检查错误,等待下一轮任务，或者手动触发修正
                    clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SCHEDULER, EventType.FAILED,
                            resp.getMessage());
                    clusterService.updateClusterWithErrorMessage(cluster.getClusterId(),
                            ClusterStatus.ERROR.nameLowerCase(), resp.getMessage());
                    // 公有云无法修正，直接返回创建失败
                    if (ClusterType.CLOUD.nameLowerCase().equals(cluster.getClusterType())) {
                        return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                                TaskStatus.FAILED.getValue());
                    } else {
                        return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                                TaskStatus.PROCESSING.getValue());
                    }
                }
                // 混合云需要check权限
                if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
                    // 检查代理节点权限
                    resp = backendGateway.actionScript(cluster.getClusterId(), EventType.CHECK_PROXY_ROLE, "");
                    if (resp.getCode() != HttpURLConnection.HTTP_OK) {
                        // 调度器服务检查错误,等待下一轮任务，或者手动触发修正
                        clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SCHEDULER,
                                EventType.FAILED, resp.getMessage());
                        clusterService.updateClusterWithErrorMessage(cluster.getClusterId(),
                                ClusterStatus.TO_BE_AUTHORIZED.nameLowerCase(), resp.getMessage());
                        return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                                TaskStatus.PROCESSING.getValue());
                    }
                }

                // 更新调度器检查事件
                clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CHECK_SCHEDULER, EventType.SUCCEED,
                        "");
            } else if (EventType.FAILED.equals(event.getStatus())) {
                // event已经失败，等待下一轮任务
                return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                        TaskStatus.PROCESSING.getValue());
            }
        }

        // 如果有chpc-app-server的安装记录，更新为已安装
        SoftwareRecord softwareByNameAndVersion = softwareRecordDAOGateway
                .findSoftwareByNameAndVersion(cluster.getClusterId(), ChpcConstant.CHPC_APP_SERVER, "1.0.1");
        if (softwareByNameAndVersion != null) {
            SoftwareRecord record = new SoftwareRecord();
            record.setStatus(SoftwareStatus.INSTALLED.nameLowerCase());
            record.setClusterId(cluster.getClusterId());
            record.setName(ChpcConstant.CHPC_APP_SERVER);
            record.setVersion("1.0.1");
            softwareRecordDAOGateway.update(record);

            SoftwareRecord vncRecord = new SoftwareRecord();
            vncRecord.setStatus(SoftwareStatus.INSTALLED.nameLowerCase());
            vncRecord.setClusterId(cluster.getClusterId());
            vncRecord.setName(ChpcConstant.CHPC_VNC_SERVER);
            vncRecord.setVersion("1.8.0");
            softwareRecordDAOGateway.update(vncRecord);
        }

        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);

        Object clusterTagsObj = extraMap.get(ChpcConstant.CLUSTER_TAGS);

        if (clusterTagsObj instanceof List<?>) {
            List<Tag> clusterTags =
                    JacksonUtil.decodeToList(JacksonUtil.encode(clusterTagsObj), Tag.class);

            if (clusterTags != null && clusterTags.size() > 0) {
                // 标签失败，不能影响集群创建
                try {
                    tagsGateway.createTags(clusterTags);
                    CreateAndAssignTagRequest request = new CreateAndAssignTagRequest();
                    List<AssignResource> resources = new ArrayList<>();
                    AssignResource resource = new AssignResource();
                    resource.setServiceType("CHPC");
                    resource.setResourceId(cluster.getClusterId());
                    resource.setResourceUuid(cluster.getClusterId());
                    resource.setRegion(regionConfiguration.getCurrentRegion());
                    resource.setAssociationType("floating");
                    resource.setTags(clusterTags);
                    resources.add(resource);
                    request.setResources(resources);
                    tagsGateway.createAndAssignTag(request);
                } catch (Exception e) {
                    log.debug(
                            "create clustertags fail,clusterId:{} ,err:{}", cluster.getClusterId(), e.getMessage());
                }
            }
        }

        // 更新集群为已经就绪状态
        clusterService.updateClusterStatus(cluster.getClusterId(), ClusterStatus.ACTIVE.nameLowerCase());

        return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                TaskStatus.SUCCEED.getValue());
    }

    /**
     * @param task    任务对象，包含任务id、集群id、队列id等信息
     * @param paraMap 参数map，包含任务状态、任务uuid等信息
     * @return void 无返回值
     * @Description: 在任务执行完成后调用的方法，用于处理任务相关逻辑
     */
    public void afterExecuteTask(final Task task, final Map<String, Object> paraMap) {
        // *暂时不删除集群集群，保留现场
        // String executeStatus = String.valueOf(paraMap.get(ChpcConstant.TASK_STATUS));

        // // 如果check返回失败，删除集群资源
        // if (TaskStatus.FAILED.getValue().equalsIgnoreCase(executeStatus)) {
        //     Map<String, Object> groupDeletedParaMap = new HashMap<>();
        //     groupDeletedParaMap.put(ChpcConstant.SOURCE_TASK_ID, task.getTaskId());
        //     taskService.insert(
        //             TaskType.GROUP_DELETED_TASK,
        //             task.getClusterId(),
        //             task.getQueueId(),
        //             TaskType.GROUP_CHECK_TASK.getTaskType(),
        //             task.getTaskUuid(),
        //             groupDeletedParaMap,
        //             task.getCosStackId());

        // }
    }
}
