package com.baidu.bce.logic.chpc.service.util;

import com.baidu.bce.internalsdk.iam.model.IamDecryptResponse;
import com.baidu.bce.internalsdk.iam.model.IamEncryptResponse;
import com.baidu.bce.logic.chpc.common.RsaUtils;
import com.baidu.bce.logic.chpc.common.holder.AccessKeyThreadHolder;
// import com.baidu.bce.logic.chpc.iam.IamDecryptResponse;
import com.baidu.bce.logic.chpc.iam.IamGateway;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;

@Slf4j
@Component
public class PasswordUtil {

    @Resource
    IamGateway iamGateway;

    private static final String UPPER_CASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWER_CASE = "abcdefghijklmnopqrstuvwxyz";
    private static final String DIGITS = "0123456789";
    private static final String SPECIAL_CHARS = "!@#$%^*()";
    private static final String ALL_CHARS = UPPER_CASE + LOWER_CASE + DIGITS + SPECIAL_CHARS;
    private static final SecureRandom RANDOM = new SecureRandom();

    private static final String LENGTH_PATTERN = "^.{8,32}$";
    private static final String UPPERCASE_PATTERN = "[A-Z]+";
    private static final String LOWERCASE_PATTERN = "[a-z]+";
    private static final String DIGIT_PATTERN = "\\d+";
    private static final String SPECIAL_CHAR_PATTERN = "[!@#$%^*()]+";

    // 解析前端Rsa为密码原文
    public String convertRsa2Pwd(String rsaEncryptedPassword) {
        log.debug("rsaEncryptedPassword:" + rsaEncryptedPassword);
        if (StringUtils.isEmpty(rsaEncryptedPassword)) {
            return null;
        }
        String password;
        try {
            password = RsaUtils.decrypt(rsaEncryptedPassword, RsaUtils.PRIVATE_KEY);
        } catch (Exception e) {
            throw new CommonExceptions.RequestInvalidException("decrypt password error");
        }
        log.debug("decrypted password:" + password);
        if (StringUtils.isEmpty(password)) {
            throw new CommonExceptions.RequestInvalidException("密码不能为空");
        }
        if (!validateInput(password)) {
            throw new CommonExceptions.RequestInvalidException("密码不符合要求");
        }
        return password;
    }

    // 请求iam解析SkEncryptedPwd为密码原文
    public String convertSkEncryptedPwd2Pwd(String skEncryptedPwd) {
        log.debug("skEncryptedPwd:" + skEncryptedPwd);
        IamDecryptResponse decryptResponse = iamGateway.decrypt(skEncryptedPwd, AccessKeyThreadHolder.getAccessKey());
        String password = "";
        try {
            password = new String(Hex.decodeHex(decryptResponse.getRawHex()));
        } catch (DecoderException e) {
            e.printStackTrace();
        }
        log.debug("password:" + password);
        if (StringUtils.isEmpty(password)) {
            throw new CommonExceptions.RequestInvalidException("密码不能为空");
        }
        if (!validateInput(password)) {
            throw new CommonExceptions.RequestInvalidException("密码不符合要求");
        }
        return password;
    }

    /**
     * 检查字符串是否符合要求
     *
     * @param input 输入的字符串
     * @return 如果字符串符合要求，返回true；否则返回false
     */
    public static boolean validateInput(String input) {
        // 检查长度
        if (!Pattern.matches(LENGTH_PATTERN, input)) {
            return false;
        }

        // 计数器，用于记录满足的字符类型数量
        int count = 0;

        // 检查大写字母
        if (Pattern.matches(".*" + UPPERCASE_PATTERN + ".*", input)) {
            count++;
        }

        // 检查小写字母
        if (Pattern.matches(".*" + LOWERCASE_PATTERN + ".*", input)) {
            count++;
        }

        // 检查数字
        if (Pattern.matches(".*" + DIGIT_PATTERN + ".*", input)) {
            count++;
        }

        // 检查特殊符号
        if (Pattern.matches(".*" + SPECIAL_CHAR_PATTERN + ".*", input)) {
            count++;
        }

        // 至少包含三种类型字符
        return count >= 3;
    }

    public String generatePassword(int length) {
        if (length < 8 || length > 32) {
            throw new IllegalArgumentException("Password length must be between 8 and 32 characters.");
        }

        StringBuilder password = new StringBuilder(length);

        List<Character> charTypes = new ArrayList<>();
        charTypes.add(randomChar(UPPER_CASE));
        charTypes.add(randomChar(LOWER_CASE));
        charTypes.add(randomChar(DIGITS));
        charTypes.add(randomChar(SPECIAL_CHARS));

        // Ensure at least three types of characters are included
        Collections.shuffle(charTypes);
        for (int i = 0; i < 3; i++) {
            password.append(charTypes.get(i));
        }

        // Fill the remaining length with random characters from all types
        for (int i = 3; i < length; i++) {
            password.append(randomChar(ALL_CHARS));
        }

        // Shuffle the final password to ensure randomness
        List<Character> passwordChars = new ArrayList<>();
        for (char c : password.toString().toCharArray()) {
            passwordChars.add(c);
        }
        Collections.shuffle(passwordChars);

        StringBuilder finalPassword = new StringBuilder(length);
        for (char c : passwordChars) {
            finalPassword.append(c);
        }

        return finalPassword.toString();
    }

    private static char randomChar(String charSet) {
        int index = RANDOM.nextInt(charSet.length());
        return charSet.charAt(index);
    }
}
