package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.domainservice.UserCallbackTaskService;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.model.UserCallbackTask;
import com.baidu.bce.logic.chpc.model.response.task.TaskDetailResponse;
import com.baidu.bce.logic.chpc.model.response.usercallbacktask.UserCallbackTaskDetailResponse;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Slf4j
@Service
public class ChpcTaskServiceImpl implements ChpcTaskService {


    @Resource
    private TaskService taskService;

    @Resource
    private UserCallbackTaskService userCallbackTaskService;

    @Override
    @ValidateAuthentication
    public TaskDetailResponse getClusterTaskDetail(String clusterId, String taskId) {

        List<Task> tasks = taskService.getByTaskUuid(clusterId, taskId);
        if (CollectionUtils.isEmpty(tasks)) {
            throw new CommonExceptions.RequestInvalidException("The task id is incorrect");
        }

        boolean status = tasks.stream()
                .anyMatch(task -> TaskStatus.SUCCEED.getValue().equalsIgnoreCase(task.getStatus()));


        TaskDetailResponse response = new TaskDetailResponse();
        response.setTaskId(taskId);
        response.setStatus(status ?
                TaskStatus.SUCCEED.getValue()
                : TaskStatus.FAILED.getValue());
        return response;
    }

    @Override
    public UserCallbackTaskDetailResponse getUserCallbackTaskDetail(String clusterId, String taskId) {
        List<UserCallbackTask> tasks = userCallbackTaskService.getByTaskId(clusterId, taskId);
        if (CollectionUtils.isEmpty(tasks)) {
            throw new CommonExceptions.RequestInvalidException("The task id is incorrect");
        }
        UserCallbackTaskDetailResponse response = new UserCallbackTaskDetailResponse();
        response.setTaskId(taskId);
        response.setAction(tasks.get(0).getAction());
        response.setParameters(tasks.get(0).getParameters());
        response.setStatus(tasks.get(0).getStatus());
        return response;
    }

    @Override
    public UserCallbackTaskDetailResponse updateUserCallbackTaskStatus(String clusterId, String taskId, 
            String status) {
        userCallbackTaskService.updateStatus(taskId, status);
        return getUserCallbackTaskDetail(clusterId, taskId);
    }
}
