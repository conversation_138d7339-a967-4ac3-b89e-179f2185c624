package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.WorkspaceStatus;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.ClusterOosTaskStatus;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.oos.CreateOosExecutionRequest;
import com.baidu.bce.logic.chpc.oos.GetOosExecutionResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;
import java.util.List;
import java.util.Collections;
import java.util.ArrayList;
import java.util.HashMap;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

@Slf4j
@Component(value = "group_cromwell_save_task")
public class CromwellSaveTask extends AbstractSchedulerTask {


    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Override
    Map<String, Object> executeTask(Task task, Cluster cluster, Queue queue) {
        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
        String workspaceId = (String) extraMap.get(ChpcConstant.WORKSPACE_ID);

        log.debug("begin to install cromwell clusterId: {}", cluster.getClusterId());
        List<Instance> instances = instanceDAOGateway.findByClusterId(cluster.getClusterId());
        Instance master = null;
        for (Instance instance : instances) {
            if ("master".equals(instance.getNodeType())) {
                master = instance;
                break;
            }
        }
        if (instances.size() == 0 || null == master) {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            return extraMap;
        }

        Cluster clusterDB = clusterDAOGateway.findByClusterIdAll(cluster.getClusterId(), getAccountId());
        if (!ClusterStatus.ACTIVE.nameLowerCase().equals(cluster.getStatus())) {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            return extraMap;
        }

        String status = checkOrStartInstance(task, master, clusterDB.getSchedulerType(), workspaceId);
        if (WorkspaceStatus.INSTALLFAILED.nameLowerCase().equals(status)) {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            return extraMap;
        }

        if (WorkspaceStatus.INSTALLED.nameLowerCase().equals(status)) {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
            return extraMap;
        }
        extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
        return extraMap;

    }

    private CreateOosExecutionRequest generateOosExecutionRequest(Instance instance, String schedulerType) {

        Map<String, String> idMaps = new HashMap<>();
        idMaps.put("instanceId", instance.getInstanceId());

        List<Map<String, String>> idLists = new ArrayList<>();
        idLists.add(idMaps);

        Map<String, Object> properties = new HashMap<>();
        properties.put("content", generateCommandContent(schedulerType));
        properties.put("user", "root");
        properties.put("workDir", "/");
        // 待执行命令的虚机列表
        properties.put("__workerSelectors__", idLists);

        CreateOosExecutionRequest.Operator operator = new CreateOosExecutionRequest.Operator();
        operator.setName("cloud_assist_shell");
        operator.setOperator("BCE::Agent::ExecuteShell");
        operator.setDescription("exec shell command");
        operator.setProperties(properties);

        CreateOosExecutionRequest.Template template = new CreateOosExecutionRequest.Template();
        template.setName("BCE-BCC-BulkyRunCommand");
        template.setLinear(true);
        template.setOperators(Collections.singletonList(operator));

        CreateOosExecutionRequest request = new CreateOosExecutionRequest();
        request.setTemplate(template);
        return request;
    }

    private String generateCommandContent(String schedulerType) {


        StringBuilder result = new StringBuilder();

        result.append("/usr/bin/bash /opt/cromwell/cromwell.sh install_cromwell ").append(schedulerType);

        return result.toString();
    }


    private String checkOrStartInstance(Task task, Instance instance, String schedulerType, String workspaceId) {


        if (InstanceStatus.STARTED.nameLowerCase().equalsIgnoreCase(instance.getStatus())) {

            CreateOosExecutionRequest request = generateOosExecutionRequest(instance, schedulerType);

            log.debug("begin to send oos command, instance id: {}, content: {}",
                    instance.getInstanceId(), JacksonUtil.toJson(request));


            String executionId = "";
            try {
                executionId = oosGateway.createExecution(request);
            } catch (Exception e) {
                log.error("call oos service failed in cromwell installing, instanceId:{}, exception:{}",
                        instance.getInstanceId(), e);
            }

            instanceService.updateStatusAndExecutionId(instance.getInstanceId(),
                    executionId, InstanceStatus.INSTALLING.nameLowerCase());

            log.debug("success to send oos, instance id: {}", instance.getInstanceId());
            return InstanceStatus.INSTALLING.nameLowerCase();
        } else if (InstanceStatus.INSTALLING.nameLowerCase().equalsIgnoreCase(instance.getStatus())) {

            GetOosExecutionResponse.Result response = oosGateway.getExecutionById(instance.getOosExecutionId());

            if (response == null || ClusterOosTaskStatus.FAILED.name().equalsIgnoreCase(response.getState())) {

                String state = response == null ? "" : response.getState();
                String reason = response == null ? "" : response.getReason();

                log.debug("oos response, id :{}, status:{}, output:{}",
                        instance.getOosExecutionId(), state, reason);
                // cromwell安装失败
                workspaceService.updateWorkspaceStatus(workspaceId,
                        WorkspaceStatus.INSTALLFAILED.nameLowerCase(), getAccountId());

                instanceService.updateStatusAndExecutionId(instance.getInstanceId(),
                        "", InstanceStatus.STARTED.nameLowerCase());

                return WorkspaceStatus.INSTALLFAILED.nameLowerCase();
            }
            log.debug("oos response, id :{}, status:{}, output:{}",
                    instance.getOosExecutionId(), response.getState(), response.getReason());
            if (ClusterOosTaskStatus.SUCCESS.name().equalsIgnoreCase(response.getState())) {
                // cromwell安装成功
                workspaceService.updateWorkspaceStatus(workspaceId,
                        WorkspaceStatus.INSTALLED.nameLowerCase(), getAccountId());
                instanceService.updateStatusAndExecutionId(instance.getInstanceId(),
                        "", InstanceStatus.STARTED.nameLowerCase());
                return WorkspaceStatus.INSTALLED.nameLowerCase();
            }


        }

        return InstanceStatus.INSTALLING.nameLowerCase();
    }
}
