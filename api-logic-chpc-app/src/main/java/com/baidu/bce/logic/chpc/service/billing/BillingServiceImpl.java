package com.baidu.bce.logic.chpc.service.billing;

import com.baidu.bce.internalsdk.core.BceConstant;
import com.baidu.bce.internalsdk.core.BceInternalResponseException;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.order.model.ResourceMapping;
import com.baidu.bce.internalsdk.order.model.ResourceStatus;
import com.baidu.bce.internalsdk.order.model.UpdateOrderRequest;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.billing.LegacyChargeDataRequest;
import com.baidu.bce.logic.chpc.billing.LegacyDimension;
import com.baidu.bce.logic.chpc.billing.LegacyMetricData;
import com.baidu.bce.logic.chpc.billing.LegacyStatisticValues;
import com.baidu.bce.logic.chpc.billing.LegacyUserChargeData;
import com.baidu.bce.logic.chpc.billing.ResourceUsage;
import com.baidu.bce.logic.chpc.billing.ResourceUsageRequest;
import com.baidu.bce.logic.chpc.billing.ResourceUsageRequest.UsageMeta;
import com.baidu.bce.logic.chpc.billing.gateway.BillingGateway;
import com.baidu.bce.logic.chpc.billing.gateway.ResourceChargeGateway;
import com.baidu.bce.logic.chpc.billing.gateway.ResourceUsageGateway;
import com.baidu.bce.logic.chpc.billing.gateway.ServiceCatalogGateway;
import com.baidu.bce.logic.chpc.common.HelixConstant;
import com.baidu.bce.logic.chpc.common.HelixJobStatus;
import com.baidu.bce.logic.chpc.common.JobStatusType;
import com.baidu.bce.logic.chpc.common.SaasResourceType;
import com.baidu.bce.logic.chpc.config.LockConfig;
import com.baidu.bce.logic.chpc.gateway.ChargeDataDAOGateway;
import com.baidu.bce.logic.chpc.gateway.HelixJobDAOGateway;
import com.baidu.bce.logic.chpc.gateway.JobDataDAOGateway;
import com.baidu.bce.logic.chpc.gateway.OrderDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SaasResourceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ServiceStatusDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.JobDetail;
import com.baidu.bce.logic.chpc.job.model.SubmitJobRequest;
import com.baidu.bce.logic.chpc.model.ChargeData;
import com.baidu.bce.logic.chpc.model.HelixJob;
import com.baidu.bce.logic.chpc.model.Order;
import com.baidu.bce.logic.chpc.model.SaasResource;
import com.baidu.bce.logic.chpc.model.request.billing.BillingActiveRequest;
import com.baidu.bce.logic.chpc.model.response.billing.ActiveResourceResponse;
import com.baidu.bce.logic.chpc.model.response.billing.OrderCreateResponse;
import com.baidu.bce.logic.chpc.model.response.billing.OrderExecutorResponse;
import com.baidu.bce.logic.chpc.model.response.billing.OrderInfoResponse;
import com.baidu.bce.logic.chpc.model.response.billing.QueryResourceResponse;
import com.baidu.bce.logic.chpc.service.IBillingService;
import com.baidu.bce.logic.chpc.skyform.GetTenantResponse;
import com.baidu.bce.logic.chpc.skyform.GetUserResponse;
import com.baidu.bce.logic.chpc.skyform.ListJobsResponse;
import com.baidu.bce.logic.chpc.skyform.gateway.SkyFormGateway;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.IamLogicService;
import com.baidu.bce.plat.servicecatalog.model.order.CreateOrderRequest;
import com.baidu.bce.plat.servicecatalog.model.order.OrderUuidResult;
import com.baidu.bce.plat.servicecatalog.model.order.item.CreateNewTypeOrderItem;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class BillingServiceImpl implements IBillingService {

    private String prefix = "BCE_";

    @Autowired
    IamLogicService iamLogicService;

    @Value("${helix.helixfold3.cluster}")
    private String helixfold3Cluster;

    @Value("${helix.helixvs.cluster}")
    private String helixvsCluster;

    @Resource
    OrderDAOGateway orderDAOGateway;

    @Resource
    BackendGateway backendGateway;

    @Resource
    HelixJobDAOGateway helixJobDAOGateway;

    @Resource
    SaasResourceDAOGateway saasResourceDAOGateway;

    @Resource
    ChargeDataDAOGateway chargeDataDAOGateway;

    @Resource
    ResourceUsageGateway resourceUsageGateway;

    @Resource
    ResourceChargeGateway resourceChargeGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    ServiceCatalogGateway serviceCatalogGateway;

    @Resource
    BillingGateway billingGateway;

    @Resource
    SkyFormGateway skyformGateway;

    @Resource
    ServiceStatusDAOGateway serviceStatusDAOGateway;

    @Resource
    JobDataDAOGateway jobDataDAOGateway;

    @Resource
    LockConfig lockConfig;

    // 创建订单
    @Override
    public OrderCreateResponse newOrder(CreateOrderRequest<CreateNewTypeOrderItem> orderRequest, String accountId) {
        if (orderRequest.getItems().isEmpty()) {
            throw new CommonExceptions.RequestInvalidException("item is empty");
        }

        // 创建订单
        OrderCreateResponse response = new OrderCreateResponse();
        try {
            OrderUuidResult orderUuidResult = serviceCatalogGateway.createOrder(accountId, orderRequest);
            String orderId = orderUuidResult.getOrderId();
            response.setOrderId(orderId);

            String productType = orderRequest.getItems().get(0).getProductType();
            if (productType.equalsIgnoreCase("postpay")) {
                response.setStatus(OrderStatus.READY_FOR_CREATE);
            } else {
                response.setStatus(OrderStatus.NEED_PURCHASE);
            }

        } catch (WebClientResponseException e) {
            response.setStatus(OrderStatus.CREATE_FAILED);
            response.setReason(e.getResponseBodyAsString());
            log.error("=====>create order failed: {}", e.getResponseBodyAsString());

            e.printStackTrace();
        }  catch (Exception e) {
            response.setStatus(OrderStatus.CREATE_FAILED);
            response.setReason(e.getMessage());
            log.error("=====>create order failed: {}", e.getMessage());

            e.printStackTrace();
        }
        return response;
    }

    // 执行订单，开始创建资源
    @Override
    public OrderExecutorResponse execute(String orderId) {
        log.info("order executor execute: {}", orderId);
        // 先查一次订单.区分helixfold3和天云订单
        com.baidu.bce.internalsdk.order.model.Order order = billingGateway.getOrder("", orderId);
        if (order.getItems().size() > 0) {
            OrderExecutorResponse response = new OrderExecutorResponse();
            // helixfold3 订单
            if (HelixConstant.HELIXFOLD3_ORDER_KEY.equals(order.getItems().get(0).getKey())) {
                return submitHelixfold3Job(HelixConstant.HELIXFOLD3_ORDER_KEY, orderId, order, response);
            } else if (HelixConstant.HELIXVS_ORDER_KEY.equals(order.getItems().get(0).getKey())) {
                // helixvs订单
                return submitHelixvsJob(HelixConstant.HELIXVS_ORDER_KEY, orderId, order, response);
            } else if (HelixConstant.HELIX_CPU_ORDER_KEY.equals(order.getItems().get(0).getKey())) {
                // helixcpu订单
                return submitHelixCPUJob(HelixConstant.HELIX_CPU_ORDER_KEY, orderId, order, response);
            }
            return response;
        }

        // 天云订单处理
        return checkResource(orderId);
    }

    @Nullable
    private OrderExecutorResponse submitHelixCPUJob(String orderKey, String orderId, com.baidu.bce.internalsdk.order.model.Order order, OrderExecutorResponse response) {
        return getOrderExecutorResponse(orderKey, orderId, order, response);
    }


    @Nullable
    private OrderExecutorResponse submitHelixvsJob(String orderKey, String orderId, com.baidu.bce.internalsdk.order.model.Order order, OrderExecutorResponse response) {
        // TODO 提交Helixvs任务

        // taskId为mock,只推送账单
        if (HelixConstant.HELIXVS_MOCK_TASK.equals(order.getItems().get(0).getExtra())) {
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.CREATING);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus(OrderStatus.CREATING.toString());
            return response;
        }
        return getOrderExecutorResponse(orderKey, orderId, order, response);

    }

    private OrderExecutorResponse getOrderExecutorResponse(String orderKey, String orderId, com.baidu.bce.internalsdk.order.model.Order order, OrderExecutorResponse response) {

        // 1、查询db是否有订单
        HelixJob helixJob = helixJobDAOGateway.findByTaskId(order.getItems().get(0).getExtra());
        // 2、作业信息先入库，然后订单执行器回调，如果有订单说明作业是一定存在的，查不到应该是db异常，订单状态还是READY_FOR_CREATE，等待下一轮回调
        if (helixJob == null) {
            response.setExecutionStatus(OrderStatus.READY_FOR_CREATE.toString());
            return response;
        } else if (StringUtils.isEmpty(helixJob.getOrderId())) {
            // 如果没有orderId，orderId关联到db中
            helixJob.setOrderId(orderId);
            Boolean success = helixJobDAOGateway.update(order.getItems().get(0).getExtra(), orderId, "", "", "", "", false);
            if (!success) {
                // 可能是db异常，订单状态还是READY_FOR_CREATE，等待下一轮回调
                response.setExecutionStatus(OrderStatus.READY_FOR_CREATE.toString());
                return response;
            }
        }

        // 如果订单id和作业id不匹配
        if (!orderId.equals(helixJob.getOrderId())) {
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus(OrderStatus.CREATE_FAILED.toString());
            return response;
        }

        // 3、如果作业是cancel状态，推送到mq，并将订单置为failed

        if (HelixJobStatus.USER_CANCEL.equals(helixJob.getAction())) {
            // taskId都是paddlehelix_infer_66193这种，解析出最后下划线的taskId
            String taskId = order.getItems().get(0).getExtra();
            String[] parts = order.getItems().get(0).getExtra().split("_");
            if (parts.length > 0) {
                taskId = parts[parts.length - 1];
            }
            String arguments = " --task-id " + taskId + " --task-status " + HelixConstant.HELIXFOLD3_CANCEL_TASK_STATUS
                    + " --job-fail-reason " + "CANCELLED";
            backendGateway.actionProxy(helixJob.getClusterId(), "job_error_pub", arguments);
            helixJobDAOGateway.update(taskId, "", "", HelixJobStatus.CANCELLED, "", "", false);
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus(OrderStatus.CREATE_FAILED.toString());
            return response;
        }

        // 4、如果作业是submit_failed状态，推送到mq，并将订单置为failed
        if (HelixJobStatus.SUBMIT_FAILED.equals(helixJob.getJobStatus())) {
            return submitJobFail(helixJob.getClusterId(), orderId, order, response);
        }

        // 5、已经记录，如果没有关联作业id,将订单状态改为creating

        if (StringUtils.isEmpty(helixJob.getJobId())) {
            SubmitJobRequest request = JacksonUtil.decode(helixJob.getExtra(), SubmitJobRequest.class);
            Map<String, Object> paramMap = new HashMap<>();
            // 直接传入accountId
            paramMap.put("CHPC_USERNAME", helixJob.getAccountId());
            paramMap.put("CHPC_APPLICATION", "common");
            if (!StringUtils.isEmpty(request.getJobName())) {
                paramMap.put("CHPC_JOB_NAME", request.getJobName());
            }
            if (!StringUtils.isEmpty(request.getJobCmd())) {
                paramMap.put("CHPC_JOB_CMD", request.getJobCmd());
            }
            if (!StringUtils.isEmpty(request.getQueue())) {
                paramMap.put("CHPC_QUEUE", request.getQueue());
            }
            if (!StringUtils.isEmpty(request.getPostCmd())) {
                paramMap.put("CHPC_POST_CMD", request.getPostCmd());
            }
            if (!StringUtils.isEmpty(request.getBosFilePath())) {
                paramMap.put("CHPC_BOS_FILE_PATH", request.getBosFilePath());
            }
            if (!StringUtils.isEmpty(request.getDecompressCmd())) {
                paramMap.put("CHPC_DECOMPRESS_CMD", request.getDecompressCmd());
            }
            if (request.getNhosts() != null) {
                paramMap.put("CHPC_NHOSTS", request.getNhosts());
            }
            if (request.getLimitTimeInMinutes() != 0) {
                paramMap.put("CHPC_WALLTIME", request.getLimitTimeInMinutes());
            }

            if (helixfold3Cluster.equals(helixJob.getClusterId())) {
                log.info("submit helixfold3 order job, paramMap is: {}", paramMap.toString());
                if (request.getEnvVars() != null) {
                    for (Map.Entry<String, String> entry : request.getEnvVars().entrySet()) {
                        paramMap.put(entry.getKey(), entry.getValue());
                    }
                }
            } else if (helixvsCluster.equals(helixJob.getClusterId())) {
                log.info("submit helixvs order job, paramMap is: {}", paramMap.toString());
                // helixvs算法参数在model_input_json中
                Gson gson = new Gson();
                JsonObject jsonObject = gson.fromJson(request.getEnvVars().get("extra_params"), JsonObject.class);
                JsonElement modelInputElement = jsonObject.get("model_input_json");
                JsonObject modelInputObject = modelInputElement.getAsJsonObject();
                if (StringUtils.isNotEmpty(request.getEnvVars().get("output_path"))) {
                    modelInputObject.addProperty("output_path", request.getEnvVars().get("output_path"));
                }

                String env = jsonObject.get("env").getAsString();
                modelInputObject.addProperty("env", env);

                JsonArray itemsArray = jsonObject.get("items").getAsJsonArray();
                String taskId = "";
                for (JsonElement itemElement : itemsArray) {
                    JsonObject itemObject = itemElement.getAsJsonObject();
                    taskId = itemObject.get("task_id").getAsString();
                    modelInputObject.addProperty("task_id", taskId);
                }

                String modelInputJson = gson.toJson(modelInputObject);
                paramMap.put("extra_params", modelInputJson);
            } else {
                log.error("clusterId not supported, clusterId: {}", helixJob.getClusterId());
                return submitJobFail(helixJob.getClusterId(), orderId, order, response);
            }
            String arguments = "";
            ObjectMapper mapper = new ObjectMapper();
            String backendJobParam = "";
            try {
                backendJobParam = mapper.writeValueAsString(paramMap);
            } catch (Exception e) {
                log.error("failed to writeValueAsString, error is: ", e);
                // 此处为job提交信息解析失败，直接返回fail
                return submitJobFail(helixJob.getClusterId(), orderId, order, response);
            }
            arguments = arguments + " --param " + "\'" + backendJobParam + "\'";
            String jobId = "";
            // 加锁，避免重复提交
            String lockKey = "helix_job_submit_" + helixJob.getTaskId();
            if (!lockConfig.tryLock(lockKey, 0, 12, TimeUnit.HOURS)) {
                // 加锁失败说明已经提交过，但某步失败导致未更新订单状态，此处直接按照失败处理
                // 更新作业为提交失败
                log.info("helix task submit lock failed, taskId is: {}", helixJob.getTaskId());
                return submitJobFail(helixJob.getClusterId(), orderId, order, response);
            }
            log.info("helix task submit lock succ, taskId is: {}", helixJob.getTaskId());
            try {
                BackendActionProxyResponse resp = backendGateway.actionProxy(helixJob.getClusterId(), "job_submit", arguments, true);
                // 正常返回结果，但是状态码非200，说明任务提交失败
                if (resp.getCode() != 200) {
                    // 更新作业为提交失败
                    return submitJobFail(helixJob.getClusterId(), orderId, order, response);
                }
                mapper = new ObjectMapper();
                jobId = mapper.readValue(resp.getData(), String.class);
            } catch (Exception e) {
                log.error("failed to submit job, error is: ", e);
                // 此类情况是作业已经提交，但是提交失败了
                if (e instanceof WebClientResponseException) {
                    WebClientResponseException webClientException = (WebClientResponseException) e;
                    if (webClientException.getResponseBodyAsString().contains("调度器插件脚本调用失败") || webClientException.getResponseBodyAsString().contains("调度器连接失败")) {
                        // 更新作业为提交失败
                        return submitJobFail(helixJob.getClusterId(), orderId, order, response);
                    }
                } else if (e.getMessage().contains("调度器插件脚本调用失败") || e.getMessage().contains("调度器连接失败")) {
                    // 更新作业为提交失败
                    return submitJobFail(helixJob.getClusterId(), orderId, order, response);
                }
                response.setExecutionStatus(OrderStatus.READY_FOR_CREATE.toString());
                return response;
            }

            // 6、更新jobId到db,状态更新为已提交
            Boolean success = helixJobDAOGateway.update(order.getItems().get(0).getExtra(), orderId, jobId, HelixJobStatus.SUBMITTED, "", "", false);
            if (!success) {
                // taskId都是paddlehelix_infer_66193这种，解析出最后下划线的taskId
                String taskId = order.getItems().get(0).getExtra();
                String[] parts = order.getItems().get(0).getExtra().split("_");
                if (parts.length > 0) {
                    taskId = parts[parts.length - 1];
                }
                // 如果任务已经提交，但是更新到db失败了。后续check是拿不到jobId的，因此认为这个订单也失败了
                arguments = " --task-id " + taskId + " --task-status " + HelixConstant.HELIXFOLD3_FAILED_TASK_STATUS
                        + " --job-fail-reason " + "SUBMIT_FAILED";
                try {
                    backendGateway.actionProxy(helixJob.getClusterId(), "job_error_pub", arguments);
                } catch (Exception e) {
                    log.error("failed to pub job, error is: ", e);
                }
                // 更新作业为提交失败
                helixJobDAOGateway.update(order.getItems().get(0).getExtra(), orderId, jobId, HelixJobStatus.SUBMIT_FAILED, "", "", true);
                UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
                updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
                billingGateway.updateOrder("", orderId, updateOrderRequest);
                response.setExecutionStatus(OrderStatus.CREATE_FAILED.toString());
                return response;
            }
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.CREATING);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus(OrderStatus.CREATING.toString());
        }
        return response;
    }

    @Nullable
    private OrderExecutorResponse submitHelixfold3Job(String orderKey, String orderId, com.baidu.bce.internalsdk.order.model.Order order, OrderExecutorResponse response) {
        return getOrderExecutorResponse(orderKey, orderId, order, response);
    }

    // 更新作业为提交失败
    private OrderExecutorResponse submitJobFail(String clusterId, String orderId, com.baidu.bce.internalsdk.order.model.Order order, OrderExecutorResponse response) {
        // taskId都是paddlehelix_infer_66193这种，解析出最后下划线的taskId
        String taskId = order.getItems().get(0).getExtra();
        String[] parts = order.getItems().get(0).getExtra().split("_");
        if (parts.length > 0) {
            taskId = parts[parts.length - 1];
        }
        String arguments = " --task-id " + taskId + " --task-status " + HelixConstant.HELIXFOLD3_FAILED_TASK_STATUS
                + " --job-fail-reason " + "SUBMIT_FAILED";
        try {
            BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "job_error_pub", arguments);
            if (resp.getCode() != 200) {
                response.setExecutionStatus(OrderStatus.READY_FOR_CREATE.toString());
                return response;
            }
        } catch (Exception jobPubError) {
            log.error("failed to pub job, error is: ", jobPubError);
            response.setExecutionStatus(OrderStatus.READY_FOR_CREATE.toString());
            return response;
        }
        Boolean success = helixJobDAOGateway.update(order.getItems().get(0).getExtra(), orderId, "", HelixJobStatus.SUBMIT_FAILED, "", "", true);
        if (!success) {
            // 可能是db异常，订单状态还是READY_FOR_CREATE，等待下一轮回调
            response.setExecutionStatus(OrderStatus.READY_FOR_CREATE.toString());
            return response;
        }
        UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
        updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
        billingGateway.updateOrder("", orderId, updateOrderRequest);
        response.setExecutionStatus(OrderStatus.CREATE_FAILED.toString());
        return response;
    }

    // 查询订单是否执行完成
    @Override
    public OrderExecutorResponse check(String orderId) {
        log.info("order executor check: {}", orderId);
        // 先查一次订单.区分helixfold3、helixvs、天云订单
        com.baidu.bce.internalsdk.order.model.Order order = billingGateway.getOrder("", orderId);

        if (order.getItems().size() > 0) {
            OrderExecutorResponse response = new OrderExecutorResponse();
            // helixfold3 订单
            if (HelixConstant.HELIXFOLD3_ORDER_KEY.equals(order.getItems().get(0).getKey())) {
                return checkHelixfold3Job(HelixConstant.HELIXFOLD3_ORDER_KEY, orderId, order, response);
            } else if (HelixConstant.HELIXVS_ORDER_KEY.equals(order.getItems().get(0).getKey())) {
                // helixvs订单
                return checkHelixCPUJob(HelixConstant.HELIXVS_ORDER_KEY, orderId, order, response);
            } else if (HelixConstant.HELIX_CPU_ORDER_KEY.equals(order.getItems().get(0).getKey())) {
                // helixcpu订单
                return checkHelixCPUJob(HelixConstant.HELIX_CPU_ORDER_KEY, orderId, order, response);
            }
        }
        // 天云订单处理
        return checkResource(orderId);
    }


    private OrderExecutorResponse checkHelixCPUJob(String orderKey, String orderId, com.baidu.bce.internalsdk.order.model.Order order, OrderExecutorResponse response) {
        // TODO 检查Helixvs任务

        // taskId为mock,只推送账单
        if (HelixConstant.HELIXVS_MOCK_TASK.equals(order.getItems().get(0).getExtra())) {
            String resourceId = String.valueOf(order.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            for (int i = 0; i < order.getItems().get(0).getFlavors().size(); i++) {
                if (HelixConstant.HELIXVS_CHEMDIV_NORMAL.equals(order.getItems().get(0).getFlavors().get(i).getName())
                        || HelixConstant.HELIXVS_LIFECHEMICALS_NORMAL.equals(order.getItems().get(0).getFlavors().get(i).getName())
                        || HelixConstant.HELIXVS_TARGETMOL_NORMAL.equals(order.getItems().get(0).getFlavors().get(i).getName())
                        || HelixConstant.HELIXVS_TOPSCIENCE_DATABASE_NORMAL.equals(order.getItems().get(0).getFlavors().get(i).getName())) {
                    resourceId = order.getItems().get(0).getFlavors().get(i).getName() + "_Task_" + resourceId;
                    break;
                }
            }
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            List<ResourceMapping> resourceMappings = new LinkedList<>();
            ResourceMapping resourceMapping = new ResourceMapping();
            resourceMapping.setId(resourceId);
            resourceMapping.setKey(HelixConstant.HELIXVS_ORDER_KEY);
            resourceMapping.setStatus(ResourceStatus.RUNNING);
            resourceMappings.add(resourceMapping);
            updateOrderRequest.setResources(resourceMappings);
            updateOrderRequest.setStatus(OrderStatus.CREATED);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus(OrderStatus.CREATED.toString());
            return response;
        }

        return orderCheckResponse(orderKey, orderId, order, response);
    }

    private OrderExecutorResponse checkHelixfold3Job(String orderKey, String orderId, com.baidu.bce.internalsdk.order.model.Order order, OrderExecutorResponse response) {
        return orderCheckResponse(orderKey, orderId, order, response);
    }

    @NotNull
    private OrderExecutorResponse orderCheckResponse(String orderKey, String orderId, com.baidu.bce.internalsdk.order.model.Order order, OrderExecutorResponse response) {
        // 1、查询作业id
        HelixJob helixJob = helixJobDAOGateway.findByTaskId(order.getItems().get(0).getExtra());

        // 这里不会出现数据库查不到的情况，如果有，应该是db暂时异常，等待下一次订单check回调
        if (helixJob == null) {
            response.setExecutionStatus(OrderStatus.CREATING.toString());
            return response;
        }

        // 如果订单id和作业id不匹配
        if (!orderId.equals(helixJob.getOrderId())) {
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus(OrderStatus.CREATE_FAILED.toString());
            return response;
        }

        // 2、如果作业是cancel状态，推送到mq，并将订单置为failed
        if (HelixJobStatus.USER_CANCEL.equals(helixJob.getAction())) {
            // taskId都是paddlehelix_infer_66193这种，解析出最后下划线的taskId
            String taskId = order.getItems().get(0).getExtra();
            String[] parts = order.getItems().get(0).getExtra().split("_");
            if (parts.length > 0) {
                taskId = parts[parts.length - 1];
            }
            String arguments = " --task-id " + taskId + " --task-status " + HelixConstant.HELIXFOLD3_CANCEL_TASK_STATUS
                    + " --job-fail-reason " + "CANCELLED";
            backendGateway.actionProxy(helixJob.getClusterId(), "job_error_pub", arguments);
            helixJobDAOGateway.update(taskId, "", "", HelixJobStatus.CANCELLED, "", "", false);
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus(OrderStatus.CREATE_FAILED.toString());
            return response;
        }

        // 3、查询作业的状态
        JobDetail jobDetail;
        String arguments = " --job-id " + helixJob.getJobId();
        try {
            ObjectMapper mapper;
            BackendActionProxyResponse resp = backendGateway.actionProxy(helixJob.getClusterId(), "job_detail", arguments);
            if (resp.getCode() != 200) {
                response.setExecutionStatus(OrderStatus.CREATING.toString());
                return response;
            }
            mapper = new ObjectMapper();
            jobDetail = mapper.readValue(resp.getData(), JobDetail.class);
        } catch (Exception e) {
            response.setExecutionStatus(OrderStatus.CREATING.toString());
            return response;
        }

        // 兼容下查不到作业的情况
        if (jobDetail == null) {
            response.setExecutionStatus(OrderStatus.CREATING.toString());
            return response;
        }

        // 4、更新作业信息
        helixJobDAOGateway.update(helixJob.getTaskId(), "", "", jobDetail.getJobState(), "", String.valueOf(jobDetail.getRunTime()), false);

        // 5、根据作业信息更新订单状态，COMPLETED认为订单成功
        if (HelixJobStatus.COMPLETED.equals(jobDetail.getJobState())) {
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            List<ResourceMapping> resourceMappings = new LinkedList<>();
            ResourceMapping resourceMapping = new ResourceMapping();
            resourceMapping.setId(helixJob.getTaskId());
            resourceMapping.setKey(orderKey);
            resourceMapping.setStatus(ResourceStatus.RUNNING);
            resourceMappings.add(resourceMapping);
            updateOrderRequest.setResources(resourceMappings);
            updateOrderRequest.setStatus(OrderStatus.CREATED);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus(OrderStatus.CREATED.toString());
        } else if (!HelixJobStatus.RUNNING.equals(jobDetail.getJobState())
                && !HelixJobStatus.PENDING.equals(jobDetail.getJobState())
                && !HelixJobStatus.CONFIGURING.equals(jobDetail.getJobState())
                && !HelixJobStatus.COMPLETING.equals(jobDetail.getJobState())
                && !HelixJobStatus.RESIZING.equals(jobDetail.getJobState())
                && !HelixJobStatus.REQUEUED.equals(jobDetail.getJobState())
                && !HelixJobStatus.REVOKED.equals(jobDetail.getJobState())
                && !HelixJobStatus.SUSPENDED.equals(jobDetail.getJobState())) {
            // 非COMPLETED、RUNNING、PENDING、CONFIGURING、COMPLETING、RESIZING、
            // REQUEUED、REVOKED、SUSPENDED认为作业失败
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus(OrderStatus.CREATE_FAILED.toString());
        } else {
            // RUNNING、PENDING、CONFIGURING、COMPLETING、RESIZING、REQUEUED、REVOKED、SUSPENDED认为作业还在执行中
            response.setExecutionStatus(OrderStatus.CREATING.toString());
        }
        return response;
    }

    private OrderExecutorResponse checkResource(String orderId) {
        OrderExecutorResponse response = new OrderExecutorResponse();

        // 查找订单
        Order orderInfo = orderDAOGateway.findByOrderId(orderId);
        if (orderInfo == null || !OrderStatus.CREATED.toString().equals(orderInfo.getStatus())) {
            log.error("order {} not exist", orderId);
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus("NOT_EXISTED");
            return response;
        }

        SaasResource serviceResource = saasResourceDAOGateway.findByOrderId(orderId);
        if (serviceResource == null || !"RUNNING".equals(serviceResource.getStatus())) {
            log.error("order {} resource not exist", orderId);
            UpdateOrderRequest updateOrderRequest = new UpdateOrderRequest();
            updateOrderRequest.setStatus(OrderStatus.CREATE_FAILED);
            billingGateway.updateOrder("", orderId, updateOrderRequest);
            response.setExecutionStatus("NOT_EXISTED");
            return response;
        }

        response.setExecutionStatus(OrderStatus.CREATED.toString());
        return response;
    }

    // 查询订单信息
    @Override
    public OrderInfoResponse queryOrder(String orderId) {
        Order orderInfo = orderDAOGateway.findByOrderId(orderId);
        if (orderInfo == null) {
            log.error("order {} not exist", orderId);
            throw new CommonExceptions.ResourceNotExistException(String.format("order %s not exist", orderId));
        }
        com.baidu.bce.internalsdk.order.model.Order order = billingGateway.getOrder(orderInfo.getAccountId(), orderId);
        OrderInfoResponse response = new OrderInfoResponse();
        response.setOrder(order);
        return response;
    }

    @Override
    public com.baidu.bce.internalsdk.order.model.Order queryOrderDetail(String orderId) {
        com.baidu.bce.internalsdk.order.model.Order order = billingGateway.getOrder("", orderId);
        if (!"project".equals(order.getSubProductType())) {
            return order;
        }
        if (HelixConstant.HELIXFOLD3_ORDER_KEY.equals(order.getItems().get(0).getKey())) {
            List<String> chargeType = new ArrayList<>();
            chargeType.add("ONETIME");
            order.getItems().get(0).setChargeType(chargeType);
            // TODO 一次性订单无法传入实际的单价，实际展示为一次性订单的总金额
            order.getItems().get(0).setUnitPriceShow("￥0.069元");
            List<String> configuration = new ArrayList<>();
            configuration.add("类型: HelixFold3 算力单元");
            String jobChargeAmount;
            if (!"subServiceType".equals(order.getItems().get(0).getFlavors().get(0).getName())) {
                jobChargeAmount = order.getItems().get(0).getFlavors().get(0).getValue();
            } else {
                jobChargeAmount = order.getItems().get(0).getFlavors().get(1).getValue();
            }
            BigDecimal bd = new BigDecimal(jobChargeAmount);

            BigDecimal realJobChargeAmount = bd.setScale(2, RoundingMode.HALF_UP);
            configuration.add("数量*时长:" + realJobChargeAmount);
            order.getItems().get(0).setConfiguration(configuration);
        } else if (HelixConstant.HELIXVS_ORDER_KEY.equals(order.getItems().get(0).getKey())) {
            List<String> chargeType = new ArrayList<>();
            chargeType.add("ONETIME");
            order.getItems().get(0).setChargeType(chargeType);
        }
        return order;
    }

    // 取消订单
    @Override
    public OrderExecutorResponse terminate(String orderId, String status, String serviceType) {
        throw new CommonExceptions.ResourceNotExistException("not implemented");
    }

    // 查询资源状态
    @Override
    public QueryResourceResponse queryResource(String resourceUuid, String accountId) {
        SaasResource serviceResource = saasResourceDAOGateway.findByResourceUuid(resourceUuid, accountId);
        if (serviceResource == null) {
            log.error("resource {} {} not exist", resourceUuid, accountId);
            throw new CommonExceptions.ResourceNotExistException(
                    String.format("resource %s %s not exist", resourceUuid, accountId));
        }

        QueryResourceResponse response = new QueryResourceResponse();
        response.setId(resourceUuid);
        response.setAccountId(accountId);
        response.setRegion(regionConfiguration.getCurrentRegion());
        response.setService(serviceResource.getResourceType());
        response.setStatus(serviceResource.getStatus());
        return response;
    }

    // 启停资源
    @Override
    public ActiveResourceResponse activateResource(
            String resourceUuid, String accountId, String action, BillingActiveRequest billingActiveForm) {
        log.info("billing query resource: {}", resourceUuid);
        log.info("billing query billingActiveForm: {}", billingActiveForm);
        SaasResource saasResource = saasResourceDAOGateway.findByResourceUuid(resourceUuid, accountId);
        if (saasResource == null) {
            log.error("resource {} {} not exist", resourceUuid, accountId);
            throw new CommonExceptions.ResourceNotExistException(
                    String.format("resource %s %s not exist", resourceUuid, accountId));
        }
        ActiveResourceResponse response = new ActiveResourceResponse();
        // todo 根据 resourceType 找到 Saas 服务实例
        switch (action) {
            case "START":
                saasResource.setStatus(ResourceStatus.RUNNING.toString());
                saasResourceDAOGateway.update(saasResource);
                // 恢复组织配额
                GetTenantResponse tenantByTenantName = skyformGateway.getTenantByTenantName(accountId);
                if (null != tenantByTenantName.getRetObj()) {
                    skyformGateway.configTenantQuota(tenantByTenantName.getRetObj().getUuid(), "null", "null", "null");
                }
                // 更新db订单为CREATED
                Order orderInfo = orderDAOGateway.findByAccountIdAndServiceType(accountId, SaasResourceType.CHPC.name());
                orderInfo.setStatus(OrderStatus.CREATED.toString());
                orderInfo.setReason(billingActiveForm.getReason());
                Boolean success = orderDAOGateway.update(orderInfo);
                log.debug("updateByJobId order status: {} {}", success, orderInfo.getStatus());
                response.setStatus("STARTED");
                break;

            case "STOP":
                saasResource.setStatus(ResourceStatus.STOPPED.toString());
                saasResourceDAOGateway.update(saasResource);
                // 设置组织配额为0
                tenantByTenantName = skyformGateway.getTenantByTenantName(accountId);
                if (null != tenantByTenantName.getRetObj()) {
                    skyformGateway.configTenantQuota(tenantByTenantName.getRetObj().getUuid(), "0", "0", "0");
                }
                // 更新db订单的reason
                orderInfo = orderDAOGateway.findByAccountIdAndServiceType(accountId, SaasResourceType.CHPC.name());
                orderInfo.setReason(billingActiveForm.getReason());
                success = orderDAOGateway.update(orderInfo);
                log.debug("updateByJobId order reason: {} {}", success, billingActiveForm.getReason());
                // 此处不更新订单状态，客户正在运行的job还可能继续产生费用
                response.setStatus("STOPPED");
                break;

            case "DELETE":
                saasResource.setStatus(ResourceStatus.DESTROYED.toString());
                saasResource.setDeleted(true);
                saasResourceDAOGateway.update(saasResource);
                //  销毁 SaaS 资源
                tenantByTenantName = skyformGateway.getTenantByTenantName(accountId);
                GetUserResponse usersByTenantId =
                        skyformGateway.getUsersByTenantId(tenantByTenantName.getRetObj().getUuid());
                if (null != usersByTenantId.getRetObj() && usersByTenantId.getRetObj().size() > 0) {
                    // 从天云查询最新的作业状态
                    ListJobsResponse listJobsResponse =
                            skyformGateway.listJobs(tenantByTenantName.getRetObj().getUuid(), "", "");

                    List<String> uuids = new ArrayList<>();
                    // 批量删除用户未完成的作业
                    if (listJobsResponse.getRetObj() != null && listJobsResponse.getRetObj().getContent() != null) {
                        for (ListJobsResponse.Content content : listJobsResponse.getRetObj().getContent()) {
                            if (!JobStatusType.EXIT.toString().equals(content.getJobStatus())
                                    && !JobStatusType.FINISH.toString().equals(content.getJobStatus())
                                    && !JobStatusType.ERROR.toString().equals(content.getJobStatus())) {
                                uuids.add(content.getUuid());
                            }
                        }
                    }
                    skyformGateway.stopJobs(uuids);
                    try {
                        // 让当前线程等待 3 秒，保证作业删除
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    // 批量删除用户
                    uuids.clear();
                    for (int i = 0; i < usersByTenantId.getRetObj().size(); i++) {
                        uuids.add(usersByTenantId.getRetObj().get(i).getUuid());
                    }
                    skyformGateway.removeUsers(uuids);
                    try {
                        // 让当前线程等待 3 秒，保证用户删除
                        Thread.sleep(3000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    // 删除组织
                    uuids.clear();
                    uuids.add(usersByTenantId.getRetObj().get(0).getTenantId());
                    skyformGateway.removeTenants(uuids);
                }
                // 更新db订单为DELETED
                Order order = orderDAOGateway.findByAccountIdAndServiceType(accountId, SaasResourceType.CHPC.name());
                order.setStatus("DELETED");
                order.setReason(billingActiveForm.getReason());
                order.setDeleted(true);
                success = orderDAOGateway.update(order);
                // 删除天云服务开通记录
                serviceStatusDAOGateway.delete(Long.valueOf(2), accountId);
                // db删除用户作业
                jobDataDAOGateway.delete(accountId);
                log.debug("updateByJobId order status: {} {}", success, order.getStatus());
                response.setStatus("DELETED");
                break;

            default:
                log.error("action {} not supported", action);
                throw new CommonExceptions.RequestInvalidException(String.format("action %s not supported", action));
        }
        return response;
    }

    @Override
    public Boolean chargeData(String orderId, LegacyChargeDataRequest request) {
        try {
            // 推送用量到 billing
            resourceChargeGateway.charge(request);
            return true;
        } catch (Exception e) {
            log.error("charge billing data failed", e);
            return false;
        }
    }

    @Override
    public LegacyChargeDataRequest makeChargeData(
            String userId,
            String serviceName,
            String chargeItem,
            String instanceId,
            String sum,
            String unit,
            Long chargeDataTime) {
        LegacyChargeDataRequest request = new LegacyChargeDataRequest();
        List<LegacyUserChargeData> userChargeDatas = new ArrayList<>();
        LegacyUserChargeData userChargeData = new LegacyUserChargeData();
        userChargeData.setScope(String.format("BCE_%s", serviceName));
        userChargeData.setUserId(userId);
        userChargeData.setMsgId(UUID.randomUUID().toString());
        List<LegacyMetricData> metricDataList = new ArrayList<>();

        LegacyMetricData metricData1 = new LegacyMetricData();
        metricData1.setMetricName(chargeItem);
        List<LegacyDimension> dimensions = new ArrayList<>();
        LegacyDimension dimension1 = new LegacyDimension();
        dimension1.setName("region");
        dimension1.setValue(regionConfiguration.getCurrentRegion());
        dimensions.add(dimension1);
        LegacyDimension dimension2 = new LegacyDimension();
        dimension2.setName("instanceId");
        dimension2.setValue(instanceId);
        dimensions.add(dimension2);
        metricData1.setDimensions(dimensions);
        LegacyStatisticValues statisticValues = new LegacyStatisticValues();
        statisticValues.setSum(new BigDecimal(sum)); // *具体数额
        statisticValues.setUnit(unit);
        metricData1.setStatisticValues(statisticValues);
        metricData1.setTimestamp(chargeDataTime); // todo 用量时间
        metricData1.setTag(false);

        metricDataList.add(metricData1);
        userChargeData.setMetricData(metricDataList);
        userChargeDatas.add(userChargeData);
        request.setUserChargeDatas(userChargeDatas);

        return request;
    }

    // 查询用户计费用量（正式使用，只传 chargeData）
    @Override
    public Boolean resourceUsageTrail(ChargeData chargeData, String startTime, String endTime, String region) {
        // 根据用量计算推送时间查询该用量是否被记录
        ResourceUsageRequest request = new ResourceUsageRequest();
        request.setAccountId(chargeData.getAccountId());
        request.setInstanceId(chargeData.getInstanceId());
        request.setServiceName(chargeData.getServiceType());
        if (region != null && !region.isEmpty()) {
            request.setRegion(region);
        } else {
            request.setRegion(regionConfiguration.getCurrentRegion());
        }
        request.setChargeItem(chargeData.getChargeItem());

        if (startTime != null && !startTime.isEmpty() && endTime != null && !endTime.isEmpty()) {
            // *指定起止时间，测试使用
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(BceConstant.DATETIME_FORMAT);
            request.setStartTime(LocalDateTime.parse(startTime, formatter));
            request.setEndTime(LocalDateTime.parse(endTime, formatter));
        } else if (chargeData.getChargeTime() != null) {
            // *根据推量时间，计算起止时间，正式使用
            LocalDateTime chargeDateTime = chargeData.getChargeTime();
            LocalDateTime startDateTime =
                    LocalDateTime.of(
                            chargeDateTime.getYear(),
                            chargeDateTime.getMonth(),
                            chargeDateTime.getDayOfMonth(),
                            chargeDateTime.getHour(),
                            0);
            LocalDateTime endDateTime = startDateTime.plusHours(1);
            // log.info("======== startDateTime: {}, endDateTime: {} ========",
            // startDateTime, endDateTime);
            request.setStartTime(startDateTime);
            request.setEndTime(endDateTime);
        } else {
            log.error("resource usage time is emtpy, instanceId: %s", chargeData.getInstanceId());
            throw new BceInternalResponseException("resource usage time is emtpy");
        }

        UsageMeta usageMeta = new UsageMeta();
        usageMeta.setMinuteReducer("SumReducer");
        usageMeta.setDayReducer("SumReducer");
        usageMeta.setMonthReducer("SumReducer");
        usageMeta.setRemoveZero(false);
        request.setUsageMeta(usageMeta);

        ResourceUsage[] response = resourceUsageGateway.getUsage(request);
        if (response.length == 0) {
            log.error(
                    "resource usage is emtpy, instanceId: %s, chargeTime: %s",
                    chargeData.getInstanceId(),
                    chargeData.getChargeTime());
            return false;
        }
        if (response[0].getAmount() == null) {
            log.error(
                    "resource usage get amount is emtpy, instanceId: %s, chargeTime: %s",
                    chargeData.getInstanceId(),
                    chargeData.getChargeTime());
            return false;
        }
        BigDecimal targetAmount = new BigDecimal(chargeData.getChargeAmount());
        BigDecimal realAmount = new BigDecimal(response[0].getAmount());

        if (realAmount.compareTo(targetAmount) != 0) {
            log.error(
                    "resource usage amount[%s] is not equal to charge data[%s], instanceId: %s, chargeTime: %s",
                    realAmount,
                    targetAmount,
                    chargeData.getInstanceId(),
                    chargeData.getChargeTime());
            return false;
        }

        // todo 成功记录之后，将该用量设置为有效(调用函数里实现逻辑)
        chargeData.setValid(true);
        chargeDataDAOGateway.update(chargeData);

        return true;
    }
}
