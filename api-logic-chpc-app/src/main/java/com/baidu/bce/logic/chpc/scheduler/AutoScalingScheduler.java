package com.baidu.bce.logic.chpc.scheduler;

import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.baidu.bce.logic.chpc.autoscaling.gateway.IAutoScalingDAOGateway;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.config.LockConfig;
import com.baidu.bce.logic.chpc.config.SchedulerThreadLocalHolder;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.service.IAutoScalingService;
import com.baidu.bce.logic.chpc.service.util.SchedulerUserConfig;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class AutoScalingScheduler {

    @Resource
    IAutoScalingService iAutoScalingService;

    @Resource
    QueueDAOGateway queueDAOGateway;

    @Resource
    IAutoScalingDAOGateway autoScalingDAOGateway;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    LockConfig lockConfig;

    @Resource
    SchedulerUserConfig schedulerUserConfig;

    @Scheduled(fixedDelay = 30 * 1000)
    public void execAutoScalingTask() {
        try {
            List<Queue> queues = queueDAOGateway.getByIsAutoScaling(true);
            List<String> queueIds = queues.stream().map(Queue::getQueueId).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(queueIds)) {
                log.debug("There is no autoScaling queueIds to execute");
                return;
            }

            Map<String, Queue> queueId2Queue = queues.stream().map(entry -> new AbstractMap.SimpleEntry<>
                            (entry.getQueueId(), entry)).
                    collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));

            List<AutoScaling> autoScalingList = autoScalingDAOGateway.getByQueueIds(queueIds,
                    AutoScalingStatus.NORMAL.name());

            for (AutoScaling autoScaling : autoScalingList) {
                // 获取集群信息
                Cluster clusterDB = clusterDAOGateway.findByClusterIdAll(autoScaling.getClusterId(), null);
                // 集群已删除直接跳过
                if (clusterDB == null) {
                    log.info("cluster is deleted, clusterId: {}", autoScaling.getClusterId());
                    continue;
                }
                // 有心跳的集群通过心跳执行自动伸缩
                if (clusterDB.getHeartbeat() != 0){
                    continue;
                }

                String lockKey = generateAutoScalingLock(autoScaling);

                if (!lockConfig.tryLock(lockKey)) {
                    continue;
                }

                // 获得锁后，获取最新的AutoScaling信息，因为for循环执行时间不可控，需要防止AutoScaling被其他实例修改
                AutoScaling refreshedAutoScaling = autoScalingDAOGateway.getByClusterIdAndQueueId(
                        autoScaling.getClusterId(),
                        autoScaling.getQueueId(),
                        autoScaling.getAccountId()
                );
                if (refreshedAutoScaling == null || refreshedAutoScaling.getStatus() != AutoScalingStatus.NORMAL) {
                    log.info("autoScaling is null or status is not normal, clusterId:{}, queueId:{}",
                            autoScaling.getClusterId(),
                            autoScaling.getQueueId()
                    );
                    continue;
                }
                autoScaling = refreshedAutoScaling;


                log.debug("success get AutoScalingLockKey: {}", lockKey);
                Queue queue = queueId2Queue.get(autoScaling.getQueueId());

                // 保存用户AccountId
                SchedulerThreadLocalHolder.setAccountId(autoScaling.getAccountId());
                schedulerUserConfig.setUserToken(autoScaling.getAccountId());

                try {
                    iAutoScalingService.executeAutoScalingTask(autoScaling, queue);
                } catch (Exception e) {
                    log.error("autoScaling failed to execute, asId: {}, exception: {}", autoScaling.getAsId(), e);
                }

                // 移除用户信息
                SchedulerThreadLocalHolder.clear();
                schedulerUserConfig.removeUserToken();

                // 解锁
                lockConfig.unLock(lockKey);

            }


        } catch (Exception e) {
            log.error("error in executing AutoScaling task", e);
        }
    }


    private String generateAutoScalingLock(AutoScaling autoScaling) {

        return String.format("AutoScaling-%s-%s-%s",
                autoScaling.getAsId(), autoScaling.getClusterId(), autoScaling.getQueueId());
    }


}
