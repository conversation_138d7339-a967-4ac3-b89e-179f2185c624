package com.baidu.bce.logic.chpc.scheduler;

import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.config.LockConfig;
import com.baidu.bce.logic.chpc.config.SchedulerThreadLocalHolder;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.scheduler.task.SchedulerTaskFactory;
import com.baidu.bce.logic.chpc.scheduler.task.SchedulerTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */
@Component
@Slf4j
public class ChpcSchedulerTaskService {


    @Resource
    private LockConfig lockConfig;

    @Resource
    private ThreadPoolTaskExecutor clusterScheduledTaskThreadPool;

    @Resource
    private SchedulerTaskFactory schedulerTaskFactory;


    @Resource
    private TaskService taskService;

    @Scheduled(fixedDelay = 30 * 1000)
    public void executeSchedulerTask() {

        try {

            List<Task> tasks = taskService.getByStatus(TaskStatus.PROCESSING.getValue());

            for (Task task : tasks) {
                // 校验任务类型是否正确
                TaskType taskType = TaskType.fromType(task.getTaskType());
                if (taskType == null) {
                    taskService.updateStatus(task.getTaskId(), TaskStatus.FAILED.getValue());
                    log.debug("no such task processor, task id: {}", task.getTaskId());
                    continue;
                }
                if (TaskType.OOS_ASYNC_TASK.equals(taskType)) {
                    continue;
                }

                this.executeClusterSchedulerTask(task);
            }
        } catch (Exception e) {
            log.debug("failed to run scheduled cluster failed task, exception is ", e);
        }
    }


    private void executeClusterSchedulerTask(Task task) {


        clusterScheduledTaskThreadPool.execute(() -> {
            String lockKey = this.generateTaskLock(task);

            if (!lockConfig.tryLock(lockKey)) {
                log.debug("failed to lock key: {}", lockKey);
                return;
            }

            log.debug("success to get lock: {}", lockKey);

            try {

                SchedulerTaskService taskService = schedulerTaskFactory.
                        getSchedulerTaskService(task.getTaskType());

                taskService.executeTask(task);

            } catch (Exception e) {
                log.error("failed to execute task: {}, exception is {}", task, e);
            }


            // 解锁
            if (!lockConfig.unLock(lockKey)) {
                log.warn("failed to unlock key: {}", lockKey);
            }
            log.debug("success to unlock key: {}", lockKey);

            // 清除所有线程配置
            SchedulerThreadLocalHolder.clear();
        });
    }

    private String generateTaskLock(Task task) {

        return String.format("SchedulerTask-%s-%s-%s", task.getTaskId(), task.getClusterId(), task.getQueueId());
    }

}
