package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.BccInstanceDetail;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: lilu24
 * @Date: 2023-01-13
 */

@Component(value = "instance_status_sync_task")
public class InstanceStatusSyncTask extends AbstractSchedulerTask {


    @Override
    Map<String, Object> executeTask(Task task, Cluster cluster, Queue queue) {

        Map<String, String> extraMap = JacksonUtil.decodeToMap(task.getExtra(), String.class);
        String instanceId = Optional.of(extraMap.get(ChpcConstant.INSTANCE_ID))
                .orElse("");


        BccInstanceDetail instanceDetail = bccExternalGateway.getBccServerDetail(instanceId);
        if (!(ClusterStatus.ACTIVE.nameLowerCase().equalsIgnoreCase(instanceDetail.getStatus()) ||
                ClusterStatus.RUNNING.nameLowerCase().equalsIgnoreCase(instanceDetail.getStatus()))) {
            return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
        }

        instanceService.updateStatusAndExecutionId(instanceId, null,
                ClusterStatus.ACTIVE.nameLowerCase());

        return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
    }
}
