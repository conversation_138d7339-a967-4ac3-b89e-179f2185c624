package com.baidu.bce.logic.chpc.service.scheduler;

import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendSchedulerResponse;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.scheduler.AclInfo;
import com.baidu.bce.logic.chpc.model.scheduler.GetSchedulerResponse;
import com.baidu.bce.logic.chpc.model.scheduler.QueueConfig;
import com.baidu.bce.logic.chpc.model.scheduler.SchedulerCommonResponse;
import com.baidu.bce.logic.chpc.service.SchedulerService;
import com.baidu.bce.logic.chpc.sheduler.SchedulerAddRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.HashSet;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

@Slf4j
@Service
public class SchedulerServiceImpl implements SchedulerService {

    @Resource
    BackendGateway backendGateway;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Override
    public GetSchedulerResponse getScheduler(String clusterId) {
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        preCheck(cluster);

        // 拿到调度器配置
        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "scheduler_info", "");
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "get scheduler info failed, " + resp.getMessage(), "scheduler plugin");
        }
        ObjectMapper mapper = new ObjectMapper();
        BackendSchedulerResponse res;
        try {
            res = mapper.readValue(resp.getData(), BackendSchedulerResponse.class);
        } catch (Exception e) {
            log.error("json unmarshal BackendSchedulerResponse failed", e);
            return null;
        }
        GetSchedulerResponse getSchedulerResponse = new GetSchedulerResponse();
        getSchedulerResponse.setClusterName(cluster.getName());
        getSchedulerResponse.setClusterId(cluster.getClusterId());
        getSchedulerResponse.setScheduler(cluster.getSchedulerType());

        String[] parts = res.getJobHistory().split(":");
        if (parts.length == 3) {
            int hours = Integer.parseInt(parts[0]);
            int minutes = Integer.parseInt(parts[1]);
            int seconds = Integer.parseInt(parts[2]);

            int totalHours = hours + minutes / 60 + seconds / 3600;
            int days = totalHours / 24;
            getSchedulerResponse.setJobHistory(String.valueOf(days));
        } else {
            getSchedulerResponse.setJobHistory(res.getJobHistory());
        }

        getSchedulerResponse.setMaxQueuedJobs(res.getMaxQueuedJobs());
        getSchedulerResponse.setSchedIteration(res.getSchedIteration());
        getSchedulerResponse.setMaxJobs(res.getMaxJobs());
        List<AclInfo> aclInfoList = new ArrayList<>();
        for (int i = 0; i < res.getAclInfo().size(); i++) {
            AclInfo aclInfo = new AclInfo();
            aclInfo.setQueueName(res.getAclInfo().get(i).getQueueName());
            aclInfo.setUserList(res.getAclInfo().get(i).getUserList());
            aclInfo.sortUserList();
            aclInfoList.add(aclInfo);
        }
        Collections.sort(aclInfoList, (aclInfo1, aclInfo2) -> aclInfo1.getQueueName().compareTo(aclInfo2.getQueueName()));
        getSchedulerResponse.setAclInfo(aclInfoList);

        List<QueueConfig> queueConfigList = new ArrayList<>();
        for (int i = 0; i < res.getQueueConfigList().size(); i++) {
            QueueConfig queueConfig = new QueueConfig();
            queueConfig.setQueueName(res.getQueueConfigList().get(i).getQueueName());
            List<QueueConfig.UserMaxRunLimit> userMaxRunLimitList = new ArrayList<>();
            for (int j = 0; j < res.getQueueConfigList().get(i).getUserMaxRunLimitList().size(); j++) {
                QueueConfig.UserMaxRunLimit userMaxRunLimit = new QueueConfig.UserMaxRunLimit();
                userMaxRunLimit.setCpus(res.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getCpus());
                userMaxRunLimit.setMaxJobs(res.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getMaxJobs());
                userMaxRunLimit.setMem(res.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getMem());
                userMaxRunLimit.setNodes(res.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getNodes());
                userMaxRunLimit.setUserName(res.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getUserName());
                userMaxRunLimitList.add(userMaxRunLimit);
            }
            Collections.sort(userMaxRunLimitList, (user1, user2) -> user1.getUserName().compareTo(user2.getUserName()));
            queueConfig.setUserMaxRunLimitList(userMaxRunLimitList);
            queueConfigList.add(queueConfig);
        }
        Collections.sort(queueConfigList, (QueueConfig1, QueueConfig2) -> QueueConfig1.getQueueName().compareTo(QueueConfig2.getQueueName()));
        getSchedulerResponse.setQueueConfigList(queueConfigList);

        return getSchedulerResponse;
    }

    @Override
    public SchedulerCommonResponse modifyScheduler(String clusterId, SchedulerAddRequest request) {
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        preCheck(cluster);
        checkMemAndUserName(request);

        // 先拿到调度器配置
        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "scheduler_info", "");
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "get scheduler info failed, " + resp.getMessage(), "scheduler plugin");
        }
        ObjectMapper mapper = new ObjectMapper();
        BackendSchedulerResponse schedulerResponse;
        try {
            schedulerResponse = mapper.readValue(resp.getData(), BackendSchedulerResponse.class);
        } catch (Exception e) {
            log.error("json unmarshal BackendSchedulerResponse failed", e);
            return null;
        }

        String queueName = "";
        // 需要删除的用户列表
        List<BackendSchedulerResponse.UserMaxRunLimit> userList = new ArrayList<>();
        // 原始的调度器资源限制用户列表
        List<String> queueConfigUserList = new ArrayList<>();


        if (request.getQueueConfigList().size() > 0) {
            queueName = request.getQueueConfigList().get(0).getQueueName();
            if (request.getQueueConfigList().get(0).getUserMaxRunLimitList() != null) {
                for (int i = 0; i < request.getQueueConfigList().get(0).getUserMaxRunLimitList().size(); i++) {
                    queueConfigUserList.add(request.getQueueConfigList().get(0).getUserMaxRunLimitList().get(i).getUserName());
                }
            }
        }


        // 已经设置了资源限制，但是更新时候没有传入，恢复默认值
        for (int i = 0; i < schedulerResponse.getQueueConfigList().size(); i++) {
            if (queueName.equals(schedulerResponse.getQueueConfigList().get(i).getQueueName())) {
                if (schedulerResponse.getQueueConfigList().get(i).getUserMaxRunLimitList() != null) {
                    for (int j = 0; j < schedulerResponse.getQueueConfigList().get(i).getUserMaxRunLimitList().size(); j++) {
                        if (!queueConfigUserList.contains(schedulerResponse.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getUserName())) {
                            userList.add(schedulerResponse.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j));
                        }
                    }
                }
            }
        }


        for (int i = 0; i < userList.size(); i++) {
            if (request.getQueueConfigList().size() > 0) {
                SchedulerAddRequest.UserMaxRunLimit userMaxRunLimit = new SchedulerAddRequest.UserMaxRunLimit();
                userMaxRunLimit.setUserName(userList.get(i).getUserName());
                userMaxRunLimit.setCpus("");
                userMaxRunLimit.setMem("");
                userMaxRunLimit.setMaxJobs("");
                userMaxRunLimit.setNodes("");
                if (request.getQueueConfigList().get(0).getUserMaxRunLimitList() == null) {
                    request.getQueueConfigList().get(0).setUserMaxRunLimitList(new ArrayList<>());
                }
                request.getQueueConfigList().get(0).getUserMaxRunLimitList().add(userMaxRunLimit);
            }
        }

        String arguments = "";
        if (request.getJobHistory() != null) {
            if (!"".equals(request.getJobHistory())) {
                // 天转换为秒
                Long jobHistory = Long.valueOf(request.getJobHistory());
                jobHistory = jobHistory * 86400;
                arguments = arguments + " -jhd " + jobHistory;
            } else {
                arguments = arguments + " -jhd \"\"";
            }
        }
        if (request.getSchedIteration() != null) {
            if (!"".equals(request.getSchedIteration())) {
                arguments = arguments + " -si " + request.getSchedIteration();
            } else {
                arguments = arguments + " -si \"\"";
            }
        }
        if (request.getMaxQueuedJobs() != null) {
            if (!"".equals(request.getMaxQueuedJobs())) {
                arguments = arguments + " -mq " + request.getMaxQueuedJobs();
            } else {
                arguments = arguments + " -mq \"\"";
            }
        }
        if (request.getMaxJobs() != null) {
            if (!"".equals(request.getMaxJobs())) {
                arguments = arguments + " -qjt " + request.getMaxJobs();
            } else {
                arguments = arguments + " -qjt \"\"";
            }
        }

        // 设置调度器全局参数
        resp = backendGateway.actionProxy(clusterId, "scheduler_info_set", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "set scheduler info failed, " + resp.getMessage(), "scheduler plugin");
        }

        arguments = "";
        if (request.getAclInfo() != null && request.getAclInfo().size() > 0) {
            Gson gson = new Gson();
            String json = gson.toJson(request.getAclInfo());
            arguments = arguments + " -acl '" + json + "'";
        }
        if (request.getQueueConfigList() != null && request.getQueueConfigList().size() > 0) {
            Gson gson = new Gson();
            String json = gson.toJson(request.getQueueConfigList());
            arguments = arguments + " -qr '" + json + "'";
        }


        // 设置用户队列权限和资源限制
        resp = backendGateway.actionProxy(clusterId, "user_limit_info_set", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "set user limit failed, " + resp.getMessage(), "scheduler plugin");
        }
        SchedulerCommonResponse schedulerAddResponse = new SchedulerCommonResponse();
        schedulerAddResponse.setClusterId(clusterId);
        schedulerAddResponse.setClusterName(cluster.getName());
        List<String> bctMessages = generateModifyRequestMessages(request);
        if (!CollectionUtils.isEmpty(bctMessages)){
            schedulerAddResponse.setMessage(String.join("，", bctMessages));
        }
        return schedulerAddResponse;
    }

    // 生成bct操作消息
    private List<String> generateModifyRequestMessages(SchedulerAddRequest request){
        if (request == null){
            return null;
        }
        List<String> modifyRequestMessages = new ArrayList<>();
        // 插入队列的acl用户审计信息
        if (request.getAclInfo() != null && !request.getAclInfo().isEmpty()) {
            for (SchedulerAddRequest.AclInfo info : request.getAclInfo()) {
                String str = String.format("在队列%s中，添加访问用户: %s", info.getQueueName(), String.join("，", info.getUserList()));
                modifyRequestMessages.add(str);
            }
        }
        // 插入队列用户资源限制信息
        if (request.getQueueConfigList() != null && !request.getQueueConfigList().isEmpty()){
            for(SchedulerAddRequest.QueueConfig config : request.getQueueConfigList()){
                for(SchedulerAddRequest.UserMaxRunLimit userMaxRunLimit : config.getUserMaxRunLimitList()){
                    String str = String.format("设置用户%s在队列%s的限制，maxMem: %s，maxCpus: %s，maxNodes: %s，maxJobs: %s",
                            userMaxRunLimit.getUserName(), config.getQueueName(), userMaxRunLimit.getMem(),
                            userMaxRunLimit.getCpus(), userMaxRunLimit.getNodes(), userMaxRunLimit.getMaxJobs());
                    modifyRequestMessages.add(str);
                }
            }
        }

        return modifyRequestMessages.isEmpty() ? null : modifyRequestMessages;
    }

    // 校验内存单位和用户是否重复
    private void checkMemAndUserName(SchedulerAddRequest request) {
        String regex = "\\d+[GgMm][Bb]";
        String regexNumber = "^[1-9]\\d*$";
        Set<String> set = new HashSet<>();
        Pattern pattern = Pattern.compile(regex);
        Pattern patternNumber = Pattern.compile(regexNumber);
        if (request.getQueueConfigList() != null && request.getQueueConfigList().size() > 0) {
            for (int i = 0; i < request.getQueueConfigList().size(); i++) {
                if (request.getQueueConfigList().get(i).getUserMaxRunLimitList() != null && request.getQueueConfigList().get(i).getUserMaxRunLimitList().size() > 0) {
                    for (int j = 0; j < request.getQueueConfigList().get(i).getUserMaxRunLimitList().size(); j++) {
                        if (set.contains(request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getUserName())) {
                            throw new CommonException.RequestInvalidException(
                                    "queue resource exist repeat user name");
                        }
                        if (request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getMem() != null &&
                                !("".equals(request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getMem()))) {
                            Matcher matcher = pattern.matcher(request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getMem());
                            if (!matcher.matches()) {
                                throw new CommonException.RequestInvalidException(
                                        "set user mem failed, illegal memory data, unit must be gb or mb");
                            }
                        }
                        if (request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getCpus() != null &&
                                !("".equals(request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getCpus()))) {
                            Matcher matcher = patternNumber.matcher(request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getCpus());
                            if (!matcher.matches()) {
                                throw new CommonException.RequestInvalidException(
                                        "set user cpus failed, illegal cpus data, must be an integer");
                            }
                        }
                        if (request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getMaxJobs() != null &&
                                !("".equals(request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getMaxJobs()))) {
                            Matcher matcher = patternNumber.matcher(request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getMaxJobs());
                            if (!matcher.matches()) {
                                throw new CommonException.RequestInvalidException(
                                        "set user MaxJobs failed, illegal MaxJobs data, must be an integer");
                            }
                        }
                        if (request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getNodes() != null &&
                                !("".equals(request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getNodes()))) {
                            Matcher matcher = patternNumber.matcher(request.getQueueConfigList().get(i).getUserMaxRunLimitList().get(j).getNodes());
                            if (!matcher.matches()) {
                                throw new CommonException.RequestInvalidException(
                                        "set user Nodes failed, illegal Nodes data, must be an integer");
                            }
                        }
                    }
                }
            }
        }
    }

    private void preCheck(Cluster cluster) {

        if (cluster == null || cluster.getSchedulePlugin() == 0) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
    }
}