package com.baidu.bce.logic.chpc.service;

import com.baidu.bce.logic.chpc.annotation.ValidateAuthentication;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.CommonValidateUtil;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.domainservice.ClusterService;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.BccExternalGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.request.instance.InstanceAddRequest;
import com.baidu.bce.logic.chpc.model.request.instance.InstanceOperationRequest;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceAddResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceOperationResponse;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Slf4j
@Service
public class ChpcInstanceServiceImpl implements ChpcInstanceService {


    @Resource
    private ClusterService clusterService;

    @Resource
    private QueueDAOGateway queueDAOGateway;

    @Resource
    private InstanceService instanceService;

    @Resource
    private TaskService taskService;

    @Resource
    private BackendGateway backendGateway;

    @Resource
    private BccExternalGateway bccExternalGateway;


    @Override
    @ValidateAuthentication
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InstanceAddResponse addInstanceToCluster(String clusterId, String groupId,
                                                    InstanceAddRequest request) {

        Cluster cluster = clusterService.findBy(clusterId);

        Queue queue = queueDAOGateway.getByQueueId(groupId);

        com.baidu.bce.internalsdk.cos.model.ClusterResponse cosCusterResponse =
                clusterService.createClusterResourceWithCos(
                        ServiceUtil.convertToCosRequest(request, cluster, queue));

        queue.setStatus(QueueStatus.WAITING_TO_JOIN.nameLowerCase());
        queueDAOGateway.update(queue);

        // 资源创建任务
        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.GROUP_RESOURCE_SAVE_TASK,
                cluster.getClusterId(), queue.getQueueId(),
                TaskSourceType.ADD_INSTANCE_TO_CLUSTER.getTaskSourceType(),
                taskId);


        InstanceAddResponse instanceAddResponse = new InstanceAddResponse();
        instanceAddResponse.setClusterId(clusterId);
        instanceAddResponse.setQueueName(groupId);
        instanceAddResponse.setStatus(ClusterStatus.CREATING.nameLowerCase());
        instanceAddResponse.setTaskId(taskId);
        return instanceAddResponse;
    }


    @Override
    @ValidateAuthentication
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InstanceOperationResponse removeInstanceFromCluster(String clusterId, String groupId, String instanceId,
                                                               InstanceOperationRequest request) {

        Queue queue = queueDAOGateway.getByQueueId(groupId);

        Instance instance = instanceService.findBy(instanceId);

        BackendCommonResponse backendResponse = backendGateway.removeNode(
                clusterId, queue.getName(),
                instance.getHostName(), instance.getInstanceId(),
                false,
                request.getForce());

        CommonValidateUtil.validBackendCommonResponse(backendResponse);

        Map<String, Object> extraMap = new HashMap<>(2);
        extraMap.put(ChpcConstant.BACKEND_TASK_ID, backendResponse.getTaskId());
        extraMap.put(ChpcConstant.BACKEND_INSTANCE_ID, instance.getInstanceId());
        extraMap.put(ChpcConstant.BACKEND_INSTANCE_UUID, instance.getInstanceUuid());
        extraMap.put(ChpcConstant.BACKEND_COS_STACK_ID, instance.getCosStackId());

        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.BACKEND_ASYNC_TASK,
                clusterId, groupId,
                TaskSourceType.REMOVE_INSTANCE_FROM_CLUSTER.getTaskSourceType(),
                taskId,
                extraMap);


        return this.convertToResponse(clusterId, groupId, instanceId,
                ClusterStatus.DELETING.nameLowerCase(), taskId);
    }

    @Override
    @ValidateAuthentication
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InstanceOperationResponse stopInstanceFromCluster(String clusterId, String groupId, String instanceId,
                                                             InstanceOperationRequest request) {

        bccExternalGateway.stopServer(instanceId,
                request.getForce(), request.getStopWithNoCharge());

        instanceService.updateStatusAndExecutionId(instanceId,
                null, ClusterStatus.STOPPED.nameLowerCase());

        return this.convertToResponse(clusterId, groupId, instanceId,
                ClusterStatus.STOPPED.nameLowerCase(), null);
    }


    @Override
    @ValidateAuthentication
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InstanceOperationResponse startInstanceFromCluster(String clusterId, String groupId,
                                                              String instanceId) {

        bccExternalGateway.startServer(instanceId);

        instanceService.updateStatusAndExecutionId(instanceId,
                null, InstanceStatus.STARTING.nameLowerCase());

        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.INSTANCE_STATUS_SYNC_TASK,
                clusterId, groupId,
                taskId,
                Collections.singletonMap(ChpcConstant.INSTANCE_ID, instanceId));

        return this.convertToResponse(clusterId, groupId, instanceId,
                InstanceStatus.STARTING.nameLowerCase(), taskId);
    }

    @Override
    @ValidateAuthentication
    public InstanceOperationResponse rebootInstanceFromCluster(String clusterId, String groupId, String instanceId,
                                                               InstanceOperationRequest request) {

        bccExternalGateway.rebootServer(instanceId, request.getForce());

        instanceService.updateStatusAndExecutionId(instanceId,
                null, ClusterStatus.REBOOTING.nameLowerCase());

        String taskId = UUID.randomUUID().toString();
        taskService.insert(TaskType.INSTANCE_STATUS_SYNC_TASK,
                clusterId, groupId,
                taskId,
                Collections.singletonMap(ChpcConstant.INSTANCE_ID, instanceId));

        return this.convertToResponse(clusterId, groupId, instanceId,
                ClusterStatus.REBOOTING.nameLowerCase(), taskId);
    }


    private InstanceOperationResponse convertToResponse(String clusterId, String groupId,
                                                        String instanceId, String status,
                                                        String taskId) {

        InstanceOperationResponse response = new InstanceOperationResponse();
        response.setInstanceList(Collections.singletonList(instanceId));
        response.setClusterId(clusterId);
        response.setQueueName(groupId);
        response.setStatus(status);
        response.setTaskId(taskId);

        return response;
    }


}
