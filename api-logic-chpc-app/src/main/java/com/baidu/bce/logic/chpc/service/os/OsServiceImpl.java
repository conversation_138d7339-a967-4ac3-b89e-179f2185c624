package com.baidu.bce.logic.chpc.service.os;

import com.baidu.bce.logic.chpc.gateway.OsDAOGateway;
import com.baidu.bce.logic.chpc.model.Os;
import com.baidu.bce.logic.chpc.model.response.os.OsListResponse;
import com.baidu.bce.logic.chpc.os.OsListRequest;
import com.baidu.bce.logic.chpc.service.IOsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class OsServiceImpl implements IOsService {

    @Resource
    private OsDAOGateway osDAOGateway;

    @Override
    public OsListResponse osList(OsListRequest request) {
        List<Os> osList = osDAOGateway.osList(request.getSchedulerType(), request.getSchedulerVersion());
        OsListResponse osListResponse = new OsListResponse();
        osListResponse.setOsList(osList);
        return osListResponse;
    }
}
