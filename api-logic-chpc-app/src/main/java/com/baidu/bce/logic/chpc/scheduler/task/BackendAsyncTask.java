package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.cos.DeleteStackResponse;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendTaskDetail;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendTaskResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendTaskStatus;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: lilu24
 * @Date: 2023-01-05
 */
@Slf4j
@Component("backend_async_task")
public class BackendAsyncTask extends AbstractSchedulerTask {

    @Resource
    TagsGateway tagsGateway;

    @Resource
    TagsDAOGateway tagsDAOGateway;

    /**
     * {@inheritDoc}
     * 执行任务，根据任务类型和集群类型调用不同的后台接口获取任务状态，并返回任务结果。
     * 如果任务类型为Hybrid或PBS，则直接返回成功，不进行后台请求。
     * 如果任务类型为Hybrid或PBS，则返回正在处理中。
     * 如果api-server异常，继续轮询。
     * 如果api-server返回非法查询，返回失败。
     * 如果api-server处理中，继续轮询。
     * 如果api-server任务成功或失败，停止轮询，返回结果。
     *
     * @param task    任务对象
     * @param cluster 集群对象
     * @param queue   队列对象
     * @return 包含任务结果信息的Map，包含任务状态（SUCCEED、FAILED）和其他额外信息
     * @throws Exception 可能抛出的异常
     */
    @Override
    public Map<String, Object> executeTask(final Task task, final Cluster cluster, final Queue queue) {
        Map<String, String> extraMap = JacksonUtil.decodeToMap(task.getExtra(), String.class);
        String backendTaskId = Optional.of(extraMap.get(ChpcConstant.BACKEND_TASK_ID))
                .orElse("");

        BackendTaskResponse taskResponse = backendGateway.getTaskDetail(
                cluster.getClusterId(), backendTaskId);

        // api-server异常，继续轮询
        // todo 交互失败是否返回null
        if (taskResponse == null || ChpcConstant.HTTP_STATUS_500.equals(taskResponse.getCode())) {
            return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                    TaskStatus.PROCESSING.getValue());
        }

        // 非法查询，返回失败
        if (!ChpcConstant.HTTP_STATUS_200.equals(taskResponse.getCode())) {
            return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                    TaskStatus.FAILED.getValue());
        }

        BackendTaskDetail taskDetail = taskResponse.getTaskDetail();

        // api-server处理中，继续轮询
        if (BackendTaskStatus.INIT.getValue().equalsIgnoreCase(taskDetail.getTaskStatus()) ||
                BackendTaskStatus.DOING.getValue().equalsIgnoreCase(taskDetail.getTaskStatus())) {
            return Collections.singletonMap(ChpcConstant.TASK_STATUS,
                    TaskStatus.PROCESSING.getValue());
        }

        // 任务成功或者失败，停止轮询，返回结果
        TaskStatus taskStatus = BackendTaskStatus.SUCCESS.getValue().equalsIgnoreCase(taskDetail.getTaskStatus())
                ? TaskStatus.SUCCEED
                : TaskStatus.FAILED;

        Map<String, Object> resultMap = new HashMap<>(extraMap);
        resultMap.put(ChpcConstant.TASK_STATUS, taskStatus.getValue());
        return resultMap;
    }

    @Override
    public void afterExecuteTask(final Task task, final Map<String, Object> paraMap) {

        /*
         * String executeStatus = String.valueOf(paraMap.get(ChpcConstant.TASK_STATUS));
         * if (!TaskStatus.SUCCEED.getValue().equalsIgnoreCase(executeStatus)) {
         * return;
         * }
         *
         */

        TaskSourceType taskSourceType = TaskSourceType.fromType(task.getSource());
        switch (Objects.requireNonNull(taskSourceType)) {
            case REMOVE_GROUP_FROM_CLUSTER:
                removeGroupCallBack(task, paraMap);
                break;
            case REMOVE_INSTANCE_FROM_CLUSTER:
                removeInstanceCallBack(task, paraMap);
                break;
            case STOP_CLUSTER:
                stopClusterCallBack(task, paraMap);
                break;
        }
    }

    private void removeGroupCallBack(final Task task, final Map<String, Object> paraMap) {

        String executeStatus = String.valueOf(paraMap.get(ChpcConstant.TASK_STATUS));

        // 删除队列失败
        if (TaskStatus.FAILED.getValue().equalsIgnoreCase(executeStatus)) {
            // 报警
            log.error("[BACKEND_ASYNC_TASK] delete queue fail, task id:{}, cluster id:{}, queue id:{}",
                    task.getTaskUuid(), task.getClusterId(), task.getQueueId());
        }

        // 只支持删除空队列，无需再起异步任务释放节点
        /*
         * taskService.insert(TaskType.GROUP_DELETED_TASK,
         * task.getClusterId(),
         * task.getGroupId(),
         * TaskSourceType.REMOVE_GROUP_FROM_CLUSTER.getTaskSourceType(),
         * task.getTaskUuid(),
         * Collections.singletonMap(ChpcConstant.SOURCE_TASK_ID, task.getTaskId()));
         *
         */
    }

    private void removeInstanceCallBack(final Task task, final Map<String, Object> paraMap) {

        if (paraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)) {
            // 无论成功或失败，都将自动伸缩状态从缩容状态置回 Normal，如果处于其他状态，则不修改
            AutoScaling autoScaling = new AutoScaling();
            autoScaling.setAsId(String.valueOf(paraMap.get(ChpcConstant.AUTO_SCALING_FLAG)));
            autoScaling.setStatus(AutoScalingStatus.NORMAL);
            autoScalingDAOGateway.updateByStatus(autoScaling, AutoScalingStatus.SHRINKING);
        }

        String executeStatus = String.valueOf(paraMap.get(ChpcConstant.TASK_STATUS));

        // 删除节点失败，直接返回
        if (TaskStatus.FAILED.getValue().equalsIgnoreCase(executeStatus)) {
            return;
        }

        String instanceId = String.valueOf(paraMap.get(ChpcConstant.BACKEND_INSTANCE_ID));
        String instanceUuId = String.valueOf(paraMap.get(ChpcConstant.BACKEND_INSTANCE_UUID));
        String hostname = String.valueOf(paraMap.get(ChpcConstant.BACKEND_HOSTNAME));
        String cosStackId = String.valueOf(paraMap.get(ChpcConstant.BACKEND_COS_STACK_ID));

        // 在数据库中不存在，说明不是公有云节点，无需删除
        if (!instanceService.isExisted(task.getClusterId(), null, null, hostname, null)) {
            log.debug("[BACKEND_ASYNC_TASK] instance is not cloud node, task id:{}, node id:{}", task.getTaskId(),
                    instanceId);
            // 删除节点标签
            tagsDAOGateway.deleteByClusterIdAndName(task.getClusterId(), "localNode", hostname);
            return;
        }
        Instance instance = instanceService.findBy(instanceId);
        // 检查是否需要释放资源
        if (!paraMap.containsKey(ChpcConstant.RELEASE_RESOURCE_FLAG) ||
                !ChargeType.Postpaid.name().equalsIgnoreCase(instance.getChargeType())) {
            log.debug("[BACKEND_ASYNC_TASK: {}] instance {} do not need release", task.getTaskId(), instanceId);
            instanceService.delete(instanceId);
            return;
        }

        bccExternalGateway.deleteServer(instanceId);

        instanceService.delete(instanceId);

        // 云上节点解绑，标签操作失败，不能影响删除节点流程
        try {
            CreateAndAssignTagRequest request = new CreateAndAssignTagRequest();
            List<AssignResource> resources = new ArrayList<>();
            AssignResource resource = new AssignResource();
            resource.setServiceType("BCC");
            resource.setResourceId(instanceId);
            resource.setResourceUuid(instanceUuId);
            resource.setRegion(regionConfiguration.getCurrentRegion());
            resource.setAssociationType("floating");
            resources.add(resource);
            request.setResources(resources);
            tagsGateway.createAndAssignTag(request);
        } catch (Exception e) {
            log.debug(
                    "delete instanceTags fail,instanceId:{} ,err:{}", instanceId, e.getMessage());
        }

        if (StringUtils.isEmpty(cosStackId)) {
            return;
        }

        // 删除可能存在的占位符实例信息
        List<String> instanceIds = ServiceUtil.castToList(paraMap.get(ChpcConstant.INSTANCE_IDS));
        for (String id : instanceIds) {
            if (instanceId.startsWith("i-cloud-")) {
                instanceService.delete(id);
            }
        }

        // 后端删除bcc实例,如果是cos栈中最后一个实例，删除COS资源栈
        List<Instance> instances = instanceService.findByCosStackId(task.getClusterId(),
                null, cosStackId);

        if (CollectionUtils.isEmpty(instances)) {

            try {
                DeleteStackResponse deleteStackResponse = cosGateway.deleteStack(cosStackId);
            } catch (Exception e) {
                log.error("cos deleteStack error", e);
            }
        }

    }

    private void stopClusterCallBack(final Task task, final Map<String, Object> paraMap) {

        String clusterDeletedType = String.valueOf(paraMap.get(ChpcConstant.CLUSTER_DELETED_TASK));
        if (StringUtils.isNotEmpty(clusterDeletedType) &&
                ChpcConstant.TYPE_EXISTED.equalsIgnoreCase(clusterDeletedType)) {
            taskService.insert(
                    TaskType.CLUSTER_DELETED_TASK,
                    task.getClusterId(), "",
                    TaskSourceType.STOP_CLUSTER.getTaskSourceType(),
                    task.getTaskUuid());

            return;
        }

        clusterService.updateClusterStatus(task.getClusterId(),
                ClusterStatus.STOPPED.nameLowerCase());
    }

}
