package com.baidu.bce.logic.chpc.scheduler;

import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.logic.chpc.billing.LegacyChargeDataRequest;
import com.baidu.bce.logic.chpc.billing.gateway.ResourceChargeGateway;
import com.baidu.bce.logic.chpc.common.SaasResourceType;
import com.baidu.bce.logic.chpc.config.LockConfig;
import com.baidu.bce.logic.chpc.config.SchedulerThreadLocalHolder;
import com.baidu.bce.logic.chpc.gateway.OrderDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SaasResourceDAOGateway;
import com.baidu.bce.logic.chpc.model.Order;
import com.baidu.bce.logic.chpc.model.SaasResource;
import com.baidu.bce.logic.chpc.pfs.GetFilesetRequest;
import com.baidu.bce.logic.chpc.pfs.GetFilesetResponse;
import com.baidu.bce.logic.chpc.pfs.gateway.PfsGateway;
import com.baidu.bce.logic.chpc.service.IBillingService;
import com.baidu.bce.logic.chpc.service.util.SchedulerUserConfig;
import org.apache.commons.lang3.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.time.Instant;
import java.util.List;

@Component
@Slf4j
public class PfsBillingTask {

    @Resource
    LockConfig lockConfig;

    @Resource
    PfsGateway pfsGateway;


    @Resource
    SaasResourceDAOGateway saasResourceDAOGateway;

    @Resource
    OrderDAOGateway orderDAOGateway;

    @Resource
    ResourceChargeGateway resourceChargeGateway;

    @Resource
    IBillingService iBillingService;

    @Resource
    SchedulerUserConfig schedulerUserConfig;

    @Resource
    private ThreadPoolTaskExecutor pfsBillingTaskThreadPool;

    // 定时请求pfs,获取存储用量并上报billing

    // 五分钟触发一次
    @Scheduled(cron = "0 0/5 * * * ? ")
    public void execPfsBillingTask() {

        String lockKey = generatePfsBillingLock();
        if (!lockConfig.tryLock(lockKey)) {
            log.debug("pfs tryLock failed: {}", lockKey);
            return;
        }
        log.debug("success to get lock: {}", lockKey);

        List<Order> orderList = orderDAOGateway.findAll(SaasResourceType.CHPC.getName());
        // 暂时用一个节点上报
        for (Order order : orderList) {
            this.executePfsBillingTask(order);

        }
        // 移除用户信息
        SchedulerThreadLocalHolder.clear();
        schedulerUserConfig.removeUserToken();
        // 解锁
        lockConfig.unLock(lockKey);

    }

    private void executePfsBillingTask(Order order) {
        pfsBillingTaskThreadPool.execute(() -> {
            // 订单未创建完成
            if (!order.getStatus().equals(OrderStatus.CREATED.toString())) {
                return;
            }
            log.debug("chargeData orderId: {}", order.getOrderId());

            // 保存用户AccountId
            SchedulerThreadLocalHolder.setAccountId(order.getAccountId());
            schedulerUserConfig.setUserToken(order.getAccountId());

            SaasResource saasResource = saasResourceDAOGateway.findByAccountId(order.getAccountId(), SaasResourceType.SKYFORM.getName());
            // 没有pfs，不计费
            if (null == saasResource || StringUtils.isBlank(saasResource.getPfsId()) || StringUtils.isBlank(saasResource.getFilesetId())) {
                return;
            }
            GetFilesetRequest getFilesetRequest = new GetFilesetRequest();
            getFilesetRequest.setInstanceId(saasResource.getPfsId());
            getFilesetRequest.setFilesetId(saasResource.getFilesetId());

            GetFilesetResponse getFilesetResponse = pfsGateway.getFilesetInfo(getFilesetRequest);

            if (null == getFilesetResponse) {
                return;
            }

            // 计算五分钟用量，单位GB/分钟

            int blockUsageKb = getFilesetResponse.getBlockUsage();

            
            // 没有存储用量
            if (blockUsageKb == 0) {
                return;
            }

            double blockUsageGb = (double) blockUsageKb / (1024 * 1024) * 5;


            // 统计用量，上报计费
            LegacyChargeDataRequest legacyChargeDataRequest = iBillingService.makeChargeData(order.getAccountId(), "CHPC", "pfs",
                    saasResource.getResourceUuid(), String.valueOf(blockUsageGb), "Count", Instant.now().getEpochSecond());
            log.debug("pfs chargeDataRequest is {}", legacyChargeDataRequest.toString());
            resourceChargeGateway.charge(legacyChargeDataRequest);

        });
    }

    private String generatePfsBillingLock() {

        return String.format("PfsBilling-lock");
    }
}
