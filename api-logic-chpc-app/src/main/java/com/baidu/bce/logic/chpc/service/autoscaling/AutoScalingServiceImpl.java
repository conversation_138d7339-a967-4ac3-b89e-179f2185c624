package com.baidu.bce.logic.chpc.service.autoscaling;

import com.baidu.bce.logic.chpc.autoscaling.AutoScalingConst;
import com.baidu.bce.logic.chpc.autoscaling.gateway.IAutoScalingDAOGateway;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.autoscaling.model.DeleteAutoScalingRequest;
import com.baidu.bce.logic.chpc.autoscaling.model.ExecuteAutoScalingInfo;
import com.baidu.bce.logic.chpc.autoscaling.model.SetAutoScalingRequest;
import com.baidu.bce.logic.chpc.autoscaling.model.UpdateAutoScalingTagsRequest;
import com.baidu.bce.logic.chpc.chpc.gateway.ChpcGateway;
import com.baidu.bce.logic.chpc.chpc.model.PushAutoScalingBctEventRequest;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.CommonValidateUtil;
import com.baidu.bce.logic.chpc.common.GlobalUuidUtil;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.ZoneUtil;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.common.validator.NetworkValidator;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AutoScalingProbeRequest;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.AutoScalingProbeResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.iam.IamGateway;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceDeleteRequest;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.response.autoscale.SetAutoScalingResponse;
import com.baidu.bce.logic.chpc.model.response.autoscale.UpdateAutoScalingResponse;
import com.baidu.bce.logic.chpc.service.IAutoScalingService;
import com.baidu.bce.logic.chpc.service.IInstanceService;
import com.baidu.bce.logic.chpc.service.util.DiskTypeUtil;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

@Slf4j
@Service
public class AutoScalingServiceImpl implements IAutoScalingService {

    // @Resource
    // AutoScalingServiceImpl autoScalingServiceImpl;

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    QueueDAOGateway queueDAOGateway;

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Resource
    IAutoScalingDAOGateway autoScalingDAOGateway;

    @Resource
    GlobalUuidUtil globalUuidUtil;

    @Resource
    BackendGateway backendGateway;

    @Resource
    IInstanceService iInstanceService;

    @Resource
    TagsGateway tagsGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    SubnetGateway subnetGateway;

    @Resource
    ChpcGateway chpcGateway;

    @Resource
    ZoneUtil zoneUtil;

    @Resource
    NetworkValidator networkValidator;

    @Resource
    IamGateway iamGateway;

    /**
     * {@inheritDoc}
     * 设置自动扩缩容，并更新到集群管理器中。如果不存在则创建，否则更新。
     * 对于系统盘和数据盘的磁盘类型进行转换，以适应集群管理器的需求。
     * 如果请求中包含了数据盘列表，则对每个数据盘的存储类型进行转换。
     *
     * @param request SetAutoScalingRequest，包含自动扩缩容相关信息，包括队列名称、集群ID等。
     * @throws Exception 如果出现任何异常，将会回滚事务。
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public SetAutoScalingResponse setAutoScaling(SetAutoScalingRequest request) {
        this.validSetAutoScalerRequest(request);

        log.debug("[AutoScalingServiceImpl] start to setAutoScaling, request: {}", request);

        Queue queue = queueDAOGateway.getByQueueName(request.getQueueName(), request.getClusterId());

        AutoScaling autoScaling = autoScalingDAOGateway.getByQueueId(queue.getQueueId(), getAccountId());
        if (autoScaling == null) {
            log.debug("[AutoScalingServiceImpl] setAutoScaling, create new auto scaling");
            // 更新队列的hasAutoScale字段
            queue.setHasAutoScale(true);
            queueDAOGateway.update(queue);
            // 扩缩容对磁盘类型做一层做转换
            request.setSystemDiskType(DiskTypeUtil.getDiskType(request.getSystemDiskType()));
            if (CollectionUtils.isNotEmpty(request.getDataDiskList())) {
                request
                        .getDataDiskList()
                        .forEach(disk -> disk.setStorageType(DiskTypeUtil.getDiskType(disk.getStorageType())));
            }
            autoScaling = generateAutoScaling(request, queue);
        } else {
            log.debug("[AutoScalingServiceImpl] setAutoScaling, update auto scaling");
            // 对存量做转换
            request.setSystemDiskType(DiskTypeUtil.getDiskType(request.getSystemDiskType()));
            if (CollectionUtils.isNotEmpty(request.getDataDiskList())) {
                request
                        .getDataDiskList()
                        .forEach(disk -> disk.setStorageType(DiskTypeUtil.getDiskType(disk.getStorageType())));
            }
            autoScaling = updateAutoScaling(request, autoScaling, queue);
        }

        updateAutoScalerToClusterManager(autoScaling);

        log.debug("[AutoScalingServiceImpl] finish to setAutoScaling, autoScaling: {}", autoScaling);
        Cluster cluster = clusterDAOGateway.findByClusterId(request.getClusterId(), getAccountId());
        // 添加bct message
        String bctMessage = "";
        if (request.getEnableAutoGrow()) {
            bctMessage += "开启自动扩容，";
        } else {
            bctMessage += "关闭自动扩容，";
        }
        if (request.getEnableAutoShrink()) {
            bctMessage += "开启自动缩容";
        } else {
            bctMessage += "关闭自动缩容";
        }
        if (request.getTags() != null) {
            // 标签操作失败，不能影响主流程
            try {
                List<com.baidu.bce.logical.tag.sdk.model.Tag> tags = new ArrayList<>();
                if (request.getTags().size() > 0) {
                    tags = BeanCopyUtil.copyListProperties(request.getTags(), com.baidu.bce.logical.tag.sdk.model.Tag::new);
                    tagsGateway.createTags(tags);
                }
                CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
                List<AssignResource> resources = new ArrayList<>();
                AssignResource resource = new AssignResource();
                resource.setServiceType("CHPC");
                resource.setResourceId(autoScaling.getAsId());
                resource.setResourceUuid(autoScaling.getAsId());
                resource.setRegion(regionConfiguration.getCurrentRegion());
                resource.setAssociationType("floating");
                resource.setTags(tags);
                resources.add(resource);
                createAndAssignTagRequest.setResources(resources);
                tagsGateway.createAndAssignTag(createAndAssignTagRequest);
            } catch (Exception e) {
                log.debug(
                        "setAutoScaling fail, clusterId:{} ,asId:{} ,err:{}", autoScaling.getClusterId(), autoScaling.getAsId(), e.getMessage());
            }
        }
        SetAutoScalingResponse response = new SetAutoScalingResponse(autoScaling.getAsId(), bctMessage);
        response.setClusterName(cluster != null ? cluster.getName() : null);
        return response;
    }

    @Override
    public UpdateAutoScalingResponse updateAutoScaling(UpdateAutoScalingTagsRequest request) {
        AutoScaling autoScaling = autoScalingDAOGateway.getByAsId(request.getAsId());
        if (autoScaling == null) {
            log.warn("updateAutoScaling failed. no auto scaler found by asId: {}", request.getAsId());
            throw new CommonExceptions.ResourceNotExistException(
                    String.format("updateAutoScaling %s failed: ", request.getAsId()));
        }
        // 标签操作失败，不能影响主流程
        if (request.getTags() != null) {
            try {
                List<com.baidu.bce.logical.tag.sdk.model.Tag> tags = BeanCopyUtil.copyListProperties(request.getTags(), com.baidu.bce.logical.tag.sdk.model.Tag::new);
                CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
                List<AssignResource> resources = new ArrayList<>();
                AssignResource resource = new AssignResource();
                resource.setServiceType("CHPC");
                resource.setResourceId(request.getAsId());
                resource.setResourceUuid(request.getAsId());
                resource.setTags(tags);
                resource.setRegion(regionConfiguration.getCurrentRegion());
                resource.setAssociationType("floating");
                resources.add(resource);
                createAndAssignTagRequest.setResources(resources);
                tagsGateway.createAndAssignTag(createAndAssignTagRequest);
            } catch (Exception e) {
                log.debug(
                        "updateAutoScaling fail, asId:{} ,err:{}", request.getAsId(), e.getMessage());
            }
        }
        UpdateAutoScalingResponse response = new UpdateAutoScalingResponse();
        response.setAsId(request.getAsId());
        return response;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteAutoScaling(String clusterId, String queueName) {
        Queue queue = queueDAOGateway.getByQueueName(queueName, clusterId);
        if (queue == null) {
            log.warn("delete autoscaler failed. no queue found by name: {}", queueName);
            throw new CommonExceptions.ResourceNotExistException(
                    String.format("no queue found by name: %s; ", queueName));
        }

        AutoScaling autoScaling =
                autoScalingDAOGateway.getByClusterIdAndQueueId(clusterId, queue.getQueueId(), getAccountId());
        if (autoScaling == null) {
            log.warn(
                    "delete autoscaler failed. no autoscaler found by clusterId: {}, queueId:{}",
                    clusterId,
                    queue.getQueueId());
            throw new CommonExceptions.ResourceNotExistException(
                    String.format("Delete autoscaler %s failed: ", queueName));
        }

        // 更新队列的is/hasAutoScale字段
        queue.setIsAutoScale(false);
        queue.setHasAutoScale(false);
        queueDAOGateway.update(queue);

        // 更新cluster-api的自动伸缩信息
        autoScaling.setEnableAutoGrow(false);
        autoScaling.setEnableAutoShrink(false);
        autoScaling.setQueueName(queueName);
        updateAutoScalerToClusterManager(autoScaling);

        // 删除自动扩缩容配置
        autoScalingDAOGateway.delete(autoScaling.getAsId());
    }

    @Override
    public List<AutoScaling> getAutoScalingTags(List<AutoScaling> autoScalers) {
        FullTagListRequest fullTagListRequest = new FullTagListRequest();
        List<String> regions = new ArrayList<>();
        regions.add(regionConfiguration.getCurrentRegion());
        fullTagListRequest.setRegions(regions);
        List<String> resourceIds = new ArrayList<>();
        List<String> resourceUuids = new ArrayList<>();
        for (AutoScaling autoScaler : autoScalers) {
            resourceIds.add(autoScaler.getAsId());
            resourceUuids.add(autoScaler.getAsId());
        }
        fullTagListRequest.setResourceIds(resourceIds);
        fullTagListRequest.setResourceUuids(resourceUuids);
        List<String> serviceTypes = new ArrayList<>();
        serviceTypes.add("CHPC");
        fullTagListRequest.setServiceTypes(serviceTypes);
        TagAssociationFulls tagAssociationFulls = null;
        try {
            tagAssociationFulls = tagsGateway.listTags(fullTagListRequest);
        } catch (Exception e) {
            log.error("[instances {}]: get tags failed, err {}", resourceIds, e.getMessage());
        }
        for (AutoScaling autoScaler : autoScalers) {
            if (tagAssociationFulls != null) {
                List<Tag> tags = new ArrayList<>();
                for (TagAssociationFull tagAssociationFull : tagAssociationFulls.getTagAssociationFulls()) {
                    if (tagAssociationFull.getResourceId().equals(autoScaler.getAsId())) {
                        Tag tag = new Tag();
                        tag.setTagKey(tagAssociationFull.getTagKey());
                        tag.setTagValue(tagAssociationFull.getTagValue());
                        tags.add(tag);
                    }
                }
                autoScaler.setTags(tags);
            }
        }
        return autoScalers;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void deleteAutoScaling(DeleteAutoScalingRequest request) {

        for (String asId : request.getAsIds().
                stream().
                map(DeleteAutoScalingRequest.AsClusterId::getAsId).
                collect(Collectors.toList())) {
            AutoScaling autoScaling = autoScalingDAOGateway.getByAsId(asId);
            if (autoScaling == null) {
                log.warn("delete autoscaler failed. no auto scaler found by asId: {}", asId);
                throw new CommonExceptions.ResourceNotExistException(
                        String.format("Delete autoscaler %s failed: ", asId));
            }

            // 删除自动扩缩容配置
            autoScalingDAOGateway.delete(asId);

            // 更新队列的is/hasAutoScale字段
            Queue queue = queueDAOGateway.getByQueueId(autoScaling.getQueueId());
            if (queue == null) {
                log.warn("delete autoscaler failed. no queue found by id: {}", autoScaling.getQueueId());
                // 如果队列不存在说明是脏数据,删除后继续处理下一条
                continue;
                // throw new CommonExceptions.ResourceNotExistException(String.format("no queue found by id: %s; ", autoScaling.getQueueId()));
            } else {
                queue.setIsAutoScale(false);
                queue.setHasAutoScale(false);
                queueDAOGateway.update(queue);
            }

            // 判断集群是否存在(脏数据--集群已经删除，但是伸缩策略没删除)
            // 如果不存在说明是脏数据，删除后直接处理下一条记录
            Cluster cluster = clusterDAOGateway.findByClusterId(autoScaling.getClusterId(), LogicUserService.getAccountId());
            if (cluster == null) {
                log.warn("autoscaler asId: {} is dirty data, cluster has been deleted", autoScaling.getAsId());
                continue;
            }
            // 更新cluster-api的自动伸缩信息
            try {
                autoScaling.setEnableAutoGrow(false);
                autoScaling.setEnableAutoShrink(false);
                updateAutoScalerToClusterManager(autoScaling);
            } catch (Exception e) {
                log.error(
                        "delete auto scaling failed. update auto scaler to cluster manager failed. asId: {}", asId, e);
                throw new CommonExceptions.RequestInvalidException(
                        "delete auto scaling failed. update auto scaler to cluster manager failed.");
            }
            try {
                List<com.baidu.bce.logical.tag.sdk.model.Tag> tags = new ArrayList<>();
                CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
                List<AssignResource> resources = new ArrayList<>();
                AssignResource resource = new AssignResource();
                resource.setServiceType("CHPC");
                resource.setResourceId(autoScaling.getAsId());
                resource.setResourceUuid(autoScaling.getAsId());
                resource.setRegion(regionConfiguration.getCurrentRegion());
                resource.setAssociationType("floating");
                resource.setTags(tags);
                resources.add(resource);
                createAndAssignTagRequest.setResources(resources);
                tagsGateway.createAndAssignTag(createAndAssignTagRequest);
            } catch (Exception e) {
                log.debug(
                        "setAutoScaling fail, clusterId:{} ,asId:{} ,err:{}", autoScaling.getClusterId(), autoScaling.getAsId(), e.getMessage());
            }
        }
    }

    /**
     * 将自动扩缩容配置同步到集群管理节点
     *
     * @param autoScaling
     */
    @Override
    public void updateAutoScalerToClusterManager(AutoScaling autoScaling) {
        Set<String> excludeInstanceIdSet = new HashSet<>();

        // 预付费不参与自动缩容
        List<Instance> prepaidInstances =
                instanceDAOGateway.findAllByClusterIdAndQueueIdAndChargeType(
                        autoScaling.getClusterId(), autoScaling.getQueueId(), ChargeType.Prepaid);
        if (CollectionUtils.isNotEmpty(prepaidInstances)) {
            excludeInstanceIdSet.addAll(
                    prepaidInstances.stream().map(Instance::getInstanceId).collect(Collectors.toList()));
        }

        // 自动扩缩容规则人工指定的节点不参与自动扩缩容
        if (CollectionUtils.isNotEmpty(autoScaling.getExcludeNodes())) {
            excludeInstanceIdSet.addAll(autoScaling.getExcludeNodes());
        }

        // 获取最新的hostname
        List<Instance> excludeInstances =
                instanceDAOGateway.findAllByClusterIdAndInstanceIds(
                        autoScaling.getClusterId(), excludeInstanceIdSet.stream().collect(Collectors.toList()));
        List<Instance> instances = iInstanceService.encapsulateBccInfo(excludeInstances, true);
        List<String> excludeHostnames = instances.stream().map(Instance::getHostName).collect(Collectors.toList());

        // 同步到集群管理节点
        BackendCommonResponse backendCommonResponse =
                backendGateway.updateQueueAutoScaling(autoScaling, excludeHostnames);
        CommonValidateUtil.validBackendCommonResponse(backendCommonResponse);
    }

    /**
     * @Description 校验 SetAutoScalingRequest 请求参数的合法性
     * @Param request SetAutoScalingRequest 类型，包含需要校验的请求参数
     * @Return void 无返回值
     * @Throws CommonException.ResourceNotExistException 当集群或队列不存在时抛出该异常
     * @Throws CommonExceptions.RequestInvalidException 当 maxNodesInQueue 小于
     * minNodesInQueue 时抛出该异常
     * @Throws CommonExceptions.RequestInvalidException 当 spec、imageId 为空时抛出该异常
     */
    private void validSetAutoScalerRequest(SetAutoScalingRequest request) {
        Cluster cluster = clusterDAOGateway.findByClusterId(request.getClusterId(), getAccountId());
        if (cluster == null) {
            throw new CommonException.ResourceNotExistException("clusterId does not exist.");
        }
        Queue queue = queueDAOGateway.getByQueueName(request.getQueueName(), request.getClusterId());
        if (queue == null) {
            throw new CommonException.ResourceNotExistException("queueName does not exist.");
        }
        if (cluster.getSchedulerType().contains("pbs") && queue.getName().equals("route")) {
            throw new CommonExceptions.RequestInvalidException("pbs 集群 route 队列不支持设置自动扩缩容");
        }
        request.setSecurityGroupId(cluster.getSecurityGroupId());
        // 队列中最大节点数和最小节点数合法性校验
        if (request.getMaxNodesInQueue() != null && request.getMinNodesInQueue() != null) {
            if (request.getMaxNodesInQueue() < request.getMinNodesInQueue()) {
                log.warn(
                        "maxNodesInQueue should bigger than minNodesInQueue, clusterId: {}, queue: {}",
                        request.getClusterId(),
                        request.getQueueName());
                throw new CommonExceptions.RequestInvalidException(
                        "maxNodesInQueue should bigger than minNodesInQueue.");
            }
        }

        // 请求存在更新子网或可用区，就校验子网和可用区
        networkValidator.validateSubnetAndVpc(request.getClusterId(), request.getSubnetId(), request.getZoneName());

        // 校验节点是否存在
        if (CollectionUtils.isNotEmpty(request.getExcludeNodes())) {
            List<Instance> existNodes = instanceDAOGateway.findBy(request.getExcludeNodes());
            if (request.getExcludeNodes().size() != existNodes.size()) {
                log.error("exclude nodes {}, existNodes {}", request.getExcludeNodes(), existNodes);
                throw new CommonException.ResourceNotExistException("some instances do not exist.");
            }
        }

        if (StringUtils.isEmpty(request.getSpec())) {
            log.warn("spec is empty, clusterId: {}, queue: {}", request.getClusterId(), request.getQueueName());
            throw new CommonExceptions.RequestInvalidException("spec must not be empty.");
        }

        if (StringUtils.isEmpty(request.getImageId())) {
            log.warn("imageId is empty, clusterId: {}, queue: {}", request.getClusterId(), request.getQueueName());
            throw new CommonExceptions.RequestInvalidException("imageId must not be empty.");
        }
    }

    /**
     * {@inheritDoc}
     * 根据集群ID获取自动扩缩容配置列表，包含队列名称。
     * 该方法会先查询所有与集群关联的队列，然后根据队列ID查询对应的自动扩缩容配置，最后将队列名称添加到自动扩缩容配置中返回。
     *
     * @param clusterId 集群ID，不能为空
     * @return 自动扩缩容配置列表，每个配置包含队列名称，不能为空
     * @throws IllegalArgumentException 如果集群ID为空，则抛出此异常
     */
    @Override
    public List<AutoScaling> getAutoScaling(String clusterId) {
        List<AutoScaling> autoScalingList = new ArrayList<>();
        if (StringUtils.isEmpty(clusterId)) {
            autoScalingList = autoScalingDAOGateway.getAll(getAccountId(), "");
        } else {
            List<Queue> queues = queueDAOGateway.listByClusterId(clusterId);
            List<String> queueIds = queues.stream().map(Queue::getQueueId).collect(Collectors.toList());
            autoScalingList = autoScalingDAOGateway.getByQueueIds(queueIds, null);

            Map<String, String> queueId2QueueName =
                    queues
                            .stream()
                            .map(queue -> new AbstractMap.SimpleEntry<>(queue.getQueueId(), queue.getName()))
                            .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
            autoScalingList.forEach(
                    autoScaling -> autoScaling.setQueueName(queueId2QueueName.get(autoScaling.getQueueId())));
        }

        for (int i = 0; i < autoScalingList.size(); i++) {
            if (CollectionUtils.isNotEmpty(autoScalingList.get(i).getDataDiskList())) {
                autoScalingList
                        .get(i)
                        .getDataDiskList()
                        .forEach(disk -> disk.setStorageType(DiskTypeUtil.diskType2Console(disk.getStorageType())));
            }
            autoScalingList
                    .get(i)
                    .setSystemDiskType(DiskTypeUtil.diskType2Console(autoScalingList.get(i).getSystemDiskType()));
        }
        log.debug("cluster autoScaling size {}", autoScalingList.size());
        return autoScalingList;
    }

    @Override
    public List<AutoScaling> getAutoScalingByOrder(String orderByType) {
        List<AutoScaling> autoScalingList = autoScalingDAOGateway.getAll(getAccountId(), orderByType);
        for (int i = 0; i < autoScalingList.size(); i++) {
            if (CollectionUtils.isNotEmpty(autoScalingList.get(i).getDataDiskList())) {
                autoScalingList
                        .get(i)
                        .getDataDiskList()
                        .forEach(disk -> disk.setStorageType(DiskTypeUtil.diskType2Console(disk.getStorageType())));
            }
            autoScalingList
                    .get(i)
                    .setSystemDiskType(DiskTypeUtil.diskType2Console(autoScalingList.get(i).getSystemDiskType()));
        }
        log.debug("cluster autoScaling size {}", autoScalingList.size());
        return autoScalingList;
    }

    @Override
    public AutoScaling getAutoScaling(String clusterId, String queueName) {
        Queue queue = queueDAOGateway.getByQueueName(queueName, clusterId);
        if (queue == null) {
            if ("route".equalsIgnoreCase(queueName)) {
                return null;
            }
            log.warn("get auto scaling failed. no queue found by name: {}", queueName);
            throw new CommonExceptions.ResourceNotExistException(
                    String.format("no queue found by name: %s; ", queueName));
        }

        AutoScaling autoScaling =
                autoScalingDAOGateway.getByClusterIdAndQueueId(clusterId, queue.getQueueId(), getAccountId());

        if (autoScaling == null) {
            log.warn(
                    "get auto scaling failed. no auto scaler found by clusterId: {}, queueId:{}",
                    clusterId,
                    queue.getQueueId());
            throw new CommonExceptions.ResourceNotExistException(
                    String.format("no auto scaler found by queue: %s; ", queueName));
        }
        if (CollectionUtils.isNotEmpty(autoScaling.getDataDiskList())) {
            autoScaling
                    .getDataDiskList()
                    .forEach(disk -> disk.setStorageType(DiskTypeUtil.diskType2Console(disk.getStorageType())));
        }
        autoScaling.setSystemDiskType(DiskTypeUtil.diskType2Console(autoScaling.getSystemDiskType()));

        FullTagListRequest fullTagListRequest = new FullTagListRequest();
        List<String> regions = new ArrayList<>();
        regions.add(regionConfiguration.getCurrentRegion());
        fullTagListRequest.setRegions(regions);
        List<String> resourceIds = new ArrayList<>();
        List<String> resourceUuids = new ArrayList<>();
        resourceIds.add(autoScaling.getAsId());
        resourceUuids.add(autoScaling.getAsId());
        fullTagListRequest.setResourceIds(resourceIds);
        fullTagListRequest.setResourceUuids(resourceUuids);
        List<String> serviceTypes = new ArrayList<>();
        serviceTypes.add("CHPC");
        fullTagListRequest.setServiceTypes(serviceTypes);
        TagAssociationFulls tagAssociationFulls = null;
        try {
            tagAssociationFulls = tagsGateway.listTags(fullTagListRequest);
        } catch (Exception e) {
            log.error("[instances {}]: get tags failed, err {}", resourceIds, e.getMessage());
        }
        if (tagAssociationFulls != null) {
            List<Tag> tags = new ArrayList<>();
            for (TagAssociationFull tagAssociationFull : tagAssociationFulls.getTagAssociationFulls()) {
                if (tagAssociationFull.getResourceId().equals(autoScaling.getAsId())) {
                    Tag tag = new Tag();
                    tag.setTagKey(tagAssociationFull.getTagKey());
                    tag.setTagValue(tagAssociationFull.getTagValue());
                    tags.add(tag);
                }
                autoScaling.setTags(tags);
            }
        }
        return autoScaling;
    }

    public void executeAutoScalingTask(AutoScaling autoScaling, Queue queue) {
        AutoScalingProbeRequest request = new AutoScalingProbeRequest();
        request.setQueueName(queue.getName());
        AutoScalingProbeResponse response = backendGateway.autoScalingProbe(autoScaling.getClusterId(), request);
        log.debug("[autoscale] AutoScalingProbe response: {}", response);

        if (response.getInfo() == null || response.getInfo().getScaleCount() == null) {
            return;
        }
        // scaleCount > 0 执行扩容
        if (response.getInfo().getScaleCount() > 0) {
            executeAutoGrow(autoScaling, queue, response.getInfo().getScaleCount());
        } else if (response.getInfo().getScaleCount() < 0) {
            List<ExecuteAutoScalingInfo.Node> nodes = new ArrayList<>();
            for (AutoScalingProbeResponse.Node node : response.getInfo().getShrinkNodes()) {
                ExecuteAutoScalingInfo.Node n = new ExecuteAutoScalingInfo.Node();
                n.setHostIP(node.getHostIP());
                n.setStatus(node.getStatus());
                n.setNodeID(node.getNodeID());
                n.setNodeName(node.getNodeName());
                n.setSpec(node.getSpec());
                n.setQueue(node.getQueue());
                nodes.add(n);
            }
            executeAutoShrink(
                    queue.getClusterId(), autoScaling, nodes, -response.getInfo().getScaleCount(), response.getInfo().getShrinkCycle());
        }
    }

    public void executeHeartbeatAutoScalingTask(AutoScaling autoScaling, Queue queue, ExecuteAutoScalingInfo info) {
        if (info.getScaleCount() == null) {
            return;
        }
        // scaleCount > 0 执行扩容
        if (info.getScaleCount() > 0) {
            executeAutoGrow(autoScaling, queue, info.getScaleCount());
        } else if (info.getScaleCount() < 0) {
            executeAutoShrink(
                    queue.getClusterId(), autoScaling, info.getNodes(), -info.getScaleCount(), info.getShrinkCycle());
        }
    }

    /**
     * {@summary}
     * 执行自动增长操作。
     * 如果未开启自动增长功能或者当前节点数量已达到最大值、CPU核心数已达到最大值或者队列中的任务数量已达到最大值，则不进行增长操作。
     * 然后根据集群配置和自动扩容配置创建新实例请求，并将其添加到集群中。
     * 如果添加失败，则更新自动扩容状态为正常，并记录日志。
     *
     * @param autoScaling      自动扩容信息
     * @param queue            队列信息
     * @param autoScalingCount 需要增长的实例数量
     */
    public void executeAutoGrow(AutoScaling autoScaling, Queue queue, int autoScalingCount) {

        if (!autoScaling.getEnableAutoGrow()) {
            return;
        }

        Long nodeInQueueCount =
                instanceDAOGateway.countComputeNode(queue.getQueueId(), InstanceNodeType.COMPUTE.name());

        List<Instance> nodesInCluster =
                instanceDAOGateway.findBy(autoScaling.getClusterId(), null, InstanceNodeType.COMPUTE.name(), null, null);
        int cpusInClusterCount = 0;
        for (Instance node : nodesInCluster) {
            cpusInClusterCount += getCpusFromSpec(node.getSpec());
        }

        log.debug("autoScaling info {}", autoScaling);
        log.debug(
                "auto scaling {}, grow nodeInQueueCount {} maxNodesInQueue {} autoScalingCount {}",
                autoScaling.getAsId(),
                nodeInQueueCount,
                autoScaling.getMaxNodesInQueue(),
                autoScalingCount);
        log.debug("{} - {} > {}", autoScaling.getMaxNodesInQueue(), nodeInQueueCount, autoScalingCount);

        Cluster cluster = clusterDAOGateway.findByClusterId(autoScaling.getClusterId(), null);
        InstanceAddRequest autoGrowRequest = new InstanceAddRequest();
        if (cluster.getMaxNodes() > 0) {
            if (nodesInCluster.size() + autoScalingCount > cluster.getMaxNodes()) {
                return;
            }
        }
        if (cluster.getMaxCpus() > 0) {
            int autoScalingCpus = autoScalingCount * getCpusFromSpec(autoScaling.getSpec());
            if (cpusInClusterCount + autoScalingCpus > cluster.getMaxCpus()) {
                return;
            }
        }
        if (autoScaling.getMaxNodesInQueue() > 0) {
            if (nodeInQueueCount + autoScalingCount > autoScaling.getMaxNodesInQueue()) {
                return;
            }
        }

        autoGrowRequest.setCount(autoScalingCount);
        autoGrowRequest.setSpec(autoScaling.getSpec());
        autoGrowRequest.setSystemDiskSize(autoScaling.getSystemDiskSize());
        autoGrowRequest.setSystemDiskType(DiskTypeUtil.getDiskType(autoScaling.getSystemDiskType()));
        if (CollectionUtils.isNotEmpty(autoScaling.getDataDiskList())) {
            autoGrowRequest.setDataDiskList(
                    autoScaling
                            .getDataDiskList()
                            .stream()
                            .peek(
                                    disk -> {
                                        disk.setStorageType(DiskTypeUtil.getDiskType(disk.getStorageType()));
                                    })
                            .collect(Collectors.toList()));
        }

        autoGrowRequest.setZoneName(autoScaling.getZoneName());
        autoGrowRequest.setSubnetId(autoScaling.getSubnetId());
        autoGrowRequest.setSecurityGroupId(autoScaling.getSecurityGroupId());
        autoGrowRequest.setImageId(autoScaling.getImageId());

        if (StringUtils.isNotEmpty(cluster.getPassword())) {
            autoGrowRequest.setPassword(cluster.getPassword());
        }
        autoGrowRequest.setCudaVersion(autoScaling.getCudaVersion());
        autoGrowRequest.setGpuDriverVersion(autoScaling.getGpuDriverVersion());
        autoGrowRequest.setCudnnVersion(autoScaling.getCudnnVersion());

        // ebc ht&numa配置;
        autoGrowRequest.setCpuThreadConfig(autoScaling.getCpuThreadConfig());
        autoGrowRequest.setNumaConfig(autoScaling.getNumaConfig());

        AutoScaling updateAutoScaling = new AutoScaling();
        updateAutoScaling.setAsId(autoScaling.getAsId());
        updateAutoScaling.setStatus(AutoScalingStatus.GROWING);
        boolean success = autoScalingDAOGateway.updateByStatus(updateAutoScaling, AutoScalingStatus.NORMAL);
        if (!success) {
            log.warn("update auto scaling status to GROWING failed, asId: {}", autoScaling.getAsId());
            return;
        }

        log.debug("{} ready to execute auto growing", autoScaling.getAsId());
        log.debug("auto growing request detail: {}", autoGrowRequest);
        try {
            iInstanceService.addInstanceToCluster(
                    queue.getClusterId(), queue.getName(), autoGrowRequest, updateAutoScaling);
        } catch (Exception e) {
            log.error(
                    "hit an error when automatic expand instance to cluster， clusterId:{}, asId:{}, error:",
                    autoScaling.getAccountId(),
                    autoScaling.getAsId(),
                    e);
            updateAutoScaling = new AutoScaling();
            updateAutoScaling.setAsId(autoScaling.getAsId());
            updateAutoScaling.setStatus(AutoScalingStatus.NORMAL);
            autoScalingDAOGateway.update(updateAutoScaling);
            // 推送BCT事件
            PushAutoScalingBctEventRequest request = new PushAutoScalingBctEventRequest();
            request.setUserId(LogicUserService.getAccountId());
            request.setClusterId(cluster.getClusterId());
            request.setClusterName(cluster.getName());
            request.setQueueName(queue.getName());
            request.setMessage(String.format("当前队列存在作业排队，需要 %d 节点，因此自动扩容，新建节点",
                    autoGrowRequest.getCount()));
            request.setSuccess(false);
            request.setAsId(autoScaling.getAsId());
            chpcGateway.pushAutoExpandBctEvent(request);
        }
    }

    private int getCpusFromSpec(String spec) {
        String[] specParts = spec.split("\\.");
        if (specParts.length < 3) {
            throw new CommonExceptions.RequestInvalidException("invalid spec " + spec);
        }
        String[] resParts = specParts[2].split("m");
        if (resParts.length != 2) {
            throw new CommonExceptions.RequestInvalidException("invalid spec " + spec);
        }
        return Integer.parseInt(resParts[0].substring(1));
    }

    /**
     * @return void
     * @Description 执行自动缩容操作
     * <p>
     * 如果自动伸缩策略开启了自动缩容功能，则会根据当前对列中的节点数和自动伸缩要求的队列最小数量进行判断。
     * 如果对列中的节点数 - 自动伸缩要求队列最小数量 > 后付费的节点数量，则会删除后付费的节点；
     * 否则，如果对列中的节点数 > 自动伸缩要求队列最小数量，则会删除大于【minNodesInQueue】数量的节点。
     * @Param clusterId String 集群ID
     * @Param autoScaling AutoScaling 自动伸缩策略对象
     * @Param nodes List<ExecuteAutoScalingInfo.Node> 节点列表，包含节点名称和实例ID
     * @Throws RuntimeException 更新自动伸缩状态失败时抛出异常
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void executeAutoShrink(
            String clusterId, AutoScaling autoScaling, List<ExecuteAutoScalingInfo.Node> nodes, int shrinkNums, int shrinkCycle) {

        if (!autoScaling.getEnableAutoShrink()) {
            return;
        }

        List<String> hostNames =
                nodes.stream().map(ExecuteAutoScalingInfo.Node::getNodeName).collect(Collectors.toList());

        // 查询所有节点信息
        List<Instance> instanceList = instanceDAOGateway.findAllByClusterIdAndHostNames(clusterId, hostNames);

        // 排除自动伸缩策略中设置不参与缩容的节点
        if (CollectionUtils.isNotEmpty(autoScaling.getExcludeNodes())) {
            instanceList =
                    instanceList
                            .stream()
                            .filter(instance -> !autoScaling.getExcludeNodes().contains(instance.getInstanceId()))
                            .filter(instance -> instance.getNodeType().equals(InstanceNodeType.COMPUTE.getType()))
                            .collect(Collectors.toList());
        }

        // 过滤后付费节点
        List<Instance> readyToDeleteInstances =
                instanceList
                        .stream()
                        .filter(instance -> ChargeType.Postpaid.name().equalsIgnoreCase(instance.getChargeType()))
                        .collect(Collectors.toList());

        // 待删除节点为空，直接返回
        if (CollectionUtils.isEmpty(readyToDeleteInstances)) {
            return;
        }

        Long nodeInQueueCount =
                instanceDAOGateway.countComputeNode(autoScaling.getQueueId(), InstanceNodeType.COMPUTE.name());

        InstanceDeleteRequest instanceDeleteRequest = new InstanceDeleteRequest();
        instanceDeleteRequest.setForce(false);

        log.debug("[autoshrink] autoScaling info {}", autoScaling);
        log.debug(
                "[autoshrink] asId {} auto scaling shrink nodeInQueueCount {} minNodesInQueue {}",
                autoScaling.getAsId(),
                nodeInQueueCount,
                autoScaling.getMinNodesInQueue());
        log.debug("[autoshrink] readyToDeleteInstances {}", readyToDeleteInstances);
        log.debug(
                "[autoshrink] {} - {} > {}",
                nodeInQueueCount,
                autoScaling.getMinNodesInQueue(),
                readyToDeleteInstances.size());

        List<String> readyToDeleteHostnames =
                readyToDeleteInstances.stream().map(Instance::getHostName).collect(Collectors.toList());
        // 队列中的节点数 - 自动伸缩要求队列最小数量 > 后付费的节点数量
        shrinkNums = Math.min((int) (nodeInQueueCount - autoScaling.getMinNodesInQueue()), shrinkNums);
        if (shrinkNums <= 0) {
            // 队列中的节点数量 小于等于 最小节点数量，不需要缩容
            log.debug(
                    "[autoshrink] nodeInQueueCount is smaller than minNodesInQueue, nodeInQueueCount:{}, minNodesInQueue:{}",
                    nodeInQueueCount,
                    autoScaling.getMinNodesInQueue());
            return;
        }
        if (readyToDeleteInstances.size() == 0) {
            log.debug("[autoshrink] no nodes ready to delete");
            return;
        }

        log.debug("[autoshrink] {} ready to execute auto shrinking", autoScaling.getAsId());
        log.debug("[autoshrink] auto shrinking request detail: {}", instanceDeleteRequest);

        AutoScaling updateAutoScaling = new AutoScaling();
        updateAutoScaling.setAsId(autoScaling.getAsId());
        updateAutoScaling.setStatus(AutoScalingStatus.SHRINKING);
        boolean success = autoScalingDAOGateway.updateByStatus(updateAutoScaling, AutoScalingStatus.NORMAL);
        if (!success) {
            log.warn("[autoshrink] update auto scaling status to SHRINKING failed, asId: {}", autoScaling.getAsId());
            throw new RuntimeException("[autoshrink] update auto scaling status to SHRINKING failed");
        }
        try {
            iInstanceService.shrinkInstanceFromCluster(autoScaling, readyToDeleteHostnames, shrinkNums, shrinkCycle);
        } catch (Exception e) {
            log.error(
                    "[autoshrink] execute auto shrinking failed, asId: {}, error: {}",
                    autoScaling.getAsId(),
                    e.getMessage());
            updateAutoScaling = new AutoScaling();
            updateAutoScaling.setAsId(autoScaling.getAsId());
            updateAutoScaling.setStatus(AutoScalingStatus.NORMAL);
            autoScalingDAOGateway.update(updateAutoScaling);
            // 推送BCT事件
            PushAutoScalingBctEventRequest request = new PushAutoScalingBctEventRequest();
            request.setUserId(LogicUserService.getAccountId());
            Cluster cluster = clusterDAOGateway.findByClusterId(autoScaling.getClusterId(), null);
            request.setClusterId(clusterId);
            request.setClusterName(cluster.getName());
            Queue queue = queueDAOGateway.getByQueueId(autoScaling.getQueueId());
            request.setQueueName(queue.getName());
            request.setMessage(String.format(
                    "当前队列 %d 节点连续空闲 %d 秒，因此自动缩容，释放节点",
                    readyToDeleteHostnames.size(),
                    shrinkCycle));
            request.setSuccess(false);
            request.setAsId(autoScaling.getAsId());
            chpcGateway.pushAutoShrinkBctEvent(request);
        }
    }

    @Override
    public AutoScaling generateAutoScaling(SetAutoScalingRequest request, Queue queue) {
        AutoScaling autoScaling = new AutoScaling();
        autoScaling.setAsId(globalUuidUtil.genAutoScalingShortId());

        autoScaling.setAsName(request.getAsName());
        autoScaling.setClusterId(request.getClusterId());
        autoScaling.setClusterName(request.getClusterName());
        autoScaling.setQueueId(queue.getQueueId());
        autoScaling.setQueueName(request.getQueueName());
        autoScaling.setZoneName(request.getZoneName());
        autoScaling.setExcludeNodes(request.getExcludeNodes());
        autoScaling.setSecurityGroupId(request.getSecurityGroupId());

        if (StringUtils.isEmpty(request.getSpec())) {
            throw new CommonExceptions.RequestInvalidException("spec must not be empty.");
        }

        if (StringUtils.isEmpty(request.getImageId())) {
            throw new CommonExceptions.RequestInvalidException("imageId must not be empty.");
        }
        autoScaling.setImageId(request.getImageId());
        autoScaling.setSpec(request.getSpec());
        if (request.getMaxNodesInQueue() != null) {
            autoScaling.setMaxNodesInQueue(request.getMaxNodesInQueue());
        } else {
            autoScaling.setMaxNodesInQueue(AutoScalingConst.DEFAULT_MAX_NODES_IN_QUEUE);
        }
        if (request.getMinNodesInQueue() != null) {
            autoScaling.setMinNodesInQueue(request.getMinNodesInQueue());
        } else {
            autoScaling.setMinNodesInQueue(AutoScalingConst.DEFAULT_MIN_NODES_IN_QUEUE);
        }
        if (request.getMaxScalePerCycle() != null) {
            autoScaling.setMaxScalePerCycle(request.getMaxScalePerCycle());
        } else {
            autoScaling.setMaxScalePerCycle(AutoScalingConst.DEFAULT_MAX_SCALING_PER_CYCLE_IN_QUEUE);
        }
        if (request.getEnableAutoGrow() != null) {
            autoScaling.setEnableAutoGrow(request.getEnableAutoGrow());
        } else {
            autoScaling.setEnableAutoGrow(AutoScalingConst.DEFAULT_ENABLE_AUTO_GROW);
        }
        if (request.getEnableAutoShrink() != null) {
            autoScaling.setEnableAutoShrink(request.getEnableAutoShrink());
        } else {
            autoScaling.setEnableAutoShrink(AutoScalingConst.DEFAULT_ENABLE_AUTO_SHRINK);
        }

        if (request.getSystemDiskSize() != null) {
            autoScaling.setSystemDiskSize(request.getSystemDiskSize());
        } else {
            autoScaling.setSystemDiskSize(AutoScalingConst.DEFAULT_SYSTEM_DISK_SIZE);
        }

        if (request.getSystemDiskType() != null) {
            autoScaling.setSystemDiskType(DiskTypeUtil.getDiskType(request.getSystemDiskType()));
        } else {
            autoScaling.setSystemDiskType(AutoScalingConst.DEFAULT_SYSTEM_DISK_TYPE);
        }

        if (StringUtils.isNotEmpty(request.getSubnetId())) {
            autoScaling.setSubnetId(request.getSubnetId());
        }

        autoScaling.setDataDiskList(request.getDataDiskList());

        if (StringUtils.isNotEmpty(request.getCudaVersion())) {
            autoScaling.setCudaVersion(request.getCudaVersion());
        }
        if (StringUtils.isNotEmpty(request.getGpuDriverVersion())) {
            autoScaling.setGpuDriverVersion(request.getGpuDriverVersion());
        }
        if (StringUtils.isNotEmpty(request.getCudnnVersion())) {
            autoScaling.setCudnnVersion(request.getCudnnVersion());
        }

        // ebc ht&numa 配置
        if (StringUtils.isNotEmpty(request.getCpuThreadConfig())){
            autoScaling.setCpuThreadConfig(request.getCpuThreadConfig());
        }
        if (StringUtils.isNotEmpty(request.getNumaConfig())){
            autoScaling.setNumaConfig(request.getNumaConfig());
        }

        autoScaling.setCreatedTime(LocalDateTime.now());
        autoScaling.setAccountId(getAccountId());
        autoScaling.setStatus(AutoScalingStatus.NORMAL);

        // 当开启了自动扩容或自动缩容，更新队列开启自动伸缩
        if (autoScaling.getEnableAutoGrow() || autoScaling.getEnableAutoShrink()) {
            Queue readyForUpdate = new Queue();
            readyForUpdate.setQueueId(queue.getQueueId());
            readyForUpdate.setIsAutoScale(true);
            readyForUpdate.setHasAutoScale(true);
            queueDAOGateway.update(readyForUpdate);
        }
        // 后端同步更新队列扩容时的spec，用来计算需要扩容多少个节点
        if (StringUtils.isEmpty(request.getSpec())) {
            throw new CommonExceptions.RequestInvalidException("spec is empty.");
        }

        autoScalingDAOGateway.insert(autoScaling);
        return autoScaling;
    }

    /**
     * @Description: 根据请求参数更新AutoScaling对象，并返回更新后的AutoScaling对象。
     * 同时更新原来的AutoScaling对象和对应的队列。
     * @Param request SetAutoScalingRequest - 包含更新AutoScaling对象所需的参数，包括最大节点数、最小节点数等。
     * @Param oldAutoScaling AutoScaling - 原始的AutoScaling对象，将会被更新。
     * @Param queue Queue - 对应的队列，将会被更新。
     * @Return AutoScaling - 返回更新后的AutoScaling对象。
     * @Throws CommonExceptions.RequestInvalidException - 当maxNodesInQueue小于minNodesInQueue时抛出该异常。
     */
    public AutoScaling updateAutoScaling(SetAutoScalingRequest request, AutoScaling oldAutoScaling, Queue queue) {
        AutoScaling autoScaling = new AutoScaling();
        autoScaling.setAsId(oldAutoScaling.getAsId());
        autoScaling.setClusterId(oldAutoScaling.getClusterId());
        autoScaling.setClusterName(oldAutoScaling.getClusterName());
        autoScaling.setAccountId(oldAutoScaling.getAccountId());
        autoScaling.setCreatedTime(oldAutoScaling.getCreatedTime());
        autoScaling.setDeleted(false);
        autoScaling.setQueueName(queue.getName());

        if (StringUtils.isNotEmpty(request.getAsName())) {
            autoScaling.setAsName(request.getAsName());
        } else {
            autoScaling.setAsName(oldAutoScaling.getAsName());
        }

        if (request.getMaxNodesInQueue() != null) {
            autoScaling.setMaxNodesInQueue(request.getMaxNodesInQueue());
            oldAutoScaling.setMaxNodesInQueue(request.getMaxNodesInQueue());
        }
        if (request.getMinNodesInQueue() != null) {
            autoScaling.setMinNodesInQueue(request.getMinNodesInQueue());
            oldAutoScaling.setMinNodesInQueue(request.getMinNodesInQueue());
        }
        if (request.getMaxScalePerCycle() != null) {
            autoScaling.setMaxScalePerCycle(request.getMaxScalePerCycle());
        } else {
            autoScaling.setMaxScalePerCycle(AutoScalingConst.DEFAULT_MAX_SCALING_PER_CYCLE_IN_QUEUE);
        }
        if (request.getEnableAutoGrow() != null) {
            autoScaling.setEnableAutoGrow(request.getEnableAutoGrow());
            oldAutoScaling.setEnableAutoGrow(request.getEnableAutoGrow());
        }
        if (request.getEnableAutoShrink() != null) {
            autoScaling.setEnableAutoShrink(request.getEnableAutoShrink());
            oldAutoScaling.setEnableAutoShrink(request.getEnableAutoShrink());
        }
        // 如果excludeNodes数组不为null，但是是个空数组，则设置为空，表示自动伸缩规则中不需要排除节点
        if (request.getExcludeNodes() != null) {
            autoScaling.setExcludeNodes(request.getExcludeNodes());
        }
        if (request.getSpec() != null) {
            autoScaling.setSpec(request.getSpec());
        }

        if (StringUtils.isNotEmpty(request.getGpuDriverVersion())) {
            autoScaling.setGpuDriverVersion(request.getGpuDriverVersion());
        } else {
            // 用空字符串表示不设置gpu相关软件
            // 不设置为null的原因是，如果设置为null，执行自动伸缩时update操作会覆盖掉软件信息
            autoScaling.setGpuDriverVersion("");
        }

        if (StringUtils.isNotEmpty(request.getCudaVersion())) {
            autoScaling.setCudaVersion(request.getCudaVersion());
        } else {
            autoScaling.setCudaVersion("");
        }

        if (StringUtils.isNotEmpty(request.getCudnnVersion())) {
            autoScaling.setCudnnVersion(request.getCudnnVersion());
        } else {
            autoScaling.setCudnnVersion("");
        }

        // ebc ht&numa 配置
        if (StringUtils.isNotEmpty(request.getCpuThreadConfig())){
            autoScaling.setCpuThreadConfig(request.getCpuThreadConfig());
        }
        if (StringUtils.isNotEmpty(request.getNumaConfig())){
            autoScaling.setNumaConfig(request.getNumaConfig());
        }

        // 系统盘
        if (request.getSystemDiskSize() != null) {
            autoScaling.setSystemDiskSize(request.getSystemDiskSize());
        }
        if (request.getSystemDiskType() != null) {
            autoScaling.setSystemDiskType(DiskTypeUtil.getDiskType(request.getSystemDiskType()));
        }

        // 数据盘
        autoScaling.setDataDiskList(request.getDataDiskList());

        if (autoScaling.getMaxNodesInQueue() != null && autoScaling.getMinNodesInQueue() != null) {
            if (autoScaling.getMaxNodesInQueue() < autoScaling.getMinNodesInQueue()) {
                throw new CommonExceptions.RequestInvalidException(
                        "maxNodesInQueue should bigger than minNodesInQueue.");
            }
        }

        // 更新镜像ID
        if (StringUtils.isNotEmpty(request.getImageId())) {
            autoScaling.setImageId(request.getImageId());
        }

        if (StringUtils.isNotEmpty(request.getSubnetId())) {
            autoScaling.setSubnetId(request.getSubnetId());
        }

        if (StringUtils.isNotEmpty(request.getZoneName())) {
            autoScaling.setZoneName(request.getZoneName());
        }

        Queue readyForUpdateQueue = new Queue();
        readyForUpdateQueue.setQueueId(queue.getQueueId());
        // 更新队列是否开启自动伸缩
        readyForUpdateQueue.setIsAutoScale(oldAutoScaling.getEnableAutoGrow() || oldAutoScaling.getEnableAutoShrink());
        queueDAOGateway.update(readyForUpdateQueue);

        log.debug(
                "xxxxx update auto scaling, getAccountId: {} oldAccountId: {}",
                getAccountId(),
                oldAutoScaling.getAccountId());
        log.debug("xxxxx update auto scaling, accountId: {} autoScaling: {}", autoScaling.getAccountId(), autoScaling);

        autoScalingDAOGateway.update(autoScaling);
        return autoScaling;
    }
}
