package com.baidu.bce.logic.chpc.service.queue;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.internalsdk.iam.model.BatchVerifyResults;
import com.baidu.bce.internalsdk.iam.model.VerifyResult;
import com.baidu.bce.logic.chpc.annotation.AspectResultHolder;
import com.baidu.bce.logic.chpc.annotation.PermissionVerify;
import com.baidu.bce.logic.chpc.autoscaling.gateway.IAutoScalingDAOGateway;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.cluster.domainservice.ClusterServiceV2;
import com.baidu.bce.logic.chpc.cluster.model.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.QueueInfo;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.GlobalUuidUtil;
import com.baidu.bce.logic.chpc.common.InstanceAttribution;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.ComputeResource;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.request.queue.QueueAddRequest;
import com.baidu.bce.logic.chpc.model.response.queue.QueueAddResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueDetailResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueRemoveResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueTagsResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueUpdateResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueVO;
import com.baidu.bce.logic.chpc.permission.PermissionConstant;
import com.baidu.bce.logic.chpc.permission.PermissionResourceID;
import com.baidu.bce.logic.chpc.service.IInstanceService;
import com.baidu.bce.logic.chpc.service.IQueueService;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.chpc.tag.DbTag;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.baidu.bce.logic.chpc.tag.UpdateTagsRequest;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Slf4j
@Service
public class QueueServiceImpl implements IQueueService {

    @Resource
    private ClusterDAOGateway clusterDAOGateway;

    @Resource
    private InstanceService instanceService;

    @Resource
    private TaskService taskService;

    @Resource
    private TagsDAOGateway tagsDAOGateway;


    @Resource
    private QueueDAOGateway queueDAOGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    IInstanceService instanceServiceImpl;

    @Resource
    TagsGateway tagsGateway;

    @Resource
    GlobalUuidUtil globalUuidUtil;

    @Resource
    private BackendGateway backendGateway;

    @Resource
    private IAutoScalingDAOGateway autoScalingDAOGateway;

    // todo 切面拆分为两个，权限校验（是否是当前用户资源）、参数检查（字段合法性，是否为空）
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public QueueAddResponse addQueue(String clusterId, QueueAddRequest request) {

        String queueName = request.getQueueName();

        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        // 只有active状态的集群才可以增加队列
        if (!ClusterStatus.ACTIVE.nameLowerCase().equalsIgnoreCase(cluster.getStatus())) {
            throw new CommonExceptions.RequestInvalidException("cluster " + clusterId + " is in " +
                    cluster.getStatus() + " status, cannot add queue, only active status allowed. ");
        }

        ClusterServiceV2.isValidQueueName(queueName, cluster.getSchedulerType());

        QueueAddResponse queueAddResp = new QueueAddResponse();

        if (ClusterType.CLOUD.nameLowerCase().equals(cluster.getClusterType())) {
            // 检查队列是否已经存在
            // todo 判空逻辑
            if (queueDAOGateway.getByName(clusterId, queueName) != null) {
                throw new CommonExceptions.RequestInvalidException("queue already exists");
            }

            // 构造队列元信息
            Queue queue = this.generateGroup(clusterId, request);

            // todo 后续操作挪入han job
            // action1. 队列元信息写入DB，通过unique key确保不会重名
            queueDAOGateway.insert(queue);

            // action2. 请求HPC集群增加队列
            // this.createBackendQueueForCluster(group);
            if (cluster.getSchedulePlugin() == 0) {
                BackendCommonResponse backendResponse = backendGateway.addQueue(
                        queue.getClusterId(), this.generateQueue(queue));
                if (!ChpcConstant.HTTP_STATUS_200.equals(backendResponse.getCode())) {
                    throw new CommonExceptions.RequestInvalidException(backendResponse.getMessage());
                }
            } else {
                BackendActionProxyResponse resp;
                try {
                    resp = backendGateway.actionProxy(clusterId, "queue_add", "-q " + queueName);
                } catch (WebClientResponseException e) {
                    if (e.getResponseBodyAsString().contains("already exists")) {
                        throw new CommonExceptions.RequestInvalidException(
                                "添加队列失败，该命名队列已存在");
                    } else {
                        throw new CommonExceptions.RequestInvalidException(e.getResponseBodyAsString());
                    }
                } catch (Exception e) {
                    if (e.getMessage().contains("already exists")) {
                        throw new CommonExceptions.RequestInvalidException(
                                "添加队列失败，该命名队列已存在");
                    } else {
                        throw new CommonExceptions.RequestInvalidException(e.getMessage());
                    }
                }
                if (resp.getCode().equals(ChpcConstant.HTTP_STATUS_200)) {
                    queueAddResp.setQueueName(queueName);
                }

            }

            // action3. 更新队列状态
            queueDAOGateway.updateStatus(queue.getQueueId(), QueueStatus.ACTIVE.nameLowerCase());
        } else {
            BackendActionProxyResponse resp;
            try {
                resp = backendGateway.actionProxy(clusterId, "queue_add", "-q " + queueName);
            } catch (WebClientResponseException e) {
                if (e.getResponseBodyAsString().contains("already exists")) {
                    throw new CommonExceptions.RequestInvalidException(
                            "添加队列失败，该命名队列已存在");
                } else {
                    throw new CommonExceptions.RequestInvalidException(e.getResponseBodyAsString());
                }
            } catch (Exception e) {
                if (e.getMessage().contains("already exists")) {
                    throw new CommonExceptions.RequestInvalidException(
                            "添加队列失败，该命名队列已存在");
                } else {
                    throw new CommonExceptions.RequestInvalidException(e.getMessage());
                }
            }
            if (resp.getCode().equals(ChpcConstant.HTTP_STATUS_200)) {
                queueAddResp.setQueueName(queueName);
            }
        }

        if (request.getTags() != null) {
            // 队列标签是chpc维护，这里在db新增标签
            for (Tag tag : request.getTags()) {
                try {
                    tagsDAOGateway.insert(LogicUserService.getAccountId(), clusterId, "queue", queueName,
                            tag.getTagKey(), tag.getTagValue());
                } catch (Exception e) {
                    throw new BceException(e.getMessage());
                }
            }
        }

        queueAddResp.setClusterName(cluster.getName());
        // bct 操作详情
        String bctMsg = String.format("集群%s（ID: %s）中添加队列%s", cluster.getName(), cluster.getClusterId(), queueName);
        queueAddResp.setMessage(bctMsg);
        return queueAddResp;
    }

    /*
     * private void createBackendQueueForCluster(Group group) {
     *
     * // 查询集群已有队列，根据队列名判断是否已经存在
     * ListQueuesResponse queuesResponse =
     * backendGateway.listQueue(group.getClusterId());
     * CommonValidateUtil.validBackendCommonResponse(queuesResponse);
     *
     * boolean isExisted = queuesResponse.getQueues()
     * .stream()
     * .anyMatch(queue -> queue.getQueueName().equalsIgnoreCase(group.getName()));
     * if (isExisted) {
     * return;
     * }
     *
     * // 创建队列
     * BackendCommonResponse backendResponse = backendGateway.addQueue(
     * group.getClusterId(), this.generateQueue(group));
     * CommonValidateUtil.validBackendCommonResponse(backendResponse);
     * }
     *
     */

    private com.baidu.bce.logic.chpc.gateway.external.model.bcakend.Queue generateQueue(Queue group) {

        com.baidu.bce.logic.chpc.gateway.external.model.bcakend.Queue queue = new com.baidu.bce.logic.chpc.gateway.external.model.bcakend.Queue();
        queue.setQueueName(group.getName());
        queue.setIsDefault(false);

        // todo 去掉Queue的computeResource属性
        ComputeResource computeResource = new ComputeResource();
        computeResource.setSpec(group.getDefaultSpec());
        queue.setComputeResources(Collections.singletonList(computeResource));

        return queue;
    }

    private Queue generateGroup(String clusterId, QueueAddRequest request) {

        // Group defaultGroup = groupService.getDefaultGroupByClusterId(clusterId);

        Queue queue = new Queue();
        queue.setClusterId(clusterId);
        queue.setName(request.getQueueName());
        // 生成队列id
        queue.setQueueId(globalUuidUtil.generateQueueId());
        queue.setIsDefault(false);
        queue.setStatus(QueueStatus.CREATING.nameLowerCase());
        queue.setDescription(request.getDescription());

        return queue;
    }

    /*
     * @Override
     *
     * @ValidateAuthentication
     * public GroupResponse removeGroupFromCluster(String clusterId, String groupId,
     * Boolean force) {
     *
     * Group group = groupService.findBy(groupId);
     * CommonValidateUtil.validateGroup(group);
     *
     * BackendCommonResponse backendResponse = backendGateway.removeQueue(
     * group.getClusterId(), group.getName(), force);
     *
     * CommonValidateUtil.validBackendCommonResponse(backendResponse);
     *
     * String taskId = UUID.randomUUID().toString();
     * taskService.insert(TaskType.BACKEND_ASYNC_TASK, group.getClusterId(),
     * groupId,
     * TaskSourceType.REMOVE_GROUP_FROM_CLUSTER.getTaskSourceType(),
     * taskId,
     * Collections.singletonMap(ChpcConstant.BACKEND_TASK_ID,
     * backendResponse.getTaskId()));
     *
     *
     * groupService.updateGroupStatus(groupId, ClusterStatus.DELETING.getType());
     *
     * GroupResponse groupResponse = new GroupResponse();
     * groupResponse.setGroupId(group.getGroupId());
     * groupResponse.setStatus(ClusterStatus.DELETING.getType());
     * groupResponse.setTaskId(taskId);
     * return groupResponse;
     * }
     */
    // transactional确保queue和相关的自动伸缩策略一起删除
    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public QueueRemoveResponse removeQueue(String clusterId, String queueName, Boolean check, Boolean force) {

        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        // 只有active状态的集群才可以增加队列
        if (!ClusterStatus.ACTIVE.nameLowerCase().equalsIgnoreCase(cluster.getStatus())) {
            throw new CommonExceptions.RequestInvalidException("cluster " + clusterId + " is in " +
                    cluster.getStatus() + " status, cannot add queue, only active status allowed.");
        }
        if (cluster.getSchedulerType().contains("pbs") && "route".equals(queueName)) {
            throw new CommonExceptions.RequestInvalidException("pbs 集群不能删除 route 队列");
        }
        // 公有云，不能删除默认队列
        if (ClusterType.CLOUD.nameLowerCase().equals(cluster.getClusterType())) {
            Queue queue = queueDAOGateway.getByName(clusterId, queueName);
            if (queue != null && queue.getIsDefault()) {
                throw new CommonExceptions.RequestInvalidException("默认队列不能删除");
            }
        }
        QueueRemoveResponse response = new QueueRemoveResponse();
        // 如果没有使用调度器插件，则使用之前的代码逻辑
        if (cluster.getSchedulePlugin() == 0) {
            response.setClusterName(cluster.getName());
            Queue queue = queueDAOGateway.getByName(clusterId, queueName);
            // 校验参数
            // CommonValidateUtil.validateGroup(group);
            // 检查是否是默认队列
            if (queue.getIsDefault()) {
                throw new CommonExceptions.RequestInvalidException("The default queue does not allow deletion");
            }
            // 检查队列状态
            if (!QueueStatus.ACTIVE.nameLowerCase().equalsIgnoreCase(queue.getStatus())) {
                throw new CommonExceptions.RequestInvalidException(
                        "The queue which is not active does not allow deletion");
            }
            // 检查队列中是否有节点
            List<Instance> instances = instanceService.findBy(clusterId, queue.getQueueId());
            if (CollectionUtils.isNotEmpty(instances)) {
                throw new CommonExceptions.RequestInvalidException(
                        "The queue which is not empty does not allow deletion");
            }
            if (check) {
                // TODO: 当前slurm&sge不具备任务管理能力，这里总是认为可能有任务，上调度器插件后废弃老逻辑
                response.setQueueName(queueName);
                response.setHasRunningJob(true);
                return response;
            }
            // todo 后续操作挪入han job
            // action1. 更新队列元信息，状态为DELETED
            queueDAOGateway.delete(queue.getQueueId());
            // action2. 请求api-sever删除队列
            BackendCommonResponse backendResponse = backendGateway.removeQueue(
                    queue.getClusterId(), queue.getName(), force, false);
            if (!ChpcConstant.HTTP_STATUS_200.equals(backendResponse.getCode())) {
                throw new CommonExceptions.RequestInvalidException(backendResponse.getMessage());
            }
            // todo 后续如果只支持删除空队列，可以简化为同步，不再异步轮询
            // action3. 轮询api-server删除结果，删除成功会再创建一个异步任务，释放资源
            String taskId = UUID.randomUUID().toString();
            taskService.insert(TaskType.BACKEND_ASYNC_TASK, queue.getClusterId(), queue.getQueueId(),
                    TaskSourceType.REMOVE_GROUP_FROM_CLUSTER.getTaskSourceType(),
                    taskId,
                    Collections.singletonMap(ChpcConstant.BACKEND_TASK_ID, backendResponse.getTaskId()));
            // 返回结果（删除一定成功）
            response.setQueueName(queue.getName());
            return response;
        } else {
            // 使用调度器插件，则使用新的逻辑代码

            response.setClusterName(cluster.getName());
            BackendCommonResponse backendResponse = new BackendCommonResponse();
            if (check) {
                // 第一次请求，检查阶段，只需要抛出异常即可，前端弹出选择框
                response.setQueueName(queueName);
                try {
                    backendResponse = backendGateway.removeQueue(clusterId, queueName, force, check);
                } catch (WebClientResponseException e) {
                    // 这里应该是跳出选择框的代码
                    if (e.getResponseBodyAsString().contains("check_have_task_in_queue")) {
                        response.setHasRunningJob(true);
                    } else if (e.getResponseBodyAsString().contains("check_no_task_in_queue")) {
                        response.setHasRunningJob(false);
                    } else {
                        throw e;
                    }
                } catch (Exception e) {
                    // 这里应该是跳出选择框的代码
                    if (e.getMessage().contains("check_have_task_in_queue")) {
                        response.setHasRunningJob(true);
                    } else if (e.getMessage().contains("check_no_task_in_queue")) {
                        response.setHasRunningJob(false);
                    } else {
                        throw e;
                    }
                }
                return response;
            } else {
                try {
                    backendResponse = backendGateway.removeQueue(clusterId, queueName, force, check);
                } catch (WebClientResponseException e) {
                    // 请求失败，抛异常
                    String msg = e.getResponseBodyAsString();
                    if (msg.contains("only_one_queue")) {
                        // 某用户只有一个队列，不能删除
                        String userListString = msg.split("the user_list = ")[1];
                        int index = userListString.indexOf('"');
                        if (index != -1) {
                            userListString = userListString.substring(0, index);
                        }
                        ArrayList<String> userList = new ArrayList<>(Arrays.asList(userListString.split(",")));
                        String userListOutput = "";
                        if (userList.size() == 1) {
                            userListOutput = userListOutput + userList.get(0);
                        } else if (userList.size() == 2) {
                            userListOutput = userListOutput + userList.get(0) + " 和 " + userList.get(1);
                        } else {
                            userListOutput = userListOutput + userList.get(0) + "、" + userList.get(1) + "等";
                        }
                        throw new CommonExceptions.RequestInvalidException(
                                "不能删除:用户" + userListOutput + "只有这一个队列");
                    } else {
                        // 其他错误，抛异常
                        throw new CommonExceptions.RequestInvalidException(e.getMessage());
                    }
                } catch (Exception e) {
                    // 请求失败，抛异常
                    String msg = e.getMessage();
                    if (msg.contains("only_one_queue")) {
                        // 某用户只有一个队列，不能删除
                        String userListString = msg.split("the user_list = ")[1];
                        int index = userListString.indexOf('"');
                        if (index != -1) {
                            userListString = userListString.substring(0, index);
                        }
                        ArrayList<String> userList = new ArrayList<>(Arrays.asList(userListString.split(",")));
                        String userListOutput = "";
                        if (userList.size() == 1) {
                            userListOutput = userListOutput + userList.get(0);
                        } else if (userList.size() == 2) {
                            userListOutput = userListOutput + userList.get(0) + " 和 " + userList.get(1);
                        } else {
                            userListOutput = userListOutput + userList.get(0) + "、" + userList.get(1) + "等";
                        }
                        throw new CommonExceptions.RequestInvalidException(
                                "不能删除:用户" + userListOutput + "只有这一个队列");
                    } else {
                        // 其他错误，抛异常
                        throw new CommonExceptions.RequestInvalidException(e.getMessage());
                    }
                }
                if (ChpcConstant.HTTP_STATUS_200.equals(backendResponse.getCode())) {
                    // 删除成功，返回结果
                    response.setQueueName(queueName);
                    String msg = backendResponse.getMessage();
                    if (msg.contains("success : queue will be deleted when all tasks in queue finished")) {
                        // 队列中存在任务，不能删除，需等待任务完成
                        String taskId = UUID.randomUUID().toString();
                        taskService.insert(TaskType.BACKEND_ASYNC_TASK, clusterId, queueName,
                                TaskSourceType.REMOVE_GROUP_FROM_CLUSTER.getTaskSourceType(),
                                taskId,
                                Collections.singletonMap(ChpcConstant.BACKEND_TASK_ID, backendResponse.getTaskId()));
                        response.setHasRunningJob(true);
                    } else if (msg.contains("success : queue has been deleted")) {
                        // 删除成功，返回结果
                        response.setHasRunningJob(false);
                    }
                    // todo 如果cluster_api执行队列删除失败，queueDAOGateway删除成功
                    // todo 对于异步删除的场景最好在BackendAsyncTask中调用queueDAOGateway(前端先置灰)
                    Queue queue = queueDAOGateway.getByName(clusterId, queueName);
                    if (queue != null) {
                        // 删除队列记录
                        queueDAOGateway.delete(queue.getQueueId());
                        // 删除队列同时要删除该队列所有的伸缩策略
                        AutoScaling autoScaleRecord = autoScalingDAOGateway.getByQueueId(queue.getQueueId(), LogicUserService.getAccountId());
                        if (autoScaleRecord != null) {
                            autoScalingDAOGateway.delete(autoScaleRecord.getAsId());
                        }
                        // bct 操作详情
                        String bctMsg = String.format("集群%s（ID: %s）中删除队列%s", cluster.getName(), cluster.getClusterId(), queueName);
                        response.setMessage(bctMsg);
                    }
                    // throw new CommonExceptions.RequestInvalidException(
                    // "不能删除:队列中存在任务，需等待任务完成");
                } else {
                    // 删除失败，抛异常
                    throw new CommonExceptions.RequestInvalidException(backendResponse.getMessage());
                }
            }
        }
        // 删除队列标签
        try {
            tagsDAOGateway.deleteByClusterIdAndName(clusterId, "queue", queueName);
        } catch (Exception e) {
            throw new BceException(e.getMessage());
        }
        return response;
    }

    /**
     * {@inheritDoc}
     * 获取队列详情，包括队列名称、是否默认队列、是否自动缩放、状态、描述、实例数量。
     *
     * @param clusterId 集群ID
     * @param queueName 队列名称
     * @return QueueDetailResponse 队列详情响应对象，包括队列信息和实例数量
     * @throws Exception 可能会抛出各种异常，需要上层处理
     */
    @Override
    public QueueDetailResponse getQueueDetail(String clusterId, String queueName) {

        Queue queue = queueDAOGateway.getByName(clusterId, queueName);

        QueueDetailResponse queuedetailResponse = new QueueDetailResponse();
        QueueDetailResponse.QueueInfo queueInfo = new QueueDetailResponse.QueueInfo();
        queueInfo.setQueueName(queueName);
        queueInfo.setIsDefault(queue.getIsDefault());
        queueInfo.setIsAutoScale(queue.getIsAutoScale());
        queueInfo.setHasAutoScale(queue.getHasAutoScale());
        queueInfo.setStatus(queue.getStatus());
        queueInfo.setDescription(queue.getDescription());

        queueInfo.setInstanceCount(instanceService.countByClusterIdAndQueueIdAndNodeType(
                clusterId,
                queue.getQueueId(),
                InstanceNodeType.COMPUTE));


        List<Tag> tags = tagsDAOGateway.findTags(getAccountId(), clusterId, "queue", queueName);
        queueInfo.setTags(tags);
        queuedetailResponse.setQueue(queueInfo);

        return queuedetailResponse;
    }

    /**
     * {@inheritDoc}
     * 获取队列中的用户信息，如果集群使用了调度插件则通过调度插件进行查询，否则返回空队列。
     *
     * @param clusterId 集群ID
     * @param queueName 队列名称
     * @return 包含队列中用户信息的QueueVO对象，如果没有用户则为空队列
     * @throws Exception 如果发生任何异常都会抛出该异常
     */
    @Override
    public QueueVO getQueueUsers(String clusterId, String queueName) {
        QueueVO queueVO = new QueueVO();
        if (ServiceUtil.isClusterUsingSchedulerPlugin(clusterDAOGateway, clusterId)) {
            BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId,
                    "queue_list", "--console -q " + queueName);
            ObjectMapper mapper = new ObjectMapper();
            try {
                List<QueueVO> queueList = mapper.readValue(resp.getData(),
                        mapper.getTypeFactory().constructCollectionType(List.class,
                                QueueVO.class));
                if (queueList.size() == 1) {
                    queueVO = queueList.get(0);
                } else {
                    log.error("getQueueUsers error, queueList size is not 1");
                }
            } catch (Exception e) {
                log.error("listQueues error", e);
            }
        }

        return queueVO;
    }

    /**
     * {@inheritDoc}
     * 根据传入的集群id，查询该集群下所有的队列信息。
     * 首先，通过集群id查询集群对象，并判断集群是否存在；
     * 然后，调用queueDAOGateway的listByClusterId方法查询集群下所有的队列信息，并将其转换为QueueVO类型的列表；
     * 接着，如果集群使用的是计算插件，则需要统计每个队列下的实例数量，并设置到QueueVO中的instanceCount字段上；
     * 最后，如果是公有云集群（插件模式），则要更新默认队列的isDefault属性，并返回处理后的QueueVO列表。
     *
     * @param clusterId 集群id，不能为空
     * @return 包含所有队列信息的List<QueueVO>，如果集群不存在或者没有任何队列，则返回null
     */
    @Override
    @PermissionVerify(resourceLocation = PermissionVerify.ResourceLocation.NO_RESOURCE_ID_QUEUE_LIST, permissions = {
            PermissionConstant.CHPC_READ, PermissionConstant.QUEUE_READONLY})
    public List<QueueVO> listQueues(@PermissionResourceID String clusterId) {
        if (StringUtils.isEmpty(clusterId)) {
            return null;
        }
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        if (cluster == null) {
            return null;
        }
        List<QueueVO> queueList;
        queueList = queueDAOGateway.listByClusterId(clusterId)
                .stream()
                .map(ServiceUtil::convertToGroupVO)
                .collect(Collectors.toList());
        log.debug("----listQueues---- listByClusterId succ");
        if (cluster.getSchedulePlugin() == 0) {
            for (QueueVO queue : queueList) {
                queue.setInstanceCount(instanceService.countByClusterIdAndQueueIdAndNodeType(
                        clusterId,
                        queue.getQueueId(),
                        InstanceNodeType.COMPUTE));
            }
        } else {
            try {
                BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "queue_list", "--console");
                ObjectMapper mapper = new ObjectMapper();
                queueList = mapper.readValue(resp.getData(),
                        mapper.getTypeFactory().constructCollectionType(List.class,
                                QueueVO.class));
                // 补充队列的isAutoScale属性
                for (QueueVO queue : queueList) {
                    Queue dbQueue = queueDAOGateway.getByQueueName(queue.getQueueName(), clusterId);
                    if (dbQueue != null) {
                        queue.setIsAutoScale(dbQueue.getIsAutoScale());
                        queue.setDescription(dbQueue.getDescription());
                        if (ClusterType.CLOUD.nameLowerCase().equals(cluster.getClusterType())) {
                            queue.setIsDefault(dbQueue.getIsDefault());
                        }
                        queue.setHasAutoScale(dbQueue.getHasAutoScale());
                    }
                }
            } catch (Exception e) {
                log.error("listQueues error", e);
                return queueList;
            }
        }

        // 查询db中队列的全部标签
        List<DbTag> dbTags = tagsDAOGateway.findDbTags(getAccountId(), clusterId, "queue");

        // 如果db有，但是队列查不到了，说明队列已经删了，对应的tag也需要删除
        for (int i = 0; i < dbTags.size(); i++) {
            DbTag dbtag = dbTags.get(i);
            Boolean exist = false;
            for (int j = 0; j < queueList.size(); j++) {
                if (queueList.get(j).getQueueName().equals(dbtag.getName())) {
                    exist = true;
                    break;
                }
            }
            if (!exist) {
                log.debug(
                        "deleteByClusterIdAndName , clusterId:{} ,queueList:{} ,dbTags:{}", clusterId, queueList, dbTags);
                tagsDAOGateway.deleteByClusterIdAndName(clusterId, "queue", dbtag.getName());
            }
        }


        log.debug("----listQueues---- actionProxy succ");
        // 获取批量鉴权结果
        List<QueueVO> finalQueues = new ArrayList<>();
        Object aspectResult = AspectResultHolder.getAspectResult();
        AspectResultHolder.clear();
        if (aspectResult != null) {
            log.debug("Aspect result: " + aspectResult);
            // 过滤用户有权限的队列
            List<BatchVerifyResults.BatchVerifyResult> results = (List<BatchVerifyResults.BatchVerifyResult>) aspectResult;
            BatchVerifyResults.BatchVerifyResult result = results.get(0);
            if (result == null || CollectionUtils.isEmpty(result.getResult())) {
                log.debug("size of sub verify results is 0");
            } else if (queueList.size() != result.getResult().size()) {
                log.debug("size of iam result is not equal to cluster list");
            } else {
                List<VerifyResult> iamRes = result.getResult();
                for (int i = 0; i < iamRes.size(); i++) {
                    VerifyResult subResult = iamRes.get(i);
                    if (subResult != null) {
                        if (PermissionConstant.ALLOW_PERMISSION.equals(subResult.getEffect())) {
                            finalQueues.add(queueList.get(i));
                        }
                    }
                }
            }
        } else {
            finalQueues = queueList;
        }
        for (int i = 0; i < finalQueues.size(); i++) {
            List<Tag> tags = tagsDAOGateway.findTags(getAccountId(), clusterId, "queue", finalQueues.get(i).getQueueName());
            finalQueues.get(i).setTags(tags);
        }
        log.debug("----listQueues---- getAspectResult succ");
        return finalQueues;
    }

    @Override
    public QueueTagsResponse listTags() {

        FullTagListRequest fullTagListRequest = new FullTagListRequest();
        List<com.baidu.bce.logical.tag.sdk.model.Tag> tagList = null;
        try {
            tagList = tagsGateway.listTagsV2(fullTagListRequest);
        } catch (Exception e) {
            log.error("listTags failed, err {}", e.getMessage());
        }

        Set<String> set = new HashSet<>();
        for (com.baidu.bce.logical.tag.sdk.model.Tag tag : tagList) {
            set.add(tag.getTagKey());
        }

        List<Tag> tagRes = new ArrayList<>();

        for (String key : set) {
            List<Tag> dbTags = tagsDAOGateway.findTagsByKey(getAccountId(), "queue", key);
            if (dbTags != null && dbTags.size() > 0) {
                for (int i = 0; i < dbTags.size(); i++) {
                    if (!contains(tagRes, dbTags.get(i).getTagKey(), dbTags.get(i).getTagValue())) {
                        tagRes.add(dbTags.get(i));
                    }
                }
            } else {
                Tag tag = new Tag();
                tag.setTagKey(key);
                tagRes.add(tag);
            }
        }

        QueueTagsResponse queueTagsResponse = new QueueTagsResponse();
        queueTagsResponse.setResult(tagRes);
        queueTagsResponse.setPageNo(1);
        queueTagsResponse.setPageSize(tagRes.size());
        queueTagsResponse.setTotalCount(tagRes.size());
        return queueTagsResponse;
    }

    @Override
    public QueueUpdateResponse updateQueueTags(String clusterId, String queueName, UpdateTagsRequest request) {

        List<com.baidu.bce.logical.tag.sdk.model.Tag> tagList = new ArrayList<>();
        // 这里只创建key
        if (request.getTags() != null && request.getTags().size() > 0) {

            for (Tag tag : request.getTags()) {
                com.baidu.bce.logical.tag.sdk.model.Tag bceTag = new com.baidu.bce.logical.tag.sdk.model.Tag();
                bceTag.setTagKey(tag.getTagKey());
                bceTag.setTagValue("");
                tagList.add(bceTag);
            }

        }

        try {
            if (tagList.size() > 0) {
                tagsGateway.createTags(tagList);
            }
            // 队列关联的节点，统一加标签
            if (request.getTags() != null && request.getTags().size() > 0 && request.getAssociatedResources()) {

                List<com.baidu.bce.logical.tag.sdk.model.Tag> bceTags = BeanCopyUtil.copyListProperties(request.getTags(), com.baidu.bce.logical.tag.sdk.model.Tag::new);
                Map<String, List<com.baidu.bce.logical.tag.sdk.model.Tag>> instanceTags = new HashMap<>();
                // instanceId和instanceUuid
                Map<String, String> instanceUuids = new HashMap<>();
                List<Instance> instances = instanceServiceImpl.getClusterInstances(clusterId, "", "", queueName, "", "", false);
                FullTagListRequest fullTagListRequest = new FullTagListRequest();
                List<String> regions = new ArrayList<>();
                regions.add(regionConfiguration.getCurrentRegion());
                fullTagListRequest.setRegions(regions);
                List<String> resourceIds = new ArrayList<>();
                List<String> resourceUuids = new ArrayList<>();
                for (Instance instance : instances) {
                    if (InstanceAttribution.CLOUD.getType().equals(instance.getAttribution())) {
                        resourceIds.add(instance.getInstanceId());
                        resourceUuids.add(instance.getInstanceUuid());
                        instanceTags.put(instance.getInstanceId(), new ArrayList<>(bceTags));
                        instanceUuids.put(instance.getInstanceId(), instance.getInstanceUuid());
                    }
                }
                fullTagListRequest.setResourceIds(resourceIds);
                fullTagListRequest.setResourceUuids(resourceUuids);
                List<String> serviceTypes = new ArrayList<>();
                serviceTypes.add("BCC");
                fullTagListRequest.setServiceTypes(serviceTypes);
                TagAssociationFulls tagAssociationFulls = null;
                try {
                    tagAssociationFulls = tagsGateway.listTags(fullTagListRequest);
                } catch (Exception e) {
                    log.error("[instances {}]: get tags failed, err {}", resourceIds, e.getMessage());
                }


                for (TagAssociationFull tagAssociationFull : tagAssociationFulls.getTagAssociationFulls()) {
                    if (instanceTags.containsKey(tagAssociationFull.getResourceId())) {
                        if (!containsKey(instanceTags.get(tagAssociationFull.getResourceId()), tagAssociationFull.getTagKey())) {
                            com.baidu.bce.logical.tag.sdk.model.Tag tag = new com.baidu.bce.logical.tag.sdk.model.Tag();
                            tag.setTagKey(tagAssociationFull.getTagKey());
                            tag.setTagValue(tagAssociationFull.getTagValue());
                            instanceTags.get(tagAssociationFull.getResourceId()).add(tag);
                        }
                    }
                }


                CreateAndAssignTagRequest instanceCreateAndAssignTagRequest = new CreateAndAssignTagRequest();
                List<AssignResource> instanceResources = new ArrayList<>();
                for (Map.Entry<String, List<com.baidu.bce.logical.tag.sdk.model.Tag>> instanceTagList : instanceTags.entrySet()) {
                    AssignResource instanceResource = new AssignResource();
                    instanceResource.setServiceType("BCC");
                    instanceResource.setResourceId(instanceTagList.getKey());
                    instanceResource.setResourceUuid(instanceUuids.get(instanceTagList.getKey()));
                    instanceResource.setTags(instanceTagList.getValue());
                    instanceResource.setRegion(regionConfiguration.getCurrentRegion());
                    instanceResource.setAssociationType("floating");
                    instanceResources.add(instanceResource);
                }
                instanceCreateAndAssignTagRequest.setResources(instanceResources);
                tagsGateway.createAndAssignTag(instanceCreateAndAssignTagRequest);


                // 本地节点关联标签
                for (Instance instance : instances) {
                    if (InstanceAttribution.LOCAL.getType().equals(instance.getAttribution())) {
                        List<Tag> localNodeTags = tagsDAOGateway.findTags(getAccountId(), clusterId, "localNode", null);
                        for (int i = 0; i < request.getTags().size(); i++) {
                            if (!contains(localNodeTags, request.getTags().get(i).getTagKey(), request.getTags().get(i).getTagValue())) {
                                tagsDAOGateway.insert(LogicUserService.getAccountId(), clusterId, "localNode", instance.getHostName(),
                                        request.getTags().get(i).getTagKey(), request.getTags().get(i).getTagValue());
                            }
                        }
                    }
                }
            }

        } catch (Exception e) {
            log.debug(
                    "updateQueueTags fail, clusterId:{} ,queueName:{} ,err:{}", clusterId, queueName, e.getMessage());
        }

        // 删除原有标签
        tagsDAOGateway.deleteByClusterIdAndName(clusterId, "queue", queueName);
        if (request.getTags() != null && request.getTags().size() > 0) {
            // 新增到db中
            for (int i = 0; i < request.getTags().size(); i++) {
                tagsDAOGateway.insert(LogicUserService.getAccountId(), clusterId, "queue", queueName,
                        request.getTags().get(i).getTagKey(), request.getTags().get(i).getTagValue());
            }
        }

        QueueUpdateResponse response = new QueueUpdateResponse();
        response.setQueueName(queueName);
        return response;
    }

    private Boolean contains(List<Tag> tags, String tagKey, String tagValue) {
        for (Tag tag : tags) {
            if (tag.getTagKey().equals(tagKey) && tag.getTagValue().equals(tagValue)) {
                return true;
            }
        }
        return false;
    }

    private Boolean containsKey(List<com.baidu.bce.logical.tag.sdk.model.Tag> tags, String tagKey) {
        for (com.baidu.bce.logical.tag.sdk.model.Tag tag : tags) {
            if (tag.getTagKey().equals(tagKey)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @Description 生成队列对象，包含默认属性和指定的队列信息
     * @Param queueInfo 队列信息，包括队列名称、计算规格、用户数据等
     * @Param request 创建集群请求，包括管理员镜像ID、区域名称等
     * @Param clusterId 集群ID
     * @Return Queue 返回一个队列对象，包含默认属性和指定的队列信息
     */
    public Queue genQueue(QueueInfo queueInfo, ClusterCreateRequest request, String clusterId) {
        Queue queue = new Queue();
        queue.setClusterId(clusterId);
        queue.setQueueId(globalUuidUtil.generateQueueId());
        queue.setName(queueInfo.getQueueName());
        queue.setIsDefault(queueInfo.getIsDefault());
        queue.setDescription(queueInfo.getDescription());
        if (queueInfo.getComputeSpec() != null) {
            queue.setDefaultSpec(queueInfo.getComputeSpec().getSpec());
            if (StringUtils.isNotEmpty(queueInfo.getComputeSpec().getImageId())) {
                queue.setDefaultImageId(queueInfo.getComputeSpec().getImageId());
            } else {
                queue.setDefaultImageId(request.getMasterSpec().getImageId());
            }
            queue.setDefaultUserData(queueInfo.getComputeSpec().getUserData());
            queue.setSubnetId(queueInfo.getComputeSpec().getSubnetId());
        }
        queue.setStatus(QueueStatus.CREATING.nameLowerCase());
        queue.setLogicalZone(request.getZoneName());
        queue.setIsAutoScale(queueInfo.getIsAutoScale());
        // 该字段初始化的时候与isAutoScale保持一致
        queue.setHasAutoScale(queueInfo.getIsAutoScale());

        return queue;
    }

    public Queue genQueue(com.baidu.bce.logic.chpc.queue.QueueAddRequest request, String clusterId) {
        Queue queue = new Queue();
        queue.setClusterId(clusterId);
        queue.setQueueId(globalUuidUtil.generateQueueId());
        queue.setName(request.getQueueName());
        queue.setDescription(request.getDescription());
        queue.setIsDefault(request.isDefault());
        if (CollectionUtils.isNotEmpty(request.getComputeNodes())) {
            queue.setDefaultSpec(request.getComputeNodes().get(0).getSpec());
            queue.setDefaultImageId(request.getComputeNodes().get(0).getImageId());
            queue.setLogicalZone(request.getComputeNodes().get(0).getZoneName());
        }

        // queue.setDefaultUserData(request.getUserdata());
        queue.setStatus(QueueStatus.CREATING.nameLowerCase());

        queue.setIsAutoScale(false);

        queue.setHasAutoScale(false);

        return queue;
    }

}
