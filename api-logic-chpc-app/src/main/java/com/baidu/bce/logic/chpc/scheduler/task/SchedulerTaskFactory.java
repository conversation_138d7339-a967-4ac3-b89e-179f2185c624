package com.baidu.bce.logic.chpc.scheduler.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: lilu24
 * @Date: 2022-12-08
 */

@Slf4j
@Service
public class SchedulerTaskFactory {

    @Autowired
    private final Map<String, SchedulerTaskService> schedulerTaskMap = new ConcurrentHashMap<>();

    public SchedulerTaskService getSchedulerTaskService(String taskType) {

        SchedulerTaskService taskService = schedulerTaskMap.get(taskType);

        if (taskService == null) {
            throw new RuntimeException("no such task processor");
        }

        return taskService;
    }


}
