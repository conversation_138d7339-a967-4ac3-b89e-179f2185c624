package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.skyform.gateway.SkyFormGateway;
import com.baidu.bce.logic.chpc.skyform.GetUserResponse;
import com.baidu.bce.logic.chpc.skyform.AddTenantOrUserResponse;
import com.baidu.bce.logic.chpc.skyform.BaseResponse;
import com.baidu.bce.logic.chpc.skyform.GetTenantResponse;
import com.baidu.bce.logic.chpc.skyform.ActiveUserOrRoleResponse;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.SaasResourceType;
import com.baidu.bce.logic.chpc.common.ServiceStatusType;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.gateway.OrderDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ServiceStatusDAOGateway;
import com.baidu.bce.logic.chpc.model.ServiceStatus;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Order;

import com.baidu.bce.logic.chpc.model.request.saas.ServiceCreateRequest;
import com.baidu.bce.logic.chpc.service.ISaasService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component(value = "add_skyform")
public class SkyFormTask extends AbstractSchedulerTask {
    @Resource
    private SkyFormGateway skyformClient;

    @Resource
    private ServiceStatusDAOGateway serviceStatusDAOGateway;

    @Resource
    private ISaasService saasService;


    @Resource
    OrderDAOGateway orderDAOGateway;


    @Override
    Map<String, Object> executeTask(Task task, Cluster cluster, Queue queue) {
        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
        String accountId = (String) extraMap.get(ChpcConstant.ACCOUNT_ID);
        String pfsId = (String) extraMap.get(ChpcConstant.PFS_ID);
        String filesetId = (String) extraMap.get(ChpcConstant.FILESET_ID);
        // 已经开通
        ServiceStatus serviceStatus = serviceStatusDAOGateway.findBy(Long.valueOf(2), accountId);
        if (null != serviceStatus && ServiceStatusType.ACTIVATED.nameLowerCase().equals(serviceStatus.getStatus())) {
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
            return extraMap;
        }

        Order order = orderDAOGateway.findByAccountIdAndServiceType(accountId, SaasResourceType.CHPC.name());
        // 订单已经创建
        if (order != null) {
            if (OrderStatus.CREATED.toString().equals(order.getStatus())) {
                serviceStatusDAOGateway.update(Long.valueOf(2), accountId, ServiceStatusType.ACTIVATED.nameLowerCase());
                extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
                return extraMap;
            }else{
                extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
                return extraMap;
            }
        }

        // 查询组织是否存在
        GetTenantResponse tenantByTenantName = skyformClient.getTenantByTenantName(accountId);
        String tenantId = "";
        // 新增组织，设置租户登录权限
        if (null == tenantByTenantName.getRetObj()) {
            AddTenantOrUserResponse addTenantOrUserResponse = skyformClient.addTenant(accountId);
            tenantId = addTenantOrUserResponse.getRetObj();
            BaseResponse baseResponse = skyformClient.batchSaveAspTerminalPermission(tenantId);
        } else {
            tenantId = tenantByTenantName.getRetObj().getUuid();
        }
        // 查询用户是否存在
        GetUserResponse userByloginName = skyformClient.getUserByloginName(accountId);
        // 新增并激活用户
        if (null == userByloginName.getRetObj() || userByloginName.getRetObj().size() == 0) {
            AddTenantOrUserResponse addTenantOrUserResponse = skyformClient.addUser(tenantId, accountId, "");
            String userUuid = addTenantOrUserResponse.getRetObj();
            // 激活用户
            ActiveUserOrRoleResponse activeUserOrRoleResponse = skyformClient.activeUser(userUuid);
            // 设置用户角色
            List<String> roleIds = new ArrayList<>();
            roleIds.add("4");
            roleIds.add("5");
            ActiveUserOrRoleResponse activeUserOrRoleResponse1 = skyformClient.addUserRole(userUuid, roleIds);

        }

        ServiceCreateRequest serviceCreateRequest = new ServiceCreateRequest();
        serviceCreateRequest.setAccountId(accountId);
        serviceCreateRequest.setPfsId(pfsId);
        serviceCreateRequest.setFilesetId(filesetId);
        saasService.createService(serviceCreateRequest);

        extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());

        return extraMap;
    }
}
