package com.baidu.bce.logic.chpc.service.billing;

import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.baidu.bce.service.bus.sdk.BusNioClient;
import com.baidu.bce.service.bus.sdk.util.BusConstants;
import com.baidu.bce.service.bus.sdk.util.InetAddressHelper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class BusRegisterServiceImpl implements ApplicationRunner {

    @Value("${server.port}")
    private String port;

    @Value("${bus.register:true}")
    private boolean busRegistered;

    @Value("${bus.global-register:false}")
    private boolean globalBusRegistered;

    @Value("${bus.tag-register:false}")
    private boolean tagBusRegistered;

    @Resource
    private BusNioClient busNioClient;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String endpoint = InetAddressHelper.getHostAddress() + ":" + port;
        String regionName = RegionConfiguration.getRegionName();

        // 单个地域的注册
        if (busRegistered) {
            busNioClient.registerService(BusConstants.SERVICE_TYPE_CONSOLE, ChpcConstant.SERVICE_TYPE_CHPC, endpoint, regionName,
                    null).blockOptional();
            log.info("Register success : {}.", endpoint);
        }

        // 单个地域的注册
        if (tagBusRegistered) {
            busNioClient.registerService(ChpcConstant.SERVICE_TYPE_TAG, ChpcConstant.SERVICE_TYPE_CHPC, endpoint, regionName,
                    null).blockOptional();
            log.info("Register success : {}.", endpoint);
        }

        // 订单执行器的全局的注册
        if (globalBusRegistered) {
            busNioClient.registerService(BusConstants.SERVICE_TYPE_CONSOLE, ChpcConstant.SERVICE_TYPE_CHPC, endpoint, null,
                    null).blockOptional();
            log.info("Register global success : {}.", endpoint);
        }

        // 如果全局和单个地域都没有注册
        if (!busRegistered && !globalBusRegistered) {
            busNioClient.unregisterService(BusConstants.SERVICE_TYPE_CONSOLE, ChpcConstant.SERVICE_TYPE_CHPC, endpoint).blockOptional();
            log.info("Unregister success : {}.", endpoint);
        }
    }
}