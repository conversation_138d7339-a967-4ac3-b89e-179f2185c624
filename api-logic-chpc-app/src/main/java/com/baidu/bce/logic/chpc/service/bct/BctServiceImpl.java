package com.baidu.bce.logic.chpc.service.bct;

import org.springframework.stereotype.Service;

import com.baidu.bce.logic.chpc.bct.gateway.BctGateway;
import com.baidu.bce.logic.chpc.bct.model.BctQueryEventsRequest;
import com.baidu.bce.logic.chpc.bct.model.BctQueryEventsResponse;
import com.baidu.bce.logic.chpc.service.BctEventService;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class BctServiceImpl implements BctEventService {
    
    @Resource
    BctGateway bctGateway;

    @Override
    public BctQueryEventsResponse queryEvents(BctQueryEventsRequest request) {
        return bctGateway.queryEvents(request);
    }

}
