package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.bcc.BccInstance;
import com.baidu.bce.logic.chpc.cfs.FileSystem;
import com.baidu.bce.logic.chpc.cfs.MountTarget;
import com.baidu.bce.logic.chpc.chpc.gateway.ChpcGateway;
import com.baidu.bce.logic.chpc.chpc.model.PushAutoScalingBctEventRequest;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterSchedulerType;
import com.baidu.bce.logic.chpc.common.ClusterStatus;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.EventType;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.common.StackStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.cos.GetStackListRequest;
import com.baidu.bce.logic.chpc.cos.GetStackListResponse;
import com.baidu.bce.logic.chpc.cos.StackDetail;
import com.baidu.bce.logic.chpc.cos.StackResult;
import com.baidu.bce.logic.chpc.cos.StackResult.ResourceOperation;
import com.baidu.bce.logic.chpc.gateway.ClusterEventDAOGateway;
import com.baidu.bce.logic.chpc.gateway.TaskDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.BccInstanceDetail;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag;
import com.baidu.bce.logic.chpc.model.Cfs;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Event;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import jakarta.annotation.Resource;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Slf4j
@Component(value = "group_resource_save_task")
public class GroupResourceSaveTask extends AbstractSchedulerTask {

    @Resource
    ClusterEventDAOGateway clusterEventDAOGateway;

    @Resource
    private TaskDAOGateway taskDAOGateway;

    @Resource
    ChpcGateway chpcGateway;

    @Resource
    TagsGateway tagsGateway;

    /**
     * {@inheritDoc}
     * <p>
     * 根据COS Stack的状态来判断任务是否执行成功、失败或者仍在进行中。如果执行成功，则保存BCC资源并更新队列和集群状态；
     * 如果执行失败，则更新自动扩容规则为正常，不影响下次扩容，并更新队列和集群状态。
     *
     * @param task    包含COS Stack ID和其他信息的任务对象
     * @param cluster 包含集群ID和类型等信息的集群对象
     * @param queue   包含队列ID和类型等信息的队列对象
     * @return 一个Map对象，包含任务状态（成功、失败或者进行中）以及其他信息，例如集群类型
     * @throws Exception 当任务执行失败时抛出异常
     */
    @Override
    public Map<String, Object> executeTask(final Task task, final Cluster cluster, final Queue queue) {

        String stackResultStatus = null;
        String stackId = task.getCosStackId();
        Set<Map.Entry<String, StackResult.Instance>> stackEntrySet = Collections.emptySet();
        List<StackResult.ResourceOperation> failedReasons = new ArrayList<>();
        Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
        String stackName = String.valueOf(extraMap.getOrDefault(ChpcConstant.COS_STACK_NAME, ""));
        List<com.baidu.bce.logic.chpc.tag.Tag> nodesTags = ServiceUtil.castToTags(extraMap.get(ChpcConstant.NODES_TAGS));
        log.debug("[AddNodeDebug] nodeTags : {}", nodesTags);

        if (StringUtils.isEmpty(stackId)) {
            // 根据 stackName 获取 stackId
            GetStackListRequest getStackListRequest = new GetStackListRequest();
            getStackListRequest.setName(stackName);
            try {
                GetStackListResponse getStackListResponse = cosGateway.getStackList(getStackListRequest);
                if (getStackListResponse == null || getStackListResponse.getResult() == null
                        || getStackListResponse.getResult().getTotalCount() <= 0) {
                    // 响应信息错误，抛出异常
                    throw new BceException("get stack list failed for clusterId:" + cluster.getClusterId());
                }
                stackId = getStackListResponse.getResult().getStacks().get(0).getId();
                Boolean success = taskService.updateCosStackId(task.getTaskId(), stackId);
                log.debug("[AddNodeDebug] update cos stackId {} for taskId:{}, success: {}", stackId, task.getTaskId(),
                        success);
                task.setCosStackId(stackId);
            } catch (WebClientResponseException e) {
                log.error("[AddNodeDebug] get stack list failed for clusterId:{} queueName:{}, e:{}",
                        cluster.getClusterId(), queue.getName(), e);
                // 查询不到 stack 信息，将任务设置为失败
                extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
                extraMap.put(ChpcConstant.TASK_FAILED_REASON,
                        String.format("get cos stackId from stackName [%s] failed: %s", stackName, e.getResponseBodyAsString()));
                extraMap.put(ChpcConstant.CLUSTER_TYPE, cluster.getClusterType());
                return extraMap;
            } catch (Exception e) {
                log.error("[AddNodeDebug] get stack list failed for clusterId:{} queueName:{}, e:{}",
                        cluster.getClusterId(), queue.getName(), e);
                // 查询不到 stack 信息，将任务设置为失败
                extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
                extraMap.put(ChpcConstant.TASK_FAILED_REASON,
                        String.format("get cos stackId from stackName [%s] failed: %s", stackName, e.getMessage()));
                extraMap.put(ChpcConstant.CLUSTER_TYPE, cluster.getClusterType());
                return extraMap;
            }
        }

        String clusterId = cluster.getClusterId();
        String queueName = queue.getName();

        log.debug("[AddNodeDebug] execute task for taskId:{}, clusterId:{}, queueName:{}, stackId:{}",
                task.getTaskId(),
                clusterId,
                queueName,
                stackId);

        if (stackId.startsWith("st-exist-")) {
            stackResultStatus = StackStatus.SUCCESS.name();
            // 从 extraMap 中获取 instanceIds
            log.debug("[AddNodeDebug] get instanceIds {} from extraMap for clusterId:{} queueName:{}",
                    extraMap.get(ChpcConstant.INSTANCE_IDS), clusterId, queueName);
            List<String> instanceIds = ServiceUtil.castToList(extraMap.get(ChpcConstant.INSTANCE_IDS));
            log.debug("[AddNodeDebug] cast instanceIds {} from extraMap for clusterId:{} queueName:{}", instanceIds,
                    clusterId, queueName);

            Map<String, StackResult.Instance> stackEntryMap = new HashMap<>();

            // 检查所有实例已经重装系统成功，状态变成Running
            int attempt = 0;
            int maxAttempts = 30;
            for (attempt = 0; attempt < maxAttempts; attempt++) {
                List<BccInstance> bccInstances = bccGateway.getBccInstances(instanceIds);

                boolean allRunning = true;
                for (BccInstance bccInstance : bccInstances) {
                    if (!"Running".equalsIgnoreCase(bccInstance.getStatus())) {
                        allRunning = false;
                        log.debug("[AddNodeDebug] instance {} is not running for clusterId:{} queueName:{}",
                                bccInstance.getInstanceId(), clusterId, queueName);
                        break;
                    }
                }

                if (allRunning) {
                    if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
                        if (ClusterSchedulerType.PBS.getName().equalsIgnoreCase(cluster.getSchedulerType()) ||
                                ClusterSchedulerType.OPENPBS.getName().equalsIgnoreCase(cluster.getSchedulerType())) {

                            log.debug(
                                    "[AddNodeDebug] start to fill extraMap in save task, get instance hostname and spec for clusterId:{} queueName:{}",
                                    clusterId, queueName);
                            extraMap.put(ChpcConstant.QUEUE_NAME, queue.getName());
                            List<String> instanceHostnames = new ArrayList<>();
                            List<String> instanceSpecs = new ArrayList<>();
                            for (BccInstance instance : bccInstances) {
                                if (!StringUtil.isEmpty(instance.getHostName())
                                        && !StringUtil.isEmpty(instance.getSpec())) {
                                    instanceHostnames.add(instance.getHostName());
                                    instanceSpecs.add(instance.getSpec());
                                }
                            }
                            extraMap.put(ChpcConstant.INSTANCE_HOSTNAME_LIST, instanceHostnames);
                            extraMap.put(ChpcConstant.INSTANCE_SPEC_LIST, instanceSpecs);
                        }
                    }

                    break;
                }

                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    System.err.println("Thread was interrupted");
                    break;
                }
            }

            if (attempt == maxAttempts) {
                log.error("[AddNodeError] not all instances are running for clusterId:{} queueName:{}", clusterId,
                        queueName);
                return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
            } else {
                log.debug("[AddNodeDebug] all instances are running for clusterId:{} queueName:{}", clusterId,
                        queueName);
            }

            for (String instanceId : instanceIds) {
                StackResult.Instance stackInstance = new StackResult.Instance();
                // 生成 id 字段
                stackInstance.setId(instanceId);
                // 生成 attributes 字段
                Map<String, Object> attributes = new HashMap<>();
                attributes.put("networkCapacityInMbps", 0);
                stackInstance.setProperties(attributes);
                // 生成 type 字段
                stackInstance.setType("BCE::BCC::Instance");

                stackEntryMap.put(instanceId, stackInstance);
            }
            stackEntrySet = new HashSet<>(stackEntryMap.entrySet());

            log.debug("[AddNodeDebug] the exist stack of {} for clusterId:{} queueName:{} is {}",
                    stackId,
                    clusterId,
                    queueName,
                    stackResultStatus);

        } else {
            StackDetail stack = cosGateway.getStack(stackId);
            StackResult result = stack.getResult();
            stackResultStatus = result.getStatus();
            if (result.getState() != null && result.getState().getInstances() != null) {
                stackEntrySet = result.getState().getInstances().entrySet();
            }
            log.debug("[AddNodeDebug] the new stack of {} for clusterId:{} queueName:{} is {}",
                    stackId,
                    clusterId,
                    queueName,
                    stackEntrySet);
            if (result.getResourceOperations() != null) {
                failedReasons = result.getResourceOperations();
            }
            log.debug("[AddNodeDebug] the stack of {} for clusterId:{} queueName:{} is {}",
                    stackId,
                    clusterId,
                    queueName,
                    stackResultStatus);
        }

        boolean hasSuccess = false;
        if (StackStatus.FAILED.name().equalsIgnoreCase(stackResultStatus)) {
            // 只有自动伸缩时会出现此状态，此时需要将成功部分的节点加入队列
            for (Map.Entry<String, StackResult.Instance> entry : stackEntrySet) {
                StackResult.Instance value = entry.getValue();
                if (StringUtil.isNotEmpty(value.getId())) {
                    hasSuccess = true;
                    break;
                }
            }
        }

        Event event = clusterEventDAOGateway.findByClusterIdAndEvent(task.getClusterId(),
                EventType.CREATE_RESOURCE);
        if (event != null) {
            if (EventType.READY_TO_START.equals(event.getStatus())) {
                clusterEventDAOGateway.update(cluster.getClusterId(), EventType.CREATE_RESOURCE, EventType.PROGRESSING,
                        event.getErrMsg());
            }
        }

        if (StackStatus.SUCCESS.name().equalsIgnoreCase(stackResultStatus) || hasSuccess) {
            // 全部成功或部分成功，将创建成功的节点加入队列
            // 保存BCC资源
            log.debug(
                    "=====> taskId: {}, [AddNodeDebug] create cos stack for clusterId:{} queueName:{} success, stack: {}",
                    task.getTaskId(),
                    clusterId,
                    queueName,
                    stackEntrySet);
            log.debug("[AddNodeDebug] start saving queue instances for clusterId:{} queueName:{}", clusterId,
                    queueName);
            saveOrUpdateQueueInstances(task, cluster, queue, stackId, stackEntrySet, nodesTags);
            log.debug("[AddNodeDebug] finish saving queue instances for clusterId:{} queueName:{}", clusterId,
                    queueName);
            // 删除占位符实例信息
            List<String> instanceIds = ServiceUtil.castToList(extraMap.get(ChpcConstant.INSTANCE_IDS));
            log.debug("[AddNodeDebug] delete instances {} for clusterId:{} queueName:{}", instanceIds,
                    clusterId, queueName);
            for (String instanceId : instanceIds) {
                if (instanceId.startsWith("i-cloud-")) {
                    instanceService.delete(instanceId);
                }
            }
            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
            extraMap.put(ChpcConstant.CLUSTER_TYPE, cluster.getClusterType());

            return extraMap;

        } else if (StackStatus.ROLLBACK_SUCCESS.name().equalsIgnoreCase(stackResultStatus)
                || StackStatus.ROLLBACK_FAILED.name().equalsIgnoreCase(stackResultStatus)
                || (StackStatus.FAILED.name().equalsIgnoreCase(stackResultStatus) && !hasSuccess)) {
            // 回滚或全部节点都创建失败
            // 手动扩容或自动扩容时，扩容失败不影响已有的集群和队列
            log.debug(
                    "=====> taskId: {}, [AddNodeDebug] create cos stack for clusterId:{} queueName:{} not success, stack: {}",
                    task.getTaskId(),
                    clusterId,
                    queueName,
                    stackEntrySet);
            if (extraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)
                    || extraMap.containsKey(ChpcConstant.MANUAL_SCALING_FLAG)) {

                // 自动扩容场景，扩容失败，更新自动扩容规则为正常，不影响下次扩容
                if (extraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)) {
                    AutoScaling autoScaling = new AutoScaling();
                    autoScaling.setAsId((String) extraMap.get(ChpcConstant.AUTO_SCALING_FLAG));
                    autoScaling.setStatus(AutoScalingStatus.NORMAL);
                    autoScalingDAOGateway.update(autoScaling);
                }

                if (StackStatus.ROLLBACK_FAILED.name().equalsIgnoreCase(stackResultStatus)) {
                    log.warn("CHPC_MONITOR cos rollback failed");
                }

                log.warn("CHPC_MONITOR auto or manual scaling failed");

                // 删除占位符实例信息
                List<String> instanceIds = ServiceUtil.castToList(extraMap.get(ChpcConstant.INSTANCE_IDS));
                log.debug("[AddNodeDebug] delete instances {} for clusterId:{} queueName:{}", instanceIds,
                        clusterId, queueName);
                for (String instanceId : instanceIds) {
                    if (instanceId.startsWith("i-cloud-")) {
                        instanceService.delete(instanceId);
                    }
                }

                extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
                extraMap.put(ChpcConstant.TASK_FAILED_REASON,
                        String.format("cos stack [%s] failed: %s", stackId, stackResultStatus));
                extraMap.put(ChpcConstant.CLUSTER_TYPE, cluster.getClusterType());
                return extraMap;
            }

            // 创建失败更新集群和队列状态
            // *更新所有队列状态为失败（队列列表放在 task 的extra 中）
            Object queueIdsObj = extraMap.get(ChpcConstant.QUEUE_IDS);
            if (queueIdsObj instanceof List<?>) {
                @SuppressWarnings("unchecked")
                List<String> queueIds = (List<String>) queueIdsObj;
                // 在这里可以继续使用queueIds，但请注意，如果列表包含非String元素，在后续使用中可能会抛出ClassCastException
                for (String queueId : queueIds) {
                    queueDAOGateway.updateStatus(queueId, QueueStatus.CREATE_FAILED.nameLowerCase());
                }
            }

            // 集群初始化时需要跟更新集群状态
            // *根据 source 来判断是否为集群初始化
            if (TaskSourceType.INIT_CLUSTER.getTaskSourceType().equals(task.getSource())) {
                String errorMessage = "the cos of " + task.getCosStackId() + " is " + stackResultStatus + ",Reason:";
                for (ResourceOperation entry : failedReasons) {
                    errorMessage = errorMessage + String.format(" [%s error]: %s;", entry.getName(), entry.getError());
                }
                clusterService.updateClusterWithErrorMessage(cluster.getClusterId(),
                        ClusterStatus.CREATE_FAILED.nameLowerCase(), errorMessage);
                extraMap.put(ChpcConstant.TASK_FAILED_REASON, errorMessage);
            }

            extraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
            extraMap.put(ChpcConstant.CLUSTER_TYPE, cluster.getClusterType());
            return extraMap;
        }

        return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.PROCESSING.getValue());
    }

    /**
     * {@inheritDoc}
     * 在执行任务后，根据任务的执行结果（成功或失败），将任务信息保存到数据库中。
     * 如果任务执行成功，则将任务信息保存到数据库中，并且根据集群类型（hybrid）是否需要更新事件状态。
     * 如果任务执行失败，则将任务信息保存到数据库中，并且根据集群类型（hybrid）是否需要更新事件状态。
     *
     * @param task    任务对象，包含任务的相关信息
     * @param paraMap 参数map，包含任务的一些参数，例如任务状态、集群类型等
     * @throws Exception 可能会抛出异常，例如数据库操作异常等
     */
    @Override
    public void afterExecuteTask(final Task task, final Map<String, Object> paraMap) {

        String executeStatus = String.valueOf(paraMap.get(ChpcConstant.TASK_STATUS));

        log.debug("[AddNodeDebug] after execute task for taskId:{}, clusterId:{}, queueName:{}, stackId:{}",
                task.getTaskId(),
                task.getClusterId(),
                task.getQueueId(),
                task.getCosStackId());

        if (TaskStatus.SUCCEED.getValue().equalsIgnoreCase(executeStatus)) {
            TaskSourceType taskSourceType = TaskSourceType.fromType(task.getSource());
            log.debug(
                    "[AddNodeDebug] after execute task, task status is succeed, resource type:{}, clusterId:{}, queueId:{}",
                    taskSourceType, task.getClusterId(), task.getQueueId());
            taskService.insert(
                    this.convertTo(taskSourceType),
                    task.getClusterId(),
                    task.getQueueId(),
                    TaskSourceType.ADD_RESOURCE_TO_CLUSTER.getTaskSourceType(),
                    task.getTaskUuid(),
                    paraMap,
                    task.getCosStackId());
            log.debug("[AddNodeDebug-stackId:{}] insert task type:{}, clusterId:{}",
                    task.getCosStackId(), this.convertTo(taskSourceType), task.getClusterId());
            // *更新事件状态
            clusterEventDAOGateway.update(task.getClusterId(), EventType.CREATE_RESOURCE, EventType.SUCCEED, "");

        } else if (TaskStatus.FAILED.getValue().equalsIgnoreCase(executeStatus)) {
            log.debug("[AddNodeDebug] after execute task, task status is failed, clusterId:{}", task.getClusterId());

            paraMap.put(ChpcConstant.SOURCE_TASK_ID, task.getTaskId());
            taskService.insert(TaskType.GROUP_SAVE_FAILED_TASK, task.getClusterId(),
                    task.getQueueId(),
                    TaskSourceType.ADD_RESOURCE_TO_CLUSTER.getTaskSourceType(),
                    task.getTaskUuid(),
                    paraMap,
                    task.getCosStackId());
            // * 更新事件状态
            String taskFailedReason = String.valueOf(paraMap.get(ChpcConstant.TASK_FAILED_REASON));
            log.debug("[AddNodeDebug] after execute task, update event status to FAILED:{}, clusterId:{}",
                    taskFailedReason, task.getClusterId());
            clusterEventDAOGateway.update(task.getClusterId(), EventType.CREATE_RESOURCE, EventType.FAILED,
                    taskFailedReason);

            if (paraMap.containsKey(ChpcConstant.AUTO_SCALING_FLAG)) {
                // 推送BCT事件
                try {
                    PushAutoScalingBctEventRequest request = new PushAutoScalingBctEventRequest();
                    request.setUserId(LogicUserService.getAccountId());
                    Cluster cluster = clusterService.findByAll(task.getClusterId());
                    request.setClusterId(task.getClusterId());
                    request.setClusterName(cluster.getName());
                    Queue queue = queueDAOGateway.getByQueueId(task.getQueueId());
                    request.setQueueName(queue.getName());
                    Map<String, Object> extraMap = JacksonUtil.decodeToMap(task.getExtra(), Object.class);
                    String msg = String.format("当前队列存在作业排队，需要 %d 节点，因此自动扩容，新建节点",
                            (Integer) (extraMap.getOrDefault(ChpcConstant.ADD_INSTANCE_COUNT, 1)));
                    request.setMessage(msg);
                    request.setSuccess(false);
                    request.setAsId(String.valueOf(paraMap.get(ChpcConstant.AUTO_SCALING_FLAG)));
                    chpcGateway.pushAutoExpandBctEvent(request);
                } catch (WebClientResponseException e) {
                    log.warn("push auto expand bct event error, err:{}", e.getResponseBodyAsString());
                } catch (Exception e) {
                    log.warn("push auto expand bct event error, err:{}", e.getMessage());
                }
            }
        }
    }

    private TaskType convertTo(TaskSourceType taskSourceType) {

        switch (taskSourceType) {
            case INIT_CLUSTER:
                return TaskType.GROUP_STARTED_TASK;

            case ADD_GROUP_TO_CLUSTER:
            case ADD_INSTANCE_TO_CLUSTER:
                return TaskType.GROUP_JOIN_TASK;

            default:
                return null;
        }
    }

    private void saveOrUpdateQueueInstances(Task task, Cluster cluster, Queue queue, String stackId,
                                            Set<Map.Entry<String, StackResult.Instance>> stackEntrySet, List<com.baidu.bce.logic.chpc.tag.Tag> nodesTags) {
        List<Instance> instances = new ArrayList<>();

        for (Map.Entry<String, StackResult.Instance> entry : stackEntrySet) {

            StackResult.Instance value = entry.getValue();
            // !v3 接口中失败的资源单独放在 failedReasons中，所以这里不需要过滤了
            // if (!StackStatus.SUCCESS.name().equalsIgnoreCase(value.getState())) {
            // continue;
            // }
            if ("BCE::BCC::Instance".equalsIgnoreCase(value.getType())) {

                String instanceObject = value.getId();
                // Map<String, Object> maps = (Map<String, Object>) instanceObject;
                BccInstanceDetail bccDetail = bccExternalGateway.getBccServerDetail(instanceObject);

                if (nodesTags != null && nodesTags.size() > 0) {
                    // 标签操作失败，不能影响节点新增流程
                    try {
                        List<com.baidu.bce.logical.tag.sdk.model.Tag> tags = BeanCopyUtil.copyListProperties(nodesTags, com.baidu.bce.logical.tag.sdk.model.Tag::new);
                        tagsGateway.createTags(tags);

                        for (Tag tag : bccDetail.getTags()) {
                            com.baidu.bce.logical.tag.sdk.model.Tag bccTag = new com.baidu.bce.logical.tag.sdk.model.Tag();
                            bccTag.setTagKey(tag.getTagKey());
                            bccTag.setTagValue(tag.getTagValue());
                            tags.add(bccTag);
                        }
                        CreateAndAssignTagRequest request = new CreateAndAssignTagRequest();
                        List<AssignResource> resources = new ArrayList<>();
                        AssignResource resource = new AssignResource();
                        resource.setServiceType("BCC");
                        resource.setResourceId(bccDetail.getInstanceId());
                        resource.setResourceUuid(bccDetail.getInstanceUuid());
                        resource.setRegion(regionConfiguration.getCurrentRegion());
                        resource.setAssociationType("floating");
                        resource.setTags(tags);
                        resources.add(resource);
                        request.setResources(resources);
                        tagsGateway.createAndAssignTag(request);
                    } catch (Exception e) {
                        log.debug(
                                "create clustertags fail,clusterId:{} ,err:{}", cluster.getClusterId(), e.getMessage());
                    }
                }
                // BccInstanceDetail bccDetail =
                // bccGateway.getBccInstanceDetail(instanceObject);
                if (stackId.startsWith("st-exist-")) {
                    bccDetail.setNodeType(InstanceNodeType.COMPUTE.getType());
                    List<Tag> tags = new ArrayList<>();
                    Tag typeTag = new Tag();
                    typeTag.setTagKey("nodeType");
                    typeTag.setTagValue("compute");
                    Tag fromTag = new Tag();
                    fromTag.setTagKey("from");
                    fromTag.setTagValue("chpc");
                    tags.add(typeTag);
                    tags.add(fromTag);
                    bccDetail.setTags(tags);
                }

                Instance instance = new Instance();
                instance.setClusterId(cluster.getClusterId());
                instance.setInstanceId(bccDetail.getInstanceId());
                instance.setInstanceUuid(bccDetail.getInstanceUuid());
                // * 从 cos 的 tag 中获取队列
                String queueId = "";
                log.debug("=====> taskId: {}, [AddNodeDebug] clusterId:{} instanceObj:{} bccDetail:{}",
                        task.getTaskId(), task.getClusterId(), instanceObject, bccDetail);
                if (bccDetail.getTags() != null) {
                    for (Tag tag : bccDetail.getTags()) {
                        if ("queueId".equalsIgnoreCase(tag.getTagKey())) {
                            queueId = tag.getTagValue();
                        }
                        if (ChpcConstant.INSTANCE_NODE_TYPE.equalsIgnoreCase(tag.getTagKey())) {
                            instance.setNodeType(tag.getTagValue());
                        }
                    }
                } else {
                    Map<String, Object> attrs = value.getProperties();
                    // 获取 attrs 中的tags 属性，转化为 List<Tag>
                    Object tagsObj = attrs.getOrDefault("tags", null);
                    log.debug("=====> taskId: {}, [AddNodeDebug] clusterId:{} instanceObj:{} tagObj:{}",
                            task.getTaskId(), task.getClusterId(), instanceObject, tagsObj);
                    if (tagsObj != null) {
                        if (tagsObj instanceof List<?>) {
                            List<Tag> tags = JacksonUtil.decodeToList(JacksonUtil.encode(tagsObj), Tag.class);
                            log.debug("=====> taskId: {}, [AddNodeDebug] clusterId:{} instanceObj:{} tagObj:{}",
                                    task.getTaskId(), task.getClusterId(), instanceObject, tags);
                            for (Tag tag : tags) {
                                if (ChpcConstant.INSTANCE_NODE_TYPE.equalsIgnoreCase(tag.getTagKey())) {
                                    bccDetail.setNodeType(tag.getTagValue());
                                }
                                if ("queueId".equalsIgnoreCase(tag.getTagKey())) {
                                    queueId = tag.getTagValue();
                                }
                            }
                        }
                    }
                    log.debug("=====> nodeType: {}, queueId: {}", bccDetail.getNodeType(), queueId);
                }

                if (queue != null && !StringUtils.isEmpty(queueId)) {
                    instance.setQueueId(queueId);
                } else {
                    // 设置为默认队列的 queueId
                    instance.setQueueId(task.getQueueId());
                }
                instance.setNodeType(bccDetail.getNodeType());
                instance.setEipNetworkCapacity(
                        (Integer) value.getProperties().getOrDefault("networkCapacityInMbps", 0));
                instance.setCosStackId(stackId);
                instance.setHostName(bccDetail.getHostName());
                instance.setStatus(InstanceStatus.WAITING_TO_START.nameLowerCase());
                instance.setOosExecutionId("");
                instance.setSubnetId(bccDetail.getSubnetId());
                instance.setPrivateIp(bccDetail.getPrivateIp());
                instance.setPublicIp(bccDetail.getPublicIp());
                instance.setFloatingIp(bccDetail.getFloatingIp());
                instance.setSpec(bccDetail.getSpec());
                instance.setChargeType(bccDetail.getProductType());

                if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType()) &&
                        InstanceNodeType.MASTER.getType().equalsIgnoreCase(bccDetail.getNodeType())) {
                    // *混合云集群，插入 scheduler ip 和 scheduler host
                    Map<String, Object> extraMap = JacksonUtil.decodeToMap(cluster.getExtra(),
                            String.class, Object.class);
                    instance.setSchedulerIp(String.valueOf(extraMap.getOrDefault("schedulerIp", "")));
                    instance.setSchedulerHost(String.valueOf(extraMap.getOrDefault("schedulerHost", "")));
                }
                // 登录节点，记录sshPort和portalPort
                if (InstanceNodeType.LOGIN.getType().equalsIgnoreCase(bccDetail.getNodeType())) {
                    Map<String, Object> extraMap = JacksonUtil.decodeToMap(cluster.getExtra(),
                            String.class, Object.class);
                    if (!StringUtils.isEmpty(String.valueOf(extraMap.getOrDefault("sshPort", "")))) {
                        instance.setSshPort(Long.parseLong(String.valueOf(extraMap.getOrDefault("sshPort", ""))));
                    }
                    if (!StringUtils.isEmpty(String.valueOf(extraMap.getOrDefault("portalPort", "")))) {
                        instance.setPortalPort(Long.parseLong(String.valueOf(extraMap.getOrDefault("portalPort", ""))));
                    }
                }

                log.debug("=====> taskId: {}, add instance: {}", task.getTaskId(), instance.getInstanceId());
                instances.add(instance);
            } else if ("CFS".equalsIgnoreCase(value.getType())) {
                // *CFS 这个分支已经不用了
                String cfsId = value.getId();
                if (StringUtils.isEmpty(cfsId)) {
                    continue;
                }

                if (cfsService.isExisted(cluster.getClusterId(), cfsId)) {
                    continue;
                }

                FileSystem fileSystem = cfsGateway
                        .getCfsFileSystem(cfsId)
                        .stream()
                        .filter(f -> f.getFsId().equals(cfsId))
                        .findFirst()
                        .get();

                Cfs cfs = new Cfs();
                cfs.setClusterId(cluster.getClusterId());
                cfs.setCfsId(cfsId);
                cfs.setCfsType(fileSystem.getType());
                cfs.setStorageProtocol(fileSystem.getProtocol());
                cfs.setName(fileSystem.getFsName());

                MountTarget mountTarget = fileSystem
                        .getMountTargets()
                        .stream()
                        .filter(m -> cluster.getSubnetId().equalsIgnoreCase(m.getSubnetId()))
                        .findFirst()
                        .orElse(null);

                Map<String, Object> extraMap = JacksonUtil.decodeToMap(cluster.getExtra(), String.class, Object.class);

                if (mountTarget != null) {
                    cfs.setMountTarget(mountTarget.getDomain());
                }

                cfs.setMountDir(String.valueOf(extraMap.getOrDefault("mountDirectory", "/mnt/cfs")));

                cfsService.insert(cfs);
            } else {
                log.debug("instance type not support: {}", value.getType());
            }
        }

        instanceService.batchInsert(instances);
    }
}
