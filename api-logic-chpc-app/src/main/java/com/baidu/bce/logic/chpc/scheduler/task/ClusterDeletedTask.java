package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.JobTemplateDAOGateway;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.model.Cfs;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: lilu24
 * @Date: 2023-01-03
 */
@Slf4j
@Component(value = "cluster_deleted_task")
public class ClusterDeletedTask extends AbstractSchedulerTask {

    @Resource
    private CfsDAOGateway cfsDAOGateway;

    @Resource
    private TagsDAOGateway tagsDAOGateway;

    @Resource
    private TagsGateway tagsGateway;

    @Resource
    private JobTemplateDAOGateway jobTemplateDAOGateway;

    /**
     * {@inheritDoc}
     * 执行任务，包括：
     * 1. 获取集群ID
     * 2. 查询该集群下的所有队列
     * 3. 如果存在队列，则查询该集群下的所有实例（包括后付费和前付费），并获取其COS Stack ID
     * 4. 如果存在COS Stack ID，则删除这些COS Stack
     * 5. 如果存在实例，则更新所有BCC实例为已删除状态
     * 6. 如果存在CFS，则更新所有CFS为已删除状态
     * 7. 如果存在队列，则更新所有队列为已删除状态
     * 8. 更新集群为已删除状态
     * 9. 返回一个只含有{@link ChpcConstant#TASK_STATUS}的Map，值为{@link TaskStatus#SUCCEED}
     *
     * @param task    任务对象
     * @param cluster 集群对象
     * @param queue   队列对象
     * @return 一个只含有{@link ChpcConstant#TASK_STATUS}的Map，值为{@link TaskStatus#SUCCEED}
     * @throws Exception 如果任务执行失败，可能会抛出异常
     */
    @Override
    public Map<String, Object> executeTask(final Task task, final Cluster cluster, final Queue queue) {

        String clusterId = cluster.getClusterId();

        // 暂时不支持指定自定义CFS，必须通过CFS创建

        List<Queue> queueList = queueDAOGateway.listByClusterId(clusterId);

        if (queueList != null && queueList.size() > 0) {
            List<Instance> instanceList = new ArrayList<>();
            for (Queue g : queueList) {
                instanceList.addAll(instanceService.findBy(clusterId, g.getQueueId()));
            }

            Set<String> cosStackIds = instanceList.stream()
                    .filter(instance -> ChargeType.Postpaid.name().equalsIgnoreCase(instance.getChargeType())) // 过滤后付费
                    .map(Instance::getCosStackId)
                    .filter(StringUtils::isNotEmpty)
                    .filter(stackId -> !stackId.startsWith("st-exist-"))
                    .collect(Collectors.toSet());

            log.debug("[CLUSTER_DELETED_TASK] the need delete cos stack is: {}", JacksonUtil.toJson(cosStackIds));
            if (CollectionUtils.isNotEmpty(cosStackIds)) {
                // 删除COS实例
                try {
                    cosStackIds.forEach(cosStackId -> cosGateway.deleteStack(cosStackId));
                } catch (Exception e) {
                    log.error("delete cos stack failed, the error is: {}", e);
                }
            }

            if (CollectionUtils.isNotEmpty(instanceList)) {
                // 更新所有BCC实例为已删除状态
                try {
                    instanceList.forEach(instance -> instanceService.delete(instance.getInstanceId()));
                } catch (Exception e) {
                    log.error("delete instance failed, the error is: {}", e);
                }
            }

            // 更新所有CFS实例为已删除状态
            List<Cfs> cfsList = cfsDAOGateway.findBy(clusterId, null);
            ;
            if (CollectionUtils.isNotEmpty(cfsList)) {

                cfsList.forEach(cfs -> cfsDAOGateway.delete(clusterId, cfs.getCfsId()));
            }

            // 更新所有Group实例为已删除状态
            // queueList.forEach(q -> queueDAOGateway.delete(q.getQueueId()));
            queueList.forEach(q -> {
                queueDAOGateway.delete(q.getQueueId());
                // 如果队列存在自动伸缩策略--删除策略
                AutoScaling autoScaleRecord = autoScalingDAOGateway.getByQueueId(q.getQueueId(), LogicUserService.getAccountId());
                if (autoScaleRecord != null) {
                    autoScalingDAOGateway.delete(autoScaleRecord.getAsId());
                }
            });
        }
        // 删除集群下所有的作业模版
        jobTemplateDAOGateway.deleteAllJobTemplatesByCluster(clusterId);


        // 删除标签
        tagsDAOGateway.deleteByClusterId(cluster.getClusterId(), "queue");
        tagsDAOGateway.deleteByClusterId(cluster.getClusterId(), "localNode");

        // 解绑集群tag
        try {
            CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
            List<AssignResource> resources = new ArrayList<>();
            AssignResource resource = new AssignResource();
            resource.setServiceType("CHPC");
            resource.setResourceId(clusterId);
            resource.setResourceUuid(clusterId);
            resource.setTags(new ArrayList<>());
            resource.setRegion(regionConfiguration.getCurrentRegion());
            resource.setAssociationType("floating");
            resources.add(resource);
            createAndAssignTagRequest.setResources(resources);
            tagsGateway.createAndAssignTag(createAndAssignTagRequest);
        } catch (Exception e) {
            log.debug(
                    "unbind clustertags fail,clusterId:{} ,err:{}", clusterId, e.getMessage());
        }

        // 更新集群已删除状态
        clusterService.delete(clusterId);

        log.debug("delete cluster {} succeed", clusterId);

        return Collections.singletonMap(ChpcConstant.TASK_STATUS, TaskStatus.SUCCEED.getValue());
    }

}
