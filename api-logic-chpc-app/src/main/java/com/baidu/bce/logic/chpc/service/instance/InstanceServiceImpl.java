package com.baidu.bce.logic.chpc.service.instance;

import com.baidu.bce.internalsdk.cos.model.CdsDiskForCreate;
import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.internalsdk.iam.model.IamEncryptResponse;
import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import com.baidu.bce.logic.chpc.bcc.BccInstance;
import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.ca.gateway.CaGateway;
import com.baidu.bce.logic.chpc.ca.model.CreateActionRequest;
import com.baidu.bce.logic.chpc.ca.model.CreateActionResponse;
import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.common.AutoScalingNodeType;
import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.common.BeanCopyUtil;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.ClusterSchedulerType;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.CommonValidateUtil;
import com.baidu.bce.logic.chpc.common.InstanceAttribution;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.InstanceStatus;
import com.baidu.bce.logic.chpc.common.NumberUtils;
import com.baidu.bce.logic.chpc.common.QueueStatus;
import com.baidu.bce.logic.chpc.common.TaskSourceType;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.common.TaskType;
import com.baidu.bce.logic.chpc.common.ZoneUtil;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.common.charge.ChargeUtil;
import com.baidu.bce.logic.chpc.common.charge.PeriodUnit;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.common.validator.AesDecryptUtil;
import com.baidu.bce.logic.chpc.common.validator.NetworkValidator;
import com.baidu.bce.logic.chpc.cos.CreateStackRequest;
import com.baidu.bce.logic.chpc.cos.CreateStackResponse;
import com.baidu.bce.logic.chpc.cos.gateway.CosGateway;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.WhitelistGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendCommonResponse;
import com.baidu.bce.logic.chpc.iam.IamGateway;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceDeleteRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceMoveRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceOfflineRequest;
import com.baidu.bce.logic.chpc.instance.model.InstanceRebootRequest;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Instance.InstanceResource;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.Task;
import com.baidu.bce.logic.chpc.model.response.instance.DeleteInstancesResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceAddResponse;
import com.baidu.bce.logic.chpc.model.response.instance.InstanceTagsResponse;
import com.baidu.bce.logic.chpc.model.response.queue.QueueVO;
import com.baidu.bce.logic.chpc.oos.CreateOosExecutionRequest;
import com.baidu.bce.logic.chpc.oos.gateway.OosGateway;
import com.baidu.bce.logic.chpc.service.IInstanceService;
import com.baidu.bce.logic.chpc.service.IQueueService;
import com.baidu.bce.logic.chpc.service.util.DiskTypeUtil;
import com.baidu.bce.logic.chpc.service.util.PasswordUtil;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.chpc.tag.Tag;
import com.baidu.bce.logic.chpc.tag.UpdateInstanceTagsRequest;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logic.chpc.user.Const;
import com.baidu.bce.logic.core.exception.CommonExceptions;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.logic.core.util.UuidUtil;
import com.baidu.bce.logical.tag.sdk.model.AssignResource;
import com.baidu.bce.logical.tag.sdk.model.CreateAndAssignTagRequest;
import com.baidu.bce.logical.tag.sdk.model.FullTagListRequest;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFull;
import com.baidu.bce.logical.tag.sdk.model.TagAssociationFulls;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.baidubce.services.bcc.model.InstanceModel;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.baidu.bce.logic.core.iam.service.LogicUserService.getAccountId;

@Slf4j
@Service
public class InstanceServiceImpl implements IInstanceService {

    @Resource
    ClusterDAOGateway clusterDAOGateway;

    @Resource
    QueueDAOGateway queueDAOGateway;

    @Resource
    InstanceDAOGateway instanceDAOGateway;

    @Resource
    CosGateway cosGateway;

    @Resource
    TaskService taskService;

    @Resource
    TagsDAOGateway tagsDAOGateway;

    @Resource
    TagsGateway tagsGateway;

    @Resource
    protected RegionConfiguration regionConfiguration;

    @Resource
    WhitelistGateway whitelistGateway;

    @Resource
    SubnetGateway subnetGateway;

    @Resource
    BackendGateway backendGateway;

    @Resource
    ZoneUtil zoneUtil;

    @Resource
    PasswordUtil passwordUtil;

    @Resource
    NetworkValidator networkValidator;

    @Resource
    BccGateway bccGateway;

    @Resource
    protected InstanceService instanceService;


    @Resource
    IamGateway iamGateway;

    @Resource
    CaGateway caGateway;

    @Resource
    OosGateway oosGateway;

    @Value("${bce.web.commons.bos.bucket.endpoint}")
    private String bosEndpoint;

    @Override
    public InstanceAddResponse addInstanceToCluster(String clusterId, String queueName, InstanceAddRequest request) {
        return addInstanceToCluster(clusterId, queueName, request, null);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InstanceAddResponse addInstanceToCluster(
            String clusterId, String queueName, InstanceAddRequest request, AutoScaling autoScaling) {

        validateInstanceAddRequest(clusterId, request);
        String bctMsg;
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        // 集群子网和请求扩容节点对子网必须二选一
        if (StringUtils.isEmpty(cluster.getSubnetId()) && StringUtils.isEmpty(request.getSubnetId())) {
            throw new CommonException.RequestInvalidException("The cluster does not have a subnetId, therefore the request must include a subnetId");
        }
        Queue queue = new Queue();
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            // 混合云队列不入库，直接从插件获取
            List<QueueVO> queueVOList = listQueues(clusterId);
            Optional<QueueVO> queueVO =
                    queueVOList.stream().filter(qVO -> queueName.equals(qVO.getQueueName())).findFirst();
            if (!queueVO.isPresent()) {
                log.warn(
                        "[AddNodeDebug] add instance to clusterId:{} failed, queue:{} is not exist", clusterId, queueName);
                throw new IllegalArgumentException(String.format("hybrid queue %s is not exist", queueName));
            }

            queue.setClusterId(clusterId);
            queue.setName(queueName);
            if (StringUtils.isNotEmpty(queueVO.get().getQueueId())) {
                queue.setQueueId(queueVO.get().getQueueId());
            } else {
                queue.setQueueId("q-hybrid-" + UUID.randomUUID().toString());
            }
        } else {
            queue = queueDAOGateway.getByQueueName(queueName, clusterId);
            if (queue == null) {
                log.warn(
                        "[AddNodeDebug] add instance to clusterId:{} failed, queue:{} is not exist", clusterId, queueName);
                throw new IllegalArgumentException(String.format("cloud queue %s is not exist", queueName));
            }
        }

        log.debug("[AddNodeDebug] add instance to clusterId:{}, queue:{}, request:{}", clusterId, queue, request);

        Map<String, Object> extraMap = new HashMap<>();
        if (autoScaling != null) {
            extraMap.put(ChpcConstant.AUTO_SCALING_FLAG, autoScaling.getAsId());
        } else {
            extraMap.put(ChpcConstant.MANUAL_SCALING_FLAG, "manual_scaling_flag");
        }

        List<String> queueIds = new ArrayList<>();
        List<String> cudaVersions = new ArrayList<>();
        List<String> gpuDriverVersions = new ArrayList<>();
        List<String> cudnnVersions = new ArrayList<>();
        queueIds.add(queue.getQueueId());
        cudaVersions.add(request.getCudaVersion());
        gpuDriverVersions.add(request.getGpuDriverVersion());
        cudnnVersions.add(request.getCudnnVersion());
        extraMap.put(ChpcConstant.QUEUE_IDS, queueIds);
        extraMap.put(ChpcConstant.QUEUE_NAME, queueName);
        extraMap.put(ChpcConstant.CHPC_CUDA_VERSION, cudaVersions);
        extraMap.put(ChpcConstant.CHPC_GPU_DRIVER_VERSION, gpuDriverVersions);
        extraMap.put(ChpcConstant.CHPC_CUDNN_VERSION, cudnnVersions);
        extraMap.put(ChpcConstant.QUEUE_NAME, queueName);
        extraMap.put(ChpcConstant.ADD_INSTANCE_COUNT, request.getCount());

        String cosStackId = null;
        String taskUuid = UUID.randomUUID().toString();
        String queueId = queue.getQueueId();

        if (AutoScalingNodeType.LOCAL.equals(request.getNodeSource())) {
            log.debug(
                    "[AddNodeDebug] start to add node to clusterId:{} with exist user local instance {}",
                    clusterId,
                    request.getNodeHosts());

            // 构造一个虚拟 CosStack 资源
            cosStackId = "st-local-" + taskUuid.substring(0, 8);

            // 解析节点 Hostname
            String spec = "local";

            // 注册节点
            String[] nodeHosts = request.getNodeHosts().split(",");
            if (nodeHosts.length == 0) {
                log.warn("[AddNodeDebug] add instance to clusterId:{} failed, nodeHosts is empty", clusterId);
                throw new IllegalArgumentException("nodeHosts is empty");
            }

            // 操作入库，列表展示的时候以调度器中的为准，数据库中只用来补充展示
            log.debug(
                    "[AddNodeDebug] insert instance to clusterId:{} with exist user local instance, nodeHosts:{}",
                    clusterId,
                    nodeHosts);
            List<Instance> instances = new ArrayList<>();
            for (String nodeHost : nodeHosts) {
                Instance instance = new Instance();
                instance.setClusterId(clusterId);
                instance.setInstanceId("i-local-" + taskUuid.substring(0, 8));
                instance.setQueueId(queueId);
                instance.setHostName(nodeHost);
                instance.setStatus(InstanceStatus.WAITING_TO_MANUAL_START.nameLowerCase());
                instance.setNodeType(InstanceNodeType.COMPUTE.getType());
                instance.setChargeType(ChargeType.Unknown.name());
                instance.setAttribution(InstanceAttribution.LOCAL.getType());
                instance.setSpec(spec);
                instance.setCosStackId(cosStackId);
                instances.add(instance);
            }
            instanceService.batchInsert(instances);
            log.debug(
                    "[AddNodeDebug] finish instance instance to clusterId:{} with exist user local instance, instances:{}",
                    clusterId,
                    instances);
            //todo 如果中间某个节点加入失败，但是前面的节点已经加入成功了，会出现数据不一致的情况
            if (ClusterSchedulerType.PBS.getName().equalsIgnoreCase(cluster.getSchedulerType())
                    || ClusterSchedulerType.OPENPBS.getName().equalsIgnoreCase(cluster.getSchedulerType())) {
                for (String nodeHost : nodeHosts) {

                    BackendActionProxyResponse resp =
                            backendGateway.actionProxy(
                                    cluster.getClusterId(), "node_register", String.format("--host=%s", nodeHost));
                    if (resp.getCode() != 200) {
                        log.debug(
                                "[AddNodeDebug] clusterId:{} register local node failed, {}", clusterId, resp.getMessage());
                        throw new CommonException.RelatedServiceException(
                                "register local node failed, " + resp.getMessage(), "scheduler plugin");
                    }
                    resp =
                            backendGateway.actionProxy(
                                    cluster.getClusterId(),
                                    "node_create",
                                    String.format("--host=%s --queue=%s --spec=%s", nodeHost, queue.getName(), spec));
                    if (resp.getCode() != 200) {
                        log.debug("[AddNodeDebug] create local node failed, {}", clusterId, resp.getMessage());
                        throw new CommonException.RelatedServiceException(
                                "create local node failed, " + resp.getMessage(), "scheduler plugin");
                    }
                    // 本地节点，绑定chpc的标签
                    try {
                        for (Tag tag : request.getTags()) {
                            tagsDAOGateway.insert(LogicUserService.getAccountId(), clusterId, "localNode", nodeHost,
                                    tag.getTagKey(), tag.getTagValue());
                        }
                    } catch (Exception e) {
                        throw new BceException(e.getMessage());
                    }
                }
            }
            //bct 操作详情
            bctMsg = String.format("集群%s（ID: %s），队列%s，扩容本地节点%s", cluster.getName(),
                    cluster.getClusterId(), queueName, String.join("，", nodeHosts));
        } else if (AutoScalingNodeType.CLOUD_EXIST.equals(request.getNodeSource())) {
            log.debug(
                    "[AddNodeDebug] start to add node to clusterId:{} queueName:{} with exist bcc instance",
                    clusterId,
                    queueName);

            // 构造一个虚拟 CosStack 资源
            cosStackId = "st-exist-" + taskUuid.substring(0, 8);

            // 操作入库，列表展示的时候以调度器中的为准，数据库中只用来补充展示
            log.debug(
                    "[AddNodeDebug] insert instance to clusterId:{} with exist cloud instance, instanceIds:{}",
                    clusterId,
                    request.getInstanceIds());
            List<Instance> instances = new ArrayList<>();
            for (String instanceId : request.getInstanceIds()) {
                Instance instance = new Instance();
                instance.setClusterId(clusterId);
                instance.setInstanceId(instanceId);
                instance.setQueueId(queueId);
                instance.setHostName("i-exist-" + instanceId.substring(3));
                instance.setStatus(InstanceStatus.WAITING_TO_START.nameLowerCase());
                instance.setNodeType(InstanceNodeType.COMPUTE.getType());
                instance.setChargeType(ChargeType.Unknown.name());
                instance.setAttribution(InstanceAttribution.CLOUD.getType());
                instance.setSpec(request.getSpec());
                instance.setCosStackId(cosStackId);
                instances.add(instance);
            }
            instanceService.batchInsert(instances);
            log.debug(
                    "[AddNodeDebug] finish insert instance to clusterId:{} with exist cloud instance, instances:{}",
                    clusterId,
                    instances);

            // 批量重装系统
            String password = cluster.getPassword();
            if (StringUtils.isEmpty(password)) {
                log.warn(
                        "[AddNodeDebug] add instance to clusterId:{} password is empty, will generate a random one",
                        clusterId);
                password = passwordUtil.generatePassword(12);
            } else {
                password = AesDecryptUtil.decrypt(password);
            }
            // 解析出明文
            bccGateway.rebuildBatchBccInstance(
                    request.getInstanceIds(), request.getImageId(), password, cluster.getKeypairId());

            log.debug(
                    "[AddNodeDebug] finish rebuild batch bcc instance, clusterId:{}  queueName:{}, instanceIds:{}",
                    clusterId,
                    queueName,
                    request.getInstanceIds());

            extraMap.put(ChpcConstant.INSTANCE_IDS, request.getInstanceIds());
            // 云上已有节点，是云上tag
            if (request.getTags() != null && request.getTags().size() > 0) {
                extraMap.put(ChpcConstant.NODES_TAGS, request.getTags());
            }

            // 资源创建任务
            taskService.insert(
                    TaskType.GROUP_RESOURCE_SAVE_TASK,
                    clusterId,
                    queueId,
                    TaskSourceType.ADD_INSTANCE_TO_CLUSTER.getTaskSourceType(),
                    taskUuid,
                    extraMap,
                    cosStackId);

            log.debug(
                    "[AddNodeDebug-stackId:{}] create ADD_INSTANCE_TO_CLUSTER task with exist cloud instance, clusterId:{}, taskId:{}, queueName:{}, queueId:{}",
                    cosStackId,
                    clusterId,
                    taskUuid,
                    queueName,
                    queueId);
            bctMsg = String.format("集群%s（ID: %s），队列%s，扩容已有节点IDs: %s", cluster.getName(),
                    cluster.getClusterId(), queueName, String.join("，", request.getInstanceIds()));
        } else {
            log.debug("[AddNodeDebug] start to create instance in clusterId:{} cos with new bcc instance", clusterId);
            String stackName = "stack-" + UuidUtil.generateShortUuid();

            // *1.数据先入库
            // 操作入库，列表展示的时候以调度器中的为准，数据库中只用来补充展示
            log.debug(
                    "[AddNodeDebug] insert instance to clusterId:{} with new cloud instance, cosStackId:{}, stackName:{}",
                    clusterId, cosStackId, stackName);
            List<Instance> instances = new ArrayList<>();
            List<String> instanceIds = new ArrayList<>();
            for (int i = 0; i < request.getCount(); i++) {
                Instance instance = new Instance();
                instance.setClusterId(clusterId);
                if (cosStackId != null && !cosStackId.isEmpty()) {
                    instance.setInstanceId("i-cloud-" + cosStackId.substring(3) + "-" + i);
                } else {
                    // 如果没有 cosStackId 或其为空，则使用其他方式生成 instanceId
                    instance.setInstanceId("i-cloud-default-" + UuidUtil.generateShortUuid() + i);
                }
                instance.setQueueId(queueId);
                instance.setStatus(InstanceStatus.CREATING.nameLowerCase());
                instance.setNodeType(InstanceNodeType.COMPUTE.getType());
                instance.setChargeType(ChargeType.Postpaid.name());
                instance.setAttribution(InstanceAttribution.CLOUD.getType());
                instance.setSpec(request.getSpec());
                // 无论 cosStackId 是否为空，都设置到 instance 中
                instance.setCosStackId(cosStackId + "_pre_generate");
                instances.add(instance);
                instanceIds.add(instance.getInstanceId());
            }
            instanceService.batchInsert(instances);
            extraMap.put(ChpcConstant.INSTANCE_IDS, instanceIds);
            extraMap.put(ChpcConstant.COS_STACK_NAME, stackName);

            // 资源创建任务
            taskService.insert(
                    TaskType.GROUP_RESOURCE_SAVE_TASK,
                    clusterId,
                    queueId,
                    TaskSourceType.ADD_INSTANCE_TO_CLUSTER.getTaskSourceType(),
                    taskUuid,
                    extraMap,
                    cosStackId);

            log.debug(
                    "[AddNodeDebug] create ADD_INSTANCE_TO_CLUSTER task with new cloud instance, clusterId:{}, stackId:{}, taskId:{}, queueName:{}, queueId:{}",
                    clusterId,
                    cosStackId,
                    taskUuid,
                    queueName,
                    queueId);

            if (StringUtils.isEmpty(request.getPassword()) && (StringUtils.isNotEmpty(cluster.getPassword()) ||
                    StringUtils.isNotEmpty(cluster.getKeypairId()))) {

                if (StringUtils.isNotEmpty(cluster.getKeypairId())) {
                    request.setKeypairId(cluster.getKeypairId());
                }
                if (StringUtils.isNotEmpty(cluster.getPassword())) {
                    request.setPassword(cluster.getPassword());
                }
            }

            // *2.调用 cos 请求，这里用AES加密的密码，cos中会统一解密
            CreateStackResponse createStackResponse = createInstanceInCos(request, cluster, queue, autoScaling != null,
                    stackName);
            if (!createStackResponse.isSuccess()) {
                throw new CommonException.CosServiceException(createStackResponse.getMsg());
            }
            cosStackId = createStackResponse.getResult().getId();
            log.debug("[AddNodeDebug] finish creating instance in cos, clusterId:{}, stackId:{}", clusterId,
                    cosStackId);

            // *3.更新 cosid
            Boolean success = taskService.updateCosStackIdByTaskUuid(taskUuid, cosStackId);
            log.debug("update cos stackId to t_chpc_task success: {}", success);
            //bct 操作详情
            bctMsg = String.format("集群%s（ID: %s），队列%s，新建%d个%s的节点",
                    cluster.getName(), cluster.getClusterId(), queueName, request.getCount(), request.getSpec());
        }

        queue.setStatus(QueueStatus.WAITING_TO_JOIN.nameLowerCase());
        queueDAOGateway.update(queue);

        InstanceAddResponse instanceAddResponse = new InstanceAddResponse();
        instanceAddResponse.setClusterId(clusterId);
        instanceAddResponse.setQueueName(queueName);
        instanceAddResponse.setStatus(InstanceStatus.CREATING.nameLowerCase());
        instanceAddResponse.setTaskId(taskUuid);
        instanceAddResponse.setClusterName(cluster.getName());
        instanceAddResponse.setMessage(bctMsg);
        return instanceAddResponse;
    }

    private List<QueueVO> listQueues(String clusterId) {
        if (StringUtils.isEmpty(clusterId)) {
            return null;
        }
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        if (cluster == null) {
            return null;
        }
        List<QueueVO> queueList;
        queueList =
                queueDAOGateway
                        .listByClusterId(clusterId)
                        .stream()
                        .map(ServiceUtil::convertToGroupVO)
                        .collect(Collectors.toList());
        if (cluster.getSchedulePlugin() == 0) {
            for (QueueVO queue : queueList) {
                queue.setInstanceCount(
                        instanceService.countByClusterIdAndQueueIdAndNodeType(
                                clusterId, queue.getQueueId(), InstanceNodeType.COMPUTE));
            }
        } else {
            try {
                BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "queue_list", "--console");
                ObjectMapper mapper = new ObjectMapper();
                queueList =
                        mapper.readValue(
                                resp.getData(), mapper.getTypeFactory().constructCollectionType(List.class, QueueVO.class));
                // 补充队列的isAutoScale属性和queueId
                for (QueueVO queue : queueList) {
                    Queue dbQueue = queueDAOGateway.getByQueueName(queue.getQueueName(), clusterId);
                    if (dbQueue != null) {
                        queue.setIsAutoScale(dbQueue.getIsAutoScale());
                        queue.setDescription(dbQueue.getDescription());
                        queue.setQueueId(dbQueue.getQueueId());
                        if (ClusterType.CLOUD.nameLowerCase().equals(cluster.getClusterType())) {
                            queue.setIsDefault(dbQueue.getIsDefault());
                        }
                        queue.setHasAutoScale(dbQueue.getHasAutoScale());
                    }
                }
            } catch (Exception e) {
                log.error("listQueues error", e);
                return queueList;
            }
        }
        return queueList;
    }

    @Override
    public DeleteInstancesResponse deleteInstanceFromCluster(String clusterId, InstanceDeleteRequest request) {
        return deleteInstanceFromCluster(clusterId, request, null);
    }

    @Override
    public DeleteInstancesResponse updateInstanceTag(String clusterId, UpdateInstanceTagsRequest request) {

        List<String> hostnames = new ArrayList<>();
        hostnames.add(request.getHostName());
        List<Instance> allByClusterIdAndHostNames = instanceDAOGateway.findAllByClusterIdAndHostNames(clusterId, hostnames);
        if (allByClusterIdAndHostNames != null && allByClusterIdAndHostNames.size() > 0) {
            // 能查到是云上节点,更新云上tag
            if (request.getTags() != null) {
                for (int i = 0; i < allByClusterIdAndHostNames.size(); i++) {
                    // 标签操作失败，不能影响主流程
                    try {
                        List<com.baidu.bce.logical.tag.sdk.model.Tag> tags = new ArrayList<>();
                        if (request.getTags().size() > 0) {
                            tags = BeanCopyUtil.copyListProperties(request.getTags(), com.baidu.bce.logical.tag.sdk.model.Tag::new);
                            tagsGateway.createTags(tags);
                        }
                        CreateAndAssignTagRequest createAndAssignTagRequest = new CreateAndAssignTagRequest();
                        List<AssignResource> resources = new ArrayList<>();
                        AssignResource resource = new AssignResource();
                        resource.setServiceType("BCC");
                        resource.setResourceId(allByClusterIdAndHostNames.get(i).getInstanceId());
                        resource.setResourceUuid(allByClusterIdAndHostNames.get(i).getInstanceUuid());
                        resource.setRegion(regionConfiguration.getCurrentRegion());
                        resource.setAssociationType("floating");
                        resource.setTags(tags);
                        resources.add(resource);
                        createAndAssignTagRequest.setResources(resources);
                        tagsGateway.createAndAssignTag(createAndAssignTagRequest);
                    } catch (Exception e) {
                        log.debug(
                                "updateInstanceTag fail, clusterId:{} ,hostName:{} ,err:{}", clusterId, request.getHostName(), e.getMessage());
                    }
                }
            }

        } else {
            // 查不到，说明是本地节点,从db删除，并且新增
            tagsDAOGateway.deleteByClusterIdAndName(clusterId, "localNode", request.getHostName());
            if (request.getTags() != null) {
                List<com.baidu.bce.logical.tag.sdk.model.Tag> tagList = new ArrayList<>();
                for (int i = 0; i < request.getTags().size(); i++) {
                    com.baidu.bce.logical.tag.sdk.model.Tag bceTag = new com.baidu.bce.logical.tag.sdk.model.Tag();
                    bceTag.setTagKey(request.getTags().get(i).getTagKey());
                    bceTag.setTagValue("");
                    tagList.add(bceTag);
                    tagsDAOGateway.insert(LogicUserService.getAccountId(), clusterId, "localNode", request.getHostName(),
                            request.getTags().get(i).getTagKey(), request.getTags().get(i).getTagValue());
                }
                try {
                    if (tagList.size() > 0) {
                        tagsGateway.createTags(tagList);
                    }
                } catch (Exception e) {
                    log.debug(
                            "updateQueueTags fail, clusterId:{} ,hostname:{} ,err:{}", clusterId, request.getHostName(), e.getMessage());
                }
            }
        }

        DeleteInstancesResponse deleteInstancesResponse = new DeleteInstancesResponse();
        deleteInstancesResponse.setClusterId(clusterId);
        return deleteInstancesResponse;
    }

    @Override
    public DeleteInstancesResponse deleteInstanceFromCluster(
            String clusterId, InstanceDeleteRequest request, AutoScaling autoScaling) {

        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        if (cluster == null) {
            log.warn("delete instance from cluster failed, no cluster found by clusterId:{}", clusterId);
            throw new CommonException.ResourceNotExistException(clusterId + " does not exist.");
        }

        List<Instance> instanceList;
        if (CollectionUtils.isEmpty(request.getHostnames())) {
            log.warn("delete instance from cluster clusterId:{} failed, instances is empty;", clusterId);
            throw new IllegalArgumentException("instances is empty");
        }
        if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())) {
            // 混合云用插件获取节点列表
            String action = "node_list_all";
            String arguments = "";
            BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, action, arguments);
            ObjectMapper mapper = new ObjectMapper();
            List<Instance> allInstances;
            try {
                allInstances =
                        mapper.readValue(
                                resp.getData(), mapper.getTypeFactory().constructCollectionType(List.class, Instance.class));
            } catch (Exception e) {
                log.error("node list all error", e);
                throw new BceException(e.getMessage());
            }

            // 补充云上数据库中节点
            List<Instance> cloudInstanceList =
                    instanceDAOGateway.findAllByClusterIdAndHostNames(clusterId, request.getHostnames());

            List<Instance> schedulerComputeInstance = allInstances;
            allInstances.addAll(
                    cloudInstanceList
                            .stream()
                            .filter(
                                    onFlyInst ->
                                            schedulerComputeInstance
                                                    .stream()
                                                    .noneMatch(inst -> StringUtils.equals(onFlyInst.getHostName(), inst.getHostName())))
                            .collect(Collectors.toList()));
            // 更新allInstances中的每个元素的InstanceId和chargeType字段
            allInstances.forEach(
                    instance -> {
                        Optional<Instance> matchingInstance =
                                cloudInstanceList
                                        .stream()
                                        .filter(
                                                cloudInstance ->
                                                        StringUtils.equals(instance.getHostName(), cloudInstance.getHostName()))
                                        .findFirst();

                        matchingInstance.ifPresent(cloudInstance -> instance.setInstanceId(cloudInstance.getInstanceId()));
                        matchingInstance.ifPresent(cloudInstance -> instance.setChargeType(cloudInstance.getChargeType()));
                    });

            instanceList =
                    allInstances
                            .stream()
                            .filter(instance -> request.getHostnames().contains(instance.getHostName()))
                            .collect(Collectors.toList());
            instanceList.forEach(instance -> instance.setClusterId(clusterId));
        } else {
            // 公有云从数据库获取节点列表
            instanceList = instanceDAOGateway.findAllByClusterIdAndHostNames(clusterId, request.getHostnames());
        }

        if (CollectionUtils.isEmpty(instanceList) || instanceList.size() != request.getHostnames().size()) {
            log.warn(
                    "delete instance failed, same instance not exist in cluster; clusterId:{}, instanceList:{}, tarInstanceList:{}",
                    clusterId,
                    instanceList,
                    request.getHostnames());
            throw new CommonException.ResourceNotExistException("same instance not exist in cluster");
        }

        if (autoScaling == null) {
            List<String> otherInstances =
                    instanceList
                            .stream()
                            .filter(instance -> !Objects.equals(instance.getClusterId(), cluster.getClusterId()))
                            .map(Instance::getInstanceId)
                            .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(otherInstances)) {
                throw new CommonException.ResourceNotExistException(
                        "instanceId " + StringUtils.join(otherInstances, ",") + " does not exist. ");
            }
            List<String> masterNodes =
                    instanceList
                            .stream()
                            .filter(instance -> instance.getNodeType().equalsIgnoreCase(InstanceNodeType.MASTER.name()))
                            .map(Instance::getInstanceId)
                            .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(masterNodes)) {
                throw new CommonExceptions.RequestInvalidException(
                        "cannot delete master instance " + StringUtils.join(masterNodes, ","));
            }
        }

        if (autoScaling != null) {
            // 自动伸缩不能删除预付费的计算节点和管控节点
            instanceList = instanceList
                    .stream()
                    .filter(instance -> instance.getNodeType().equalsIgnoreCase(InstanceNodeType.COMPUTE.name()))
                    .filter(instance -> instance.getClusterId().equalsIgnoreCase(clusterId))
                    .filter(instance -> instance.getChargeType() == null || ChargeType.Postpaid.name().equalsIgnoreCase(instance.getChargeType()))
                    .collect(Collectors.toList());
        } else {
            // 主动删除不能删除管控节点
            instanceList = instanceList
                    .stream()
                    .filter(instance -> instance.getNodeType().equalsIgnoreCase(InstanceNodeType.COMPUTE.name()))
                    .filter(instance -> instance.getClusterId().equalsIgnoreCase(clusterId))
                    .collect(Collectors.toList());
        }


        Set<String> queueIds = instanceList.stream().map(Instance::getQueueId).collect(Collectors.toSet());

        List<Queue> queueList = queueDAOGateway.findAllByQueueIds(queueIds);

        Map<String, String> queueId2Name =
                queueList.stream().collect(Collectors.toMap(Queue::getQueueId, Queue::getName));

        if (CollectionUtils.isEmpty(instanceList)) {
            log.warn("delete instance from cluster failed, no valid instance to delete; clusterId:{}", clusterId);
            throw new IllegalArgumentException("no valid instance to delete");
        }

        // 修改返回值，包含bct审计信息
        DeleteInstancesResponse deleteInstancesResponse = new DeleteInstancesResponse();
        deleteInstancesResponse.setClusterId(clusterId);
        deleteInstancesResponse.setClusterName(cluster.getName());
        List<String> bctMessages = new ArrayList<>();

        for (Instance instance : instanceList) {
            BackendCommonResponse backendResponse =
                    backendGateway.removeNode(
                            clusterId,
                            queueId2Name.get(instance.getQueueId()),
                            instance.getHostName(),
                            instance.getInstanceId(),
                            autoScaling != null,
                            request.isForce());
            CommonValidateUtil.validBackendCommonResponse(backendResponse);

            // 此时优先需要更新数据库中的实例状态为删除中
            // 云下节点的instanceId为 i-local-xxx
            if (instanceService.isExisted(clusterId, null, instance.getInstanceId(), instance.getHostName(), null)) {
                log.debug(
                        "[AddNodeDebug] clusterId:{} update instance status to deleting, instanceId:{}",
                        clusterId,
                        instance.getInstanceId());
                instanceService.updateStatusAndExecutionId(
                        instance.getInstanceId(), "", InstanceStatus.DELETING.nameLowerCase());
            }

            Map<String, Object> extraMap = new HashMap<>();
            extraMap.put(ChpcConstant.BACKEND_TASK_ID, backendResponse.getTaskId());
            extraMap.put(ChpcConstant.BACKEND_INSTANCE_ID, instance.getInstanceId());
            extraMap.put(ChpcConstant.BACKEND_INSTANCE_UUID, instance.getInstanceUuid());
            extraMap.put(ChpcConstant.BACKEND_HOSTNAME, instance.getHostName());
            if (instance.getCosStackId() != null) {
                extraMap.put(ChpcConstant.BACKEND_COS_STACK_ID, instance.getCosStackId());
            }
            if (request.isRelease() && ChargeType.Postpaid.name().equalsIgnoreCase(instance.getChargeType())) {
                extraMap.put(ChpcConstant.RELEASE_RESOURCE_FLAG, request.isRelease());
            }
            if (autoScaling != null) {
                extraMap.put(ChpcConstant.AUTO_SCALING_FLAG, autoScaling.getAsId());
            }

            String taskId = UUID.randomUUID().toString();
            taskService.insert(
                    TaskType.BACKEND_ASYNC_TASK,
                    clusterId,
                    instance.getQueueId(),
                    TaskSourceType.REMOVE_INSTANCE_FROM_CLUSTER.getTaskSourceType(),
                    taskId,
                    extraMap);
            // 记录手动缩容节点的bct消息
            // 混合云集群中instance信息直接从调度器获取，此时是没有queueId的，但是有queueName
            String queueName = "";
            if (StringUtils.isNotEmpty(instance.getQueueName())) {
                queueName = instance.getQueueName();
            } else if (StringUtils.isNotEmpty(instance.getQueueId())) {
                Queue queue = queueDAOGateway.getByQueueId(instance.getQueueId());
                queueName = queue.getName();
            }
            String bctMessage = String.format("队列: %s，手动缩容节点id: %s，hostname: %s", queueName, instance.getInstanceId(), instance.getHostName());
            bctMessages.add(bctMessage);
        }
        deleteInstancesResponse.setMessage(String.join("，", bctMessages));
        return deleteInstancesResponse;
    }

    @Override
    public BaseResponse shrinkInstanceFromCluster(AutoScaling autoScaling, List<String> hostnames, int shrinkNum, int shrinkCycle) {
        if (shrinkNum == 0) {
            log.warn("[autoshrink] shrink instance from cluster clusterId:{}, shrinkNum is 0", autoScaling.getClusterId());
            return new BaseResponse();
        }

        Cluster cluster = clusterDAOGateway.findByClusterId(autoScaling.getClusterId(), getAccountId());
        if (cluster == null) {
            log.warn(
                    "[autoshrink] shrink instance from cluster failed, no cluster found by clusterId:{}", autoScaling.getClusterId());
            throw new CommonException.ResourceNotExistException("集群信息不存在");
        }

        List<Instance> instanceList;
        if (CollectionUtils.isEmpty(hostnames)) {
            log.warn(
                    "[autoshrink] shrink instance from cluster clusterId:{} failed, hostnames is empty;", autoScaling.getClusterId());
            return new BaseResponse();
        }
        // 从数据库获取节点列表
        instanceList = instanceDAOGateway.findAllByClusterIdAndHostNames(autoScaling.getClusterId(), hostnames);
        if (CollectionUtils.isEmpty(instanceList) || instanceList.size() != hostnames.size()) {
            log.warn(
                    "[autoshrink] delete instance failed, same instance not exist in cluster; clusterId:{}, instanceList:{}, tarInstanceList:{}",
                    autoScaling.getClusterId(),
                    instanceList,
                    hostnames);
            throw new IllegalArgumentException("节点信息错误");
        }

        // 不能删除预付费的计算节点和管控节点
        instanceList =
                instanceList
                        .stream()
                        .filter(instance -> instance.getNodeType().equalsIgnoreCase(InstanceNodeType.COMPUTE.name()))
                        .filter(instance -> instance.getClusterId().equalsIgnoreCase(autoScaling.getClusterId()))
                        .filter(
                                instance ->
                                        instance.getChargeType() == null
                                                || ChargeType.Postpaid.name().equalsIgnoreCase(instance.getChargeType()))
                        .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(instanceList)) {
            log.warn(
                    "[autoshrink] delete instance from cluster failed, no valid instance to delete; clusterId:{}",
                    autoScaling.getClusterId());
            return new BaseResponse();
        }
        // 创建异步任务
        Map<String, Object> extraMap = new HashMap<>();
        extraMap.put(ChpcConstant.SHRINK_HOSTNAMES, hostnames);
        extraMap.put(ChpcConstant.SHRINK_NUM, shrinkNum);
        extraMap.put(ChpcConstant.AUTO_SCALING_FLAG, autoScaling.getAsId());
        extraMap.put(ChpcConstant.SHRINK_CYCLE, shrinkCycle);
        String taskId = UUID.randomUUID().toString();
        try {
            taskService.insert(
                    TaskType.SHRINK_TASK,
                    autoScaling.getClusterId(),
                    autoScaling.getQueueId(),
                    TaskSourceType.SHRINK_NODES.getTaskSourceType(),
                    taskId,
                    extraMap);
        } catch (Exception e) {
            log.error("[autoshrink] create shrink task failed, clusterId:{}, error:{}", autoScaling.getClusterId(), e);
            throw new CommonException.RequestInvalidException("创建缩容任务失败");
        }

        return new BaseResponse();
    }

    /**
     * {@inheritDoc}
     * 如果集群使用了调度插件，则执行节点移动操作。否则抛出异常。
     * 参数request包含需要移动的实例ID和目标队列名称。
     * 返回一个BaseResponse对象，表示操作成功。
     * 如果移动实例到目标队列失败，则抛出CommonException.RequestInvalidException异常。
     *
     * @param clusterId 集群ID
     * @param request   实例移动请求，包含需要移动的实例ID和目标队列名称
     * @return BaseResponse 操作成功的BaseResponse对象
     * @throws CommonException.RequestInvalidException 如果集群使用了调度插件，且移动实例到目标队列失败，则抛出此异常
     */
    @Override
    public BaseResponse moveInstance(String clusterId, InstanceMoveRequest request) {
        // 混合云集群hostname和instanceId是相同的字段，为了兼容混合云集群/公有云集群都需要使用hostName
        BaseResponse response = new BaseResponse();
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        String bctMsg = String.format("集群%s（ID: %s），移动节点: %s到目标队列%s",
                cluster.getName(), cluster.getClusterId(), String.join("，", request.getHostnames()), request.getTargetQueue());
        response.setClusterName(cluster.getName());
        response.setMessage(bctMsg);
        if (ClusterSchedulerType.OPENPBS.name().equalsIgnoreCase(cluster.getSchedulerType())
                || ClusterSchedulerType.PBS.name().equalsIgnoreCase(cluster.getSchedulerType())
                || ClusterSchedulerType.SGE.name().equalsIgnoreCase(cluster.getSchedulerType())) {
            // pbs直接调用插件即可
            String arguments = "";
            arguments = arguments + " --target-queue " + request.getTargetQueue();
            arguments = arguments + " --nodes " + StringUtils.join(request.getHostnames(), ",");
            BackendActionProxyResponse resp;
            try {
                resp = backendGateway.actionProxy(clusterId, "node_move", arguments);
            } catch (Exception e) {
                log.error("move instance failed", e.getMessage());
                throw new CommonException.RequestInvalidException("移动实例到目标队列失败，可能由于目标队列缺少队列标签");
            }
            return response;
        } else if (ClusterSchedulerType.SLURM.name().equalsIgnoreCase(cluster.getSchedulerType())) {
            // slurm需要在计算节点执行OOS命令
            List<Instance> instances = instanceDAOGateway.findAllByClusterIdAndHostNames(clusterId, request.getHostnames());
            for (Instance instance : instances) {
                // 只能移动计算节点
                if (!instance.getNodeType().equalsIgnoreCase(InstanceNodeType.COMPUTE.name())) {
                    continue;
                }
                // 创建异步任务
                Map<String, Object> extraMap = new HashMap<>();
                extraMap.put(ChpcConstant.INSTANCE_ID, instance.getInstanceId());
                extraMap.put(ChpcConstant.TASK_RETRY_TIMES, 0);
                String taskUuid = UUID.randomUUID().toString();
                taskService.insert(
                        TaskType.OOS_ASYNC_TASK,
                        clusterId,
                        instance.getQueueId(),
                        TaskSourceType.MOVE_NODE_TO_QUEUE.getTaskSourceType(),
                        taskUuid,
                        extraMap,
                        "");
                List<Task> tasks = taskService.getByTaskUuid(null, taskUuid);
                if (tasks.size() != 1) {
                    log.error("move instance failed, task not found, taskUuid:{}", taskUuid);
                    continue;
                }
                // 更新节点状态
                instanceService.updateStatusAndExecutionId(instance.getInstanceId(), "",
                        InstanceStatus.OPERATING.nameLowerCase());
                // 执行oos命令
                try {
                    // 组装oos请求
                    Map<String, String> idMaps = new HashMap<>();
                    idMaps.put("instanceId", instance.getInstanceId());
                    List<Map<String, String>> idLists = new ArrayList<>();
                    idLists.add(idMaps);
                    Map<String, Object> properties = new HashMap<>();
                    String command =
                            String.format(
                                    "wget %s/release/pkg/amd64/slurm_move_node.sh -O slurm_move_node.sh && " +
                                            "timeout 20s bash slurm_move_node.sh %s && rm -rf slurm_move_node.sh",
                                    bosEndpoint,
                                    request.getTargetQueue());
                    properties.put("content", command);
                    properties.put("user", "root");
                    properties.put("workDir", "/");
                    // 待执行命令的虚机列表
                    properties.put("__workerSelectors__", idLists);

                    CreateOosExecutionRequest.Operator operator = new CreateOosExecutionRequest.Operator();
                    operator.setName("cloud_assist_shell");
                    operator.setOperator("BCE::Agent::ExecuteShell");
                    operator.setDescription("exec shell command");
                    operator.setTimeout(25000);
                    operator.setProperties(properties);

                    CreateOosExecutionRequest.Template template = new CreateOosExecutionRequest.Template();
                    template.setName("BCE-BCC-BulkyRunCommand");
                    template.setLinear(true);
                    template.setOperators(Collections.singletonList(operator));

                    CreateOosExecutionRequest oosRequest = new CreateOosExecutionRequest();
                    oosRequest.setTemplate(template);
                    log.debug(
                            "slurm move node begin to send oos command, instance id: {}, oos request: {}",
                            instance.getInstanceId(),
                            oosRequest.toString());
                    String executionId = oosGateway.createExecution(oosRequest);
                    log.debug(
                            "slurm move node oos command sent, instance id: {}, executionId: {}",
                            instance.getInstanceId(),
                            executionId);
                    // 更新任务OOS执行ID
                    extraMap.put(ChpcConstant.OOS_EXECUTION_ID, executionId);
                    taskService.updateExtra(tasks.get(0).getTaskId(), extraMap);
                } catch (Exception e) {
                    // 恢复节点状态
                    instanceService.updateStatusAndExecutionId(instance.getInstanceId(), "",
                            InstanceStatus.STARTED.nameLowerCase());
                    // 更新任务为失败
                    taskService.updateStatus(tasks.get(0).getTaskId(), TaskStatus.FAILED.name());
                    log.error(
                            "slurm move node call oos service failed, instanceId:{}, exception:{}",
                            instance.getInstanceId(),
                            e);
                    continue;
                }
            }
            return response;
        } else {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
    }

    /**
     * {@inheritDoc}
     * 实例离线，仅在使用调度插件时可用。如果不是使用调度插件，则会抛出CommonException.RequestInvalidException异常。
     * 返回值为BaseResponse，无特定业务逻辑。
     *
     * @param clusterId String 集群ID
     * @param request   InstanceOfflineRequest 实例离线请求参数，包含需要离线的实例ID列表
     * @return BaseResponse 基础响应对象，无特定业务逻辑
     * @throws CommonException.RelatedServiceException 调度插件相关服务异常，包含错误信息和调度插件
     * @throws CommonException.RequestInvalidException 请求无效异常，目前不支持此功能
     */
    @Override
    public BaseResponse instanceOffline(String clusterId, InstanceOfflineRequest request) {
        if (!ServiceUtil.isClusterUsingSchedulerPlugin(clusterDAOGateway, clusterId)) {
            throw new CommonException.RequestInvalidException("目前不支持此功能");
        }
        // 混合云集群hostname和instanceId是相同的字段，为了兼容混合云集群,公有云集群都需要使用hostName
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        // List<Instance> instances = instanceDAOGateway.findAllByClusterIdAndInstanceIds(clusterId, request.getInstanceIds());
        // List<String> hostnames = instances.stream().map(Instance::getHostName).collect(Collectors.toList());
        String arguments = "";
        arguments = arguments + " -n " + StringUtils.join(request.getHostnames(), ",");
        BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, "node_offline", arguments);
        if (resp.getCode() != 200) {
            throw new CommonException.RelatedServiceException(
                    "instance offline failed, " + resp.getMessage(), "scheduler plugin");
        }
        BaseResponse response = new BaseResponse();
        String bctMsg = String.format("集群%s（ID: %s），停止调度节点: %s",
                cluster.getName(), cluster.getClusterId(),
                StringUtils.join(request.getHostnames(), "，"));
        response.setClusterName(cluster.getName());
        response.setMessage(bctMsg);
        return response;
//        List<String> queueIDs = instances.stream().map(Instance::getQueueId).distinct().collect(Collectors.toList());
//        List<Queue> queues = queueDAOGateway.findAllByQueueIds(queueIDs);
//        Map<String, String> queueIDToQueueName = new HashMap<>();
//        for (Queue queue : queues) {
//            queueIDToQueueName.put(queue.getQueueId(), queue.getName());
//        }
//        List<String> msg = new ArrayList<>();
//        for (Instance instance : instances) {
//            msg.add(String.format("%s（ID: %s）所属队列%s", instance.getHostName(),
//                    instance.getInstanceId(), queueIDToQueueName.get(instance.getQueueId())));
//        }
//        String bctMsg = String.format("集群%s（ID: %s），停止调度节点: %s", cluster.getName(),
//                clusterId, StringUtils.join(msg, "，"));
//        response.setClusterName(cluster.getName());
//        response.setMessage(bctMsg);
//        return response;
    }

    @Override
    public BaseResponse rebootInstance(InstanceRebootRequest request) {
        log.debug("reboot instance ids: " + request.getInstanceIds());
        List<Instance> instances = instanceDAOGateway.findBy(request.getInstanceIds());
        log.debug("instances to reboot: {}", instances);
        List<String> instanceIds = new ArrayList<>();
        for (Instance instance : instances) {
            // 管理节点和登录节点不能重启
            if (InstanceNodeType.MASTER.getType().equalsIgnoreCase(instance.getNodeType()) ||
                    InstanceNodeType.LOGIN.getType().equalsIgnoreCase(instance.getNodeType())) {
                continue;
            }
            instanceIds.add(instance.getInstanceId());
        }
        if (instanceIds.isEmpty()) {
            log.debug("no instance to reboot");
            return new BaseResponse();
        }
        bccGateway.rebootBatchBccInstance(instanceIds, request.getForceStop());
        return new BaseResponse();
    }

    @Override
    public CreateActionResponse instanceCreateAction(CreateActionRequest request) {
        List<CreateActionRequest.Target> targets = new ArrayList<>();
        for (CreateActionRequest.Target target : request.getTargets()) {
            Instance instance = instanceDAOGateway.findBy(target.getInstanceId());
            if (instance == null) {
                continue;
            }
            // 管理节点不能发送命令
            if (InstanceNodeType.MASTER.getType().equalsIgnoreCase(instance.getNodeType())) {
                continue;
            }
            // 非正常节点不能发送命令
            if (!InstanceStatus.STARTED.name().equalsIgnoreCase(instance.getStatus())) {
                continue;
            }
            targets.add(target);
        }
        if (targets.isEmpty()) {
            log.debug("no instance to create action");
            return new CreateActionResponse();
        }
        request.setTargets(targets);
        return caGateway.instanceCreateAction(request);
    }

    /**
     * {@inheritDoc}
     * 获取集群中指定条件的实例列表。如果集群使用调度插件，则通过调度插件获取实例列表；否则从数据库中查询实例列表。
     * 可以根据节点类型、队列名称、主机名称、主机IP地址、调度状态等筛选实例列表。
     * 如果需要同步BCC信息，则在返回的实例列表中包含BCC相关信息。
     *
     * @param clusterId      集群ID，不能为空
     * @param nodeType       节点类型，可选参数，默认值为null，表示不按节点类型筛选
     * @param hostName       主机名称，可选参数，默认值为null，表示不按主机名称筛选
     * @param queueName      队列名称，可选参数，默认值为null，表示不按队列名称筛选
     * @param hostIP         主机IP地址，可选参数，默认值为null，表示不按主机IP地址筛选
     * @param scheduleStatus 调度状态，可选参数，默认值为null，表示不按调度状态筛选
     * @param syncBccInfo    是否同步BCC信息，可选参数，默认值为false，表示不同步BCC信息
     * @return {@code List<Instance>} 符合条件的实例列表，包含实例ID、节点类型、主机名称、队列ID、队列名称、主机IP地址、调度状态等信息
     * @throws BceException 当发生错误时抛出该异常
     */
    @Override
    public List<Instance> getClusterInstances(
            String clusterId,
            String nodeType,
            String hostName,
            String queueName,
            String hostIP,
            String scheduleStatus,
            Boolean syncBccInfo) {
        List<Instance> instances = new ArrayList<>();
        List<Instance> filteredInstances = new ArrayList<>();
        if (ServiceUtil.isClusterUsingSchedulerPlugin(clusterDAOGateway, clusterId)) {
            String action = "node_list_all";
            String arguments = "";
            if (StringUtils.isNotEmpty(queueName)) {
                // action = "node_list_by_queue"; 该功能集成到调度器插件node_list_all中
                arguments = arguments + " --queue " + queueName;
            }
            if (StringUtils.isNotEmpty(hostName)) {
                arguments = arguments + " --host " + hostName;
            }
            // 调度器插件没有使用nodeType属性
            if (StringUtils.isNotEmpty(nodeType)) {
                arguments = arguments + " -t " + nodeType;
            }
            if (StringUtils.isNotEmpty(hostIP)) {
                arguments = arguments + " --host-ip " + hostIP;
            }
            // 调度器插件还没有实现
            if (StringUtils.isNotEmpty(scheduleStatus)) {
                arguments = arguments + " --schedule-status " + scheduleStatus;
            }
            try {
                BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, action, arguments);
                ObjectMapper mapper = new ObjectMapper();
                instances =
                        mapper.readValue(
                                resp.getData(), mapper.getTypeFactory().constructCollectionType(List.class, Instance.class));
            } catch (Exception e) {
                log.error("node list all error", e);
                if (e instanceof WebClientResponseException) {
                    WebClientResponseException webClientException = (WebClientResponseException) e;
                    // 如果 e.getMessage() 中包含 No master instance，返回空
                    if (webClientException.getResponseBodyAsString().contains("No master instance")) {
                        return instances;
                    }
                }
                throw new BceException(e.getMessage());
            }
            instances.forEach(instance -> instance.setAttribution(InstanceAttribution.LOCAL.getType()));
            for (Instance instance : instances) {
                instance.setClusterId(clusterId);
                log.debug("=====>instanceId current: {}", instance.getInstanceId());
                List<String> hostnames = new ArrayList<>();
                hostnames.add(instance.getHostName());
                List<Instance> cloudInstance = instanceDAOGateway.findAllByClusterIdAndHostNames(clusterId, hostnames);
                if (cloudInstance != null && cloudInstance.size() > 0) {
                    log.info("=====>instanceId exists: " + cloudInstance.get(0).getInstanceId());
                    instance.setAttribution(InstanceAttribution.CLOUD.getType());
                    instance.setCreatedTime(cloudInstance.get(0).getCreatedTime());
                    instance.setInstanceId(cloudInstance.get(0).getInstanceId());
                    instance.setInstanceUuid(cloudInstance.get(0).getInstanceUuid());
                    if (cloudInstance.get(0).getStatus().equals(InstanceStatus.DELETING.nameLowerCase())
                            || cloudInstance.get(0).getStatus().equals(InstanceStatus.WAITING_TO_START.nameLowerCase())
                            || cloudInstance.get(0).getStatus().equals(InstanceStatus.OPERATING.nameLowerCase())) {
                        instance.setStatus(cloudInstance.get(0).getStatus());
                    }
                    log.info("=====>instance: " + instance);
                }
            }
            if (syncBccInfo) {
                instances = encapsulateBccInfo(instances, false);
            }
            log.debug("=====>instances: " + instances);
            // *增加管理节点和登录节点信息
            List<Instance> instanceList = instanceService.findBy(clusterId, null);
            Map<String, List<Instance>> instancesByInstanceType =
                    instanceList.stream().collect(Collectors.groupingBy(Instance::getNodeType));

            if (StringUtils.isEmpty(nodeType)) {
                // 增加 master 节点和 login 节点
                filteredInstances.addAll(instancesByInstanceType.get(InstanceNodeType.MASTER.getType()));
                if (instancesByInstanceType.get(InstanceNodeType.LOGIN.getType()) != null) {
                    filteredInstances.addAll(instancesByInstanceType.get(InstanceNodeType.LOGIN.getType()));
                }
            } else if (InstanceNodeType.MASTER.getType().equalsIgnoreCase(nodeType)
                    || InstanceNodeType.LOGIN.getType().equalsIgnoreCase(nodeType)) {
                filteredInstances.addAll(instancesByInstanceType.get(nodeType));
            }

            // 补充无法从调度器中获取到的计算节点
            List<Instance> onFlyCloudComputeInstances =
                    instancesByInstanceType.getOrDefault(InstanceNodeType.COMPUTE.getType(), new ArrayList<>());
            List<Instance> schedulerComputeInstance = instances;
            filteredInstances.addAll(
                    onFlyCloudComputeInstances
                            .stream()
                            .filter(
                                    onFlyInst ->
                                            schedulerComputeInstance
                                                    .stream()
                                                    .noneMatch(inst -> StringUtils.equals(onFlyInst.getHostName(), inst.getHostName())))
                            .collect(Collectors.toList()));

        } else {
            // TODO 数据库分页，减少BCC 压力
            filteredInstances = instanceDAOGateway.findByClusterId(clusterId);
        }
        if (syncBccInfo) {
            filteredInstances = encapsulateBccInfo(filteredInstances, true);
        }
        List<String> queueIds =
                filteredInstances.stream().map(Instance::getQueueId).distinct().collect(Collectors.toList());
        List<Queue> queues = queueDAOGateway.findAllByQueueIds(queueIds);
        Map<String, String> queueId2QueueName =
                queues
                        .stream()
                        .map(queue -> new AbstractMap.SimpleEntry<>(queue.getQueueId(), queue.getName()))
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        Cluster cluster = clusterDAOGateway.findByClusterIdAll(clusterId, getAccountId());
        for (Instance instance : filteredInstances) {
            // 管理节点，不属于任何queue
            if (StringUtils.equalsIgnoreCase(instance.getNodeType(), InstanceNodeType.MASTER.getType())
                    || StringUtils.equalsIgnoreCase(instance.getNodeType(), InstanceNodeType.LOGIN.getType())) {
                instance.setQueueName("");
            } else {
                instance.setQueueName(queueId2QueueName.get(instance.getQueueId()));
            }
            // 混合云代理节点，节点类型更新为 proxy
            if (ClusterType.HYBRID.nameLowerCase().equals(cluster.getClusterType())
                    && StringUtils.equalsIgnoreCase(instance.getNodeType(), InstanceNodeType.MASTER.getType())) {
                instance.setNodeType(InstanceNodeType.PROXY.getType());
            }

            if (StringUtils.isNotEmpty(nodeType) && !StringUtils.equalsIgnoreCase(instance.getNodeType(), nodeType)) {
                continue;
            }
            if (StringUtils.isNotEmpty(queueName)
                    && !StringUtils.equalsIgnoreCase(queueName, instance.getQueueName())) {
                continue;
            }
            if (StringUtils.isNotEmpty(hostName) && !StringUtils.containsIgnoreCase(instance.getHostName(), hostName)) {
                continue;
            }
            instance.setAttribution(InstanceAttribution.CLOUD.getType());
            instances.add(instance);
        }
        return instances;
    }

    /**
     * {@inheritDoc}
     * 获取包含预付费实例的集群ID列表。
     *
     * @param clusterIds 集群ID列表，不能为null或空列表
     * @return 包含预付费实例的集群ID列表，如果没有找到符合条件的实例则返回一个空列表
     * @throws IllegalArgumentException 如果clusterIds为null或空列表时抛出此异常
     */
    @Override
    public List<String> getContainsPrepayInstanceClusterId(List<String> clusterIds) {
        List<Instance> instances = instanceDAOGateway.findByClusterIdIn(clusterIds);
        instances = encapsulateBccInfo(instances, true);
        List<String> containsPrepayClusterIds =
                instances
                        .stream()
                        .filter(instance -> instance != null && instance.getChargeType() != null)
                        .filter(instance -> instance.getChargeType().equalsIgnoreCase(ChargeType.Prepaid.name()))
                        .map(Instance::getClusterId)
                        .distinct()
                        .collect(Collectors.toList());
        log.info("contains prepay instance clusterId: {}", containsPrepayClusterIds);

        return containsPrepayClusterIds;
    }

    public Instance getClusterManagerInstanceOrNull(String clusterId) {

        Instance masterInstance = instanceDAOGateway.findMasterInstance(clusterId);
        if (null == masterInstance) {
            return null;
        }
        masterInstance.setQueueName("");

        List<Instance> instances = new ArrayList<>();
        instances.add(masterInstance);
        instances = encapsulateBccInfo(instances, true);
        if (CollectionUtils.isEmpty(instances)) {
            return null;
        }

        return instances.get(0);
    }

    /**
     * @Description 获取集群管理器实例，返回一个LinkedHashMap，key为集群ID，value为对应的实例信息。
     * 参数clusterIds为需要查询的集群ID列表，如果为空则返回null。
     * 该方法会先查找每个集群ID对应的主节点，然后将主节点信息包装成新的实例信息并加入到结果中。
     * 最终返回的实例信息中不包含队列名称（queueName）。
     * @Param clusterIds List<String> 需要查询的集群ID列表，可以为空，为空时返回null
     * @Return {@code LinkedHashMap<String, Instance>}
     * 返回一个LinkedHashMap，key为集群ID，value为对应的实例信息，如果没有找到实例则返回null
     */
    public LinkedHashMap<String, Instance> getClusterManagerInstances(List<String> clusterIds) {
        LinkedHashMap<String, Instance> clusterManagerInstances = new LinkedHashMap<>();
        List<Instance> instances = new ArrayList<>();

        for (String clusterId : clusterIds) {
            Instance masterInstance = instanceDAOGateway.findMasterInstance(clusterId);
            if (null != masterInstance) {
                masterInstance.setQueueName("");
                instances.add(masterInstance);
            }
        }

        instances = encapsulateBccInfo(instances, true);
        if (CollectionUtils.isEmpty(instances)) {
            return null;
        }

        for (Instance instance : instances) {
            clusterManagerInstances.put(instance.getClusterId(), instance);
        }

        return clusterManagerInstances;
    }

    /**
     * {@inheritDoc}
     * 根据队列名称获取队列中的实例，并返回指定数量的实例。
     * 如果队列不存在或没有实例，则返回空列表。
     *
     * @param clusterId 集群ID，非空字符串
     * @param queueName 队列名称，非空字符串
     * @param offset    分页查询的起始位置（从0开始），正整数
     * @param limit     每次查询的最大数量，正整数，不能超过1000
     * @return 包含offset到limit之间的队列中实例信息的列表，可能为空列表
     * 每个实例都会包含BCC相关信息，如bccId、instanceType等
     * @throws IllegalArgumentException 如果clusterId、queueName、offset、limit参数为null或者小于0
     */
    @Override
    public List<Instance> getQueueInstances(String clusterId, String queueName, int offset, int limit) {
        Queue queue = queueDAOGateway.getByName(clusterId, queueName);
        List<Instance> instances =
                instanceDAOGateway.findByClusterIdAndQueueId(clusterId, queue.getQueueId(), offset, limit);
        instances = encapsulateBccInfo(instances, true);
        return instances;
    }

    @Override
    public List<Instance> getAllQueueInstances(String clusterId, String queueName) {
        List<Instance> instances = new ArrayList<>();
        if (ServiceUtil.isClusterUsingSchedulerPlugin(clusterDAOGateway, clusterId)) {
            String action = "node_list_all";
            String arguments = "";
            arguments = arguments + " --queue " + queueName;
            try {
                BackendActionProxyResponse resp = backendGateway.actionProxy(clusterId, action, arguments);
                ObjectMapper mapper = new ObjectMapper();
                instances =
                        mapper.readValue(
                                resp.getData(), mapper.getTypeFactory().constructCollectionType(List.class, Instance.class));
            } catch (Exception e) {
                log.error("node list all error", e);
                // 如果 e.getMessage() 中包含 No master instance，返回空
                if (e instanceof WebClientResponseException) {
                    WebClientResponseException webClientException = (WebClientResponseException) e;
                    if (webClientException.getResponseBodyAsString().contains("No master instance")) {
                        return instances;
                    }
                }
                throw new BceException(e.getMessage());
            }
            instances.forEach(instance -> instance.setAttribution(InstanceAttribution.LOCAL.getType()));
            for (Instance instance : instances) {
                instance.setClusterId(clusterId);
                log.debug("=====>instanceId current: {}", instance.getInstanceId());
                Instance cloudInstance = instanceDAOGateway.findByHostName(instance.getHostName());
                if (cloudInstance != null) {
                    log.info("=====>instanceId exists: " + cloudInstance.getInstanceId());
                    instance.setAttribution(InstanceAttribution.CLOUD.getType());
                    instance.setCreatedTime(cloudInstance.getCreatedTime());
                    instance.setInstanceId(cloudInstance.getInstanceId());
                    instance.setInstanceUuid(cloudInstance.getInstanceUuid());
                    if (cloudInstance.getStatus().equals(InstanceStatus.DELETING.nameLowerCase())
                            || cloudInstance.getStatus().equals(InstanceStatus.WAITING_TO_START.nameLowerCase())) {
                        instance.setStatus(cloudInstance.getStatus());
                    }
                    log.info("=====>instance: " + instance);
                }
            }
            instances = encapsulateBccInfo(instances, false);
            log.debug("=====>instances: " + instances);
            Queue queue = queueDAOGateway.getByName(clusterId, queueName);
            List<Instance> instanceList = instanceService.findBy(clusterId, queue.getQueueId());
            Map<String, List<Instance>> instancesByInstanceType =
                    instanceList.stream().collect(Collectors.groupingBy(Instance::getNodeType));

            // 补充无法从调度器中获取到的计算节点
            List<Instance> onFlyCloudComputeInstances =
                    instancesByInstanceType.getOrDefault(InstanceNodeType.COMPUTE.getType(), new ArrayList<>());
            List<Instance> schedulerComputeInstance = instances;
            instances.addAll(
                    onFlyCloudComputeInstances
                            .stream()
                            .filter(
                                    onFlyInst ->
                                            schedulerComputeInstance
                                                    .stream()
                                                    .noneMatch(inst -> StringUtils.equals(onFlyInst.getHostName(), inst.getHostName())))
                            .collect(Collectors.toList()));

        } else {
            Queue queue = queueDAOGateway.getByName(clusterId, queueName);
            instances = instanceDAOGateway.findAllByClusterIdAndQueueId(clusterId, queue.getQueueId());
            instances = encapsulateBccInfo(instances, true);
        }
        return instances;
    }

    /**
     * {@inheritDoc}
     * 根据给定的可用区、子网ID、实例ID和实例名称，获取所有符合条件的云主机实例。
     * 如果可用区不为空，则只返回该可用区下的实例；如果子网ID不为空，则只返回该子网下的实例；
     * 如果实例ID不为空，则只返回包含指定实例ID的实例；如果实例名称不为空，则只返回包含指定实例名称的实例。
     * 返回的实例列表中，每个实例都会被转换成Instance对象，并添加到结果列表中。
     *
     * @param zoneName      可用区名称，可以为空
     * @param subnetId      子网ID，可以为空
     * @param instanceIds   实例ID，可以为空
     * @param instanceNames 实例名称，可以为空
     * @return 符合条件的云主机实例列表，如果没有符合条件的实例，则返回一个空列表
     * 每个实例都会被转换成Instance对象，并添加到结果列表中
     */
    @Override
    public List<Instance> getAllCloudInstances(
            String zoneName, String subnetId, String instanceIds, String instanceNames) {
        List<InstanceModel> bccInstances = bccGateway.getBccInstancesByZoneName(zoneName, instanceIds, instanceNames);
        log.debug(
                "[AddNodeDebug] =====>zoneName:{} subnetId:{} instanceIds:{} instanceNames:{}",
                zoneName,
                subnetId,
                instanceIds,
                instanceNames);

        List<Instance> instances = new ArrayList<>();
        for (InstanceModel bccInst : bccInstances) {
            // 过滤特定命名方式的实例
            if (bccInst.getHostname().split("-").length > 2) {
                continue;
            }

            // 判断可用区信息
            if (StringUtils.isNotEmpty(zoneName)
                    && !StringUtils.equalsIgnoreCase(zoneUtil.getApiZoneName(zoneName), bccInst.getZoneName())) {
                log.debug(
                        "[AddNodeDebug] =====>zoneName not match: {}--{}--{}",
                        zoneName,
                        zoneUtil.getApiZoneName(zoneName),
                        bccInst.getZoneName());
                continue;
            }

            // 判断子网信息
            if (StringUtils.isNotEmpty(subnetId) && !StringUtils.equals(subnetId, bccInst.getSubnetId())) {
                log.debug("[AddNodeDebug] =====>subnetId not match: {}--{}", subnetId, bccInst.getSubnetId());
                continue;
            }

            // 判断当前节点是否已经存在于公有云集群
            Instance cloudInstance = instanceDAOGateway.findBy(bccInst.getId());
            if (cloudInstance != null) {
                log.debug("[AddNodeDebug] =====>instanceId exists: " + cloudInstance.getInstanceId());
                continue;
            }

            // 获取特定的instanceIds实例
            if (StringUtils.isNotEmpty(instanceIds) && !StringUtils.contains(instanceIds, bccInst.getId())) {
                log.debug("[AddNodeDebug] =====>instanceId not match: {}--{}", instanceIds, bccInst.getId());
                continue;
            }

            // 获取特定的instanceNames实例
            if (StringUtils.isNotEmpty(instanceNames) && !StringUtils.contains(instanceNames, bccInst.getHostname())) {
                log.debug("[AddNodeDebug] =====>instanceName not match: {}--{}", instanceNames, bccInst.getHostname());
                continue;
            }

            Instance instance = new Instance();
            instance.setInstanceId(bccInst.getId());
            instance.setVpcId(bccInst.getVpcId());
            instance.setSubnetId(bccInst.getSubnetId());
            instance.setPrivateIp(bccInst.getInternalIp());
            instance.setHostName(bccInst.getHostname());
            instance.setSpec(bccInst.getSpec());
            instance.setImageId(bccInst.getImageId());
            instance.setOsName(bccInst.getOsName());
            instance.setOsVersion(bccInst.getOsVersion());
            instances.add(instance);
        }
        return instances;
    }

    @Override
    public Long countInstances(String clusterId, String queueName) {
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, LogicUserService.getAccountId());
        if (cluster.getSchedulePlugin() == 0) {
            // 不使用调度插件，直接从数据库获取
            Queue queue = queueDAOGateway.getByName(clusterId, queueName);
            return instanceDAOGateway.count(clusterId, queue.getQueueId(), null, null, null, null);
        } else {
            // 调度插件，从调度插件获取
            BackendActionProxyResponse backendResponse = new BackendActionProxyResponse();
            String action = "node_list_by_queue";
            String arguments = String.format("--queue=%s --count", queueName);
            backendResponse = backendGateway.actionProxy(clusterId, action, arguments);
            String data = backendResponse.getData();
            return Long.parseLong(data);
        }
    }

    /**
     * {@inheritDoc}
     * 将传入的实例列表中的信息包装成BCC格式，并返回。如果excludeNotExist为true，则从结果中移除不存在于BCC的实例。
     *
     * @param instances       需要包装的实例列表，每个实例都应该有instanceId、hostName属性
     * @param excludeNotExist 是否排除不存在于BCC的实例，默认值为false
     * @return 包装后的实例列表，每个实例都会添加vpcId、vpcName、subnetName、zoneName、subnetId、publicIp、privateIp、spec等属性，
     * 其中os
     * information属性取自BCC获取到的实例信息，如imageId、osLang、osName、osType、osVersion等属性取自BCC获取到的实例信息。
     * 如果excludeNotExist为true，则不存在于BCC的实例会被移除。
     */
    @Override
    public List<Instance> encapsulateBccInfo(List<Instance> instances, Boolean excludeNotExist) {
        if (CollectionUtils.isEmpty(instances)) {
            return new ArrayList<>();
        }

        List<String> instanceIds = instances.stream().map(Instance::getInstanceId).collect(Collectors.toList());
        List<BccInstance> bccInstances = bccGateway.getBccInstances(instanceIds);

        Map<String, BccInstance> bccId2Instance = new HashMap<>();
        for (BccInstance bccInstance : bccInstances) {
            bccId2Instance.put(bccInstance.getInstanceId(), bccInstance);
        }
        List<Instance> notExistInstances = new ArrayList<>();

        for (Instance instance : instances) {
            if (instance.getInstanceId() == null
                    || instance.getInstanceId().startsWith("i-local-")
                    || instance.getInstanceId().startsWith("i-cloud-")) {
                instance.setInstanceId("-");
                instance.setHostName("-");
                continue;
            }

            if (instance.getHostName() == null || instance.getHostName().startsWith("i-exist-")) {
                instance.setHostName("-");
                continue;
            }

            BccInstance tempInstance = bccId2Instance.get(instance.getInstanceId());
            if (tempInstance == null) {
                log.debug("=====> instance not exist in bcc: {}", instance.getInstanceId());
                notExistInstances.add(instance);
                continue;
            }

            instance.setVpcId(tempInstance.getVpcId());
            instance.setVpcName(tempInstance.getVpcName());
            instance.setSubnetName(tempInstance.getSubnetName());
            instance.setZoneName(tempInstance.getLogicalZone());
            instance.setSubnetId(tempInstance.getSubnetId());
            instance.setPublicIp(tempInstance.getEip());
            instance.setEipNetworkCapacity(tempInstance.getEipSize());
            instance.setPrivateIp(tempInstance.getInternalIp());
            instance.setHostName(tempInstance.getHostName());
            instance.setSpec(tempInstance.getSpec());

            // 节点初始化成功后，状态以BCC为准
            if (instance.getStatus().equalsIgnoreCase(InstanceStatus.STARTED.nameLowerCase())) {
                instance.setStatus(tempInstance.getStatus().toLowerCase());
            }

            instance.setChargeType(tempInstance.getProductType());
            // 后付费虚机无expireTime为空
            if (tempInstance.getExpireTime() != null) {
                instance.setExpireTime(tempInstance.getExpireTime().toLocalDateTime());
            }

            // os information
            instance.setImageId(tempInstance.getImageId());
            instance.setOsLang(tempInstance.getOsLang());
            instance.setOsName(tempInstance.getOsName());
            instance.setOsType(tempInstance.getOsType());
            instance.setOsVersion(tempInstance.getOsVersion());
            if (instance.getTotalResource() == null) {
                InstanceResource totalResource = instance.new InstanceResource();
                log.debug("=====>bcc {}: {}", instance.getInstanceId(), tempInstance);
                Pattern pattern = Pattern.compile("c(\\d+)m(\\d+)"); // 匹配 c 后面的数字和 m 后面的数字
                Matcher matcher = pattern.matcher(tempInstance.getSpec());

                if (matcher.find()) {
                    int cpu = Integer.parseInt(matcher.group(1)); // 第一个括号内的内容
                    totalResource.setCpu(cpu);
                }

                totalResource.setMemory(String.format("%dGB", tempInstance.getMemoryMb()));
                instance.setTotalResource(totalResource);
            }
        }
        if (excludeNotExist) {
            instances.removeAll(notExistInstances);
        }
        return instances;
    }

    @Override
    public List<Instance> getInstancesTags(List<Instance> instances) {
        FullTagListRequest fullTagListRequest = new FullTagListRequest();
        List<String> regions = new ArrayList<>();
        regions.add(regionConfiguration.getCurrentRegion());
        fullTagListRequest.setRegions(regions);
        List<String> resourceIds = new ArrayList<>();
        List<String> resourceUuids = new ArrayList<>();

        for (Instance instance : instances) {

            // 本地节点
            if (instance.getAttribution().equals("local")) {
                List<Tag> localNodeTags = tagsDAOGateway.findTags(getAccountId(), instance.getClusterId(), "localNode", instance.getHostName());
                log.debug("=====>localNodeTags {}", localNodeTags);
                instance.setTags(localNodeTags);
            } else if (instance.getAttribution().equals("cloud")) {
                // 云上节点
                resourceIds.add(instance.getInstanceId());
                resourceUuids.add(instance.getInstanceUuid());
            }
        }

        fullTagListRequest.setResourceIds(resourceIds);
        fullTagListRequest.setResourceUuids(resourceUuids);
        List<String> serviceTypes = new ArrayList<>();
        // 云上节点使用bcc的标签
        serviceTypes.add("BCC");
        fullTagListRequest.setServiceTypes(serviceTypes);
        TagAssociationFulls tagAssociationFulls = null;
        try {
            tagAssociationFulls = tagsGateway.listTags(fullTagListRequest);
        } catch (Exception e) {
            log.error("[instances {}]: get tags failed, err {}", resourceIds, e.getMessage());
        }

        for (Instance instance : instances) {
            // 云上节点
            if (instance.getAttribution().equals("cloud")) {
                if (tagAssociationFulls != null) {
                    List<Tag> tags = new ArrayList<>();
                    for (TagAssociationFull tagAssociationFull : tagAssociationFulls.getTagAssociationFulls()) {
                        if (tagAssociationFull.getResourceId().equals(instance.getInstanceId())) {
                            Tag tag = new Tag();
                            tag.setTagKey(tagAssociationFull.getTagKey());
                            tag.setTagValue(tagAssociationFull.getTagValue());
                            tags.add(tag);
                        }
                    }
                    instance.setTags(tags);
                }
            }
        }
        return instances;
    }

    @Override
    public InstanceTagsResponse listTags() {

        FullTagListRequest fullTagListRequest = new FullTagListRequest();
        List<com.baidu.bce.logical.tag.sdk.model.Tag> tagList = null;
        try {
            tagList = tagsGateway.listTagsV2(fullTagListRequest);
        } catch (Exception e) {
            log.error("listTags failed, err {}", e.getMessage());
        }

        Set<String> set = new HashSet<>();
        for (com.baidu.bce.logical.tag.sdk.model.Tag tag : tagList) {
            set.add(tag.getTagKey());
        }

        List<Tag> tagRes = new ArrayList<>();

        for (String key : set) {
            List<Tag> dbTags = tagsDAOGateway.findTagsByKey(getAccountId(), "localNode", key);
            if (dbTags != null && dbTags.size() > 0) {
                for (int i = 0; i < dbTags.size(); i++) {
                    if (!contains(tagRes, dbTags.get(i).getTagKey(), dbTags.get(i).getTagValue())) {
                        tagRes.add(dbTags.get(i));
                    }
                }
            } else {
                Tag tag = new Tag();
                tag.setTagKey(key);
                tagRes.add(tag);
            }
        }
        InstanceTagsResponse instanceTagsResponse = new InstanceTagsResponse();
        instanceTagsResponse.setResult(tagRes);
        instanceTagsResponse.setPageNo(1);
        instanceTagsResponse.setPageSize(tagRes.size());
        instanceTagsResponse.setTotalCount(tagRes.size());
        return instanceTagsResponse;
    }

    private Boolean contains(List<Tag> tags, String tagKey, String tagValue) {
        for (Tag tag : tags) {
            if (tag.getTagKey().equals(tagKey) && tag.getTagValue().equals(tagValue)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @Description: 验证实例添加请求，包括付费类型、时长、时长单位、quota、子网和安全组等参数的合法性。
     * @Param clusterId String 集群ID
     * @Param request InstanceAddRequest 实例添加请求对象，包含付费类型、时长、时长单位、quota、子网和安全组等信息
     * @Return void 无返回值
     * @Throws IllegalArgumentException 如果任何一个参数不合法，则抛出IllegalArgumentException异常
     */
    public void validateInstanceAddRequest(String clusterId, InstanceAddRequest request) {
        // 检验付费类型、时长、时长单位
        ChargeUtil.validateChargingTypeAndPeriod(request.getChargeType(), request.getPeriod(), request.getPeriodUnit());

        // 校验quota
        validateQuota(clusterId, request);

        // 校验子网 TODO 安全组校验
        Map<String, String> subnetOrZoneMap =
                networkValidator.validateSubnetAndVpc(clusterId, request.getSubnetId(), request.getZoneName());
        if (subnetOrZoneMap.containsKey("subnetId")) {
            request.setSubnetId(subnetOrZoneMap.get("subnetId"));
        }
        if (subnetOrZoneMap.containsKey("zoneName")) {
            request.setZoneName(subnetOrZoneMap.get("zoneName"));
        }
    }

    /**
     * @param clusterId 集群ID
     * @param request   添加实例请求对象，包含需要创建的计算节点数量
     * @throws CommonException.QuotaException 如果超出配额，抛出QuotaException异常
     * @Description 验证计算节点配额是否超出限制，如果超出则抛出异常
     */
    public void validateQuota(String clusterId, InstanceAddRequest request) {
        Map<String, String> quotaType2Quota =
                whitelistGateway.getQuota(LogicUserService.getAccountId(), Const.Quota.CHPC_COMPUTE_NODE_QUOTA);

        int computeNodeQuota =
                NumberUtils.parseIntWithDefault(
                        quotaType2Quota.get(Const.Quota.CHPC_COMPUTE_NODE_QUOTA),
                        Const.DefaultQuota.CHPC_COMPUTE_NODE_DEFAULT_QUOTA);
        Cluster cluster = clusterDAOGateway.findByClusterId(clusterId, getAccountId());
        int clusterComputeNodeQuota = cluster.getMaxNodes();

        // Long createdComputeCount = instanceDAOGateway.countComputeNode(clusterId, InstanceNodeType.COMPUTE.name());
        Long createdComputeCount =  instanceDAOGateway.count(clusterId, null, null,
                null, null, InstanceNodeType.COMPUTE.name());
        if (clusterComputeNodeQuota > 0) {
            computeNodeQuota = Math.min(computeNodeQuota, clusterComputeNodeQuota);
        }
        if (request.getCount() + createdComputeCount > computeNodeQuota) {
            log.debug(
                    "chpc_compute_quota is: {}, cluster_compute_quota: {}, compute node created: {}, need create: {} ",
                    computeNodeQuota,
                    clusterComputeNodeQuota,
                    createdComputeCount,
                    request.getCount());
            throw new CommonException.QuotaException("Exceed compute node quota: " + Math.min(computeNodeQuota,
                    clusterComputeNodeQuota));
        }
    }

    /**
     * @Description: 在COS中创建一个实例，并返回创建的堆栈响应。
     * 如果是自动伸缩，则不回滚；否则回滚。
     * @Param request InstanceAddRequest 实例添加请求参数，包含实例信息和队列、集群等相关信息
     * @Param cluster Cluster 集群对象，包含集群信息
     * @Param queue Queue 队列对象，包含队列信息
     * @Param autoScaling boolean 是否为自动伸缩，true表示自动伸缩，false表示非自动伸缩
     * @Return CreateStackResponse 创建堆栈响应，包含实例ID等信息
     * @Throws Exception 可能会抛出异常，需要上层处理
     */
    public CreateStackResponse createInstanceInCos(
            InstanceAddRequest request, Cluster cluster, Queue queue, boolean autoScaling, String stackName) {
        CreateStackRequest createStackRequest = new CreateStackRequest();
        createStackRequest.setUserId(LogicUserService.getAccountId());
        // createStackRequest.setName("stack-" + UUIDUtil.generateShortUuid());
        createStackRequest.setName(stackName);

        createStackRequest.getTags().put("from", "chpc");
        if (request.getTags() != null && request.getTags().size() > 0) {
            for (Tag tag : request.getTags()) {
                createStackRequest.getTags().put(tag.getTagKey(), tag.getTagValue());
            }
        }
        createStackRequest.setTemplate(generateTemplate(queue, cluster, request));
        createStackRequest.setTimeout(3600 * 6);

        // 自动伸缩时不回滚
        if (autoScaling == true) {
            createStackRequest.setDisableRollback(true);
        } else {
            createStackRequest.setDisableRollback(false);
        }

        return cosGateway.createStack(createStackRequest);
    }

    public CreateStackRequest.Template generateTemplate(Queue queue, Cluster cluster, InstanceAddRequest request) {
        Map<String, Object> templates = new HashMap<>();

        for (int i = 0; i < request.getCount(); i++) {
            Map<String, Object> resource = new HashMap<>();
            resource.put("type", "BCE::BCC::Instance");
            Map<String, Object> properties = new HashMap<>();
            resource.put("count", 1);

            if (StringUtils.isNotEmpty(request.getSpec())) {
                properties.put("spec", request.getSpec());
            } else if (StringUtils.isNotEmpty(queue.getDefaultSpec())) {
                properties.put("spec", queue.getDefaultSpec());
            } else {
                properties.put("spec", cluster.getSpec());
            }

            if (StringUtils.isNotEmpty(request.getImageId())) {
                properties.put("imageId", request.getImageId());
            } else if (StringUtils.isNotEmpty(queue.getDefaultImageId())) {
                properties.put("imageId", queue.getDefaultImageId());
            } else {
                properties.put("imageId", cluster.getImageId());
            }

            if (StringUtils.isNotEmpty(request.getPassword())) {
                // 数据库AES密码解析为原文
                String password = AesDecryptUtil.decrypt(request.getPassword());
                // 兼容用户没有ak时候，给用户创建随机密码的bcc
                try {
                    IamEncryptResponse encryptResponse = iamGateway.encrypt(password, LogicUserService.getAccountId());
                    String ak = encryptResponse.getAccesskeyId();
                    String skEncryptedPwd = encryptResponse.getCipherHex();
                    properties.put("adminPass", skEncryptedPwd);
                    properties.put("encryptedKey", ak);
                } catch (Exception e) {
                    log.error("iamGateway encrypt password failed", e);
                }
            }
            if (StringUtils.isNotEmpty(request.getKeypairId())) {
                properties.put("keypairId", request.getKeypairId());
            }

            if (StringUtils.isNotEmpty(request.getZoneName())) {
                properties.put("zoneName", request.getZoneName());
            } else {
                properties.put("zoneName", zoneUtil.getApiZoneName(cluster.getLogicalZone()));
            }

            if (StringUtils.isNotEmpty(request.getSubnetId())) {
                properties.put("subnetId", request.getSubnetId());
            } else {
                properties.put("subnetId", cluster.getSubnetId());
            }
            // TODO 需要校验子网和cfs的关系

            List<String> securityGroups = new ArrayList<>();
            if (StringUtils.isNotEmpty(request.getSecurityGroupId())) {
                // properties.put("securityGroupId", request.getSecurityGroupId());
                securityGroups.add(request.getSecurityGroupId());
            } else {
                // properties.put("securityGroupId", cluster.getSecurityGroupId());
                securityGroups.add(cluster.getSecurityGroupId());
            }

            if (ChpcConstant.SECURITY_GROUP_TYPE_ENTERPRISE.equalsIgnoreCase(cluster.getSecurityGroupType())) {
                properties.put("enterpriseSecurityGroupIds", securityGroups);
            } else {
                properties.put("securityGroupIds", securityGroups);
            }

            properties.put("rootDiskSizeInGb", request.getSystemDiskSize());
            properties.put("rootDiskStorageType", DiskTypeUtil.getDiskType(request.getSystemDiskType()));
            // properties.put("purchaseCount", 1);

            Map<String, Object> billing = new HashMap<>();
            billing.put("paymentTiming", request.getChargeType());
            if (ChargeType.Prepaid.name().equalsIgnoreCase(request.getChargeType())) {
                Map<String, Object> reservation = new HashMap<>();
                // console bcc 目前不支持Year单位，故用 Month*12
                if (PeriodUnit.Year.name().equalsIgnoreCase(request.getPeriodUnit())) {
                    reservation.put("reservationLength", request.getPeriod() * 12);
                } else {
                    reservation.put("reservationLength", request.getPeriod());
                }
                reservation.put("reservationTimeUnit", PeriodUnit.Month.name());
                billing.put("reservation", reservation);
                if (request.isAutoRenew()) {
                    properties.put("autoRenewTimeUnit", request.getAutoRenewPeriodUnit());
                    properties.put("autoRenewTime", request.getAutoRenewPeriod());
                    properties.put("cdsAutoRenew", true);
                }
            }
            properties.put("billing", billing);

            List<com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag> tags = new ArrayList<>();

            com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag tag = new com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag();
            tag.setTagKey("nodeType");
            tag.setTagValue("compute");
            tags.add(tag);
            // 来源tag，用于BCC通往后，返回创建成功
            com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag fromTag = new com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag();
            fromTag.setTagKey("from");
            fromTag.setTagValue("chpc");
            tags.add(fromTag);

              // 为gpu相关软件版本打标签
            if (StringUtils.isNotEmpty(request.getGpuDriverVersion())){
                com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag gpuDriverTag = new com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag();
                gpuDriverTag.setTagKey(ChpcConstant.GPU_DRIVER_VERSION);
                gpuDriverTag.setTagValue(request.getGpuDriverVersion());
                tags.add(gpuDriverTag);
            }

            if (StringUtils.isNotEmpty(request.getCudaVersion())){
                com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag cudaTag = new com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag();
                cudaTag.setTagKey(ChpcConstant.CUDA_VERSION);
                cudaTag.setTagValue(request.getCudaVersion());
                tags.add(cudaTag);
            }

            if (StringUtils.isNotEmpty(request.getCudnnVersion())){
                com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag cudnnTag = new com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag();
                cudnnTag.setTagKey(ChpcConstant.CUDNN_VERSION);
                cudnnTag.setTagValue(request.getCudnnVersion());
                tags.add(cudnnTag);
            }

            properties.put("tags", tags);

            if (CollectionUtils.isNotEmpty(request.getDataDiskList())) {
                List<CdsDiskForCreate> cdsList = new ArrayList<>();
                for (DiskInfo disk : request.getDataDiskList()) {
                    for (int j = 0; j < disk.getCdsNum(); j++) {
                        CdsDiskForCreate cdsDiskForCreate = new CdsDiskForCreate();
                        cdsDiskForCreate.setCdsSizeInGB(disk.getSize());
                        cdsDiskForCreate.setStorageType(DiskTypeUtil.getDiskType(disk.getStorageType()));
                        cdsList.add(cdsDiskForCreate);
                    }
                }
                properties.put("createCdsList", cdsList);
            }

            // ebc ht&numa配置
            if (StringUtils.isNotEmpty((request.getCpuThreadConfig()))){
                properties.put("cpuThreadConfig", request.getCpuThreadConfig());
            }
            if (StringUtils.isNotEmpty((request.getNumaConfig()))){
                properties.put("numaConfig", request.getNumaConfig());
            }

            resource.put("properties", properties);
            templates.put("bcc_" + i, resource);
        }

        CreateStackRequest.Resources resources = new CreateStackRequest.Resources();
        resources.setResources(templates);

        CreateStackRequest.Template template = new CreateStackRequest.Template();
        template.setJson(JacksonUtil.encode(resources));
        return template;
    }
}
