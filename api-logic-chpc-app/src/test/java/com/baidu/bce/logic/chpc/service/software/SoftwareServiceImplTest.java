package com.baidu.bce.logic.chpc.service.software;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.SoftwareStatus;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.Software;
import com.baidu.bce.logic.chpc.model.SoftwareRecord;
import com.baidu.bce.logic.chpc.software.model.InstallSoftwareRequest;
import com.baidu.bce.logic.chpc.software.model.QuerySoftwareForNodeRequest;
import com.baidu.bce.logic.chpc.software.model.QuerySoftwareForNodeResponse;
import com.baidu.bce.logic.chpc.software.model.SoftwareQueryResponse;
import com.baidu.bce.logic.chpc.software.model.SoftwareVersionList;
import com.baidu.bce.logic.chpc.software.model.UnInstallSoftwareRequest;
import com.baidu.bce.logic.chpc.software.model.UnInstallSoftwareResponse;

@ExtendWith(MockitoExtension.class)
public class SoftwareServiceImplTest{

    @Mock
    ClusterDAOGateway clusterDAOGateway;

    @Mock
    BccGateway bccGateway;

    @Mock
    TaskService taskService;

    @Mock
    private InstanceDAOGateway instanceDAOGateway;

    @Mock
    private SoftwareDAOGateway softwareDAOGateway;

    private QuerySoftwareForNodeRequest request;

    private List<SoftwareVersionList> softwareVersionLists;

    private List<SoftwareRecord> softwareRecords;

    @Mock
    private SoftwareRecordDAOGateway softwareRecordDAOGateway;

    @InjectMocks
    private SoftwareServiceImpl softwareService;

    @BeforeEach
    public void setUp() {
        request = new QuerySoftwareForNodeRequest();
        softwareVersionLists = new ArrayList<>();
        softwareRecords = new ArrayList<>();
    
        SoftwareVersionList softwareVersionList = new SoftwareVersionList();
        softwareVersionList.setName("testSoftware");
        softwareVersionList.setVersions(List.of("1.0.0", "2.0.0"));
        softwareVersionLists.add(softwareVersionList);
    
        request.setSoftwareList(softwareVersionLists);
        request.setInstanceId("testInstanceId");
    }

    @Test
    public void testGetSoftwareRecordByInstanceWithNullRequest() {
        QuerySoftwareForNodeResponse response = softwareService.getSoftwareRecordByInstance("testClusterId", null);
    
        assertEquals(0, response.getSoftwareList().size());
    }

    @Test
    public void testGetSoftwareRecordByInstanceWithEmptySoftwareList() {
        request.setSoftwareList(new ArrayList<>());
    
        QuerySoftwareForNodeResponse response = softwareService.getSoftwareRecordByInstance("testClusterId", request);
    
        assertEquals(0, response.getSoftwareList().size());
    }

    @Test
    public void testGetSoftwareRecordByInstanceWithInstalledSoftware() {
        SoftwareRecord softwareRecord = new SoftwareRecord();
        softwareRecord.setName("testSoftware");
        softwareRecord.setVersion("1.0.0");
        softwareRecord.setStatus("installed");
    
        softwareRecords.add(softwareRecord);
    
        when(softwareRecordDAOGateway.findSoftwareByInstanceId("testClusterId", "testInstanceId"))
                .thenReturn(softwareRecords);
    
        QuerySoftwareForNodeResponse response = softwareService.getSoftwareRecordByInstance("testClusterId", request);
    
        assertEquals(1, response.getSoftwareList().size());
        assertEquals("1.0.0", response.getSoftwareList().get(0).getVersionList().get(0).getValue());
        assertEquals("installed", response.getSoftwareList().get(0).getVersionList().get(0).getStatus());
    }

    @Test
    public void testGetSoftwareRecordByInstanceWithUnInstalledSoftware() {
        SoftwareRecord softwareRecord = new SoftwareRecord();
        softwareRecord.setName("testSoftware");
        softwareRecord.setVersion("2.0.0");
        softwareRecord.setStatus("uninstalled");
    
        softwareRecords.add(softwareRecord);
    
        when(softwareRecordDAOGateway.findSoftwareByInstanceId("testClusterId", "testInstanceId"))
                .thenReturn(softwareRecords);
    
        QuerySoftwareForNodeResponse response = softwareService.getSoftwareRecordByInstance("testClusterId", request);
    
        assertEquals(1, response.getSoftwareList().size());
        assertEquals("2.0.0", response.getSoftwareList().get(0).getVersionList().get(1).getValue());
        assertEquals("uninstalled", response.getSoftwareList().get(0).getVersionList().get(1).getStatus());
    }

    @Test
    public void testGetSoftwareRecordByInstanceWithNoSoftware() {
        when(softwareRecordDAOGateway.findSoftwareByInstanceId("testClusterId", "testInstanceId"))
                .thenReturn(new ArrayList<>());
    
        QuerySoftwareForNodeResponse response = softwareService.getSoftwareRecordByInstance("testClusterId", request);
    
        assertEquals(1, response.getSoftwareList().size());
    }

    @Test
    public void testGetSoftwareListWithValidClusterIdAndEmptyName() {
        String clusterId = "test-cluster-id";
        String name = "";
    
        List<Instance> instances = new ArrayList<>();
        Instance masterInstance = new Instance();
        masterInstance.setInstanceId("test-instance-id");
        masterInstance.setNodeType(InstanceNodeType.MASTER.getType());
        instances.add(masterInstance);
    
        when(instanceDAOGateway.findByClusterId(clusterId)).thenReturn(instances);
        when(softwareDAOGateway.findAll()).thenReturn(createSoftwareList());
    
        SoftwareQueryResponse response = softwareService.getSoftwareList(clusterId, name);
    
        assertEquals(0, response.getSoftwareList().size());
        // assertEquals("test-software", response.getSoftwareList().get(0).getName());
        // assertEquals("test-version", response.getSoftwareList().get(0).getVersionList().get(0).getValue());
    }

    @Test
    public void testGetSoftwareListWithValidClusterIdAndNonEmptyName() {
        String clusterId = "test-cluster-id";
        String name = "test-software";
    
        List<Instance> instances = new ArrayList<>();
        Instance masterInstance = new Instance();
        masterInstance.setInstanceId("test-instance-id");
        masterInstance.setNodeType(InstanceNodeType.MASTER.getType());
        instances.add(masterInstance);
    
        when(instanceDAOGateway.findByClusterId(clusterId)).thenReturn(instances);
        when(softwareDAOGateway.findSoftwareByNameOrCategory(name)).thenReturn(createSoftwareList());
    
        SoftwareQueryResponse response = softwareService.getSoftwareList(clusterId, name);
    
        assertEquals(0, response.getSoftwareList().size());
        // assertEquals("test-software", response.getSoftwareList().get(0).getName());
        // assertEquals("test-version", response.getSoftwareList().get(0).getVersionList().get(0).getValue());
    }

    @Test
    public void testGetSoftwareListWithInvalidClusterId() {
        String clusterId = "invalid-cluster-id";
        String name = "";
    
        when(instanceDAOGateway.findByClusterId(clusterId)).thenReturn(new ArrayList<>());
    
        try {
            softwareService.getSoftwareList(clusterId, name);
        } catch (BceException e) {
            assertEquals("获取管理节点信息异常", e.getMessage());
        }
    }

    private List<Software> createSoftwareList() {
        List<Software> softwareList = new ArrayList<>();
        Software software = new Software();
        software.setName("test-software");
        software.setVersion("test-version");
        software.setSupportArch("amd64");
        software.setNodeType(InstanceNodeType.COMPUTE.getType());
        softwareList.add(software);
        return softwareList;
    }

    // @Test
    // public void testInstallSoftwareSuccess() {
    //     softwareService = new SoftwareServiceImpl();
    //     softwareService.clusterDAOGateway = clusterDAOGateway;
    //     softwareService.bccGateway = bccGateway;
    //     softwareService.instanceDAOGateway = instanceDAOGateway;
    //     softwareService.taskService = taskService;
    //     softwareService.softwareRecordDAOGateway = softwareRecordDAOGateway;
    //     softwareService.softwareDAOGateway = softwareDAOGateway;
    
    //     String clusterId = "cluster-123";
    //     InstallSoftwareRequest request = new InstallSoftwareRequest();
    //     SoftwareVersionList softwareVersionList = new SoftwareVersionList();
    //     softwareVersionList.setName("chpc-app-server");
    //     softwareVersionList.setVersions(List.of("1.0.0"));
    //     request.setSoftwareList(List.of(softwareVersionList));
    
    //     List<Instance> instances = new ArrayList<>();
    //     Instance loginInstance = new Instance();
    //     loginInstance.setNodeType(InstanceNodeType.LOGIN.getType());
    //     instances.add(loginInstance);
    
    //     when(instanceDAOGateway.findByClusterId(anyString())).thenReturn(instances);
    //     when(softwareDAOGateway.findSoftwareByNameAndVersion(anyString(), anyString())).thenReturn(new Software());
    
    //     InstallSoftwareResponse response = softwareService.installSoftware(clusterId, request);
    
    //     assertEquals(1, response.getSoftwareList().size());
    //     assertEquals("chpc-app-server", response.getSoftwareList().get(0).getName());
    // }

    @Test
    public void testInstallSoftwareNoLoginInstance() {
        softwareService = new SoftwareServiceImpl();
        softwareService.clusterDAOGateway = clusterDAOGateway;
        softwareService.bccGateway = bccGateway;
        softwareService.instanceDAOGateway = instanceDAOGateway;
        softwareService.taskService = taskService;
        softwareService.softwareRecordDAOGateway = softwareRecordDAOGateway;
        softwareService.softwareDAOGateway = softwareDAOGateway;
    
        String clusterId = "cluster-123";
        InstallSoftwareRequest request = new InstallSoftwareRequest();
        SoftwareVersionList softwareVersionList = new SoftwareVersionList();
        softwareVersionList.setName("chpc-app-server");
        softwareVersionList.setVersions(List.of("1.0.0"));
        request.setSoftwareList(List.of(softwareVersionList));
    
        List<Instance> instances = new ArrayList<>();
        when(instanceDAOGateway.findByClusterId(anyString())).thenReturn(instances);
    
        assertThrows(BceException.class, () -> {
            softwareService.installSoftware(clusterId, request);
        });
    }

    @Test
    public void testInstallSoftwareSoftwareNotInList() {
        softwareService = new SoftwareServiceImpl();
        softwareService.clusterDAOGateway = clusterDAOGateway;
        softwareService.bccGateway = bccGateway;
        softwareService.instanceDAOGateway = instanceDAOGateway;
        softwareService.taskService = taskService;
        softwareService.softwareRecordDAOGateway = softwareRecordDAOGateway;
        softwareService.softwareDAOGateway = softwareDAOGateway;
    
        String clusterId = "cluster-123";
        InstallSoftwareRequest request = new InstallSoftwareRequest();
        SoftwareVersionList softwareVersionList = new SoftwareVersionList();
        softwareVersionList.setName("chpc-app-server");
        softwareVersionList.setVersions(List.of("1.0.0"));
        request.setSoftwareList(List.of(softwareVersionList));
    
        List<Instance> instances = new ArrayList<>();
        Instance loginInstance = new Instance();
        loginInstance.setNodeType(InstanceNodeType.LOGIN.getType());
        instances.add(loginInstance);
    
        when(instanceDAOGateway.findByClusterId(anyString())).thenReturn(instances);
        when(softwareDAOGateway.findSoftwareByNameAndVersion(anyString(), anyString())).thenReturn(null);
    
        assertThrows(BceException.class, () -> {
            softwareService.installSoftware(clusterId, request);
        });
    }

    // @Test
    // public void testUnInstallSoftwareSuccess() {
    //     softwareService = new SoftwareServiceImpl();
    //     softwareService.clusterDAOGateway = clusterDAOGateway;
    //     softwareService.bccGateway = bccGateway;
    //     softwareService.instanceDAOGateway = instanceDAOGateway;
    //     softwareService.taskService = taskService;
    //     softwareService.softwareRecordDAOGateway = softwareRecordDAOGateway;
    //     softwareService.softwareDAOGateway = softwareDAOGateway;
    
    //     String clusterId = "test-cluster-id";
    //     UnInstallSoftwareRequest request = new UnInstallSoftwareRequest();
    //     List<SoftwareVersionList> softwareList = new ArrayList<>();
    //     SoftwareVersionList softwareVersion = new SoftwareVersionList();
    //     softwareVersion.setName("test-software");
    //     softwareVersion.setVersions(List.of("1.0.0"));
    //     softwareList.add(softwareVersion);
    //     request.setSoftwareList(softwareList);
    //     request.setInstanceId("test-instance-id");
    
    //     List<Instance> instances = new ArrayList<>();
    //     Instance instance = new Instance();
    //     instance.setInstanceId("test-instance-id");
    //     instances.add(instance);
    
    //     when(instanceDAOGateway.findByClusterId(anyString())).thenReturn(instances);
    //     when(softwareDAOGateway.findSoftwareByNameAndVersion(anyString(), anyString())).thenReturn(new Software());
    //     SoftwareRecord record = new SoftwareRecord();
    //     record.setStatus(SoftwareStatus.INSTALLING.toString());
    //     when(softwareRecordDAOGateway.findSoftwareByNameAndVersion(anyString(), anyString(), anyString())).thenReturn(record);
    
    //     UnInstallSoftwareResponse response = softwareService.unInstallSoftware(clusterId, request);
    
    //     assertEquals(1, response.getSoftwareList().size());
    //     assertEquals("test-software", response.getSoftwareList().get(0).getName());
    //     assertEquals(List.of("1.0.0"), response.getSoftwareList().get(0).getVersions());
    // }



    @Test
    public void testUnInstallSoftwareNoInstanceId() {
        softwareService = new SoftwareServiceImpl();
        softwareService.clusterDAOGateway = clusterDAOGateway;
        softwareService.bccGateway = bccGateway;
        softwareService.instanceDAOGateway = instanceDAOGateway;
        softwareService.taskService = taskService;
        softwareService.softwareRecordDAOGateway = softwareRecordDAOGateway;
        softwareService.softwareDAOGateway = softwareDAOGateway;

        String clusterId = "test-cluster-id";
        UnInstallSoftwareRequest request = new UnInstallSoftwareRequest();
        List<SoftwareVersionList> softwareList = new ArrayList<>();
        SoftwareVersionList softwareVersion = new SoftwareVersionList();
        softwareVersion.setName("test-software");
        softwareVersion.setVersions(List.of("1.0.0"));
        softwareList.add(softwareVersion);
        request.setSoftwareList(softwareList);

        assertThrows(BceException.class, () -> softwareService.unInstallSoftware(clusterId, request));
    }

    @Test
    public void testUnInstallSoftwareSoftwareNotFound() {
        softwareService = new SoftwareServiceImpl();
        softwareService.clusterDAOGateway = clusterDAOGateway;
        softwareService.bccGateway = bccGateway;
        softwareService.instanceDAOGateway = instanceDAOGateway;
        softwareService.taskService = taskService;
        softwareService.softwareRecordDAOGateway = softwareRecordDAOGateway;
        softwareService.softwareDAOGateway = softwareDAOGateway;
    
        String clusterId = "test-cluster-id";
        UnInstallSoftwareRequest request = new UnInstallSoftwareRequest();
        List<SoftwareVersionList> softwareList = new ArrayList<>();
        SoftwareVersionList softwareVersion = new SoftwareVersionList();
        softwareVersion.setName("test-software");
        softwareVersion.setVersions(List.of("1.0.0"));
        softwareList.add(softwareVersion);
        request.setSoftwareList(softwareList);
        request.setInstanceId("test-instance-id");
    
        List<Instance> instances = new ArrayList<>();
        Instance instance = new Instance();
        instance.setInstanceId("test-instance-id");
        instances.add(instance);
    
        when(instanceDAOGateway.findByClusterId(anyString())).thenReturn(instances);
        when(softwareDAOGateway.findSoftwareByNameAndVersion(anyString(), anyString())).thenReturn(null);
    
        assertThrows(BceException.class, () -> softwareService.unInstallSoftware(clusterId, request));
    }

}