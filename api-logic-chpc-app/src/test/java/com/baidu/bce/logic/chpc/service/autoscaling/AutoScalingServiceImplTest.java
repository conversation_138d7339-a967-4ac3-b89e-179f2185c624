package com.baidu.bce.logic.chpc.service.autoscaling;

import com.baidu.bce.logic.chpc.common.GlobalUuidUtil;
import com.baidu.bce.logic.chpc.database.dataobject.AutoScalingDO;
import com.baidu.bce.logic.chpc.database.mapper.AutoScalingMapper;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.service.util.DiskTypeUtil;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;

import java.time.LocalDateTime;
import java.util.ArrayList;
import com.baidu.bce.logic.chpc.autoscaling.model.ExecuteAutoScalingInfo;
import com.baidu.bce.logic.chpc.common.AutoScalingStatus;
import com.baidu.bce.logic.chpc.model.Queue;

import java.util.Collections;
import java.util.List;
import com.baidu.bce.logic.chpc.autoscaling.gateway.IAutoScalingDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Arrays;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import static org.mockito.ArgumentMatchers.any;
import com.baidu.bce.logic.chpc.autoscaling.model.AutoScaling;
import org.junit.jupiter.api.Test;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import static org.mockito.ArgumentMatchers.anyList;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.autoscaling.model.SetAutoScalingRequest;


@ExtendWith(MockitoExtension.class)
public class AutoScalingServiceImplTest{

    @Mock
    InstanceDAOGateway instanceDAOGateway;

    @Mock
    IAutoScalingDAOGateway autoScalingDAOGateway;

    @Mock
    AutoScalingMapper autoScalingMapper;

    @Mock
    private QueueDAOGateway queueDAOGateway;

    @Mock
    GlobalUuidUtil globalUuidUtil;

    @InjectMocks
    AutoScalingServiceImpl autoScalingService;

    private String clusterId;

    private AutoScaling autoScaling;

    private List<ExecuteAutoScalingInfo.Node> nodes;

    private int shrinkNums;

    private int shrinkCycle;

    private SetAutoScalingRequest request;

    private AutoScaling oldAutoScaling;

    private Queue queue;



    @BeforeEach
    public void setUp() {
        clusterId = "test-cluster-id";
        autoScaling = new AutoScaling();
        autoScaling.setAsId("test-as-id");
        autoScaling.setClusterId(clusterId);
        autoScaling.setQueueId("test-queue-id");
        autoScaling.setEnableAutoShrink(true);
        autoScaling.setMinNodesInQueue(5);
        autoScaling.setExcludeNodes(Arrays.asList("node1", "node2"));
    
        nodes = new ArrayList<>();
        ExecuteAutoScalingInfo.Node node1 = new ExecuteAutoScalingInfo.Node();
        node1.setNodeName("node1");
        nodes.add(node1);
    
        shrinkNums = 3;
        shrinkCycle = 60;

        request = new SetAutoScalingRequest();
        request.setAsName("test-as-name");
        request.setMaxNodesInQueue(10);
        request.setMinNodesInQueue(5);
        request.setEnableAutoGrow(true);
        request.setEnableAutoShrink(true);
        request.setExcludeNodes(Arrays.asList("node1", "node2"));
        request.setGpuDriverVersion("1.0.0");
        request.setCudaVersion("11.2");
        request.setCudnnVersion("8.0");
        request.setSystemDiskSize(100);
        request.setSystemDiskType("ssd");
        request.setDataDiskList(Collections.emptyList());
        request.setImageId("test-image-id");
        request.setSubnetId("test-subnet-id");
        request.setZoneName("test-zone-name");
        request.setCpuThreadConfig("2");
        request.setNumaConfig("1");
        request.setSpec("bcc.g5.c2m8");
        request.setQueueName("test-queue-name");

        oldAutoScaling = new AutoScaling();
        oldAutoScaling.setAsId("test-as-id");
        oldAutoScaling.setClusterId("test-cluster-id");
        oldAutoScaling.setClusterName("test-cluster-name");
        oldAutoScaling.setAccountId("test-account-id");
        LocalDateTime now = LocalDateTime.now();
        oldAutoScaling.setCreatedTime(now);
        oldAutoScaling.setDeleted(false);
        oldAutoScaling.setQueueName("test-queue-name");
        oldAutoScaling.setAsName("old-as-name");
        oldAutoScaling.setMaxNodesInQueue(10);
        oldAutoScaling.setMinNodesInQueue(5);
        oldAutoScaling.setEnableAutoGrow(true);
        oldAutoScaling.setEnableAutoShrink(true);
        oldAutoScaling.setExcludeNodes(Arrays.asList("node1", "node2"));
        oldAutoScaling.setGpuDriverVersion("1.0.0");
        oldAutoScaling.setCudaVersion("11.2");
        oldAutoScaling.setCudnnVersion("8.0");
        oldAutoScaling.setCpuThreadConfig("1");
        oldAutoScaling.setNumaConfig("2");
        oldAutoScaling.setSystemDiskSize(100);
        oldAutoScaling.setSystemDiskType(DiskTypeUtil.getDiskType("ssd"));
        oldAutoScaling.setDataDiskList(Collections.emptyList());
        oldAutoScaling.setImageId("test-image-id");
        oldAutoScaling.setSubnetId("test-subnet-id");
        oldAutoScaling.setZoneName("test-zone-name");

        queue = new Queue();
        queue.setQueueId("test-queue-id");
        queue.setName("test-queue-name");
    }

    @Test
void testGenerateAutoScalingWithValidRequest() {
    try(MockedStatic<LogicUserService> mockLogicUser = mockStatic(LogicUserService.class)) {
        mockLogicUser.when(LogicUserService::getAccountId).thenReturn("test-account-id");
        // Setup
        when(globalUuidUtil.genAutoScalingShortId()).thenReturn("generated-id");
        when(queueDAOGateway.update(any(Queue.class))).thenReturn(true);
        // when(autoScalingDAOGateway.insert(any(AutoScaling.class))).thenReturn(true);
        // when(autoScalingMapper.insertSelective(any(AutoScalingDO.class))).thenReturn(1);
        // Execute
        AutoScaling result = autoScalingService.generateAutoScaling(request, queue);

        // Verify
        Assertions.assertNotNull(result);
        assertEquals("generated-id", result.getAsId());
        assertEquals("test-as-name", result.getAsName());
        assertEquals("test-queue-id", result.getQueueId());
        assertEquals("test-queue-name", result.getQueueName());
        assertEquals(10, result.getMaxNodesInQueue());
        assertEquals(5, result.getMinNodesInQueue());
        assertTrue(result.getEnableAutoGrow());
        assertTrue(result.getEnableAutoShrink());
        assertEquals(Arrays.asList("node1", "node2"), result.getExcludeNodes());
        assertEquals("1.0.0", result.getGpuDriverVersion());
        assertEquals("11.2", result.getCudaVersion());
        assertEquals("8.0", result.getCudnnVersion());
        assertEquals(100, result.getSystemDiskSize());
        assertEquals(DiskTypeUtil.getDiskType("ssd"), result.getSystemDiskType());
        assertEquals(Collections.emptyList(), result.getDataDiskList());
        assertEquals("test-image-id", result.getImageId());
        assertEquals("test-subnet-id", result.getSubnetId());
        assertEquals("test-zone-name", result.getZoneName());
        assertEquals("2", result.getCpuThreadConfig());
        assertEquals("1", result.getNumaConfig());
        assertNotNull(result.getCreatedTime());
        assertEquals(AutoScalingStatus.NORMAL, result.getStatus());

        verify(queueDAOGateway).update(any(Queue.class));
        // verify(autoScalingDAOGateway).insert(any(AutoScaling.class));
        // verify(autoScalingMapper).insertSelective(any(AutoScalingDO.class));
    }
}

    // testExecuteAutoShrinkWhenAutoShrinkIsDisabled 用于测试 executeAutoShrink
    // generated by Comate
    @Test
    public void testExecuteAutoShrinkWhenAutoShrinkIsDisabled() {
        autoScaling.setEnableAutoShrink(false);
    
        autoScalingService.executeAutoShrink(clusterId, autoScaling, nodes, shrinkNums, shrinkCycle);
    
        verify(autoScalingDAOGateway, never()).updateByStatus(any(AutoScaling.class), any(AutoScalingStatus.class));
    }

    // testExecuteAutoShrinkWhenQueueNodeCountIsSmallerThanMinNodesInQueue 用于测试 executeAutoShrink
    // generated by Comate
    @Test
    public void testExecuteAutoShrinkWhenQueueNodeCountIsSmallerThanMinNodesInQueue() {
        clusterId = "test-cluster-id";
        autoScaling = new AutoScaling();
        autoScaling.setAsId("test-as-id");
        autoScaling.setClusterId(clusterId);
        autoScaling.setQueueId("test-queue-id");
        autoScaling.setEnableAutoShrink(true);
        autoScaling.setMinNodesInQueue(5);
        autoScaling.setExcludeNodes(Arrays.asList("node1", "node2"));
    
        nodes = new ArrayList<>();
        ExecuteAutoScalingInfo.Node node1 = new ExecuteAutoScalingInfo.Node();
        node1.setNodeName("node1");
        nodes.add(node1);
    
        shrinkNums = 3;
        shrinkCycle = 60;
    
        Instance instance = new Instance();
        instance.setChargeType(ChargeType.Postpaid.name());
        instance.setNodeType(InstanceNodeType.COMPUTE.getType());
        instance.setHostName("node1");
        when(instanceDAOGateway.findAllByClusterIdAndHostNames(anyString(), anyList()))
                .thenReturn(Arrays.asList(instance));
        when(instanceDAOGateway.countComputeNode(anyString(), anyString()))
                .thenReturn(3L);
    
        autoScalingService.executeAutoShrink(clusterId, autoScaling, nodes, shrinkNums, shrinkCycle);
    
        verify(autoScalingDAOGateway, never()).updateByStatus(any(AutoScaling.class), any(AutoScalingStatus.class));
    }

    // testExecuteAutoShrinkWhenNoNodesReadyToDelete 用于测试 executeAutoShrink
    // generated by Comate
    @Test
    public void testExecuteAutoShrinkWhenNoNodesReadyToDelete() {
        clusterId = "test-cluster-id";
        autoScaling = new AutoScaling();
        autoScaling.setAsId("test-as-id");
        autoScaling.setClusterId(clusterId);
        autoScaling.setQueueId("test-queue-id");
        autoScaling.setEnableAutoShrink(true);
        autoScaling.setMinNodesInQueue(5);
        autoScaling.setExcludeNodes(Arrays.asList("node1", "node2"));
    
        nodes = new ArrayList<>();
        ExecuteAutoScalingInfo.Node node1 = new ExecuteAutoScalingInfo.Node();
        node1.setNodeName("node1");
        nodes.add(node1);
    
        shrinkNums = 3;
        shrinkCycle = 60;
    
        Instance instance = new Instance();
        instance.setNodeType("COMPUTE");
        when(instanceDAOGateway.findAllByClusterIdAndHostNames(anyString(), anyList()))
                .thenReturn(Arrays.asList(instance));
    
        autoScalingService.executeAutoShrink(clusterId, autoScaling, nodes, shrinkNums, shrinkCycle);
    
        verify(autoScalingDAOGateway, never()).updateByStatus(any(AutoScaling.class), any(AutoScalingStatus.class));
    }

    @Test
    public void testUpdateAutoScalingWithGpuSoftware() {
        try(MockedStatic<LogicUserService> mockLogicUser = mockStatic(LogicUserService.class)) {
            mockLogicUser.when(LogicUserService::getAccountId).thenReturn("test-account-id");
            when(queueDAOGateway.update(any(Queue.class))).thenReturn(true);
            when(autoScalingDAOGateway.update(any(AutoScaling.class))).thenReturn(true);

            AutoScaling result = autoScalingService.updateAutoScaling(request, oldAutoScaling, queue);

            assertEquals("test-as-name", result.getAsName());
            assertEquals(10, result.getMaxNodesInQueue());
            assertEquals(5, result.getMinNodesInQueue());
            assertEquals(true, result.getEnableAutoGrow());
            assertEquals(true, result.getEnableAutoShrink());
            assertEquals(Arrays.asList("node1", "node2"), result.getExcludeNodes());
            assertEquals("1.0.0", result.getGpuDriverVersion());
            assertEquals("11.2", result.getCudaVersion());
            assertEquals("8.0", result.getCudnnVersion());
            assertEquals(100, result.getSystemDiskSize());
            assertEquals(DiskTypeUtil.getDiskType("ssd"), result.getSystemDiskType());
            assertEquals(Collections.emptyList(), result.getDataDiskList());
            assertEquals("test-image-id", result.getImageId());
            assertEquals("test-subnet-id", result.getSubnetId());
            assertEquals("test-zone-name", result.getZoneName());
            assertEquals("1.0.0", result.getGpuDriverVersion());
            assertEquals("11.2", result.getCudaVersion());
            assertEquals("8.0", result.getCudnnVersion());
            assertEquals("2", result.getCpuThreadConfig());
            assertEquals("1", result.getNumaConfig());
        }

    }

    @Test
    public void testUpdateAutoScalingWithoutGpuSoftware() {
        try(MockedStatic<LogicUserService> mockLogicUser = mockStatic(LogicUserService.class)) {
            mockLogicUser.when(LogicUserService::getAccountId).thenReturn("test-account-id");
            when(queueDAOGateway.update(any(Queue.class))).thenReturn(true);
            when(autoScalingDAOGateway.update(any(AutoScaling.class))).thenReturn(true);
            request.setGpuDriverVersion(null);
            request.setCudaVersion(null);
            request.setCudnnVersion(null);
            AutoScaling result = autoScalingService.updateAutoScaling(request, oldAutoScaling, queue);

            assertEquals("test-as-name", result.getAsName());
            assertEquals(10, result.getMaxNodesInQueue());
            assertEquals(5, result.getMinNodesInQueue());
            assertEquals(true, result.getEnableAutoGrow());
            assertEquals(true, result.getEnableAutoShrink());
            assertEquals(Arrays.asList("node1", "node2"), result.getExcludeNodes());
            assertEquals(100, result.getSystemDiskSize());
            assertEquals(DiskTypeUtil.getDiskType("ssd"), result.getSystemDiskType());
            assertEquals(Collections.emptyList(), result.getDataDiskList());
            assertEquals("test-image-id", result.getImageId());
            assertEquals("test-subnet-id", result.getSubnetId());
            assertEquals("test-zone-name", result.getZoneName());
            assertEquals("", result.getGpuDriverVersion());
            assertEquals("", result.getCudaVersion());
            assertEquals("", result.getCudnnVersion());
        }

    }

}