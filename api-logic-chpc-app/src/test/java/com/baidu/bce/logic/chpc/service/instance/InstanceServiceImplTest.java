package com.baidu.bce.logic.chpc.service.instance;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.ca.gateway.CaGateway;
import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.InstanceNodeType;
import com.baidu.bce.logic.chpc.common.ZoneUtil;
import com.baidu.bce.logic.chpc.common.charge.ChargeType;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.common.validator.NetworkValidator;
import com.baidu.bce.logic.chpc.cos.CreateStackRequest;
import com.baidu.bce.logic.chpc.cos.gateway.CosGateway;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.TagsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.WhitelistGateway;
import com.baidu.bce.logic.chpc.iam.IamGateway;
import com.baidu.bce.logic.chpc.instance.model.InstanceAddRequest;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.oos.gateway.OosGateway;
import com.baidu.bce.logic.chpc.service.util.PasswordUtil;
import com.baidu.bce.logic.chpc.subnet.gateway.SubnetGateway;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logic.chpc.user.Const;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.test.context.TestPropertySource;

import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
@TestPropertySource(properties = {"bce.web.commons.bos.bucket.endpoint=http://mock-endpoint"})
@MockitoSettings(strictness = Strictness.LENIENT)
public class InstanceServiceImplTest {


    @InjectMocks
    private InstanceServiceImpl instanceService1;

    private String clusterId = "cluster-1";

    private InstanceAddRequest request;
    @Mock
    OosGateway oosGateway;

    @Mock
    CaGateway caGateway;

    @Mock
    IamGateway iamGateway;

    @Mock
    InstanceService instanceService;

    @Mock
    BccGateway bccGateway;

    @Mock
    NetworkValidator networkValidator;

    @Mock
    PasswordUtil passwordUtil;

    @Mock
    ZoneUtil zoneUtil;

    @Mock
    BackendGateway backendGateway;

    @Mock
    SubnetGateway subnetGateway;

    @Mock
    WhitelistGateway whitelistGateway;

    @Mock
    RegionConfiguration regionConfiguration;

    @Mock
    TagsGateway tagsGateway;

    @Mock
    TagsDAOGateway tagsDAOGateway;

    @Mock
    TaskService taskService;

    @Mock
    CosGateway cosGateway;

    @Mock
    InstanceDAOGateway instanceDAOGateway;

    @Mock
    QueueDAOGateway queueDAOGateway;

    @Mock
    ClusterDAOGateway clusterDAOGateway;

    @InjectMocks
    InstanceServiceImpl instanceServiceImpl;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testGenerateTemplate() {
        // Arrange
        Queue queue = new Queue();
        queue.setQueueId("queue-id");
        queue.setName("queue-name");
        queue.setDefaultSpec("default-spec");
        queue.setDefaultImageId("default-image-id");

        Cluster cluster = new Cluster();
        cluster.setSecurityGroupType(ChpcConstant.SECURITY_GROUP_TYPE_NORMAL);
        InstanceAddRequest request = new InstanceAddRequest();
        request.setCount(1);
        request.setSpec("spec");
        request.setImageId("image-id");
        request.setZoneName("zone-name");
        request.setSubnetId("subnet-id");
        request.setSecurityGroupId("security-group-id");
        request.setDataDiskSize(40);
        request.setSystemDiskType("system-disk-type");
        request.setChargeType(ChargeType.Postpaid.name().toLowerCase());
        request.setGpuDriverVersion("gpu-driver-version");
        request.setCudaVersion("cuda-version");
        request.setCudnnVersion("cudnn-version");
        request.setCpuThreadConfig("2");
        request.setNumaConfig("1");


        // Act
        CreateStackRequest.Template template = instanceServiceImpl.generateTemplate(queue, cluster, request);

        // Assert
        assertNotNull(template);
    }

    @Test
    public void testValidateQuotaExceedQuota() {
        request = new InstanceAddRequest();
        request.setCount(5);
    
        Map<String, String> quotaMap = new HashMap<>();
        quotaMap.put(Const.Quota.CHPC_COMPUTE_NODE_QUOTA, "20");
        when(whitelistGateway.getQuota(anyString(), anyString())).thenReturn(quotaMap);
    
        Cluster cluster = new Cluster();
        cluster.setMaxNodes(10);
        when(clusterDAOGateway.findByClusterId(any(), any())).thenReturn(cluster);
    
        when(instanceDAOGateway.count(anyString(), any(), any(), any(), any(), eq(InstanceNodeType.COMPUTE.name())))
                .thenReturn(6L);
        try (MockedStatic<LogicUserService> mockedStatic = Mockito.mockStatic(LogicUserService.class)) {
            mockedStatic.when(LogicUserService::getAccountId).thenReturn("account-id");
    
            assertThrows(CommonException.QuotaException.class, () -> {
                instanceServiceImpl.validateQuota(clusterId, request);
            });
        }
        
    }

    @Test
    public void testValidateQuotaWithinQuota() {
        request = new InstanceAddRequest();
        request.setCount(5);
    
        Map<String, String> quotaMap = new HashMap<>();
        quotaMap.put("CHPC_COMPUTE_NODE_QUOTA", "20");
        when(whitelistGateway.getQuota(anyString(), anyString())).thenReturn(quotaMap);
    
        Cluster cluster = new Cluster();
        cluster.setMaxNodes(0);
        when(clusterDAOGateway.findByClusterId(any(), any())).thenReturn(cluster);
    
        when(instanceDAOGateway.count(anyString(), any(), any(), any(), any(), eq(InstanceNodeType.COMPUTE.name())))
                .thenReturn(5L);
    
        try (MockedStatic<LogicUserService> mockedStatic = Mockito.mockStatic(LogicUserService.class)) {
            mockedStatic.when(LogicUserService::getAccountId).thenReturn("account-id");
    
            instanceServiceImpl.validateQuota(clusterId, request);
        }
    }
}