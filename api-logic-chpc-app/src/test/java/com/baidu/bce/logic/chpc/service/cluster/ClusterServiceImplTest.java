package com.baidu.bce.logic.chpc.service.cluster;

import com.baidu.bce.internalsdk.exception.BceException;
import com.baidu.bce.logic.chpc.cluster.domainservice.ClusterServiceV2;
import com.baidu.bce.logic.chpc.cluster.model.ClusterCreateRequest;
import com.baidu.bce.logic.chpc.cluster.model.DiskInfo;
import com.baidu.bce.logic.chpc.cluster.model.NodeInfo;
import com.baidu.bce.logic.chpc.cluster.model.QueueInfo;
import com.baidu.bce.logic.chpc.cos.CreateStackResponse;
import com.baidu.bce.logic.chpc.domainservice.TaskService;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.response.cluster.ClusterResponse;
import com.baidu.bce.logic.chpc.securitygroup.gateway.SecurityGroupGateway;
import com.baidu.bce.logic.chpc.service.IAutoScalingService;
import com.baidu.bce.logic.chpc.service.cluster.ClusterServiceImpl;
import com.baidu.bce.logic.chpc.service.queue.QueueServiceImpl;
import com.baidu.bce.logic.chpc.service.util.ServiceUtil;
import com.baidu.bce.logic.chpc.tag.gateway.TagsGateway;
import com.baidu.bce.logic.chpc.vpc.gateway.VpcGateway;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.logic.core.util.UuidUtil;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ClusterServiceImplTest {

    @InjectMocks
    private ClusterServiceImpl clusterServiceImpl;


    @Mock
    private QueueServiceImpl queueServiceImpl;

    @Mock
    private IAutoScalingService iAutoScalingService;

    @Mock
    private TaskService taskService;

    @Mock
    private ClusterDAOGateway clusterDAOGateway;

    @Mock
    private QueueDAOGateway queueDAOGateway;

    @Mock
    private TagsGateway tagsGateway;

    @Mock
    private RegionConfiguration regionConfiguration;

    @Mock
    private VpcGateway vpcGateway;

    @Mock
    private InstanceDAOGateway instanceDAOGateway;

    @Mock
    private SecurityGroupGateway securityGroupGateway;

    @Mock
    private ClusterServiceV2 clusterServiceV2;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testCreateCluster_Success() {
        // Arrange
        ClusterCreateRequest clusterCreateRequest = new ClusterCreateRequest();
        clusterCreateRequest.setClusterName("testCluster");
        clusterCreateRequest.setSecurityGroupId("testSecurityGroupId");
        clusterCreateRequest.setVpcId("testVpcId");
        clusterCreateRequest.setQueueList(new ArrayList<>());

        QueueInfo queueInfo = new QueueInfo();
        queueInfo.setQueueName("testQueue");
        NodeInfo nodeInfo = new NodeInfo();
        nodeInfo.setSpec("testSpec");
        nodeInfo.setCount(1);
        nodeInfo.setChargeType("Postpaid");
        nodeInfo.setGpuDriverVersion("testGpuDriverVersion");
        nodeInfo.setCudaVersion("testCudaVersion");
        nodeInfo.setCudnnVersion("testCudnnVersion");
        nodeInfo.setCpuThreadConfig("2");
        nodeInfo.setNumaConfig("1");
        DiskInfo diskInfo = new DiskInfo();
        diskInfo.setSize(100);
        diskInfo.setStorageType("testStorageType");
        nodeInfo.setSystemDisk(diskInfo);
        queueInfo.setComputeSpec(nodeInfo);
        queueInfo.setIsAutoScale(true);
        clusterCreateRequest.getQueueList().add(queueInfo);

        ClusterResponse clusterResponse = new ClusterResponse();
        clusterResponse.setClusterId("testClusterId");

        Queue queue = new Queue();
        queue.setQueueId("testQueueId");

        CreateStackResponse createStackResponse = new CreateStackResponse();
        CreateStackResponse.StackResult stackResult = new CreateStackResponse.StackResult();
        stackResult.setId("testStackId");
        createStackResponse.setResult(stackResult);
        createStackResponse.setSuccess(true);
        createStackResponse.setMsg("success");

        when(clusterServiceV2.createCluster(any(ClusterCreateRequest.class), anyString())).thenReturn(clusterResponse);
        // when(clusterServiceV2.getOrCreateSecurityGroup(anyString(), anyString(), anyLong(), anyLong())).thenReturn("testSecurityGroupId");
        when(queueServiceImpl.genQueue(any(QueueInfo.class), any(ClusterCreateRequest.class), anyString())).thenReturn(queue);
        when(taskService.insert(any(), anyString(), anyString(), anyString(), anyString(), any(), anyString())).thenReturn(true);
        when(taskService.updateCosStackIdByTaskUuid(anyString(), anyString())).thenReturn(true);
        when(clusterServiceV2.createClusterInCos(any(ClusterCreateRequest.class), anyString())).thenReturn(createStackResponse);

        // Act
        clusterServiceImpl.createCluster(clusterCreateRequest);

        // Assert
        verify(clusterServiceV2, times(1)).createCluster(any(ClusterCreateRequest.class), anyString());
        // verify(clusterServiceV2, times(1)).getOrCreateSecurityGroup(anyString(), anyString(), anyLong(), anyLong());
        verify(queueServiceImpl, times(1)).genQueue(any(QueueInfo.class), any(ClusterCreateRequest.class), anyString());
        verify(taskService, times(1)).insert(any(), anyString(), anyString(), anyString(), anyString(), any(), anyString());
        verify(taskService, times(1)).updateCosStackIdByTaskUuid(anyString(), anyString());
        verify(clusterServiceV2, times(1)).createClusterInCos(any(ClusterCreateRequest.class), anyString());
    }

}