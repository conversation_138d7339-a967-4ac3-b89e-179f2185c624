package com.baidu.bce.logic.chpc.service.user;

import com.baidu.bce.logic.chpc.common.BaseResponse;
import com.baidu.bce.logic.chpc.common.ClusterType;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.gateway.ClusterDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.internalsdk.iam.model.Token;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UserServiceImplTest {

    @Mock
    private BackendGateway backendGateway;

    @Mock
    private ClusterDAOGateway clusterDAOGateway;

    @InjectMocks
    private UserServiceImpl userService;

    private static final String TEST_CLUSTER_ID = "test-cluster-id";
    private static final String TEST_USERNAME = "testuser";
    private static final String TEST_QOS = "normal";
    private static final String TEST_ACCOUNT_ID = "test-account-id";

    @BeforeEach
    void setUp() {
        // 设置用户token
        Token.TokenResult tokenResult = new Token.TokenResult();
        Token.TokenResult.User.Domain domain = new Token.TokenResult.User.Domain();
        domain.setName(TEST_ACCOUNT_ID);
        domain.setId(TEST_ACCOUNT_ID);
        Token.TokenResult.User user = new Token.TokenResult.User();
        user.setDomain(domain);
        tokenResult.setUser(user);
        LogicUserService.setSubjectToken(tokenResult);
    }

    @Test
    void testChangeDomainUserQos_Success() {
        // Given
        Cluster cluster = createValidCluster();
        BackendActionProxyResponse response = createSuccessResponse();

        when(clusterDAOGateway.findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID)).thenReturn(cluster);
        when(backendGateway.actionProxy(eq(TEST_CLUSTER_ID), eq("domain_user_change_qos"), anyString()))
                .thenReturn(response);

        // When
        BaseResponse result = userService.changeDomainUserQos(TEST_CLUSTER_ID, TEST_USERNAME, TEST_QOS);

        // Then
        assertNotNull(result);
        verify(clusterDAOGateway).findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID);
        verify(backendGateway).actionProxy(TEST_CLUSTER_ID, "domain_user_change_qos", " -n " + TEST_USERNAME + " -q " + TEST_QOS);
    }

    @Test
    void testChangeDomainUserQos_ClusterNotFound() {
        // Given
        when(clusterDAOGateway.findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID)).thenReturn(null);

        // When & Then
        CommonException.RequestInvalidException exception = assertThrows(
                CommonException.RequestInvalidException.class,
                () -> userService.changeDomainUserQos(TEST_CLUSTER_ID, TEST_USERNAME, TEST_QOS)
        );

        assertEquals("目前不支持此功能", exception.getMessage());
        verify(clusterDAOGateway).findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID);
        verifyNoInteractions(backendGateway);
    }

    @Test
    void testChangeDomainUserQos_SchedulePluginDisabled() {
        // Given
        Cluster cluster = createValidCluster();
        cluster.setSchedulePlugin(0); // 禁用调度插件

        when(clusterDAOGateway.findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID)).thenReturn(cluster);

        // When & Then
        CommonException.RequestInvalidException exception = assertThrows(
                CommonException.RequestInvalidException.class,
                () -> userService.changeDomainUserQos(TEST_CLUSTER_ID, TEST_USERNAME, TEST_QOS)
        );

        assertEquals("目前不支持此功能", exception.getMessage());
        verify(clusterDAOGateway).findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID);
        verifyNoInteractions(backendGateway);
    }

    @Test
    void testChangeDomainUserQos_HybridClusterNotSupported() {
        // Given
        Cluster cluster = createValidCluster();
        cluster.setClusterType(ClusterType.HYBRID.nameLowerCase()); // 设置为混合云集群

        when(clusterDAOGateway.findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID)).thenReturn(cluster);

        // When & Then
        CommonException.RequestInvalidException exception = assertThrows(
                CommonException.RequestInvalidException.class,
                () -> userService.changeDomainUserQos(TEST_CLUSTER_ID, TEST_USERNAME, TEST_QOS)
        );

        assertEquals("目前不支持此功能", exception.getMessage());
        verify(clusterDAOGateway).findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID);
        verifyNoInteractions(backendGateway);
    }

    @Test
    void testChangeDomainUserQos_BackendServiceError() {
        // Given
        Cluster cluster = createValidCluster();
        BackendActionProxyResponse response = createErrorResponse();

        when(clusterDAOGateway.findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID)).thenReturn(cluster);
        when(backendGateway.actionProxy(eq(TEST_CLUSTER_ID), eq("domain_user_change_qos"), anyString()))
                .thenReturn(response);

        // When & Then
        CommonException.RelatedServiceException exception = assertThrows(
                CommonException.RelatedServiceException.class,
                () -> userService.changeDomainUserQos(TEST_CLUSTER_ID, TEST_USERNAME, TEST_QOS)
        );

        assertTrue(exception.getMessage().contains("delete domain user failed"));
        assertTrue(exception.getMessage().contains("Backend service error"));
        verify(clusterDAOGateway).findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID);
        verify(backendGateway).actionProxy(TEST_CLUSTER_ID, "domain_user_change_qos", " -n " + TEST_USERNAME + " -q " + TEST_QOS);
    }

    @Test
    void testChangeDomainUserQos_WithSpecialCharactersInParameters() {
        // Given
        String specialUsername = "user-with_special.chars";
        String specialQos = "high-priority";
        Cluster cluster = createValidCluster();
        BackendActionProxyResponse response = createSuccessResponse();

        when(clusterDAOGateway.findByClusterIdAll(TEST_CLUSTER_ID, TEST_ACCOUNT_ID)).thenReturn(cluster);
        when(backendGateway.actionProxy(eq(TEST_CLUSTER_ID), eq("domain_user_change_qos"), anyString()))
                .thenReturn(response);

        // When
        BaseResponse result = userService.changeDomainUserQos(TEST_CLUSTER_ID, specialUsername, specialQos);

        // Then
        assertNotNull(result);
        verify(backendGateway).actionProxy(TEST_CLUSTER_ID, "domain_user_change_qos",
                " -n " + specialUsername + " -q " + specialQos);
    }

    private Cluster createValidCluster() {
        Cluster cluster = new Cluster();
        cluster.setClusterId(TEST_CLUSTER_ID);
        cluster.setName("Test Cluster");
        cluster.setSchedulePlugin(1); // 启用调度插件
        cluster.setClusterType(ClusterType.CLOUD.nameLowerCase()); // 公有云集群
        cluster.setSchedulerType("slurm");
        return cluster;
    }

    private BackendActionProxyResponse createSuccessResponse() {
        BackendActionProxyResponse response = new BackendActionProxyResponse();
        response.setCode(200);
        response.setMessage("Success");
        response.setData("Operation completed successfully");
        return response;
    }

    private BackendActionProxyResponse createErrorResponse() {
        BackendActionProxyResponse response = new BackendActionProxyResponse();
        response.setCode(500);
        response.setMessage("Backend service error");
        response.setData(null);
        return response;
    }
}
