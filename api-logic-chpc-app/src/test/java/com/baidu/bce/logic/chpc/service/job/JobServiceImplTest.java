package com.baidu.bce.logic.chpc.service.job;

import com.baidu.bce.internalsdk.iam.model.Token;
import com.baidu.bce.logic.chpc.gateway.HelixJobDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.job.model.SubmitJobRequest;
import com.baidu.bce.logic.chpc.model.HelixJob;
import com.baidu.bce.logic.chpc.model.response.billing.OrderCreateResponse;
import com.baidu.bce.logic.chpc.model.response.job.CommonHelixJobResponse;
import com.baidu.bce.logic.chpc.service.IBillingService;
import com.baidu.bce.logic.chpc.service.util.SchedulerUserConfig;
import com.baidu.bce.logic.core.iam.service.LogicUserService;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import com.baidu.bce.logic.chpc.common.HelixConstant;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class JobServiceImplTest {

    @Mock
    private HelixJobDAOGateway helixJobDAOGateway;

    @Mock
    private IBillingService billingService;

    @Mock
    private RegionConfiguration regionConfiguration;

    @Mock
    private BackendGateway backendGateway;

    @Mock
    SchedulerUserConfig schedulerUserConfig;

    @InjectMocks
    private JobServiceImpl jobService;

    private SubmitJobRequest submitJobRequest;

    @BeforeEach
    public void setUp() {
        ReflectionTestUtils.setField(jobService, "globalBusRegistered", true);
        ReflectionTestUtils.setField(jobService, "helixfold3Price", 1.0);
        ReflectionTestUtils.setField(jobService, "helixCPUPrice", 1.0);
    }

    @Test
    public void testSubmitHelixvsSynJob() {
        submitJobRequest = new SubmitJobRequest();
        Map<String, String> envVars = new HashMap<>();
        envVars.put("extra_params", "{\"iam_account_id\":\"test-account\",\"billing_unit_count\":1,\"n_tokens\":10,\"github_real_id\":**********,\"items\":[{\"task_id\":\"test-task-id\"}]}");
        submitJobRequest.setEnvVars(envVars);
        submitJobRequest.setJobProduct(HelixConstant.HELIXVS_SYN);
        
        Token.TokenResult tokenResult = new Token.TokenResult();
        Token.TokenResult.User.Domain domain = new Token.TokenResult.User.Domain();
        domain.setName("test-account-id");
        domain.setId("test-account-id");
        Token.TokenResult.User user = new Token.TokenResult.User();
        user.setDomain(domain);
        tokenResult.setUser(user);
        LogicUserService.setSubjectToken(tokenResult);
        // when(LogicUserService.getAccountId()).thenReturn("test-account-id");
        // Mocking the backendGateway.actionProxy()
        BackendActionProxyResponse bresp = new BackendActionProxyResponse();
        bresp.setCode(200);
        bresp.setData("\"jobId123\"");
        // when(backendGateway.actionProxy(anyString(), anyString(), anyString())).thenReturn(bresp);
        // Mocking the helixJobDAOGateway.findByTaskId()
        when(helixJobDAOGateway.findByTaskId("test-task-id")).thenReturn(null);

        // Mocking the billingService.newOrder()
        OrderCreateResponse resp = new OrderCreateResponse();
        resp.setOrderId("test-order-id");
        when(billingService.newOrder(any(), anyString())).thenReturn(resp);
        // when(billingService.newOrder(any(CreateOrderRequest.class), anyString()).getOrderId()).thenReturn("test-order-id");

        // Mocking the helixJobDAOGateway.insert()
        when(helixJobDAOGateway.insert(any(HelixJob.class))).thenReturn(true);

        // Mocking the helixJobDAOGateway.update()
        when(helixJobDAOGateway.update(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(true);

        // Call the method
        CommonHelixJobResponse response = jobService.submitHelixJob(submitJobRequest);

        // Verify the results
        assertNotNull(response);
    }

    @Test
    public void testSubmitHelixvsJob() {
        submitJobRequest = new SubmitJobRequest();
        Map<String, String> envVars = new HashMap<>();
        envVars.put("extra_params", "{\"billing_unit_count\":1,\"n_tokens\":10,\"github_real_id\":**********,\"items\":[{\"task_id\":\"test-task-id\"}]}");
        submitJobRequest.setEnvVars(envVars);
        submitJobRequest.setJobProduct(HelixConstant.HELIXVS);
        
        Token.TokenResult tokenResult = new Token.TokenResult();
        Token.TokenResult.User.Domain domain = new Token.TokenResult.User.Domain();
        domain.setName("test-account-id");
        domain.setId("test-account-id");
        Token.TokenResult.User user = new Token.TokenResult.User();
        user.setDomain(domain);
        tokenResult.setUser(user);
        LogicUserService.setSubjectToken(tokenResult);
        // when(LogicUserService.getAccountId()).thenReturn("test-account-id");
        // Mocking the backendGateway.actionProxy()
        BackendActionProxyResponse bresp = new BackendActionProxyResponse();
        bresp.setCode(200);
        bresp.setData("\"jobId123\"");
        // when(backendGateway.actionProxy(anyString(), anyString(), anyString())).thenReturn(bresp);
        // Mocking the helixJobDAOGateway.findByTaskId()
        when(helixJobDAOGateway.findByTaskId("test-task-id")).thenReturn(null);

        // Mocking the billingService.newOrder()
        OrderCreateResponse resp = new OrderCreateResponse();
        resp.setOrderId("test-order-id");
        when(billingService.newOrder(any(), anyString())).thenReturn(resp);
        // when(billingService.newOrder(any(CreateOrderRequest.class), anyString()).getOrderId()).thenReturn("test-order-id");

        // Mocking the helixJobDAOGateway.insert()
        when(helixJobDAOGateway.insert(any(HelixJob.class))).thenReturn(true);

        // Mocking the helixJobDAOGateway.update()
        when(helixJobDAOGateway.update(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(true);

        // Call the method
        CommonHelixJobResponse response = jobService.submitHelixJob(submitJobRequest);

        // Verify the results
        assertNotNull(response);
    }

    @Test
    public void testSubmitLinearDesignJob() {
        submitJobRequest = new SubmitJobRequest();
        Map<String, String> envVars = new HashMap<>();
        envVars.put("extra_params", "{\"iam_account_id\":\"test-account\",\"billing_unit_count\":1,\"n_tokens\":10,\"github_real_id\":**********,\"items\":[{\"task_id\":\"test-task-id\"}]}");
        submitJobRequest.setEnvVars(envVars);
        submitJobRequest.setJobProduct(HelixConstant.LINEAR_DESIGN);
        
        Token.TokenResult tokenResult = new Token.TokenResult();
        Token.TokenResult.User.Domain domain = new Token.TokenResult.User.Domain();
        domain.setName("test-account-id");
        domain.setId("test-account-id");
        Token.TokenResult.User user = new Token.TokenResult.User();
        user.setDomain(domain);
        tokenResult.setUser(user);
        LogicUserService.setSubjectToken(tokenResult);
        // when(LogicUserService.getAccountId()).thenReturn("test-account-id");
        // Mocking the backendGateway.actionProxy()
        BackendActionProxyResponse bresp = new BackendActionProxyResponse();
        bresp.setCode(200);
        bresp.setData("\"jobId123\"");
        // when(backendGateway.actionProxy(anyString(), anyString(), anyString())).thenReturn(bresp);
        // Mocking the helixJobDAOGateway.findByTaskId()
        when(helixJobDAOGateway.findByTaskId("test-task-id")).thenReturn(null);

        // Mocking the billingService.newOrder()
        OrderCreateResponse resp = new OrderCreateResponse();
        resp.setOrderId("test-order-id");
        when(billingService.newOrder(any(), anyString())).thenReturn(resp);
        // when(billingService.newOrder(any(CreateOrderRequest.class), anyString()).getOrderId()).thenReturn("test-order-id");

        // Mocking the helixJobDAOGateway.insert()
        when(helixJobDAOGateway.insert(any(HelixJob.class))).thenReturn(true);

        // Mocking the helixJobDAOGateway.update()
        when(helixJobDAOGateway.update(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(true);

        // Call the method
        CommonHelixJobResponse response = jobService.submitHelixJob(submitJobRequest);

        // Verify the results
        assertNotNull(response);
    }

    @Test
    public void testSubmitLinearFoldJob() {
        submitJobRequest = new SubmitJobRequest();
        Map<String, String> envVars = new HashMap<>();
        envVars.put("extra_params", "{\"iam_account_id\":\"test-account\",\"billing_unit_count\":1,\"n_tokens\":10,\"github_real_id\":**********,\"items\":[{\"task_id\":\"test-task-id\"}]}");
        submitJobRequest.setEnvVars(envVars);
        submitJobRequest.setJobProduct(HelixConstant.LINEAR_FOLD);
        
        Token.TokenResult tokenResult = new Token.TokenResult();
        Token.TokenResult.User.Domain domain = new Token.TokenResult.User.Domain();
        domain.setName("test-account-id");
        domain.setId("test-account-id");
        Token.TokenResult.User user = new Token.TokenResult.User();
        user.setDomain(domain);
        tokenResult.setUser(user);
        LogicUserService.setSubjectToken(tokenResult);
        // when(LogicUserService.getAccountId()).thenReturn("test-account-id");
        // Mocking the backendGateway.actionProxy()
        BackendActionProxyResponse bresp = new BackendActionProxyResponse();
        bresp.setCode(200);
        bresp.setData("\"jobId123\"");
        // when(backendGateway.actionProxy(anyString(), anyString(), anyString())).thenReturn(bresp);
        // Mocking the helixJobDAOGateway.findByTaskId()
        when(helixJobDAOGateway.findByTaskId("test-task-id")).thenReturn(null);

        // Mocking the billingService.newOrder()
        OrderCreateResponse resp = new OrderCreateResponse();
        resp.setOrderId("test-order-id");
        when(billingService.newOrder(any(), anyString())).thenReturn(resp);
        // when(billingService.newOrder(any(CreateOrderRequest.class), anyString()).getOrderId()).thenReturn("test-order-id");

        // Mocking the helixJobDAOGateway.insert()
        when(helixJobDAOGateway.insert(any(HelixJob.class))).thenReturn(true);

        // Mocking the helixJobDAOGateway.update()
        when(helixJobDAOGateway.update(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(true);

        // Call the method
        CommonHelixJobResponse response = jobService.submitHelixJob(submitJobRequest);

        // Verify the results
        assertNotNull(response);
    }

    @Test
    public void testSubmitLinearPartitionJob() {
        submitJobRequest = new SubmitJobRequest();
        Map<String, String> envVars = new HashMap<>();
        envVars.put("extra_params", "{\"iam_account_id\":\"test-account\",\"billing_unit_count\":1,\"n_tokens\":10,\"github_real_id\":**********,\"items\":[{\"task_id\":\"test-task-id\"}]}");
        submitJobRequest.setEnvVars(envVars);
        submitJobRequest.setJobProduct(HelixConstant.LINEAR_PARTITION);
        
        Token.TokenResult tokenResult = new Token.TokenResult();
        Token.TokenResult.User.Domain domain = new Token.TokenResult.User.Domain();
        domain.setName("test-account-id");
        domain.setId("test-account-id");
        Token.TokenResult.User user = new Token.TokenResult.User();
        user.setDomain(domain);
        tokenResult.setUser(user);
        LogicUserService.setSubjectToken(tokenResult);
        // when(LogicUserService.getAccountId()).thenReturn("test-account-id");
        // Mocking the backendGateway.actionProxy()
        BackendActionProxyResponse bresp = new BackendActionProxyResponse();
        bresp.setCode(200);
        bresp.setData("\"jobId123\"");
        // when(backendGateway.actionProxy(anyString(), anyString(), anyString())).thenReturn(bresp);
        // Mocking the helixJobDAOGateway.findByTaskId()
        when(helixJobDAOGateway.findByTaskId("test-task-id")).thenReturn(null);

        // Mocking the billingService.newOrder()
        OrderCreateResponse resp = new OrderCreateResponse();
        resp.setOrderId("test-order-id");
        when(billingService.newOrder(any(), anyString())).thenReturn(resp);
        // when(billingService.newOrder(any(CreateOrderRequest.class), anyString()).getOrderId()).thenReturn("test-order-id");

        // Mocking the helixJobDAOGateway.insert()
        when(helixJobDAOGateway.insert(any(HelixJob.class))).thenReturn(true);

        // Mocking the helixJobDAOGateway.update()
        when(helixJobDAOGateway.update(anyString(), anyString(), anyString(), anyString(), anyString(), anyString(), anyBoolean())).thenReturn(true);

        // Call the method
        CommonHelixJobResponse response = jobService.submitHelixJob(submitJobRequest);

        // Verify the results
        assertNotNull(response);
    }
}