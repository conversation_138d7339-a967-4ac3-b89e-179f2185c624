package com.baidu.bce.logic.chpc.scheduler.task;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.when;

import com.baidu.bce.logic.chpc.common.ChpcConstant;
import com.baidu.bce.logic.chpc.common.SoftwareStatus;
import com.baidu.bce.logic.chpc.common.TaskStatus;
import com.baidu.bce.logic.chpc.gateway.InstanceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.model.Cluster;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.model.SoftwareRecord;
import com.baidu.bce.logic.chpc.model.Task;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class SoftwareOperationTaskTest{

    @Mock
    private SoftwareRecordDAOGateway softwareRecordDAOGateway;

    private Map<String, Object> paraMap;

    private Task task;

    @Mock
    private InstanceDAOGateway instanceDAOGateway;

    @InjectMocks
    private SoftwareOperationTask softwareOperationTask;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // testExecuteTaskNoMasterInstance 用于测试 executeTask
    // generated by Comate
    @Test
    public void testExecuteTaskNoMasterInstance() {
        Task task = new Task();
        task.setTaskId("test-task");
        task.setExtra("{\"extraKey\":\"extraValue\"}");
        Cluster cluster = new Cluster();
        cluster.setClusterId("test-cluster");
        Queue queue = new Queue();
        when(instanceDAOGateway.findByClusterId("test-cluster")).thenReturn(Arrays.asList());
        Map<String, Object> result = softwareOperationTask.executeTask(task, cluster, queue);
        assertEquals(TaskStatus.FAILED.getValue(), result.get(ChpcConstant.TASK_STATUS));
        assertEquals("[test-task] can't find master instance, clusterId: test-cluster", result.get(ChpcConstant.TASK_FAILED_REASON));
    }

    // testAfterExecuteTaskTaskFailed 用于测试 afterExecuteTask
    // generated by Comate
    @Test
    public void testAfterExecuteTaskTaskFailed() {
        paraMap = new HashMap<>();
        task = new Task();
        task.setClusterId("test-cluster-id");
    
        // Setup
        paraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
        paraMap.put(ChpcConstant.SOFTWARE_NAME, "test-software-name");
        paraMap.put(ChpcConstant.SOFTWARE_VERSION, "1.0.0");
        paraMap.put(ChpcConstant.TASK_FAILED_REASON, "Test failure reason");
    
        SoftwareRecord softwareRecord = new SoftwareRecord();
        softwareRecord.setStatus(SoftwareStatus.INSTALLING.nameLowerCase());
    
        // when(softwareRecordDAOGateway.findSoftwareByNameAndVersion(any(), any(), any())).thenReturn(softwareRecord);
    
        // Execute
        softwareOperationTask.afterExecuteTask(task, paraMap);
    
        // Verify
        // verify(softwareRecordDAOGateway, times(1)).update(any(SoftwareRecord.class));
    }

    // testAfterExecuteTaskTaskFailedAlreadyUpdated 用于测试 afterExecuteTask
    // generated by Comate
    @Test
    public void testAfterExecuteTaskTaskFailedAlreadyUpdated() {
        paraMap = new HashMap<>();
        task = new Task();
        task.setClusterId("test-cluster-id");
    
        // Setup
        paraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
        paraMap.put(ChpcConstant.SOFTWARE_NAME, "test-software-name");
        paraMap.put(ChpcConstant.SOFTWARE_VERSION, "1.0.0");
        paraMap.put(ChpcConstant.TASK_FAILED_REASON, "Test failure reason");
    
        SoftwareRecord softwareRecord = new SoftwareRecord();
        softwareRecord.setStatus(SoftwareStatus.INSTALL_FAILED.nameLowerCase());
    
        // when(softwareRecordDAOGateway.findSoftwareByNameAndVersion(any(), any(), any())).thenReturn(softwareRecord);
    
        // Execute
        softwareOperationTask.afterExecuteTask(task, paraMap);
    
        // Verify
        verify(softwareRecordDAOGateway, never()).update(any(SoftwareRecord.class));
    }

    // testAfterExecuteTaskTaskFailedUninstallFailed 用于测试 afterExecuteTask
    // generated by Comate
    @Test
    public void testAfterExecuteTaskTaskFailedUninstallFailed() {
        paraMap = new HashMap<>();
        task = new Task();
        task.setClusterId("test-cluster-id");
        paraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
        paraMap.put(ChpcConstant.SOFTWARE_NAME, "test-software-name");
        paraMap.put(ChpcConstant.SOFTWARE_VERSION, "1.0.0");
        paraMap.put(ChpcConstant.TASK_FAILED_REASON, "Test failure reason");
        SoftwareRecord softwareRecord = new SoftwareRecord();
        softwareRecord.setStatus(SoftwareStatus.UNINSTALLING.nameLowerCase());
        // when(softwareRecordDAOGateway.findSoftwareByNameAndVersion(any(), any(), any())).thenReturn(softwareRecord);
        softwareOperationTask.afterExecuteTask(task, paraMap);
        // verify(softwareRecordDAOGateway, times(1)).update(any(SoftwareRecord.class));
    }

    // testAfterExecuteTaskFailedStatusInstallFailed 用于测试 afterExecuteTask
    // generated by Comate
    @Test
    public void testAfterExecuteTaskFailedStatusInstallFailed() {
        // Prepare test data
        Task task = new Task();
        task.setClusterId("clusterId");
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
        paraMap.put(ChpcConstant.SOFTWARE_NAME, "softwareName");
        paraMap.put(ChpcConstant.SOFTWARE_VERSION, "1.0.0");
        paraMap.put(ChpcConstant.INSTANCE_ID, "instanceId");
        paraMap.put(ChpcConstant.TASK_FAILED_REASON, "Installation failed");
    
        SoftwareRecord softwareRecord = new SoftwareRecord();
        softwareRecord.setStatus(SoftwareStatus.INSTALLING.nameLowerCase());
        when(softwareRecordDAOGateway.findSoftwareByNameAndVersionAndInstanceId(
                eq("clusterId"), eq("softwareName"), eq("1.0.0"), eq("instanceId"))).thenReturn(softwareRecord);
    
        // Execute the method
        softwareOperationTask.afterExecuteTask(task, paraMap);
    
        // Verify the interactions
        // Verify that the software record status is updated to INSTALL_FAILED
        // You can add more assertions here to verify the updated status and message
    }

    // testAfterExecuteTaskFailedStatusUninstallFailed 用于测试 afterExecuteTask
    // generated by Comate
    @Test
    public void testAfterExecuteTaskFailedStatusUninstallFailed() {
        // Prepare test data
        Task task = new Task();
        task.setClusterId("clusterId");
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
        paraMap.put(ChpcConstant.SOFTWARE_NAME, "softwareName");
        paraMap.put(ChpcConstant.SOFTWARE_VERSION, "1.0.0");
        paraMap.put(ChpcConstant.INSTANCE_ID, "instanceId");
        paraMap.put(ChpcConstant.TASK_FAILED_REASON, "Uninstallation failed");
    
        SoftwareRecord softwareRecord = new SoftwareRecord();
        softwareRecord.setStatus(SoftwareStatus.UNINSTALLING.nameLowerCase());
        when(softwareRecordDAOGateway.findSoftwareByNameAndVersionAndInstanceId(
                eq("clusterId"), eq("softwareName"), eq("1.0.0"), eq("instanceId"))).thenReturn(softwareRecord);
    
        // Execute the method
        softwareOperationTask.afterExecuteTask(task, paraMap);
    
        // Verify the interactions
        // Verify that the software record status is updated to UNINSTALL_FAILED
        // You can add more assertions here to verify the updated status and message
    }

    // testAfterExecuteTaskFailedStatusInstallFailedAlreadyUpdated 用于测试 afterExecuteTask
    // generated by Comate
    @Test
    public void testAfterExecuteTaskFailedStatusInstallFailedAlreadyUpdated() {
        // Prepare test data
        Task task = new Task();
        task.setClusterId("clusterId");
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put(ChpcConstant.TASK_STATUS, TaskStatus.FAILED.getValue());
        paraMap.put(ChpcConstant.SOFTWARE_NAME, "softwareName");
        paraMap.put(ChpcConstant.SOFTWARE_VERSION, "1.0.0");
        paraMap.put(ChpcConstant.INSTANCE_ID, "instanceId");
        paraMap.put(ChpcConstant.TASK_FAILED_REASON, "Installation failed");
    
        SoftwareRecord softwareRecord = new SoftwareRecord();
        softwareRecord.setStatus(SoftwareStatus.INSTALL_FAILED.nameLowerCase());
        when(softwareRecordDAOGateway.findSoftwareByNameAndVersionAndInstanceId(
                eq("clusterId"), eq("softwareName"), eq("1.0.0"), eq("instanceId"))).thenReturn(softwareRecord);
    
        // Execute the method
        softwareOperationTask.afterExecuteTask(task, paraMap);
    
        // Verify the interactions
        // Verify that the software record status is not updated
        // You can add more assertions here to verify the updated status and message
    }

}