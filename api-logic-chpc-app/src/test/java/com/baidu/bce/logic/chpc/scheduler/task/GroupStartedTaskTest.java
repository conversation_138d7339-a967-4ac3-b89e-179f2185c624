package com.baidu.bce.logic.chpc.scheduler.task;

import com.baidu.bce.internalsdk.util.JacksonUtil;
import com.baidu.bce.logic.chpc.bcc.BccInstance;
import com.baidu.bce.logic.chpc.bcc.gateway.BccGateway;
import com.baidu.bce.logic.chpc.chpc.gateway.ChpcGateway;
import com.baidu.bce.logic.chpc.common.*;
import com.baidu.bce.logic.chpc.domainservice.CfsService;
import com.baidu.bce.logic.chpc.domainservice.ClusterService;
import com.baidu.bce.logic.chpc.domainservice.InstanceService;
import com.baidu.bce.logic.chpc.gateway.CfsDAOGateway;
import com.baidu.bce.logic.chpc.gateway.ClusterEventDAOGateway;
import com.baidu.bce.logic.chpc.gateway.QueueDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SoftwareRecordDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcc.Tag;
import com.baidu.bce.logic.chpc.model.*;
import com.baidu.bce.logic.chpc.model.Queue;
import com.baidu.bce.logic.chpc.oos.GetOosExecutionResponse;
import com.baidu.bce.logic.chpc.oos.gateway.OosGateway;
import com.baidu.bce.logic.chpc.service.IAutoScalingService;
import com.baidu.bce.logic.chpc.service.software.SoftwareServiceImpl;
import com.baidu.bce.plat.webframework.region.RegionConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

public class GroupStartedTaskTest {

    private static final Logger log = LoggerFactory.getLogger(GroupStartedTaskTest.class);
    @InjectMocks
    private GroupStartedTask groupStartedTask;

    @Mock
    private ChpcGateway chpcGateway;

    @Mock
    private SoftwareServiceImpl softwareServiceImpl;

    @Mock
    private IAutoScalingService iAutoScalingService;

    @Mock
    private CfsDAOGateway cfsDAOGateway;

    @Mock
    private BccGateway bccGateway;

    @Mock
    private SoftwareRecordDAOGateway softwareRecordDAOGateway;

    @Mock
    private OosGateway oosGateway;

    @Mock
    private ClusterEventDAOGateway clusterEventDAOGateway;

    @Mock
    private InstanceService instanceService;

    @Mock
    private RegionConfiguration regionConfiguration;

    @Mock
    private ClusterService clusterService;

    @Mock
    private QueueDAOGateway queueDAOGateway;

    @Mock
    private CfsService cfsService;


    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testExecuteTask_success() throws NoSuchFieldException, IllegalAccessException {
        // Arrange
        Task task = mock(Task.class);
        Cluster cluster = mock(Cluster.class);
        Queue queue = mock(Queue.class);
        Instance instance = new Instance();
        // mock(Instance.class);
        instance.setNodeType(InstanceNodeType.COMPUTE.name().toLowerCase());
        instance.setStatus(InstanceStatus.WAITING_TO_START.nameLowerCase());
        instance.setClusterId("clusterId");
        instance.setChargeType("postpaid");
        instance.setInstanceId("instanceId");
        instance.setCosStackId("cosStackId");
        instance.setOosExecutionId("oosExecutionId");
        instance.setQueueId("queueId");
        List<Instance> instances = new ArrayList<>();
        instances.add(instance);

        Instance masterInstance = new Instance();
        masterInstance.setInstanceId("masterId");
        masterInstance.setHostName("masterHost");
        masterInstance.setClusterId("clusterId");
        masterInstance.setChargeType("postpaid");
        masterInstance.setCosStackId("cosStackId");
        masterInstance.setOosExecutionId("oosExecutionId");
        masterInstance.setQueueId("queueId");
        masterInstance.setNodeType(InstanceNodeType.MASTER.name().toLowerCase());
        masterInstance.setStatus(InstanceStatus.WAITING_TO_START.nameLowerCase());
        instances.add(masterInstance);

        Instance loginInstance = new Instance();
        loginInstance.setInstanceId("loginId");
        loginInstance.setClusterId("clusterId");
        loginInstance.setChargeType("postpaid");
        loginInstance.setCosStackId("cosStackId");
        loginInstance.setOosExecutionId("oosExecutionId");
        loginInstance.setQueueId("queueId");
        loginInstance.setNodeType(InstanceNodeType.LOGIN.name().toLowerCase());
        loginInstance.setStatus(InstanceStatus.WAITING_TO_START.nameLowerCase());
        instances.add(loginInstance);

        Instance backupInstance = new Instance();
        backupInstance.setInstanceId("backupId");
        backupInstance.setClusterId("clusterId");
        backupInstance.setChargeType("postpaid");
        backupInstance.setCosStackId("cosStackId");
        backupInstance.setOosExecutionId("oosExecutionId");
        backupInstance.setQueueId("queueId");
        backupInstance.setNodeType(InstanceNodeType.BACKUP.name().toLowerCase());
        backupInstance.setStatus(InstanceStatus.WAITING_TO_START.nameLowerCase());
        instances.add(backupInstance);

        BccInstance bccInstance = new BccInstance();
        bccInstance.setInstanceId("instanceId");

        List<Tag> tags = new ArrayList<>();
        Tag gpuDriverTag = new Tag();
        gpuDriverTag.setTagKey("gpuDriverVersion");
        gpuDriverTag.setTagValue("1.0");
        tags.add(gpuDriverTag);

        Tag cudaTag = new Tag();
        cudaTag.setTagKey("cudaVersion");
        cudaTag.setTagValue("1.0");
        tags.add(cudaTag);

        Tag cudnnTag = new Tag();
        cudnnTag.setTagKey("cudnnVersion");
        cudnnTag.setTagValue("1.0");
        tags.add(cudnnTag);
        bccInstance.setTags(tags);

        BccInstance masterBccInstance = new BccInstance();
        masterBccInstance.setInstanceId("masterId");
        List<Tag> masterTags = new ArrayList<>();
        masterTags.add(gpuDriverTag);
        masterTags.add(cudaTag);
        masterTags.add(cudnnTag);
        masterBccInstance.setTags(masterTags);

        BccInstance loginBccInstance = new BccInstance();
        loginBccInstance.setInstanceId("loginId");
        List<Tag> loginTags = new ArrayList<>();
        loginTags.add(gpuDriverTag);
        loginTags.add(cudaTag);
        loginTags.add(cudnnTag);
        loginBccInstance.setTags(loginTags);

        BccInstance backupBccInstance = new BccInstance();
        backupBccInstance.setInstanceId("backupId");
        List<Tag> backupTags = new ArrayList<>();
        backupTags.add(gpuDriverTag);
        backupTags.add(cudaTag);
        backupTags.add(cudnnTag);
        backupBccInstance.setTags(backupTags);


        List<BccInstance> bccInstances = new ArrayList<>();
        bccInstances.add(bccInstance);
        bccInstances.add(masterBccInstance);
        bccInstances.add(loginBccInstance);
        bccInstances.add(backupBccInstance);
        // Collections.singletonList(bccInstance);
        GetOosExecutionResponse.Result oosResult = mock(GetOosExecutionResponse.Result.class);
        when(oosResult.getState()).thenReturn("success");

        // when(instance.getClusterId()).thenReturn("clusterId");
        // when(instance.getCosStackId()).thenReturn("cosStackId");
        // when(instance.getInstanceId()).thenReturn("instanceId");
        // when(instance.getNodeType()).thenReturn(InstanceNodeType.COMPUTE.name().toLowerCase());
        // when(instance.getStatus()).thenReturn(InstanceStatus.WAITING_TO_START.nameLowerCase());
        // when(instance.getOosExecutionId()).thenReturn("oosExecutionId");

        // when(bccInstance.getInstanceId()).thenReturn("instanceId");

        Cfs cfs = new Cfs();
        cfs.setCfsId("cfsId");
        cfs.setClusterId("clusterId");
        when(cfsService.findByClusterId(anyString())).thenReturn(Collections.singletonList(cfs));

        Queue queue1 = new Queue();
        queue.setQueueId("queueId");
        when(queueDAOGateway.getClusterDefaultQueue(anyString())).thenReturn(queue1);

        List<String> queueIds = new ArrayList<>();
        queueIds.add("queueId");
        List<String> cudaVersions = new ArrayList<>();
        cudaVersions.add("1.0");
        List<String> gpuDriverVersions = new ArrayList<>();
        gpuDriverVersions.add("1.0");
        List<String> cudnnVersions = new ArrayList<>();
        cudnnVersions.add("1.0");
        Map<String, Object> extrMap = new HashMap<>();
        extrMap.put(ChpcConstant.QUEUE_IDS, queueIds);
        extrMap.put(ChpcConstant.CHPC_CUDA_VERSION, cudaVersions);
        extrMap.put(ChpcConstant.CHPC_GPU_DRIVER_VERSION, gpuDriverVersions);
        extrMap.put(ChpcConstant.CHPC_CUDNN_VERSION, cudnnVersions);
        extrMap.put(ChpcConstant.COS_STACK_NAME, "cosStackName");
        String extra = JacksonUtil.toJson(extrMap);


        when(task.getCosStackId()).thenReturn("cosStackId");
        when(task.getClusterId()).thenReturn("clusterId");
        when(task.getExtra()).thenReturn(extra);

        when(cluster.getClusterId()).thenReturn("clusterId");
        when(cluster.getClusterType()).thenReturn(ClusterType.CLOUD.name().toLowerCase());

        when(instanceService.findByCosAndStatus(anyString(), anyString(), anyString(), anyString())).thenReturn(instances);
        when(clusterEventDAOGateway.findByClusterIdAndEvent(anyString(), anyString())).thenReturn(null);

        when(queue.getQueueId()).thenReturn("queueId");

        when(regionConfiguration.getCurrentRegion()).thenReturn("bj");
        when(clusterService.findBy(anyString())).thenReturn(cluster);
        when(instanceService.findBy(anyString(), any())).thenReturn(instances);
        when(instanceService.findByCosAndStatus(anyString(), anyString(), anyString(), anyString())).thenReturn(instances);
        when(instanceService.updateStatusAndExecutionId(anyString(), anyString(), anyString())).thenReturn(true);


        when(bccGateway.getBccInstances(anyList())).thenReturn(bccInstances);
        when(oosGateway.createExecution(any())).thenReturn("executionId");
        when(oosGateway.getExecutionById(anyString())).thenReturn(oosResult);
        Field bosEndpoint = GroupStartedTask.class.getDeclaredField("bosEndpoint");
        bosEndpoint.setAccessible(true);
        bosEndpoint.set(groupStartedTask, "bj");

        // Act
        Map<String, Object> result = groupStartedTask.executeTask(task, cluster, queue);

        // Assert
        assertEquals(TaskStatus.PROCESSING.getValue(), result.get(ChpcConstant.TASK_STATUS));
        // verify(instanceService, times(1)).updateStatusAndExecutionId("instanceId", "executionId", "starting");
    }
}
