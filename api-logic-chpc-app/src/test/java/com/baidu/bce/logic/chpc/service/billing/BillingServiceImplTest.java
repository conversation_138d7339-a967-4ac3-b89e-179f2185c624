package com.baidu.bce.logic.chpc.service.billing;

import com.baidu.bce.internalsdk.order.model.Order;
import com.baidu.bce.internalsdk.order.model.OrderStatus;
import com.baidu.bce.logic.chpc.gateway.HelixJobDAOGateway;
import com.baidu.bce.logic.chpc.gateway.OrderDAOGateway;
import com.baidu.bce.logic.chpc.gateway.SaasResourceDAOGateway;
import com.baidu.bce.logic.chpc.gateway.external.BackendGateway;
import com.baidu.bce.logic.chpc.gateway.external.model.bcakend.BackendActionProxyResponse;
import com.baidu.bce.logic.chpc.job.model.SubmitJobRequest;
import com.baidu.bce.logic.chpc.model.HelixJob;
import com.baidu.bce.logic.chpc.model.Instance;
import com.baidu.bce.logic.chpc.model.response.billing.OrderExecutorResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;

import com.baidu.bce.logic.chpc.billing.gateway.BillingGateway;
import com.baidu.bce.logic.chpc.common.HelixConstant;
import com.baidu.bce.logic.chpc.common.HelixJobStatus;
import com.baidu.bce.logic.chpc.common.exceptions.CommonException;
import com.baidu.bce.logic.chpc.config.LockConfig;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
public class BillingServiceImplTest {

    private String helixfold3Cluster = "helixfold3-cluster1234567890";

    private String helixvsCluster = "helixvs-cluster1234567890";

    @Mock
    private BillingGateway billingGateway;

    @Mock
    private OrderDAOGateway orderDAOGateway;

    @Mock
    private HelixJobDAOGateway helixJobDAOGateway;

    @Mock
    private SaasResourceDAOGateway saasResourceDAOGateway;

    @Mock
    private BackendGateway backendGateway;

    @Mock
    LockConfig lockConfig;

    @InjectMocks
    private BillingServiceImpl billingService;

    private Order order;

    @BeforeEach
    void setUp() {
        order = new Order();
        order.setStatus(OrderStatus.CREATING);
    }

    @Test
    void testExecute_HelixFlod3Order() {
        ReflectionTestUtils.setField(billingService, "helixfold3Cluster", "helixfold3-cluster1234567890");
        // Mock the order request
        Gson gson = new Gson();
        order.getItems().add(new Order.Item());
        order.getItems().get(0).setKey(HelixConstant.HELIXFOLD3_ORDER_KEY);
        order.getItems().get(0).setExtra("taskId123");
        HelixJob helixJob = new HelixJob();
        helixJob.setTaskId("taskId123");
        helixJob.setOrderId("orderId123");
        Map<String, String> modelInput = new HashMap<>();
        modelInput.put("output_path", "/home/<USER>");
        JsonArray items = new JsonArray();
        JsonObject itemObject = new JsonObject();
        itemObject.addProperty("task_id", "taskId123");
        items.add(itemObject);
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("model_input_json", modelInput);
        extraParams.put("env", "");
        extraParams.put("items", items);
        String extraParamJson = gson.toJson(extraParams);
        SubmitJobRequest submitJobRequest = new SubmitJobRequest();
        Map<String, String> envVars = new HashMap<>();
        envVars.put("extra_params", extraParamJson);
        submitJobRequest.setEnvVars(envVars);
        String extra = "";
        ObjectMapper mapper = new ObjectMapper();
        try {
            extra = mapper.writeValueAsString(submitJobRequest);
        } catch (Exception e) {
            return;
        }
        helixJob.setExtra(extra);
        // submitJobRequest
        when(lockConfig.tryLock(any(),anyLong(),anyLong(),any())).thenReturn(true);
        when(billingGateway.getOrder("", "orderId123")).thenReturn(order);
        BackendActionProxyResponse resp = new BackendActionProxyResponse();
        resp.setCode(200);
        resp.setData("\"jobId123\"");
        Instance instance = new Instance();
        instance.setInstanceId("instanceId123");
        instance.setFloatingIp("testIp");
        when(backendGateway.actionProxy(any(), any(), any(), any())).thenReturn(resp);
        when(helixJobDAOGateway.update(any(), any(), any(), any(), any(), any(), any())).thenReturn(true);
        when(backendGateway.actionProxy(any(), any(), any())).thenReturn(resp);
        helixJob.setClusterId(helixfold3Cluster);
        when(helixJobDAOGateway.findByTaskId(anyString())).thenReturn(helixJob);
        OrderExecutorResponse response = billingService.execute("orderId123");
        assertEquals(OrderStatus.CREATING.toString(), response.getExecutionStatus());

        when(backendGateway.actionProxy(any(), any(), any(), any())).thenThrow(new CommonException.RelatedServiceException("调度器连接失败", "scheduler"));
        response = billingService.check("orderId123");
        response = billingService.execute("orderId123");
        assertEquals(OrderStatus.CREATE_FAILED.toString(), response.getExecutionStatus());

        when(lockConfig.tryLock(any(),anyLong(),anyLong(),any())).thenReturn(false);
        response = billingService.check("orderId123");
        response = billingService.execute("orderId123");
        assertEquals(OrderStatus.CREATE_FAILED.toString(), response.getExecutionStatus());

        helixJob.setJobStatus(HelixJobStatus.SUBMIT_FAILED);
        response = billingService.check("orderId123");
        response = billingService.execute("orderId123");

        assertEquals(OrderStatus.CREATE_FAILED.toString(), response.getExecutionStatus());
    }

    @Test
    void testExecute_HelixVsOrder() {
        ReflectionTestUtils.setField(billingService, "helixfold3Cluster", "helixfold3-cluster1234567890");
        ReflectionTestUtils.setField(billingService, "helixvsCluster", "helixvs-cluster1234567890");
        // Mock the order request
        Gson gson = new Gson();
        order.getItems().add(new Order.Item());
        order.getItems().get(0).setKey(HelixConstant.HELIXVS_ORDER_KEY);
        order.getItems().get(0).setExtra("taskId123");
        HelixJob helixJob = new HelixJob();
        helixJob.setTaskId("taskId123");
        helixJob.setOrderId("orderId123");
        Map<String, String> modelInput = new HashMap<>();
        modelInput.put("output_path", "/home/<USER>");
        JsonArray items = new JsonArray();
        JsonObject itemObject = new JsonObject();
        itemObject.addProperty("task_id", "taskId123");
        items.add(itemObject);
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("model_input_json", modelInput);
        extraParams.put("env", "");
        extraParams.put("items", items);
        String extraParamJson = gson.toJson(extraParams);
        SubmitJobRequest submitJobRequest = new SubmitJobRequest();
        Map<String, String> envVars = new HashMap<>();
        envVars.put("extra_params", extraParamJson);
        submitJobRequest.setEnvVars(envVars);
        String extra = "";
        ObjectMapper mapper = new ObjectMapper();
        try {
            extra = mapper.writeValueAsString(submitJobRequest);
        } catch (Exception e) {
            return;
        }
        helixJob.setExtra(extra);
        // submitJobRequest
        when(lockConfig.tryLock(any(),anyLong(),anyLong(),any())).thenReturn(true);
        when(billingGateway.getOrder("", "orderId123")).thenReturn(order);
        BackendActionProxyResponse resp = new BackendActionProxyResponse();
        resp.setCode(200);
        resp.setData("\"jobId123\"");
        Instance instance = new Instance();
        instance.setInstanceId("instanceId123");
        instance.setFloatingIp("testIp");
        when(backendGateway.actionProxy(any(), any(), any(), any())).thenReturn(resp);
        when(helixJobDAOGateway.update(any(), any(), any(), any(), any(), any(), any())).thenReturn(true);
        when(backendGateway.actionProxy(any(), any(), any())).thenReturn(resp);
        helixJob.setClusterId(helixvsCluster);
        when(helixJobDAOGateway.findByTaskId(anyString())).thenReturn(helixJob);
        OrderExecutorResponse response = billingService.execute("orderId123");
        assertEquals(OrderStatus.CREATING.toString(), response.getExecutionStatus());

        when(backendGateway.actionProxy(any(), any(), any(), any())).thenThrow(new CommonException.RelatedServiceException("调度器连接失败", "scheduler"));
        response = billingService.check("orderId123");
        response = billingService.execute("orderId123");
        assertEquals(OrderStatus.CREATE_FAILED.toString(), response.getExecutionStatus());

        when(lockConfig.tryLock(any(),anyLong(),anyLong(),any())).thenReturn(false);
        response = billingService.check("orderId123");
        response = billingService.execute("orderId123");
        assertEquals(OrderStatus.CREATE_FAILED.toString(), response.getExecutionStatus());

        helixJob.setJobStatus(HelixJobStatus.SUBMIT_FAILED);
        response = billingService.check("orderId123");
        response = billingService.execute("orderId123");

        assertEquals(OrderStatus.CREATE_FAILED.toString(), response.getExecutionStatus());
        
    }

    @Test
    void testExecute_HelixCPUOrder() {
        ReflectionTestUtils.setField(billingService, "helixfold3Cluster", "helixfold3-cluster1234567890");
        ReflectionTestUtils.setField(billingService, "helixvsCluster", "helixvs-cluster1234567890");
        // Mock the order request
        Gson gson = new Gson();
        order.getItems().add(new Order.Item());
        order.getItems().get(0).setKey(HelixConstant.HELIX_CPU_ORDER_KEY);
        order.getItems().get(0).setExtra("taskId123");
        HelixJob helixJob = new HelixJob();
        helixJob.setTaskId("taskId123");
        Map<String, String> modelInput = new HashMap<>();
        modelInput.put("output_path", "/home/<USER>");
        JsonArray items = new JsonArray();
        JsonObject itemObject = new JsonObject();
        itemObject.addProperty("task_id", "taskId123");
        items.add(itemObject);
        Map<String, Object> extraParams = new HashMap<>();
        extraParams.put("model_input_json", modelInput);
        extraParams.put("env", "");
        extraParams.put("items", items);
        String extraParamJson = gson.toJson(extraParams);
        SubmitJobRequest submitJobRequest = new SubmitJobRequest();
        Map<String, String> envVars = new HashMap<>();
        envVars.put("extra_params", extraParamJson);
        submitJobRequest.setEnvVars(envVars);
        String extra = "";
        ObjectMapper mapper = new ObjectMapper();
        try {
            extra = mapper.writeValueAsString(submitJobRequest);
        } catch (Exception e) {
            return;
        }
        helixJob.setExtra(extra);
        // submitJobRequest
        when(lockConfig.tryLock(any(),anyLong(),anyLong(),any())).thenReturn(true);
        when(billingGateway.getOrder("", "orderId123")).thenReturn(order);
        BackendActionProxyResponse resp = new BackendActionProxyResponse();
        resp.setCode(200);
        resp.setData("\"jobId123\"");
        Instance instance = new Instance();
        instance.setInstanceId("instanceId123");
        instance.setFloatingIp("testIp");
        when(backendGateway.actionProxy(any(), any(), any(), any())).thenReturn(resp);
        when(helixJobDAOGateway.update(any(), any(), any(), any(), any(), any(), any())).thenReturn(true);
        when(backendGateway.actionProxy(any(), any(), any())).thenReturn(resp);
        helixJob.setClusterId(helixvsCluster);
        when(helixJobDAOGateway.findByTaskId(anyString())).thenReturn(helixJob);
        OrderExecutorResponse response = billingService.execute("orderId123");
        assertEquals(OrderStatus.CREATING.toString(), response.getExecutionStatus());

        when(backendGateway.actionProxy(any(), any(), any(), any())).thenThrow(new CommonException.RelatedServiceException("调度器连接失败", "scheduler"));
        response = billingService.check("orderId123");
        response = billingService.execute("orderId123");
        assertEquals(OrderStatus.CREATE_FAILED.toString(), response.getExecutionStatus());

        when(lockConfig.tryLock(any(),anyLong(),anyLong(),any())).thenReturn(false);
        response = billingService.check("orderId123");
        response = billingService.execute("orderId123");
        assertEquals(OrderStatus.CREATE_FAILED.toString(), response.getExecutionStatus());

        helixJob.setJobStatus(HelixJobStatus.SUBMIT_FAILED);
        response = billingService.check("orderId123");
        response = billingService.execute("orderId123");

        assertEquals(OrderStatus.CREATE_FAILED.toString(), response.getExecutionStatus());
        
    }

}